#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

# npm run format

npm run lint
exec < /dev/tty

# 使用git status --porcelain检查src和engine目录下是否有文件变更
if [ -n "$(git status --porcelain packages/webview/src/ packages/engine-connector)" ]; then
  while true; do
    read -p "Webview 或 Engine 内容有变更，请更新 packages/webview/CHANGELOG.md 后在提交，是否继续提交？(y/N)" yn
    if [ "$yn" = "" ]; then
      yn='N'
    fi
    case $yn in
        [Yy]* ) break;;  # 接受任意以Y或y开头的输入
        [Nn]* ) exit 1;;  # 接受任意以N或n开头的输入，并退出整个脚本
        * ) echo "请输入 Y 或 N.";;
    esac
  done
fi

npm run pre-commit
