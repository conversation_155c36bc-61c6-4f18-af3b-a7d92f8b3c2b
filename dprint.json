{"lineWidth": 120, "indentWidth": 4, "newLineKind": "lf", "useTabs": false, "typescript": {"semiColons": "prefer", "quoteStyle": "alwaysSingle", "quoteProps": "asNeeded", "useBraces": "always", "bracePosition": "sameLine", "singleBodyPosition": "nextLine", "nextControlFlowPosition": "nextLine", "trailingCommas": "onlyMultiLine", "arguments.trailingCommas": "never", "parameters.trailingCommas": "never", "operatorPosition": "nextLine", "preferHanging": false, "preferSingleLine": false, "parameters.preferSingleLine": true, "arrowFunction.useParentheses": "preferNone", "binaryExpression.linePerExpression": false, "jsx.bracketPosition": "nextLine", "jsx.forceNewLinesSurroundingContent": false, "jsx.quoteStyle": "preferDouble", "jsx.multiLineParens": "prefer", "memberExpression.linePerExpression": true, "typeLiteral.separatorKind.singleLine": "comma", "typeLiteral.separatorKind.multiLine": "semiColon", "enumDeclaration.memberSpacing": "newLine", "spaceAround": false, "spaceSurroundingProperties": false, "objectExpression.spaceSurroundingProperties": false, "objectPattern.spaceSurroundingProperties": false, "typeLiteral.spaceSurroundingProperties": false, "binaryExpression.spaceSurroundingBitwiseAndArithmeticOperator": true, "commentLine.forceSpaceAfterSlashes": true, "constructor.spaceBeforeParentheses": false, "constructorType.spaceAfterNewKeyword": false, "constructSignature.spaceAfterNewKeyword": false, "doWhileStatement.spaceAfterWhileKeyword": true, "exportDeclaration.spaceSurroundingNamedExports": false, "forInStatement.spaceAfterForKeyword": true, "forOfStatement.spaceAfterForKeyword": true, "forStatement.spaceAfterForKeyword": true, "forStatement.spaceAfterSemiColons": true, "functionDeclaration.spaceBeforeParentheses": false, "functionExpression.spaceBeforeParentheses": false, "functionExpression.spaceAfterFunctionKeyword": true, "getAccessor.spaceBeforeParentheses": false, "ifStatement.spaceAfterIfKeyword": true, "importDeclaration.spaceSurroundingNamedImports": false, "jsxSelfClosingElement.spaceBeforeSlash": true, "jsxExpressionContainer.spaceSurroundingExpression": false, "method.spaceBeforeParentheses": false, "setAccessor.spaceBeforeParentheses": false, "taggedTemplate.spaceBeforeLiteral": false, "typeAnnotation.spaceBeforeColon": false, "typeAssertion.spaceBeforeExpression": false, "whileStatement.spaceAfterWhileKeyword": true, "module.sortImportDeclarations": "maintain", "module.sortExportDeclarations": "maintain", "exportDeclaration.sortNamedExports": "maintain", "importDeclaration.sortNamedImports": "maintain", "ignoreNodeCommentText": "dprint-ignore", "ignoreFileCommentText": "dprint-ignore-file", "arguments.preferHanging": "never", "arrayExpression.preferHanging": "never", "parameters.preferHanging": "never", "tupleType.preferHanging": "never", "typeParameters.preferHanging": "never", "exportDeclaration.forceSingleLine": false, "importDeclaration.forceSingleLine": false, "exportDeclaration.forceMultiLine": false, "importDeclaration.forceMultiLine": false}, "json": {"indentWidth": 2}, "markdown": {}, "excludes": ["**/node_modules", "**/dist", "**/*-lock.json"], "plugins": ["https://plugins.dprint.dev/typescript-0.89.3.wasm", "https://plugins.dprint.dev/json-0.19.2.wasm", "https://plugins.dprint.dev/markdown-0.16.4.wasm"]}