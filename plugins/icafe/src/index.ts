import {PluginSetupContext} from '@comate/plugin-host';
import {HelpFallbackProvider} from './providers/fallback.js';
import {searchcmdProvider} from './providers/searchCmd.js';
import {findbyTitleProvider} from './providers/findbyTitle.js';
import {ApiQaSkillProvider} from './providers/apiQa.js';
import {HelpSkillProvider} from './providers/help.js';

export function setup({registry}: PluginSetupContext) {
    registry.registerFallbackProvider('fallback', HelpFallbackProvider, true);
    registry.registerSkillProvider('icafe-help', HelpSkillProvider);
    registry.registerSkillProvider('apiQa', ApiQaSkillProvider);
    registry.registerSkillProvider('searchBySelf', searchcmdProvider);
    registry.registerSkillProvider('findByTitle', findbyTitleProvider);
}
