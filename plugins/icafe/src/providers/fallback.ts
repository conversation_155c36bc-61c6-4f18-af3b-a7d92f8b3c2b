import {FallbackProvider, TaskProgressChunk, TaskProgressChunkStream} from '@comate/plugin-host';

const DESCRIPTION = `
该插件目前支持以下能力：
- 查询卡片（自然语言查询，输入为空时默认查询「最近14天我负责未完成的卡片」）
- 查询卡片 按标题查询（按照iCafe卡片标题搜索卡片，输入为空时默认查询「最近14天我负责未完成的卡片」）

您可以选择上述能力进行体验，也可以通过调整输入内容来体验不同的能力。
`;

export class HelpFallbackProvider extends FallbackProvider {
    static description = '能力解释';

    async *handleQuery(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new TaskProgressChunkStream();
        yield stream.fail(DESCRIPTION);
    }
}
