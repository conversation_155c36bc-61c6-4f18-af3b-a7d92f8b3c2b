import {
    ElementChunkStream,
    FunctionParameterDefinition,
    SkillProvider,
    TaskProgressChunk,
} from '@comate/plugin-host';
import {SEARCH_URL} from '../config/config.js';

interface Args {
    query: string;
}

export class findIcafeCardByTimeProvider extends SkillProvider<Args> {
    static skillName = 'findIcafeCardByTime';

    static displayName = '最近14天我负责的未完成的卡片';

    static description = '最近14天我负责的未完成的卡片';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new ElementChunkStream();
        const userDetail = await this.currentUser.requestDetail();
        const realUserName = userDetail && userDetail.name || '未知用户';
        const codePath = this.currentContext.activeFilePath;
        let queryInfo = this.currentContext.query;
        if (!queryInfo) {
            queryInfo = '最近14天我负责的未完成的卡片';
            yield stream.flushReplaceLast(<loading>输入为空时默认查询「最近14天我负责未完成的卡片」</loading>);
        }
        const raw = JSON.stringify({
            query: queryInfo,
            task_engine_userId: realUserName,
            codePath: codePath,
            source: 'comate',
            cmdType: 'findIcafeCardByTime',
        });
        const options = {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: raw,
        };

        try {
            const response = await fetch(SEARCH_URL, options);
            const result = await response.json();
            yield stream.flushReplaceLast(result.content); // markdown字符串
        }
        catch (ex) {
            yield stream.flushReplaceLast(
                <p>请求出错，请联系负责人或稍后再试。{ex instanceof Error ? ex.message : ''}</p>
            );
        }
    }

    defaultArgumentValue(): Args {
        return {query: '任意代码'};
    }
}
