import {
    ElementChunkStream,
    FunctionParameterDefinition,
    SkillProvider,
    TaskProgressChunk,
} from '@comate/plugin-host';
import {SEARCH_DATA_URL} from '../config/config.js';
interface Args {
    query: string;
}

export class findbyTitleProvider extends SkillProvider<Args> {
    static skillName = 'findByTitle';

    static displayName = '标题包含XXX的卡片';

    static description = '查询iCafe卡片';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new ElementChunkStream();
        const userDetail = await this.currentUser.requestDetail();
        const realUserName = userDetail && userDetail.name || '未知用户';
        const codePath = this.currentContext.activeFilePath;

        const host = await this.retriever.hostInfo();

        const queryDefault = '我负责的关键字包含needReplaceTitle的卡片';

        // 用户在输入框输入的问题
        let queryInfo = this.currentContext.query;
        if (!queryInfo) {
            queryInfo = '最近14天我负责的未完成的卡片';
        }
        else {
            queryInfo = queryDefault.replace('needReplaceTitle', queryInfo);
        }
        const tips = `正在为您查询卡片,当前查询条件为「${queryInfo}」,若修改查询条件,可在输入框中自定义`;
        yield stream.flushReplaceLast(<loading>{tips}</loading>);

        const raw = JSON.stringify({
            query: queryInfo,
            task_engine_userId: realUserName,
            codePath: codePath,
            source: host.appName,
            cmdType: 'findByTitle',
        });
        const options = {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: raw,
        };

        try {
            const response = await fetch(SEARCH_DATA_URL, options);
            const result = await response.json();
            const contentArrayDetails = result.content.cardReturnList;
            let icafeLinkMore = result.content.icafeLinkMore;
            const length = contentArrayDetails.length;
            yield stream.flushReplaceLast(<span>查询成功，结果如下：</span>);
            const icafeLinkMoreBack = 'https://console.cloud.baidu-int.com/devops/icafe/workspace';

            if (icafeLinkMore === '') {
                icafeLinkMore = icafeLinkMoreBack;
            }

            if (length !== 0) {
                for (let i = 0; i < length; i++) {
                    const cardPart = contentArrayDetails[i];
                    const cardIdAndTile = cardPart.cardIdAndTile;
                    const cardType = cardPart.cardType;
                    const cardStatus = cardPart.cardStatus;
                    const cardPlan = cardPart.cardPlan;
                    const cardDetailUrl = cardPart.cardDetailUrl;
                    const sequenceId = cardPart.sequenceId;
                    const title = cardPart.title;
                    const other_context = cardType + ' | ' + cardStatus + ' | ' + cardPlan;
                    const commit_context = 'git commit -m \'' + cardIdAndTile + '\'';
                    const tooltipText = '点击输入指令到终端:' + commit_context;

                    const markdownStr = ``;

                    yield stream.flush(
                        <p>
                            <blockquote>
                                <p>
                                    <flex betweenCentered gap="m">
                                        <flex gap="m">
                                            <a href={cardDetailUrl}>{sequenceId}</a>
                                        </flex>
                                        <flex flexEndCentered gap="m">
                                            <command-button
                                                commandName="commitMessage:confirmIssue"
                                                action="copy"
                                                tooltipText="点击复制: 卡片ID+标题"
                                                actionData={cardIdAndTile}
                                                data="yes"
                                            >
                                                复制
                                            </command-button>
                                            <command-button
                                                commandName="commitMessage:confirmIssue"
                                                action="insertIntoTerminal"
                                                tooltipText={tooltipText}
                                                actionData={commit_context}
                                                data="yes"
                                            >
                                                绑卡
                                            </command-button>
                                        </flex>
                                    </flex>
                                </p>
                                {title}
                                <p>{other_context}</p>
                            </blockquote>
                        </p>
                    );
                    if (i === length - 1) {
                        yield stream.flush(
                            <flex flexEndCentered>
                                <a href={icafeLinkMore}>↗去iCafe查看更多</a>
                            </flex>
                        );
                    }
                }
            }
            else {
                yield stream.flush(
                    <p>
                        <flex>
                            您暂时没有待处理的任务~ | <a href={icafeLinkMore}>↗去iCafe查看更多</a>
                        </flex>
                    </p>
                );
            }
        }
        catch (ex) {
            yield stream.flushReplaceLast(
                <p>请求出错，请联系负责人wangjie66或稍后再试。{ex instanceof Error ? ex.message : ''}</p>
            );
        }
    }

    defaultArgumentValue(): Args {
        return {query: '任意代码'};
    }
}
