import {
    FunctionParameterDefinition,
    SkillProvider,
    TaskProgressChunk,
    StringChunkStream,
    TextModel,
} from '@comate/plugin-host';
import dedent from 'dedent';

const promptTemplate = dedent`
你是一个技术专家，擅长回答iCafe的API相关问题和相关代码编写。根据以下知识回答用户的问题或完成代码编写任务：
{{trimedKnowledge}}
解答以下问题：
{{query}}
在回答问题时，要遵循前面的知识库，尽量简洁明了地回答问题。
在编写代码时，请确保最终代码完整不拆分，并尽可能减少文字内容。必要的文字内容应尽可能通过注释形式插入到代码中，而不是直接输出，涉及代码务必返回markdown格式，最后请务必保证所写代码的正确性。
`;

export class ApiQaSkillProvider extends SkillProvider {
    static skillName = 'apiQa';
    static displayName = 'API问答';
    static description = '根据iCafe API相关知识回答用户提问或完成代码编写任务';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new StringChunkStream();
        const query = this.currentContext.query;

        const knowledge = await this.retriever.knowledgeFromQuery(
            query,
            {
                knowledgeSets: [
                    {uuid: '715046cc-81e2-406b-aecf-db5e63882739', type: 'NORMAL'},
                ],
            }
        );

        const trimedKnowledge = this.trimKnowledge(knowledge, 8);

        const prompt = this.llm.createPrompt(
            promptTemplate,
            {trimedKnowledge, query}
        );

        const chunks = this.llm.askForTextStreaming(prompt, {model: TextModel.ErnieBot128});

        for await (const chunk of chunks) {
            yield stream.flush(chunk);
        }
    }

    private trimKnowledge(list: string[], maxCount = 6, maxLength = 10000) {
        const state = {
            length: 0,
        };
        const output: string[] = [];
        for (const item of list) {
            if (state.length + item.length > maxLength || output.length >= maxCount) {
                break;
            }
            output.push(item);
            state.length += item.length;
        }
        return output;
    }
}
