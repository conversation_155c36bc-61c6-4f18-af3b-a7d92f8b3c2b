{"private": true, "name": "@comate-plugin/icafe", "version": "0.8.0", "type": "module", "scripts": {"clean": "rm -rf dist", "dev": "cpd dev", "build": "tsc && cpd build"}, "dependencies": {"dedent": "^1.5.3"}, "devDependencies": {"@comate/plugin-cli": "^0.9.2", "@comate/plugin-host": "^0.9.2", "typescript": "^5.3.2"}, "comate": {"name": "icafe", "version": "1.0.0", "icon": "./assets/icafe.svg", "entry": "./dist/index.js", "displayName": "iCafe", "description": "智能查询iCafe卡片", "keyword": [], "capabilities": [{"type": "Fallback", "name": "fallback", "displayName": "插件介绍", "description": "确认插件成功运行并介绍开发方式"}, {"type": "Skill", "name": "searchBySelf", "displayName": "查询卡片", "description": "自义定查询语句查询iCafe卡片", "placeholder": "最近14天我负责未完成的卡片"}, {"type": "Skill", "name": "find<PERSON>yTitle", "displayName": "查询卡片 按标题查询", "description": "按照iCafe卡片标题搜索卡片", "placeholder": "请输入标题关键字"}, {"type": "Skill", "name": "icafe-help", "displayName": "插件介绍", "description": "插件介绍", "placeholder": "无需输入内容，回车获得插件介绍"}, {"type": "Skill", "name": "a<PERSON><PERSON><PERSON>", "displayName": "API问答", "description": "根据iCafe API相关知识回答用户提问或完成代码编写任务", "placeholder": "请输入iCafe API相关问题或编程任务"}], "configSchema": {"sections": []}}}