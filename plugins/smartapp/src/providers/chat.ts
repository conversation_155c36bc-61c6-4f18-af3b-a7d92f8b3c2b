import {
    SkillProvider,
    FunctionParameterDefinition,
    TaskProgressChunk,
    TextModel,
    TaskProgressChunkStream,
} from '@comate/plugin-host';

export class ChatSkillProvider extends SkillProvider {
    static skillName = 'answerQuestion';

    static displayName = '百度智能小程序智能问答';

    static description = '智能解答关于百度智能小程序的相关问题';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const {query} = this.currentContext;
        const stream = new TaskProgressChunkStream();

        if (!query) {
            yield stream.flushReplaceLast('请用自然语言描述你的问题');
            return;
        }

        yield stream.flush('正在思考中，请耐心等候...');

        const knowledge = await this.retriever.knowledgeFromQuery(
            query,
            {
                knowledgeSets: [
                    {uuid: '95dbb877-0d0a-4965-8774-7e988733dab1', type: 'NORMAL'},
                ],
            }
        );

        const prompt = this.llm.createPrompt(
            knowledge.length
                ? '你是百度智能小程序官方助手，不许回答微信小程序，请根据以下背景知识：\n\n{{knowledge}}\n\n回答问题：{{query}}'
                : '{{query}}',
            {knowledge, query}
        );

        const chunks = this.llm.askForTextStreaming(prompt, {model: TextModel.Default});

        yield stream.flushReplaceLast('');

        for await (const chunk of chunks) {
            yield stream.flush(chunk);
        }
    }
}
