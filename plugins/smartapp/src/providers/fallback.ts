import {
    TaskProgressChunk,
    TextModel,
    StringChunkStream,
    FallbackProvider,
} from '@comate/plugin-host';

export class CustomFallbackProvider extends FallbackProvider {
    static description = '智能解答关于百度智能小程序的相关问题';

    async *handleQuery(): AsyncIterableIterator<TaskProgressChunk> {
        const {query} = this.currentContext;
        const stream = new StringChunkStream();

        if (!query) {
            yield stream.flushReplaceLast('请用自然语言描述你的问题');
            return;
        }

        yield stream.flush('正在思考中，请耐心等候...');

        const knowledge = await this.retriever.knowledgeFromQuery(
            query,
            {
                knowledgeSets: [
                    {uuid: '95dbb877-0d0a-4965-8774-7e988733dab1', type: 'NORMAL'},
                ],
            }
        );

        const prompt = this.llm.createPrompt(
            knowledge.length
                ? '你是百度智能小程序官方助手，不许回答微信小程序，请根据以下背景知识：\n\n{{knowledge}}\n\n回答问题：{{query}}'
                : '{{query}}',
            {knowledge, query}
        );

        const chunks = this.llm.askForTextStreaming(prompt, {model: TextModel.Default});

        yield stream.flushReplaceLast('');

        for await (const chunk of chunks) {
            yield stream.flush(chunk);
        }
    }
}
