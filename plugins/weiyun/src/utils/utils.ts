import fs from 'node:fs';
import path from 'node:path';
import defaultConfig from '../config/demo.json';
import childProcess from 'child_process';

export const configFolder = './.comate';
export const configPath = `${configFolder}/weiyun.json`;

/**
 * 日志文件路径
 */
export const buildStdOutPath = `${configFolder}/weiyun_build_std_out.log`;
const buildStdErrorPath = `${configFolder}/weiyun_build_std_error.log`;
const buildErrorPath = `${configFolder}/weiyun_build_error.log`;

function checkFolderExistsSync(folderPath: string) {
    try {
        return fs.existsSync(folderPath) && fs.statSync(folderPath).isDirectory();
    }
    catch (err) {
        return false;
    }
}

function initConfigFile(cwd: string, mkdir?: boolean) {
    if (mkdir) {
        fs.mkdirSync(path.resolve(cwd, configFolder));
    }
    fs.writeFileSync(path.resolve(cwd, configPath), JSON.stringify(defaultConfig, null, 4));
}

function checkFileExistsSync(filePath: string) {
    try {
        return fs.existsSync(filePath) && fs.statSync(filePath).isFile();
    }
    catch (err) {
        return false;
    }
}

export function getDeployConfig(cwd: string) {
    try {
        const fileBuffer = fs.readFileSync(path.resolve(cwd, configPath));
        return {type: 'file', value: fileBuffer};
    }
    catch (ex: any) {
        const initMessage = '检测到您没有配置微云部署配置，已为您初始化一份Demo配置，'
            + `请参考Demo配置说明进行修改。文件地址：[点击打开配置文件](${configPath})，`
            + '修改并保存后在comate输入配置名后即可开始部署流程。';

        const isFolderExisted = checkFolderExistsSync(path.resolve(cwd, configFolder));
        if (!isFolderExisted) {
            initConfigFile(cwd, true);

            return {type: 'message', content: initMessage};
        }

        const isFileExisted = checkFileExistsSync(path.resolve(cwd, configPath));
        if (!isFileExisted) {
            initConfigFile(cwd);
            return {type: 'message', content: initMessage};
        }

        throw new Error(`获取配置文件失败 ${ex instanceof Error ? ex.message : ex}`);
    }
}

export function executeCommand(command: string, cwd: string): Promise<{stdout: string, stderr: string}> {
    return new Promise((resolve, reject) => {
        childProcess.exec(command, {
            maxBuffer: Infinity,
            cwd,
        }, (error, stdout, stderr) => {
            if (stdout) {
                fs.writeFileSync(path.resolve(cwd, buildStdOutPath), stdout);
            }
            if (stderr) {
                fs.writeFileSync(path.resolve(cwd, buildStdErrorPath), stderr);
            }
            if (error) {
                fs.writeFileSync(path.resolve(cwd, buildErrorPath), error?.message || '');
                reject(error);
            }
            else {
                resolve({stdout, stderr});
            }
        });
    });
}
