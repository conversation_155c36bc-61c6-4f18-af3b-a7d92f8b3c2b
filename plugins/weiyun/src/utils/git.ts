import {SimpleGit, simpleGit} from 'simple-git';
// const path = require('path');

export interface GitFileStatus {
    index: string;
    path: string;
}

export class GitUtil {
    private readonly git: SimpleGit;

    constructor(cwd: string) {
        this.git = simpleGit(cwd);
    }

    async getChangedFiles(): Promise<GitFileStatus[]> {
        const status = await this.git.status();
        return status.files.map(v => ({index: v.index, path: v.path}));
    }

    async getDiffForFile(file: string) {
        const diff = await this.git.diff(['HEAD', '--', file]);
        return diff;
    }

    async getHistoryCommits() {
        const history = await this.git.log({maxCount: 10, format: {message: '%s'}});
        return history.all.map(v => v.message);
    }

    async readFileAtRevision(revision: string, file: string) {
        const result = await this.git.show(`${revision}:${file}`);
        return result;
    }

    async getCurrentBranch() {
        const result = await this.git.branch();
        return result;
    }

    async getRepositoryName() {
        try {
            const remotes = await this.git.getRemotes(true);
            const originRemote = remotes.find(remote => remote.name === 'origin');
            if (originRemote && originRemote.refs.fetch) {
                const repoUrl = originRemote.refs.fetch;
                const regex = /:\/\/[^\/]+\/(.*)/;
                const match = repoUrl.match(regex);
                return match ? match[1] : null;
            }
            else {
                return null;
            }
        }
        catch (error) {
            console.error('Error:', error);
        }
    }
}
