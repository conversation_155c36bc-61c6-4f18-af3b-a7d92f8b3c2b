import {PluginSetupContext} from '@comate/plugin-host';
import {HelpSkillProvider} from './providers/help.js';
import {WeiyunFallbackProvider} from './providers/fallback.js';
import {WeiyunDeployProvider} from './providers/weiyunAppDeploy.js';

export function setup({registry}: PluginSetupContext) {
    registry.registerSkillProvider('weiyun-help', HelpSkillProvider);
    registry.registerSkillProvider('weiyunDeploy', WeiyunDeployProvider);
    registry.registerFallbackProvider('fallback', WeiyunFallbackProvider);
}
