import {FallbackProvider, TaskProgressChunk, TaskProgressChunkStream} from '@comate/plugin-host';

const DESCRIPTION = `
该插件目前支持以下能力：
- 插件介绍
- 应用部署

您可以选择上述指令进行体验，欢迎使用微云插件。
`;

export class WeiyunFallbackProvider extends FallbackProvider {
    static description = '能力解释';

    async *handleQuery(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new TaskProgressChunkStream();
        yield stream.fail(DESCRIPTION);
    }
}
