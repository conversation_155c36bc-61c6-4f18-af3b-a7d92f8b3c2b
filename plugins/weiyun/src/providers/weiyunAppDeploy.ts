import {
    FunctionParameterDefinition,
    SkillProvider,
    TaskProgressChunk,
    TaskProgressChunkStream,
    ProviderInit,
    // TableRenderColumn,
} from '@comate/plugin-host';
import FormData from 'form-data';
import fs from 'node:fs';
import path from 'node:path';
import fetch from 'node-fetch';
import crypto from 'crypto';
import {configPath, getDeployConfig, executeCommand, buildStdOutPath} from '../utils/utils.js';
import {
    getWyDeployUserTokenUrl,
    sendLogUrl,
    WEIYUNTOKEN,
    uploadMultipartFileUrl,
    uploadMultiFastDeployApp,
    usrSecretKey,
    getPipelineOutputUrl,
} from '../config/config.js';
import {GitUtil} from '../utils/git.js';

interface Args {
    query: string;
}

interface Config {
    confName?: string;
    pipelineCode?: string;
    buildCmd?: string;
    output?: string;
    noticeGroups?: boolean;
}

interface PointLog {
    startTime: string;
    userName: string;
    compileTime: string;
    compileResult: string;
    codebase: string;
    // pipelineCode: string;
    comateConfig: string;
    uploadTime: string;
    uploadResult: string;
    pipelineCodes: string[];
    ide: string;
    outputFileSize: string;
    comateVersion: string;
    outputReduceSize: string;
}

const CHUNK_SIZE = 1024 * 1024 * 10;
const MAX_RETRIES = 3;

export class WeiyunDeployProvider extends SkillProvider<Args> {
    static skillName = 'weiyunDeploy';

    static displayName = '微云应用部署';

    static description = '微云应用部署';

    private readonly cwd: string;

    private readonly git: GitUtil;

    constructor(init: ProviderInit) {
        super(init);
        this.cwd = init.cwd;
        this.git = new GitUtil(init.cwd);
    }

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const {query} = this.currentContext;
        let input = query.trim();
        const stream = new TaskProgressChunkStream();
        const userDetail = await this.currentUser.requestDetail();
        const realUserName = userDetail && userDetail.name || '未知用户';
        const host = await this.retriever.hostInfo();

        console.info('weiyun deploy!');
        let startTime = Date.now();

        const inputs = input.split(/\s+/);

        yield stream.flush('↣↣↣↣↣↣↣↣↣↣【基础信息】↤↤↤↤↤↤↤↤↤↤ \n\n');
        // yield stream.flush(` • ide:  ${host.appName}\n\n`);
        let fileBuffer;
        try {
            const data = getDeployConfig(this.cwd);
            if (data.type === 'message') {
                yield stream.flush(data.content + '\n\n');
                return;
            }
            else {
                fileBuffer = data.value;
            }
        }
        catch (ex) {
            yield stream.flush(ex instanceof Error ? ex.message : `${ex}`);
            return;
        }

        if (!fileBuffer) {
            yield stream.flush(' • 获取配置文件失败 \n\n');
            return;
        }

        // yield stream.flush(` • 获取[配置文件](${configPath})成功，查找配置: ${input} \n\n`);
        const sourceCode = fileBuffer.toString();

        let configs = [] as {confName: string}[];
        try {
            configs = JSON.parse(sourceCode);
        }
        catch (error) {
            yield stream.flush(' • 未找到指定配置，请查看上线配置文件是否配置正确 \n\n');
            return;
        }

        // 获取用户token
        let userToken = '';
        try {
            const userJSON = {
                userName: realUserName,
            };
            const response = await fetch(getWyDeployUserTokenUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'WEIYUN-TOKEN': WEIYUNTOKEN,
                },
                body: JSON.stringify(userJSON),
            });

            if (response.ok) {
                const data = await response.json();
                if (data && data.data.userToken) {
                    userToken = data.data.userToken;
                }
                else {
                    yield stream.flush(` • 获取微云用户信息失败\n\n`);
                    return;
                }
            }
            else {
                const data = await response.json();
                yield stream.flush(` • 获取微云用户信息失败,接口返回： ${JSON.stringify(data, null, 2)}\n\n`);
                return;
            }
        }
        catch (error) {
            yield stream.flush(` • 获取微云用户信息异常: ${error}\n\n`);
            return;
        }
        try {
            // yield stream.flush(` • userToken:  ${userToken}\n\n`);
            let userTokenAes = aesEncrypt(userToken, usrSecretKey);
            // yield stream.flush(` • userToken加密:  ${userTokenAes}\n\n`);
            // let userTokenDec = decrypt(userTokenAes, usrSecretKey)
            // yield stream.flush(` • userToken解密:  ${userTokenDec}\n\n`);
        }
        catch (error) {
            yield stream.flush(` • userToken加密异常:  ${error}\n\n`);
            yield stream.flush(error instanceof Error ? error.message : `${error}`);
        }

        // let userInfoEndTime = Date.now();

        // 判断是否存在改动
        let retriever = this.retriever;
        const repository = await retriever.repositoryInfo();
        if (repository.versionControl !== 'git' || !repository.repositoryUrl) {
            yield stream.flush(` • 看起来您的当前项目并不是一个Git仓库,无法进行自动化部署 \n\n`);
            return;
        }
        const branch = await this.git.getCurrentBranch();
        yield stream.flush(` • 当前分支: ${branch.current} \n\n`);
        const projectName = await this.git.getRepositoryName();
        yield stream.flush(` • 当前项目: ${projectName} \n\n`);
        const files = await this.git.getChangedFiles();
        if (files && files.length > 0) {
            yield stream.flush(` • 修改文件数量: ${files.length} \n\n`);
        }
        else {
            yield stream.flush(` • 本项目没有文件改动，请注意! \n\n`);
        }

        // 拆解命令，第二个参数可能是指令
        let inputConfNames: string[] = [];
        let command = '';
        let deleteOutput = true;
        let replacePipelineCodes: string[] = [];
        if (inputs[1] && inputs[1].startsWith('-')) {
            command = inputs[1];
            if (command === '-p' && inputs.length > 2) {
                replacePipelineCodes = inputs.slice(2).map(str => str.trim());
                yield stream.flush(` • 执行指令: ${inputs[1]}, 指定对应环境的流水线配置\n\n`);
                yield stream.flush(` • 将使用的流水线是: ${replacePipelineCodes}\n\n`);
            }
            else if (command === '-debug') {
                deleteOutput = false;
            }
            else {
                yield stream.flush(` • 输入的指令: ${command} 为不合法指令 \n\n`);
                return;
            }
            inputConfNames[0] = inputs[0];
        }
        else {
            inputConfNames = inputs;
        }

        let confNames = [...new Set(inputConfNames)];
        const missingInputs = confNames.filter(str =>
            !configs.some((obj: unknown) => (obj as {confName: string}).confName === str)
        );
        if (missingInputs.length > 0) {
            yield stream.flush(` • 未找到「${missingInputs}」的上线配置，请检查配置文件配置是否正确 \n\n`);
            return;
        }
        yield stream.flush(` • 获取[配置文件](${configPath})成功，执行配置: ${confNames} \n\n`);

        const length = confNames.length;
        for (let i = 0; i < length; i++) {
            let config: Config = {};
            for (const element of configs) {
                if (element.confName === confNames[i]) {
                    config = element;
                    break;
                }
            }

            if (!config.confName) {
                yield stream.flush(` • confName配置不能为空，请检查配置文件配置是否正确 \n\n`);
                return;
            }
            if (!config.pipelineCode && replacePipelineCodes.length == 0) {
                yield stream.flush(` • pipelineCode配置不能为空，请检查配置文件配置是否正确 \n\n`);
                return;
            }
            let configPipelineCodes: string[] = [];
            if (replacePipelineCodes.length > 0) {
                let uniqueArray = [...new Set(replacePipelineCodes)];
                configPipelineCodes = uniqueArray;
                // yield stream.flush(` • pipelineCode替换 \n\n`);
            }
            else {
                const pipelineCodeStr = config.pipelineCode?.toString().split(',').map(str => str.trim());
                let uniqueArray = [...new Set(pipelineCodeStr)];
                configPipelineCodes = uniqueArray;
            }

            const outputFileNamePath = config.output || 'output';
            if (
                outputFileNamePath.startsWith('/') || outputFileNamePath.startsWith('..')
                || outputFileNamePath.startsWith('./')
            ) {
                yield stream.flush(' • output配置请填写项目内的output相对路径: eg: 项目名/output \n\n');
                return;
            }

            let envConfig: {[key: string]: any} = {};
            envConfig = {...config};
            try {
                const {stdout, stderr} = await executeCommand('env', this.cwd);
                envConfig.env = stdout;
            }
            catch (ex) {
            }

            // 构建初始化埋点对象
            let log: PointLog = {
                userName: realUserName,
                startTime: startTime + '',
                compileTime: 0 + '',
                compileResult: '',
                codebase: projectName + '',
                // pipelineCode: configPipelineCodes.join(','),
                comateConfig: JSON.stringify(envConfig),
                uploadTime: 0 + '',
                uploadResult: '',
                pipelineCodes: configPipelineCodes,
                ide: host.appName,
                outputFileSize: 0 + '',
                comateVersion: 'v5',
                outputReduceSize: 0 + '',
            };

            // 检查流水线配置
            let isReduce = false;
            const pipelineOutputs = await getPipelineOutput(configPipelineCodes);
            console.log('getPipelineOutput返回值：', pipelineOutputs);
            if (pipelineOutputs != undefined && pipelineOutputs.size == configPipelineCodes.length) {
                for (let i = 0; i < configPipelineCodes.length; i++) {
                    let key = configPipelineCodes[i];
                    const value = pipelineOutputs.get(key);
                    if (value === undefined || value === null || (typeof value === 'string' && value.trim() === '')) {
                        isReduce = false;
                        if (length > 1) {
                            yield stream.flush(` • 本地配置名称：${config.confName} 的pipelineCode配置异常 \n\n`);
                        }
                        yield stream.flush(` • 流水线${key}异常，不存在或单服务发布没有配置output \n\n`);
                        return;
                    }
                    else {
                        if (value.startsWith('./output') || value.startsWith('/output') || value.startsWith('output')) {
                            isReduce = true;
                        }
                    }
                }
            }

            // 开始编译
            yield stream.flush('↣↣↣↣↣↣↣↣↣↣【编译环节】↤↤↤↤↤↤↤↤↤↤ \n\n');
            let compileTimeStartTime = Date.now();
            if (config.buildCmd) {
                if (length > 1) {
                    yield stream.flush(` • 执行本地编译配置环境：${config.confName} \n\n`);
                }
                let compileStartTime = Date.now();
                // yield stream.flush(`执行本地编译命令: ${config.buildCmd} \n\n`);
                try {
                    yield stream.flush(` • 执行本地编译中，请稍等... \n\n`);
                    const {stdout, stderr} = await executeCommand(config.buildCmd, this.cwd);
                    log.compileTime = (Date.now() - compileStartTime) + '';
                    log.compileResult = 'SUCCESS';
                }
                catch (ex) {
                    yield stream.flush(` • 编译命令执行异常: ${ex}，[编译日志](${buildStdOutPath}) \n\n`);
                    log.compileTime = (Date.now() - compileStartTime) + '';
                    const error = ex as Error;
                    if (error.message.length > 60000) {
                        log.compileResult = error.message.slice(0, 60000 - 3) + '...';
                    }
                    else {
                        log.compileResult = error.message;
                    }
                    sendLog(log, '');
                    return;
                }
            }
            else {
                yield stream.flush(` • 配置编译命令为空，请检查weiyun.json中的buildCmd配置 \n\n`);
                return;
            }

            let compileTimeEndTime = Date.now();
            yield stream.flush(` • 本地编译完成，耗时: ${(compileTimeEndTime - compileTimeStartTime) / 1000}秒 \n\n`);

            const outputFilePath = path.resolve(this.cwd, outputFileNamePath);
            const outputZipFilePath = outputFilePath.concat('.tar.gz');
            if (!fs.existsSync(outputFilePath)) {
                yield stream.flush(` • 没有找到output产出包: ${config.output} \n\n`);
                sendLog(log, '');
                return;
            }
            else {
                const files = fs.readdirSync(outputFilePath);
                if (files.length === 0) {
                    yield stream.flush(` • output产出包为空，请检查编译过程 \n\n`);
                    sendLog(log, '');
                    return;
                }
                console.log('产出包地址:', outputFilePath, ', 文件数量：', files.length);
                const outputFolderSize = await calculateFolderSize(outputFilePath);
                console.log('产出包文件夹大小:', outputFolderSize);

                let uniqueAllowedFiles: string[] = [];
                if (isReduce) {
                    const outputPaths = [];
                    for (const value of pipelineOutputs.values()) {
                        outputPaths.push(value);
                    }
                    console.log('保留的文件路径:', outputPaths);
                    console.log('文件路径:', outputFileNamePath);
                    console.log(`outputFilePath: ${outputFilePath}`);
                    const allowedFiles = outputPaths.map(filePath => path.normalize(filePath));
                    const filePathResults: string[] = [];
                    let reduceFileSize = 0;
                    for (const item of allowedFiles) {
                        const result = checkFileExistsSync(outputFilePath, item);
                        if (result != null) {
                            filePathResults.push(result);
                            reduceFileSize += await getFileSize(result);
                        }
                        else {
                            isReduce = false;
                            console.log(`${item}文件不存在`);
                            break;
                        }
                    }
                    uniqueAllowedFiles = Array.from(new Set(allowedFiles));
                    console.log(`reduceFileSize: ${reduceFileSize}`);
                    console.log(`reduce:`, outputFolderSize - reduceFileSize);
                    log.outputReduceSize = ((outputFolderSize - reduceFileSize) / (1024 * 1024)).toFixed(2);
                }

                let compress = '';
                if (isReduce) {
                    console.log(`uniqueAllowedFiles: ${uniqueAllowedFiles}`);
                    const modifiedArray = uniqueAllowedFiles.map(item => `./${item}`);
                    const resultString = modifiedArray.join(' ');
                    compress = 'cd '
                        .concat(outputFilePath)
                        .concat(' && cd ..')
                        .concat(' && tar -zcvf ./output.tar.gz ')
                        .concat(resultString);
                    console.log(`compress压缩命令: ${compress}`);
                }
                else {
                    compress = 'cd '
                        .concat(outputFilePath)
                        .concat('&& cd .. ')
                        .concat('&& tar -zcvf ./output.tar.gz ./output ');
                }

                // yield stream.flush(`产出包路径: ${outputFilePath} \n\n`);
                // const compress = 'cd '
                //     .concat(outputFilePath)
                //     .concat('&& cd .. ')
                //     .concat('&& tar -zcvf ./output.tar.gz ./output ');
                // yield stream.flush(`开始执行压缩, 命令${compress}\n\n`);
                try {
                    if (fs.existsSync(outputZipFilePath)) {
                        fs.unlink(outputZipFilePath, err => {
                            if (err) {
                                console.error(`Error deleting file: ${err}`);
                            }
                            else {
                                console.log('File deleted successfully.');
                            }
                        });
                    }
                    const {stdout, stderr} = await executeCommand(compress, this.cwd);
                }
                catch (ex) {
                    yield stream.flush(` • 执行压缩命令: ${ex}，[压缩日志](${buildStdOutPath}) \n\n`);
                    sendLog(log, '');
                    return;
                }
                yield stream.flush(` • 执行本地压缩命令完成\n\n`);
            }

            if (!fs.existsSync(outputZipFilePath)) {
                yield stream.flush(` • 没有找到output产出包: ${config.output}\n\n`);
                sendLog(log, '');
                return;
            }
            const stats = fs.statSync(outputZipFilePath);
            const fileSizeInMB = stats.size / (1024 * 1024);
            const fileSize = fileSizeInMB.toFixed(2);
            log.outputFileSize = fileSize;

            // 上传文件发布
            yield stream.flush('↣↣↣↣↣↣↣↣↣↣【部署环节】↤↤↤↤↤↤↤↤↤↤ \n\n');
            if (length > 1) {
                yield stream.flush(` • 上传文件,配置环境：${config.confName} \n\n`);
            }
            yield stream.flush(` • 上传文件,请稍等...\n\n`);
            // 分片上传
            let md5StartTime = Date.now();
            const fileContent = fs.readFileSync(outputZipFilePath);
            const hash = crypto.createHash('md5');
            hash.update(fileContent);
            const md5Hash = hash.digest('hex');
            // yield stream.flush(` • 生产文件md5，耗时: ${(Date.now() - md5StartTime) / 1000}秒 \n\n`);
            let uploadFileStartTime = Date.now();

            const response = await uploadFile(outputZipFilePath, md5Hash);
            if (response instanceof Array) {
                const allSuccessful = response.every(result => result.code === 0);
                if (!allSuccessful) {
                    yield stream.flush(` • 分片上传失败: ${response}\n\n`);
                    log.uploadResult = '分片上传失败';
                    sendLog(log, '');
                    return;
                }
                else {
                    log.uploadTime = (Date.now() - uploadFileStartTime) + '';
                    log.uploadResult = 'SUCCESS';
                    yield stream.flush(` • 文件上传完成，耗时: ${(Date.now() - uploadFileStartTime) / 1000}秒 \n\n`);
                }
            }
            else {
                yield stream.flush(` • 分片上传失败: ${response}\n\n`);
                log.uploadResult = response;
                sendLog(log, '');
                return;
            }

            let updateSatrtTime = Date.now();
            yield stream.flush(` • 构建部署服务，请稍等...\n\n`);
            let requestId = '';
            let userTokenAes = '';
            try {
                userTokenAes = aesEncrypt(userToken, usrSecretKey);
            }
            catch (error) {
                yield stream.flush(` • userToken加密异常:  ${error}\n\n`);
            }

            try {
                let noticeGroups = config.noticeGroups || '';
                noticeGroups = noticeGroups.toString().replace(/\s+/g, '');
                console.log(`发布上传参数noticeGroups: ${noticeGroups}`);
                const data = {
                    outputFileMd5: md5Hash,
                    userToken: userTokenAes,
                    pipelineCodeList: configPipelineCodes,
                    branch: branch.current,
                    noticeGroups: noticeGroups,
                    codebase: projectName,
                };

                // yield stream.flush(` • 发布上传参数: ${JSON.stringify(data, null, 2)} \n\n`);
                console.log(`发布上传参数: ${JSON.stringify(data, null, 2)}`);

                const headers = {
                    'WEIYUN-TOKEN': WEIYUNTOKEN,
                    'Content-Type': 'application/json',
                };

                const uploadRes = await fetch(
                    uploadMultiFastDeployApp,
                    {
                        method: 'POST',
                        body: JSON.stringify(data),
                        headers: headers,
                    }
                );

                if (uploadRes.ok) {
                    const data = await uploadRes.json();
                    requestId = data.requestId;
                    if (data && data.code == 0) {
                        const resultArray = data.data.result;
                        // yield stream.flush(` • 发布成功resultArray的类型 ${typeof resultArray} \n\n`);
                        if (!Array.isArray(resultArray)) {
                            yield stream.flush(
                                ` • 发布成功resultArray的类型不是数组 ${JSON.stringify(resultArray, null, 2)} \n\n`
                            );
                            return;
                        }
                        if (resultArray.length === 0) {
                            yield stream.flush(` • 发布返回异常: ${JSON.stringify(data, null, 2)}\n\n`);
                        }
                        else if (resultArray.length > 0 && resultArray.length == 1) {
                            if (resultArray[0].code == 0) {
                                yield stream.flush(` • 发布成功，耗时：${(Date.now() - updateSatrtTime) / 1000}秒\n\n`);
                                yield stream.flush(
                                    ` • 点击查看[服务发布地址](${resultArray[0].appUrl})（‐＾▽＾‐）\n\n`
                                );
                            }
                            else if (resultArray[0].code == 406) {
                                yield stream.flush(` • 抱歉您没有服务权限，请联系管理员添加权限\n\n`);
                            }
                            else {
                                yield stream.flush(` • 发布失败: ${resultArray[0].message}\n\n`);
                            }
                        }
                        else {
                            yield stream.flush(` • 发布成功，耗时：${(Date.now() - updateSatrtTime) / 1000}秒\n\n`);
                            for (const item of resultArray) {
                                if (item.code == 0) {
                                    yield stream.flush(` • 流水线: ${item.pipelineCode} 发布成功\n\n`);
                                    yield stream.flush(` • 点击查看[服务发布地址](${item.appUrl})（‐＾▽＾‐）\n\n`);
                                }
                                else if (item.code == 406) {
                                    yield stream.flush(
                                        ` • 抱歉您没有流水线: ${item.pipelineCode} 服务权限，请联系管理员添加权限\n\n`
                                    );
                                }
                                else {
                                    yield stream.flush(` • 流水线: ${item.pipelineCode} 发布失败: ${item.message}\n\n`);
                                }
                            }
                        }
                    }
                    else if (data && data.code == 201) {
                        yield stream.flush(` • 发布失败:  ${data.message}\n\n`);
                    }
                    else {
                        yield stream.flush(` • 发布失败:  ${JSON.stringify(data, null, 2)}\n\n`);
                    }
                }
                else {
                    const data = await uploadRes.json();
                    requestId = data.requestId;
                    yield stream.flush(` • 发布失败，异常返回: ${data.message}\n\n`);
                }
            }
            catch (error) {
                yield stream.flush(` • 发布异常: ${error}\n\n`);
            }

            // 清除压缩包
            if (deleteOutput) {
                fs.unlink(outputZipFilePath, err => {
                    if (err) {
                        console.error(`Error deleting file: ${err}`);
                    }
                    else {
                        console.log('File deleted successfully.');
                    }
                });
                fs.rmdir(outputFilePath, {recursive: true}, err => {
                    if (err) {
                        console.error(`Error deleting directory: ${err}`);
                    }
                    else {
                        console.log('Directory deleted successfully.');
                    }
                });
            }

            // 上报埋点日志
            sendLog(log, requestId);
            console.log('上报日志：', log);
        }
    }
}

async function sendLog(log: PointLog, requestId: string): Promise<any> {
    try {
        const logRes = await fetch(
            sendLogUrl,
            {
                method: 'POST',
                body: JSON.stringify(log),
                headers: {'WEIYUN-TOKEN': WEIYUNTOKEN, 'Content-Type': 'application/json', request_id: requestId},
            }
        );
        if (logRes.ok) {
            const data = await logRes.json();
            return data;
        }
        else {
            return {Status: logRes.status};
        }
    }
    catch (error) {
        return {error: error};
    }
}

async function uploadChunk(
    filePath: string,
    start: number,
    end: number,
    index: number,
    totalChunks: number,
    md5Hash: string
): Promise<any> {
    const options = {start, end: end - 1};
    const fileStream = fs.createReadStream(filePath, options);
    const form = new FormData();
    form.append('file', fileStream, path.basename(filePath));
    form.append('md5', md5Hash);
    form.append('chunkNumber', index);
    form.append('totalNumber', totalChunks);
    form.append('chunkSize', CHUNK_SIZE);

    const headers = {
        ...form.getHeaders(),
        'WEIYUN-TOKEN': WEIYUNTOKEN,
    };
    try {
        // const shouldFail = Math.random() < 0.3;
        //  if (shouldFail) {
        //     // 模拟请求失败
        //     console.info('Simulating request failure');
        //     throw new Error('Simulated request failure');
        // }
        // console.info('Simulating request failure');
        const response = await fetch(uploadMultipartFileUrl, {
            method: 'POST',
            body: form,
            headers: headers,
        });
        if (!response.ok) {
            throw new Error(`Failed to upload chunk ${index}`);
        }
        return response.json();
    }
    catch (error) {
        throw new Error(`Failed to upload chunk,error: ${error}`);
    }
}

async function uploadFile(filePath: string, md5Hash: string) {
    const stats = fs.statSync(filePath);
    const totalSize = stats.size;
    const totalChunks = Math.ceil(totalSize / CHUNK_SIZE);
    let start = 0;
    let end = CHUNK_SIZE;
    let index = 0;

    const uploadPromises = [];

    while (start < totalSize) {
        if (end > totalSize) {
            end = totalSize;
        }
        uploadPromises.push(uploadChunkWithRetry(filePath, start, end, index, totalChunks, md5Hash));

        start = end;
        end = start + CHUNK_SIZE;
        index += 1;
    }
    try {
        const results = await Promise.all(uploadPromises);
        return results;
    }
    catch (error) {
        return 'Error occurred during chunk upload:' + (error instanceof Error ? error.message : `异常`);
    }
}

async function uploadChunkWithRetry(
    filePath: string,
    start: number,
    end: number,
    index: number,
    totalChunks: number,
    md5Hash: string,
    retries = 0
): Promise<any> {
    try {
        return await uploadChunk(filePath, start, end, index, totalChunks, md5Hash);
    }
    catch (error) {
        if (retries < MAX_RETRIES) {
            console.warn(`Chunk ${index} failed. Retrying ${retries + 1}/${MAX_RETRIES}...`);
            return uploadChunkWithRetry(filePath, start, end, index, totalChunks, md5Hash, retries + 1);
        }
        else {
            throw new Error(
                `Chunk ${index} failed after ${MAX_RETRIES} retries. Error: ${
                    error instanceof Error
                        ? error.message
                        : `Unknown Error`
                }`
            );
        }
    }
}

// 定义加密函数
function aesEncrypt(text: string, secretKey: string) {
    const cipher = crypto.createCipheriv('aes-256-ecb', Buffer.from(secretKey), null);
    cipher.setAutoPadding(true);
    let encrypted = cipher.update(text, 'utf8', 'base64');
    encrypted += cipher.final('base64');
    return encrypted;
}

function decrypt(text: string, secretKey: string) {
    const decipher = crypto.createDecipheriv('aes-256-ecb', Buffer.from(secretKey), null);
    let decrypted = decipher.update(text, 'base64', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
}

async function getPipelineOutput(pipelines: string[]): Promise<Map<string, string>> {
    try {
        const url = `${getPipelineOutputUrl}?pipelines=${encodeURIComponent(pipelines.join(','))}`;
        console.info(`Getting pipeline output from ${url}`);
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'WEIYUN-TOKEN': WEIYUNTOKEN,
            },
        });

        if (!response.ok) {
            return new Map<string, string>();
        }
        const data = await response.json();
        if (data.code == 0 && data.data && data.data.outputName) {
            const pipelineOutput = data.data.outputName;
            if (pipelineOutput !== undefined) {
                // return pipelineOutput;
                return new Map<string, string>(Object.entries(pipelineOutput));
            }
            else {
                return new Map<string, string>();
            }
        }
    }
    catch (error) {
        return new Map<string, string>();
    }
    return new Map<string, string>();
}

// async function cleanDirectory(directory: string, keepPaths: string[]) {
//     console.info(`清理目录： ${directory} 保留文件: ${keepPaths}`);
//     if (keepPaths === undefined || keepPaths.length === 0) {
//         console.error(`保留文件异常keepPaths： ${keepPaths}`);
//         return;
//     }
//     try {
//         const files = await fs.promises.readdir(directory, { withFileTypes: true });
//         for (const file of files) {
//             const fullPath = path.join(directory, file.name);
//             if (file.isDirectory()) {
//                 await cleanDirectory(fullPath, keepPaths);
//                 const remainingFiles = await fs.promises.readdir(fullPath);
//                 if (remainingFiles.length === 0) {
//                     await fs.promises.rmdir(fullPath);
//                 }
//                 } else {
//                 if (!keepPaths.includes(fullPath)) {
//                     console.info(`清理文件： ${fullPath}`);
//                     await fs.promises.unlink(fullPath);
//                 }
//               }
//             }
//         } catch (err) {
//             console.error(`读取目录时出错 ${directory}: ${err}`);
//         }
// }

function checkFileExistsSync(basePath: string, relativePath: string) {
    try {
        const cleanedRelativePath = relativePath.replace(/^output\//, '');
        const absolutePath = path.resolve(basePath, cleanedRelativePath);
        console.info('checkFileExistsSync生成地址：', absolutePath);
        fs.accessSync(absolutePath);
        return absolutePath;
    }
    catch (err) {
        console.error(`Error while checkFileExistsSync :`, err);
        return null;
    }
}

async function calculateFolderSize(dirPath: string) {
    let totalSize = 0;
    const files = await fs.promises.readdir(dirPath, {withFileTypes: true});
    for (const file of files) {
        const fullPath = path.join(dirPath, file.name);
        if (file.isDirectory()) {
            totalSize += await calculateFolderSize(fullPath);
        }
        else {
            const stats = await fs.promises.stat(fullPath);
            totalSize += stats.size;
        }
    }
    return totalSize;
}

async function getFileSize(filePath: string) {
    const stats = await fs.promises.stat(filePath);
    return stats.size;
}
