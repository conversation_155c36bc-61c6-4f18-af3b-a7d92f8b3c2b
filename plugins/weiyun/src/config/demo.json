[{"confName": "『必填』，应用部署时Comate对话的命令，例如: dev", "pipelineCode": "『必填』，单服务发布中的流水线名称，支持配置多个流水线，用英文逗号分隔", "buildCmd": "『必填』，ci.yaml中的编译生成output包的打包命令，可以参考ci.yml配置填写。", "output": "『选填』，不填默认在当前跟目录下，是本地编译的产出包地址，根目录下的相对地址，无需/或则./开头，例如: test/output", "noticeGroups": "『选填』，消息通知如流群支持多个群账号用逗号隔开，如流群消息是通过机器人发送，请在群聊中添加成员:chattopic10", "desc": "『描述』附加描述,使用详情请参考知识库：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/HXFtYvbMQj/1hkwvaZ7CE/KO4fLLDr2FP0K0"}]