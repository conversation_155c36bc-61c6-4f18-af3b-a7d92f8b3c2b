// 生产环境
export const uploadFastDeployAppUrl =
    'https://developer.weiyun.baidu.com/new/console/api/v1/expose/uploadFastDeployApp';
export const getWyDeployUserTokenUrl = 'https://developer.weiyun.baidu.com/new/console/api/v1/expose/getUserToken';
export const WEIYUNTOKEN = 'xrEy%uDsX1pg^MLaW#shBXcEl&q#v2HX';
export const sendLogUrl = 'http://developer.weiyun.baidu.com/new/console/api/v1/expose/comateExecuteRecord';
export const uploadMultipartFileUrl = 'http://developer.weiyun.baidu.com/new/console/api/v1/expose/chunkUploadBigFile';
export const uploadMultiFastDeployApp =
    'http://developer.weiyun.baidu.com/new/console/api/v1/expose/uploadMultiFastDeployApp';
export const usrSecretKey = 'e1c1d00ae0ec496e8963b7e975184e03';
export const getPipelineOutputUrl = 'http://developer.weiyun.baidu.com/new/console/api/v1/expose/getPipelineOutput';

// 测试环境
// export const sendLogUrl = 'http://infoflow-mesh-test.dev.weiyun.baidu.com/weiyun/console/api/v1/expose/comateExecuteRecord';
// export const WEIYUNTOKEN = 'Ng!HTX&lDngXGWxPO^!Bc4Z*XRAS0B7F'
// export const uploadFastDeployAppUrl = 'http://infoflow-mesh-test.dev.weiyun.baidu.com/weiyun/console/api/v1/expose/uploadFastDeployApp';
// export const getWyDeployUserTokenUrl = 'http://infoflow-mesh-test.dev.weiyun.baidu.com/weiyun/console/api/v1/expose/getUserToken';
// export const uploadMultiFastDeployApp = 'http://infoflow-mesh-test.dev.weiyun.baidu.com/weiyun/console/api/v1/expose/uploadMultiFastDeployApp';
// export const uploadMultipartFileUrl = 'http://infoflow-mesh-test.dev.weiyun.baidu.com/weiyun/console/api/v1/expose/chunkUploadBigFile'
// export const usrSecretKey = 'e1c1d00ae0ec496e8963b7e975184e03'
// export const getPipelineOutputUrl = 'http://infoflow-mesh-test.dev.weiyun.baidu.com/weiyun/console/api/v1/expose/getPipelineOutput'

// 本地环境
// export const uploadMultipartFileUrl = 'http://*************:8080/console/api/v1/expose/chunkUploadBigFile'
// export const uploadFastDeployAppUrl = 'http://*************:8080/console/api/v1/expose/uploadFastDeployApp';
// export const sendLogUrl = 'http://*************:8080/console/api/v1/expose/comateExecuteRecord';
// export const WEIYUNTOKEN = 'g*BUbbXbq43VeDJb*n$UBHgRgPt8UXmG'
