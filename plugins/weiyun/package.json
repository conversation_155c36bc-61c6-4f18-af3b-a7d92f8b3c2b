{"private": true, "name": "@comate-plugin/weiyun", "version": "0.8.0", "type": "module", "scripts": {"clean": "rm -rf dist", "dev": "cpd dev", "build": "tsc && cpd build"}, "devDependencies": {"@comate/plugin-cli": "^0.9.2", "@comate/plugin-host": "^0.9.2", "form-data": "^4.0.0", "node-fetch": "^2.6.1", "@types/node-fetch": "^2.6.1"}, "comate": {"name": "wei<PERSON>", "version": "1.0.0", "icon": "./assets/icon.png", "entry": "./dist/index.js", "displayName": "微云助手", "description": "微云助手", "keyword": [], "capabilities": [{"type": "Skill", "name": "weiyun-help", "displayName": "插件介绍", "description": "微云助手是什么"}, {"type": "Skill", "name": "weiyunDeploy", "displayName": "应用部署", "description": "微云更新部署", "placeholder": "请输入部署应用对应的配置名"}, {"type": "Fallback", "name": "fallback", "displayName": "对话框兜底", "description": "对话框兜底"}], "configSchema": {"sections": []}}, "dependencies": {"form-data": "^4.0.0", "simple-git": "^3.22.0"}}