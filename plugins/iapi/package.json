{"private": true, "name": "@comate-plugin/iapi", "version": "0.9.3", "type": "module", "scripts": {"clean": "rm -rf dist", "dev": "cpd dev", "build": "tsc && cpd build && cp static/tree-sitter.wasm dist/ && cp -R static/lang dist/static"}, "devDependencies": {"@comate/plugin-cli": "^0.9.2", "@comate/plugin-host": "^0.9.2"}, "comate": {"name": "iapi", "version": "1.0.4", "icon": "./assets/icon.png", "entry": "./dist/index.js", "displayName": "iAPI", "description": "接口智能协作", "keyword": ["接口", "API", "iAPI", "iapi"], "author": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "muli", "email": "<EMAIL>"}], "capabilities": [{"type": "Skill", "name": "iapi-help", "displayName": "插件介绍", "description": "插件介绍", "placeholder": "无需输入内容，回车获得插件介绍"}, {"type": "Skill", "name": "genServerCode", "displayName": "接口实现代码", "description": "可用于接口文档智能生成接口实现代码，覆盖 controller 层、service 层、dao 层、model 层等，为接口服务开发提效。", "placeholder": "请输入iAPI接口文档ID或者接口文档协作链接"}, {"type": "Skill", "name": "genApiComment", "displayName": "接口代码注释", "description": "智能生成选中的接口代码的注释。", "placeholder": "框选代码后直接回车或点击发送"}], "configSchema": {"enabled": true, "sections": [{"title": "平台配置", "properties": {"serverLanguage": {"type": "string", "enum": ["Java"], "title": "接口实现代码的语言", "description": "选择要生成的接口实现代码的编程语言", "default": "Java"}, "serverFramework": {"type": "string", "enum": ["SpringBoot"], "title": "接口实现代码的框架", "description": "选择要生成的接口实现代码的框架，需与语言匹配", "default": "SpringBoot"}, "serverDaoFramework": {"type": "string", "enum": ["JPA"], "title": "接口实现代码的 Dao 层框架", "description": "选择要生成的接口实现代码的 Dao 层框架", "default": "JPA"}}}]}}, "dependencies": {"web-tree-sitter": "^0.20.8"}}