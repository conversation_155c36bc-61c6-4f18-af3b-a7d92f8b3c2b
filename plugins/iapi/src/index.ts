import {HelpSkillProvider} from './providers/help.js';
import {PluginSetupContext} from '@comate/plugin-host';
import {GenServerCodeSkillProvider} from './providers/genServerCode.js';
import {GenApiCommentSkillProvider} from './providers/genApiComment.js';

export function setup({registry}: PluginSetupContext) {
    registry.registerSkillProvider('iapi-help', HelpSkillProvider);
    registry.registerSkillProvider('genServerCode', GenServerCodeSkillProvider);
    registry.registerSkillProvider('genApiComment', GenApiCommentSkillProvider);
}
