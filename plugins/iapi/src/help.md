您好，我是您的 API 研发智能化助手 iAPI，我可以为您生成接口实现代码、接口代码注释等。

**接口实现代码**
* 输入来自 [iAPI官网](https://iapi.baidu-int.com/) 的接口ID或接口档协作链接后，点击发送或直接回车;
* 只需片刻，iAPI 将为您生成 Java 语言 SpringBoot 框架的实现代码，实现代码中包括 Bean、Dao、Service 、Controller 层；
* 详细教程请点击 [接口实现代码生成](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/t7n0qKWNJW/7urCLykvXO/Q0sCGFKFXa7OR8) 查看；

**接口代码注释**
* 框选需要生成注释的代码片段（接口实现方法、入参出参 Bean 结构等），点击发送或直接回车；
* 只需片刻，iAPI 将为您生成代码注释，目前支持 Java、Go 语言代码生成注释，生成的注释符合[API注释规范](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/m2l2hEPGZF/B4D1Lp4Gcj8-tJ#anchor-d8dad8d3-bfde-11ee-8384-830bbf057322)；
* 详细教程请点击 [接口代码注释生成](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/t7n0qKWNJW/7urCLykvXO/OyndOzRpviu47s) 查看；

如果您在使用过程中发现问题或有其他需求、建议，欢迎加入我们的如流用户群 **8301696** 进行反馈。
