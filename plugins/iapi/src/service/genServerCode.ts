import {request} from './index.js';
import {AI_SERVER_HOST} from '../config/config.js';
interface IGenerateServerCodeParams {
    apiId: number;
    apiName: string;
    story: string;
    username: string;
    codeType: string;
    taskId?: number;
    serverLanguage: string;
    serverFramework: string;
    serverDaoFramework: string;
}

export const generateServerCode = (_this: any, params: IGenerateServerCodeParams) => {
    return request(
        _this,
        'post',
        `${AI_SERVER_HOST}/code/generate`,
        {
            body: JSON.stringify({
                ...params,
            }),
            headers: {
                'Content-Type': 'application/json',
            },
        },
        200000
    );
};
