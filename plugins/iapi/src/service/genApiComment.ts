import {request} from './index.js';
import {AI_SERVER_HOST} from '../config/config.js';
interface IGenerateApiCommentParams {
    codeContent: string;
    codeLanguage: string;
    username: string;
}

export const generateApiComment = (_this: any, params: IGenerateApiCommentParams) => {
    return request(
        _this,
        'post',
        `${AI_SERVER_HOST}/code/comments`,
        {
            body: JSON.stringify({
                ...params,
            }),
            headers: {
                'Content-Type': 'application/json',
            },
        },
        200000
    );
};
