import {request} from './index.js';
import {BAPI_SERVER_HOST} from '../config/config.js';
export const getHttpApiInfo = (_this: any, username: string, apiDesc: string, apiId: number) => {
    return request(
        _this,
        'get',
        `${BAPI_SERVER_HOST}/bapi/iapi/api?username=${username}&apiDesc=${apiDesc}&apiId=${apiId}`,
        {
            headers: {
                'Content-Type': 'application/json',
            },
        },
        50000
    );
};
