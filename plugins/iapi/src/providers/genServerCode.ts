import {
    SkillProvider,
    FunctionParameterDefinition,
    TaskProgressChunkStream,
    TaskProgressChunk,
} from '@comate/plugin-host';
import {getHttpApiInfo} from '../service/httpApi.js';
import {generateServerCode} from '../service/genServerCode.js';

// interface iApiParams {
//     apiTitle: string;
//     apiDesc: string;
// }

export class GenServerCodeSkillProvider extends SkillProvider {
    static skillName = 'generateServerCode';
    static displayName = '生成服务代码';
    static description =
        '可用于接口文档智能生成接口服务代码，覆盖 controller 层、service 层、dao 层、model 层等，为接口服务开发提效。';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    // protected defaultArgumentValue(): iApiParams {
    //     return {apiTitle: '', apiDesc: ''};
    // }

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new TaskProgressChunkStream();
        try {
            // const {apiTitle, apiDesc} = args;
            const {query} = this.currentContext;
            const userDetail = await this.currentUser.requestDetail();

            if (!userDetail) {
                yield stream.flushReplaceLast('请先完成授权，iAPI 插件需在用户授权后才能使用。');
                return;
            }

            if (!query) {
                yield stream.flushReplaceLast('请输入需求描述。');
                return;
            }

            let apiId = parseInt(query, 10);
            if (isNaN(apiId)) {
                const regex = /api-(\d+)/;
                const match = query.match(regex);
                if (match) {
                    apiId = parseInt(match[1], 10);
                }
            }

            if (isNaN(apiId)) {
                yield stream.flushReplaceLast('未识别到有效的接口信息。');
                return;
            }

            const userName = userDetail.name;

            // 查询接口
            yield stream.flushReplaceLast('正在查询接口...\n\n');
            const retApiDetail = await getHttpApiInfo(this, userName, query, apiId);
            const {code, message, data} = retApiDetail as any;
            if (code === 500001) {
                yield stream.flush(message);
                return;
            }

            if (code !== 200) {
                this.logger.info(
                    `iAPI Info: 未查询到接口：${message} 请求参数：${JSON.stringify({userName, query})}`
                );
                yield stream.flushReplaceLast(`未查询到接口：${message}\n\n`);
                return;
            }

            if (!data) {
                this.logger.info(
                    `iAPI Info: 未查询到接口：${message} 请求参数：${JSON.stringify({userName, query, data})}`
                );
                yield stream.flushReplaceLast(`未查询到接口，请换个描述试试\n\n`);
                return;
            }

            const {id, name, desc, url, path, method} = data;
            yield stream.flush(`\n\n以下是为你查询到的接口：\n\n`);
            yield stream.flush(`接口名称：${name}\n\n`);
            yield stream.flush(`接口描述：${desc || '-'}\n\n`);
            yield stream.flush(`接口路径：${path || '-'}\n\n`);
            yield stream.flush(`请求方法：${method || '-'}\n\n`);
            yield stream.flush(`接口地址：[点击查看](${url})\n\n`);

            // 生成服务代码
            const serverLanguage = await this.config.getAsString('serverLanguage', '');
            const serverFramework = await this.config.getAsString('serverFramework', '');
            const serverDaoFramework = await this.config.getAsString('serverDaoFramework', '');

            yield stream.flush('\n\n\n\n开始生成服务代码...\n\n');
            const serverCodeList = ['model', 'dao', 'service', 'controller'];
            let currTaskId = 0;
            for (const item of serverCodeList) {
                yield stream.flush(`\n\n正在生成【${item}】层服务代码...\n\n`);
                const retServerCode = await generateServerCode(this, {
                    apiId: id,
                    apiName: name,
                    story: query,
                    username: userName,
                    codeType: item,
                    serverLanguage,
                    serverFramework,
                    serverDaoFramework,
                    ...(currTaskId ? {taskId: currTaskId} : {}),
                });

                const {code, message: msg, data} = retServerCode as any;
                if (code === 500001) {
                    yield stream.flush(msg);
                    continue;
                }

                if (code !== 200) {
                    yield stream.flush(`\n\n生成【${item}】层服务代码出错：${msg}\n\n`);
                    continue;
                }

                if (!data) {
                    yield stream.flush(`\n\n未生成【${item}】层服务代码\n\n`);
                    continue;
                }

                const layerName = item;

                const {taskId, codeContent} = data;
                currTaskId = taskId || 0;
                const {codeType, codeFiles} = codeContent;
                if (codeFiles && codeFiles.length) {
                    // yield {type: 'append', content: `生成结果：\n\n`};
                    for (const item of codeFiles) {
                        const {fileName, content} = item;
                        yield stream.flush(`\n\n已生成【${layerName}】层代码文件：${fileName}\n\n`);
                        yield stream.flush([
                            '\`\`\`java',
                            content,
                            '\`\`\`',
                        ]
                            .join('\n'));
                    }
                }
                else {
                    yield stream.flush(`\n\n未生成【${item}】层服务代码\n\n`);
                }
            }
        }
        catch (error) {
            this.logger.error(`iAPI Error: ${error as string}`);
            yield stream.flushReplaceLast(`请求出错，请联系负责人或稍后再试。`);
        }
    }
}
