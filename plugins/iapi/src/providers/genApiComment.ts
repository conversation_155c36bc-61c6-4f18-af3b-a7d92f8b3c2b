import {
    Skill<PERSON><PERSON>ider,
    FunctionParameterDefinition,
    StringChunkStream,
    TaskProgressChunk,
} from '@comate/plugin-host';
import Parser, {SyntaxNode, Tree} from 'web-tree-sitter';
import {generateApiComment} from '../service/genApiComment.js';
import path from 'node:path';
import {fileURLToPath} from 'node:url';

const getDirname = () => {
    const filename = fileURLToPath(import.meta.url);
    return path.dirname(filename);
};

const getLanguageWasmPath = (language: string) => {
    return path.join(getDirname(), 'static', `tree-sitter-${language}.wasm`);
};

const isFunctionNode = (node: SyntaxNode, language?: string) => {
    if (language === 'go' || language === 'java') {
        return node.type === 'function_declaration' || node.type === 'method_declaration';
    }
    return node.type === 'function_definition';
};

const isDecoratedDefintion = (node: SyntaxNode) => node.type === 'decorated_definition';

const isClassNode = (node: SyntaxNode) => node.type === 'class_definition' || node.type === 'class_declaration';

const stubeTrue = () => true;
function filterValidFunctionNode(tree: Tree, language: string, handle: (node: SyntaxNode) => boolean = stubeTrue) {
    const functionNodes = tree.rootNode.children.reduce<SyntaxNode[]>(
        (result, node) => {
            const pushWhenNodeMatched = (node: SyntaxNode) => {
                if (isFunctionNode(node, language) && handle(node)) {
                    result.push(node);
                }
            };
            if (language === 'go') {
                if (isClassNode(node)) {
                    const suiteBlock = node.children.find(child => child.type === 'block');
                    if (!suiteBlock) {
                        return result;
                    }
                    for (const subNode of suiteBlock.children) {
                        pushWhenNodeMatched(subNode);
                    }
                }
                else if (isDecoratedDefintion(node)) {
                    for (const subNode of node.children) {
                        pushWhenNodeMatched(subNode);
                    }
                }
                else {
                    pushWhenNodeMatched(node);
                }
            }
            else if (language === 'java') {
                if (isClassNode(node)) {
                    const suiteBlock = node.children.find(child => child.type === 'class_body');
                    if (!suiteBlock) {
                        return result;
                    }
                    for (const subNode of suiteBlock.children) {
                        pushWhenNodeMatched(subNode);
                    }
                }
            }
            return result;
        },
        []
    );

    return functionNodes;
}

const hasLeadingCommentNodeInFunction = (node: SyntaxNode | null) => {
    if (!node) {
        return false;
    }

    const block = node.children.find(child => child.type === 'block');
    return block?.firstChild?.firstChild?.type === 'string';
};

export class GenApiCommentSkillProvider extends SkillProvider {
    static skillName = 'generateApiComment';
    static description = '智能生成选中接口代码的注释';

    protected parser: Parser | null = null;

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new StringChunkStream();
        try {
            const {activeFileLanguage, selectedCode, selectedRange} = this.currentContext;
            const startPosition = selectedRange?.[0] ?? {line: Infinity};
            const endPosition = selectedRange?.[1] ?? {line: 0};
            const userDetail = await this.currentUser.requestDetail();
            if (!userDetail) {
                yield stream.flushReplaceLast('\n\n请先完成授权，iAPI 插件需在用户授权后才能使用。');
                return;
            }

            const userName = userDetail.name;
            const selectedCodeStr = (await (async () => {
                try {
                    const tree = await this.getCurrentFileTreeSitterInstance();
                    if (tree) {
                        const language = this.currentContext.activeFileLanguage;
                        const functionNodes = filterValidFunctionNode(
                            tree,
                            language,
                            node => !hasLeadingCommentNodeInFunction(node)
                        );
                        if (functionNodes.length) {
                            const funtionNodeText = functionNodes
                                .find(node =>
                                    node.startPosition.row <= startPosition.line
                                    && node.endPosition.row >= endPosition.line
                                )
                                ?.text
                                ?? selectedCode;

                            return funtionNodeText;
                        }
                    }
                    return selectedCode;
                }
                catch {
                    return selectedCode;
                }
            })())
                .replaceAll('"', '\\"');

            if (!selectedCode && !selectedCodeStr) {
                yield stream.flushReplaceLast('\n\n请先选择要生成注释的接口代码块。');
                return;
            }

            const apiParams = {
                codeContent: selectedCodeStr,
                codeLanguage: activeFileLanguage,
                username: userName,
            };

            yield stream.flush('\n\n正在生成注释...');
            this.logger.info(`Plugin Info: 正在生成注释，请求参数：${selectedCodeStr}`);
            const res = await generateApiComment(this, apiParams);
            const {code, message, data} = res as any;
            if (code !== 200) {
                this.logger.info(`Plugin Info: 生成注释失败：${message} ，请求参数：${JSON.stringify(apiParams)}`);
                yield stream.flushReplaceLast(`\n\n未生成注释：${message}`);
                return;
            }

            const {displayKey} = data as any;
            yield stream.flushReplaceLast(`\n\n已为你生成注释：`);
            yield stream.flush(
                '\n\n' + [
                    `\`\`\`${activeFileLanguage}`,
                    data[displayKey],
                    '\`\`\`',
                ]
                    .join('\n')
            );
        }
        catch (error) {
            this.logger.error(`Plugin Error: ${error as string}`);
            yield stream.flushReplaceLast(`\n\n请求出错，请稍后再试。`);
        }
    }

    async getCurrentFileTreeSitterInstance() {
        const fs = await this.requestWorkspaceFileSystem();
        if (!fs) {
            return null;
        }

        try {
            const dirname = getDirname();
            // wasm 需要，不加会报错
            // eslint-disable-next-line
            global.__dirname = dirname;

            const fileBuffer = await fs.readFile(this.currentContext.activeFilePath);
            const sourceCode = fileBuffer.toString();

            const languageBinding = getLanguageWasmPath(this.currentContext.activeFileLanguage);
            if (languageBinding) {
                const parser = await this.getParser();
                const tree = parser.parse(sourceCode);
                return tree;
            }
            return null;
        }
        catch (ex) {
            // eslint-disable-next-line
            console.log(ex);
            return null;
        }
    }

    async getParser() {
        if (this.parser) {
            return this.parser;
        }

        const dirname = getDirname();
        const wasmFile = path.resolve(dirname, 'tree-sitter.wasm');
        await Parser.init({
            locateFile() {
                return wasmFile;
            },
        });

        const parser = new Parser();
        const Lang = await Parser.Language.load(getLanguageWasmPath(this.currentContext.activeFileLanguage));
        parser.setLanguage(Lang);
        return parser;
    }
}
