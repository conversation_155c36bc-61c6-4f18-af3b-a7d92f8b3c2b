{"private": true, "name": "@comate-plugin/jarvis", "version": "0.9.2", "type": "module", "scripts": {"clean": "rm -rf dist", "dev": "cpd dev", "build": "tsc && cpd build", "lint": "eslint --max-warnings=0 src --fix"}, "devDependencies": {"@comate/plugin-cli": "^0.9.2", "@comate/plugin-host": "^0.9.2", "form-data": "^4.0.0", "node-fetch": "^2.6.1", "@types/node-fetch": "^2.6.1"}, "comate": {"name": "jarvis", "version": "1.0.0", "icon": "./assets/icon.svg", "entry": "./dist/index.js", "displayName": "<PERSON>", "description": "<PERSON>、<PERSON><PERSON>、海若插件，支持代码生成、应用部署", "keyword": ["商业平台", "<PERSON>"], "capabilities": [{"type": "Skill", "name": "jarvis-help", "displayName": "插件介绍", "description": "插件介绍", "placeholder": "无需输入内容，回车获得插件介绍"}, {"type": "Skill", "name": "functionCompute", "displayName": "函数计算", "description": "LLM生成函数计算代码和其他代码", "placeholder": "请描述您要生成的函数计算代码"}, {"type": "Skill", "name": "jarvisAppDeploy", "displayName": "应用部署", "description": "Jarvis应用、函数、海若API更新部署", "placeholder": "请输入部署应用对应的配置名"}, {"type": "Skill", "name": "jarvisEosDeploy", "displayName": "EOS环境搭建", "description": "EOS环境搭建", "placeholder": "请输入EOS环境搭建对应的配置名"}, {"type": "Skill", "name": "marsproHotUpdate", "displayName": "marspro配置热更新", "description": "marspro配置热更新", "placeholder": "请输入marspro热更新对应的配置名"}, {"type": "Skill", "name": "<PERSON><PERSON><PERSON><PERSON>", "displayName": "海若API", "description": "海若API调用方和提供方代码生成", "placeholder": "请输入curl命令/请求json/响应json，并告知要生成API调用方还是提供方代码"}, {"type": "Skill", "name": "<PERSON><PERSON><PERSON>", "displayName": "智能问答", "description": "智能问答", "placeholder": "请输入您要咨询的Jarvis、Hi<PERSON>、海若等相关问题"}, {"type": "Fallback", "name": "fallback", "displayName": "兜底", "description": "兜底"}], "configSchema": {"sections": []}}}