import {FallbackProvider, TaskProgressChunk, TaskProgressChunkStream} from '@comate/plugin-host';

const DESCRIPTION = `
该插件目前支持以下能力：
- shell代码生成
- API注解生成

您可以选择上述能力进行体验，也可以通过调整输入内容来体验不同的能力。
`;

export class CustomFallbackProvider extends FallbackProvider {
    static description = '能力解释';

    async *handleQuery(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new TaskProgressChunkStream();
        yield stream.fail(DESCRIPTION);
    }
}
