/* eslint-disable no-useless-escape */
import {
    FunctionParameterDefinition,
    SkillProvider,
    TaskProgressChunk,
    TaskProgressChunkStream,
    TextModel,
} from '@comate/plugin-host';
import defaultPromptTemplate from '../prompts/hairuoApi.prompt';

interface Args {
    query: string;
}

export class HairuoApiProvider extends SkillProvider<Args> {
    static skillName = 'hairuoApi';

    static displayName = '生成代码';

    static description = '海若API代码生成';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const query = this.currentContext.query;
        const stream = new TaskProgressChunkStream();

        yield stream.flush('正在思考中，请耐心等候...');

        const texts = await this.retriever.knowledgeFromQuery(query, {
            knowledgeSets: [{
                uuid: 'f804002e-638e-4f30-ac02-bac947d8d3d5',
                type: 'NORMAL',
            }],
        });

        const prompt = this.llm.createPrompt(
            defaultPromptTemplate,
            {knowledge: texts.join('\n'), query}
        );

        const chunks = this.llm.askForTextStreaming(prompt, {model: TextModel.Default});

        yield stream.flushReplaceLast('');

        for await (const chunk of chunks) {
            yield stream.flush(chunk);
        }
    }
}
