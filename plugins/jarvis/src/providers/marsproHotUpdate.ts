/* eslint-disable complexity, max-depth, max-statements */
import {
    FunctionParameterDefinition,
    SkillProvider,
    TaskProgressChunk,
    TaskProgressChunkStream,
    ProviderInit,
} from '@comate/plugin-host';
import crypto from 'crypto';
import fs from 'node:fs';
import fetch from 'node-fetch';
import {
    configPath,
    buildStdOutPath,
    createFormData,
    executeCommand,
    getDeployConfig,
    configFolder,
} from '../utils.js';
import path from 'node:path';
import {getTaskDetail, token, ComateTaskMap} from '../services/deploy.js';

interface Args {
    query: string;
}

interface Config {
    confName?: string;
    configPath?: string;
    deployApp?: string;
}

const TAR_FILE_NAME = 'marsproConfig.tar.gz';

function getUploadFileParams(
    {realUserName, fileName, config}: {realUserName: string, fileName: string, config: Config}
) {
    return JSON.stringify({
        userName: 'Comate',
        realUserName,
        fileName,
        comateTaskMap: {
            deployApp: config.deployApp,
            deployType: 'marspro',
            params: JSON.stringify(config),
        },
    });
}

export class MarsproHotUpdateProvider extends SkillProvider<Args> {
    static skillName = 'marsproHotUpdate';

    static displayName = 'Jarvis marspro配置热更新';

    static description = 'Jarvis marspro配置热更新能力';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    private readonly cwd: string;

    constructor(init: ProviderInit) {
        super(init);

        this.cwd = init.cwd;
    }

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const {query} = this.currentContext;

        const stream = new TaskProgressChunkStream();

        const userDetail = await this.currentUser.requestDetail();
        const realUserName = userDetail && userDetail.name || '未知用户';

        let fileBuffer = null;
        try {
            const data = getDeployConfig(this.cwd);
            if (data.type === 'message') {
                yield stream.flush(data.content + '\n\n');
                return;
            }
            else {
                fileBuffer = data.value;
            }
        }
        catch (ex) {
            yield stream.flushReplaceLast(ex instanceof Error ? ex.message : `${ex}`);
            return;
        }

        if (!fileBuffer) {
            yield stream.flushReplaceLast('获取配置文件失败 \n\n');
            return;
        }

        yield stream.flush(`获取[配置文件](${configPath})成功，查找配置: ${query} \n\n`);
        const sourceCode = fileBuffer.toString();

        let configs = [];
        try {
            configs = JSON.parse(sourceCode);
        }
        catch (error) {
            yield stream.flush('未找到指定配置，请查看上线配置文件是否配置正确 \n\n');
            return;
        }

        let config: Config = {};
        for (const element of configs) {
            if (element.confName === query) {
                config = element;
                break;
            }
        }

        if (!config.confName) {
            yield stream.flush(`未找到「${query}」的上线配置，请检查上线配置文件配置是否正确 \n\n`);
            return;
        }

        yield stream.flush(`查找指定配置成功：${JSON.stringify(config)} \n\n`);

        const outputFilePath = path.resolve(this.cwd, configFolder, `./${TAR_FILE_NAME}`);

        // 2、根据配置决定是否执行本地打包
        if (config.configPath) {
            const buildCmd = `rm -rf ${outputFilePath} && tar czf ${outputFilePath} ${config.configPath}`;
            yield stream.flush('本地配置文件打包中 \n\n');
            try {
                const {stderr} = await executeCommand(buildCmd, this.cwd);
                if (stderr) {
                    yield stream.flush(`打包错误日志: ${stderr} \n\n`);
                }
            }
            catch (ex) {
                yield stream.flush(`打包命令执行异常: ${ex}，[编译日志](${buildStdOutPath}) \n\n`);
                return;
            }
        }

        yield stream.flush('准备上传本地配置文件包 \n\n');

        // 3、获取本地打包产出和其md5值
        const fileContent = fs.readFileSync(outputFilePath);
        const hash = crypto.createHash('md5');
        hash.update(fileContent);
        const md5Hash = hash.digest('hex');
        const fileSizeInBytes = fs.statSync(outputFilePath).size;
        const fileSizeInMB = Number((fileSizeInBytes / (1024 * 1024)).toFixed(2));
        const params = getUploadFileParams({realUserName, fileName: TAR_FILE_NAME, config});

        // 4、上传文件到jarvis，发起上线任务
        let onlineStatus = 1;
        let onlineResData = [];
        let onlineFailures = [];

        yield stream.flush('开始上传并自动发起上线任务，请稍候... \n\n');
        try {
            if (!isNaN(fileSizeInMB)) {
                const file = fs.createReadStream(outputFilePath);

                const options = {
                    method: 'POST',
                    body: createFormData({
                        file,
                        params,
                        token,
                        signature: md5Hash,
                    }),
                };

                const uploadRes = await fetch(
                    'https://api.baidu-int.com/json/sms/service/JarvisMscFileUploadService/uploadFile',
                    options
                );

                const jsonData = await uploadRes.json();

                onlineResData = jsonData.body.data;
                onlineStatus = jsonData.header.status;
                onlineFailures = jsonData.header.failures;
            }

            if (onlineStatus !== 0) {
                yield stream.flushReplaceLast('');
                yield stream.flush(
                    `上线接口调用失败，接口错误：${JSON.stringify(onlineFailures || [])} \n\n`
                );
                return;
            }

            if (
                Array.isArray(onlineResData)
                && onlineResData.length > 0
                && !!onlineResData[0].mapId
            ) {
                const getConfig = () => config;
                const until = ({taskId, params}: ComateTaskMap) => {
                    const config = params ? JSON.parse(params) : {};
                    return taskId !== null && taskId > 0 && config.configTaskStatus === 'SUCCESS';
                };
                try {
                    await getTaskDetail(realUserName, onlineResData[0].mapId, getConfig, undefined, until);
                }
                catch (e) {
                    yield stream.flushReplaceLast('');
                    yield stream.flush('配置热更新任务超时，请检查配置解析是否存在异常 \n\n');
                    return;
                }
                yield stream.flushReplaceLast('');
                yield stream.flush('配置热更新任务上线成功 \n\n');
            }
        }
        catch (ex) {
            yield stream.flushReplaceLast('');
            yield stream.flush(`上线接口调用失败: ${ex instanceof Error ? ex.message : ex} \n\n`);
        }
    }
}
