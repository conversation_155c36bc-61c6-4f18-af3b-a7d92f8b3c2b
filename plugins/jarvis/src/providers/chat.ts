import {
    SkillProvider,
    FunctionParameterDefinition,
    TaskProgressChunk,
    TextModel,
    TaskProgressChunkStream,
} from '@comate/plugin-host';

export class ChatSkillProvider extends SkillProvider {
    static skillName = 'answerQuestion';

    static displayName = 'Jarvis智能问答';

    static description = '智能解答关于Jarvis的相关问题';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const {query} = this.currentContext;
        const stream = new TaskProgressChunkStream();

        yield stream.flush('正在思考中，请耐心等候...');

        const knowledge = await this.retriever.knowledgeFromQuery(
            query,
            {
                knowledgeSets: [
                    {uuid: '3121ca24-886d-46e4-b05e-823845311d62', type: 'NORMAL'},
                ],
            }
        );

        const prompt = this.llm.createPrompt(
            knowledge.length ? '请根据以下背景知识：\n\n{{knowledge}}\n\n回答问题：{{query}}' : '{{query}}',
            {knowledge, query}
        );

        const chunks = this.llm.askForTextStreaming(prompt, {model: TextModel.Default});

        yield stream.flushReplaceLast('');

        for await (const chunk of chunks) {
            yield stream.flush(chunk);
        }
    }
}
