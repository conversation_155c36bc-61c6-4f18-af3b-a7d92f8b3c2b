/* eslint-disable complexity, max-depth, max-statements */
import {
    FunctionParameterDefinition,
    SkillProvider,
    TaskProgressChunk,
    TaskProgressChunkStream,
    ProviderInit,
} from '@comate/plugin-host';
import crypto from 'crypto';
import fs from 'node:fs';
import fetch from 'node-fetch';
import {configPath, buildStdOutPath, createFormData, executeCommand, getDeployConfig, splitFile} from '../utils.js';
import path from 'node:path';
import {getTaskDetail, getWorkflowDeployTaskDetail, token} from '../services/deploy.js';

interface Args {
    query: string;
}

interface Config {
    confName?: string;
    buildCmd?: string;
    output?: string;
    deployApp?: string;
}

export class JarvisAppDeployProvider extends SkillProvider<Args> {
    static skillName = 'jarvisAppDeploy';

    static displayName = 'Jarvis 应用部署';

    static description = 'Jarvis 应用部署能力';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    protected currentConfig?: Config;
    private readonly cwd: string;

    constructor(init: ProviderInit) {
        super(init);

        this.cwd = init.cwd;
        this.currentConfig = undefined;
    }

    getUploadFileParams({realUserName, fileName, config}: {realUserName: string, fileName: string, config: Config}) {
        return JSON.stringify({
            userName: 'Comate',
            realUserName,
            fileName,
            comateTaskMap: {
                deployApp: config.deployApp,
                params: JSON.stringify(config),
            },
        });
    }

    // 获取任务详情后的回调，入参是获取任务详情接口返回的响应数据，继承本类后重写此方法即可
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    callbackOnGetDetail(detailData: any) {
    }

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const {query} = this.currentContext;

        const stream = new TaskProgressChunkStream();

        const userDetail = await this.currentUser.requestDetail();
        const realUserName = userDetail && userDetail.name || '未知用户';

        // eslint-disable-next-line @typescript-eslint/init-declarations
        let fileBuffer;
        try {
            const data = getDeployConfig(this.cwd);
            if (data.type === 'message') {
                yield stream.flush(data.content + '\n\n');
                return;
            }
            else {
                fileBuffer = data.value;
            }
        }
        catch (ex) {
            yield stream.flushReplaceLast(ex instanceof Error ? ex.message : `${ex}`);
            return;
        }

        if (!fileBuffer) {
            yield stream.flushReplaceLast('获取配置文件失败 \n\n');
            return;
        }

        yield stream.flush(`获取[配置文件](${configPath})成功，查找配置: ${query} \n\n`);
        const sourceCode = fileBuffer.toString();

        let configs = [];
        try {
            configs = JSON.parse(sourceCode);
        }
        catch (error) {
            yield stream.flush('未找到指定配置，请查看上线配置文件是否配置正确 \n\n');
            return;
        }

        let config: Config = {};
        for (const element of configs) {
            if (element.confName === query) {
                config = element;
                this.currentConfig = config;
                break;
            }
        }

        if (!config.confName) {
            yield stream.flush(`未找到「${query}」的上线配置，请检查上线配置文件配置是否正确 \n\n`);
            return;
        }

        yield stream.flush(`查找指定配置成功：${JSON.stringify(config)} \n\n`);

        // 2、根据配置决定是否执行本地编译
        if (config.buildCmd) {
            yield stream.flush(`执行本地编译命令: ${config.buildCmd} \n\n`);
            try {
                const {stderr} = await executeCommand(config.buildCmd, this.cwd);
                if (stderr) {
                    yield stream.flush(`编译错误日志: ${stderr} \n\n`);
                }
            }
            catch (ex) {
                yield stream.flush(`编译命令执行异常: ${ex}，[编译日志](${buildStdOutPath}) \n\n`);
                return;
            }
        }

        if (!config.output) {
            yield stream.flush(`未找到指定output配置${config.output}，请检查output配置 \n\n`);
            return;
        }

        yield stream.flush(`准备上传output产出文件包: ${config.output} \n\n`);

        // 3、获取本地编译产出和其md5值
        const outputFileNamePath = config.output;
        if (outputFileNamePath.startsWith('/') || outputFileNamePath.startsWith('..')) {
            yield stream.flush('output配置请填写项目内的相对路径: eg: output/cloudnative-consumer.tar.gz \n\n');
            return;
        }

        const outputFilePath = path.resolve(this.cwd, outputFileNamePath);
        if (!fs.existsSync(outputFilePath)) {
            yield stream.flush(`没有找到output产出包: ${config.output}`);
            return;
        }

        const fileContent = fs.readFileSync(outputFilePath);
        const fileName = outputFileNamePath.substring(outputFileNamePath.lastIndexOf('/') + 1);
        const hash = crypto.createHash('md5');
        hash.update(fileContent);
        const md5Hash = hash.digest('hex');
        const fileSizeInBytes = fs.statSync(outputFilePath).size;
        const fileSizeInMB = Number((fileSizeInBytes / (1024 * 1024)).toFixed(2));
        const params = this.getUploadFileParams({realUserName, fileName, config});

        // 4、上传文件到jarvis，发起上线任务
        let onlineStatus = 1;
        let onlineResData = [];
        let onlineFailures = [];

        yield stream.flush('开始上传并自动发起上线任务，请稍候... \n\n');
        try {
            if (!isNaN(fileSizeInMB)) {
                if (fileSizeInMB < 100) {
                    const file = fs.createReadStream(outputFilePath);

                    const options = {
                        method: 'POST',
                        body: createFormData({
                            file,
                            params,
                            token,
                            signature: md5Hash,
                        }),
                    };

                    const uploadRes = await fetch(
                        'https://api.baidu-int.com/json/sms/service/JarvisMscFileUploadService/uploadFile',
                        options
                    );

                    const jsonData = await uploadRes.json();

                    onlineResData = jsonData.body.data;
                    onlineStatus = jsonData.header.status;
                    onlineFailures = jsonData.header.failures;
                }
                else {
                    const splitNum = Math.ceil(fileSizeInMB * 1024 / 92160);
                    splitFile(outputFilePath, splitNum);
                    // 分片上传 分InitiateMultipartUpload、UploadPart、CompleteMultipartUpload 三个阶段
                    const initOptions = {
                        method: 'POST',
                        body: createFormData({
                            params,
                            token,
                            filename: fileName,
                            signature: md5Hash,
                            step: 'InitiateMultipartUpload',
                        }),
                    };

                    const initRes = await fetch(
                        'https://api.baidu-int.com/json/sms/service/JarvisMscFileUploadService/uploadFile',
                        initOptions
                    );
                    const initJsonData = await initRes.json();
                    if (initJsonData.header.status !== 0) {
                        throw new Error('上传文件初始化失败');
                    }
                    const parts = [];
                    const uploadId = initJsonData.body.data[0].uploadId;
                    for (let i = 0; i < splitNum; i++) {
                        const partNumber = i + 1;
                        const file = fs.createReadStream(`${outputFilePath}_${i}`);
                        const splitOptions = {
                            method: 'POST',
                            body: createFormData({
                                params,
                                file,
                                token,
                                uploadId,
                                partNumber,
                                step: 'UploadPart',
                            }),
                        };
                        const splitRes = await fetch(
                            'https://api.baidu-int.com/json/sms/service/JarvisMscFileUploadService/uploadFile',
                            splitOptions
                        );
                        const splitJsonData = await splitRes.json();
                        if (splitJsonData.header.status !== 0) {
                            throw new Error('分片上传应用包失败');
                        }
                        parts.push({
                            partNumber,
                            ETag: splitJsonData.body.data[0].ETag,
                        });
                    }
                    const completeOptions = {
                        method: 'POST',
                        body: createFormData({
                            params,
                            parts: JSON.stringify(parts),
                            uploadId,
                            token,
                            filename: fileName,
                            signature: md5Hash,
                            step: 'CompleteMultipartUpload',
                        }),
                    };
                    const completeRes = await fetch(
                        'https://api.baidu-int.com/json/sms/service/JarvisMscFileUploadService/uploadFile',
                        completeOptions
                    );
                    const completeJsonData = await completeRes.json();
                    onlineResData = completeJsonData.body.data;
                    onlineStatus = completeJsonData.header.status;
                    onlineFailures = completeJsonData.header.failures;
                }
            }

            if (onlineStatus !== 0) {
                yield stream.flush(
                    `上线接口调用失败，接口错误：${JSON.stringify(onlineFailures || [])} \n\n`
                );
                return;
            }

            let taskDetail = {taskId: 0};
            if (
                Array.isArray(onlineResData)
                && onlineResData.length > 0
                && !!onlineResData[0].mapId
            ) {
                const getConfig = () => this.currentConfig || config;
                taskDetail = await getTaskDetail(
                    realUserName,
                    onlineResData[0].mapId,
                    getConfig,
                    this.callbackOnGetDetail
                );
            }
            const taskId = taskDetail.taskId;
            if (taskId > 0) {
                const {taskDetailUrl, startTime} = await getWorkflowDeployTaskDetail(realUserName, taskId, 1);
                yield stream.flushReplaceLast('');
                yield stream.flush(
                    `发起Jarvis上线任务，任务地址：[${taskId}](${taskDetailUrl})，开始时间：${startTime} \n\n`
                );
                yield stream.flush('轮询任务执行状态中，请稍等... \n\n');

                const {taskStatus, endTime} = await getWorkflowDeployTaskDetail(realUserName, taskId, 29);
                yield stream.flushReplaceLast('');
                if (taskStatus === 'SUCCESS') {
                    yield stream.flush(
                        `Jarvis任务上线成功，任务地址：[${taskId}](${taskDetailUrl})，结束时间：${endTime} \n\n`
                    );
                }
                else if (taskStatus === 'CANCELED') {
                    yield stream.flush(`Jarvis任务已被撤销，任务地址：[${taskId}](${taskDetailUrl}) \n\n`);
                }
                else if (taskStatus === 'PAUSING') {
                    yield stream.flush(
                        `Jarvis任务已暂停，可进入网页点击任务继续，任务地址：[${taskId}](${taskDetailUrl}) \n\n`
                    );
                }
                // 包含 RUNNING 和 兜底
                else {
                    yield stream.flush(
                        `Jarvis任务超时，可进入网页查看详情，任务地址：[${taskId}](${taskDetailUrl}) \n\n`
                    );
                }
            }
        }
        catch (ex) {
            yield stream.flush(`上线接口调用失败: ${ex instanceof Error ? ex.message : ex} \n\n`);
        }
    }
}
