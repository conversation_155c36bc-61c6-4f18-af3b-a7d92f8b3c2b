/* eslint-disable complexity, max-depth, max-statements */
import {
    FunctionParameterDefinition,
    SkillProvider,
    TaskProgressChunk,
    TaskProgressChunkStream,
    ProviderInit,
} from '@comate/plugin-host';
import crypto from 'crypto';
import fs from 'node:fs';
import fetch from 'node-fetch';
import {
    configPath,
    buildStdOutPath,
    createFormData,
    executeCommand,
    getDeployConfig,
    splitFile,
    sleep,
} from '../utils.js';
import path from 'node:path';
import {token, USER_ID, OPT_ID, ComateTaskMap} from '../services/deploy.js';
import {overrideConfig, parseJson, normalizeTime} from '../utils.js';

interface Args {
    query: string;
}

interface Config {
    confName?: string;
    buildCmd?: string;
    output?: string;
    deployApp?: string;
}

type UploadFile = (val: {
    outputFilePath: string;
    outputFileNamePath: string;
    realUserName: string;
    config: Config;
}) => Promise<{
    onlineStatus: number;
    onlineResData: Array<{mapId: number}>;
    onlineFailures: unknown[];
}>;

const uploadFilePath = 'https://api.baidu-int.com/json/sms/service/JarvisMscFileUploadService/uploadFile';

function getConfigByDetailData(detailData: ComateTaskMap) {
    const {envId, comateTaskMap} = parseJson(detailData.params as string, {});
    return {
        envId,
        ...parseJson(comateTaskMap?.params, {}),
    };
}

export class JarvisEosDeployProvider extends SkillProvider<Args> {
    static skillName = 'jarvisEosDeploy';

    static displayName = 'Jarvis EOS环境搭建';

    static description = 'Jarvis EOS环境搭建能力';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    protected currentConfig?: Config;

    private deployStatus: ComateTaskMap['deployStatus'] | 'PEDNING';

    private readonly cwd: string;

    constructor(init: ProviderInit) {
        super(init);

        this.cwd = init.cwd;
    }

    getUploadFileParams({realUserName, fileName, config}: {realUserName: string, fileName: string, config: Config}) {
        return JSON.stringify({
            userName: 'Comate',
            realUserName,
            fileName,
            comateTaskMap: {
                deployType: 'eos-create',
                params: JSON.stringify(config),
            },
        });
    }

    callbackOnGetDetail = (detailData: any) => {
        let config = {} as Config;
        try {
            config = getConfigByDetailData(detailData);
        }
        catch (err) {
            return;
        }
        if (JSON.stringify(this.currentConfig) !== JSON.stringify(config)) {
            this.currentConfig = config;
            overrideConfig(config, this.cwd);
        }
    };

    uploadFile: UploadFile = async ({outputFilePath, outputFileNamePath, realUserName, config}) => {
        const res = {
            onlineStatus: 1,
            onlineResData: [],
            onlineFailures: [],
        };

        const fileContent = fs.readFileSync(outputFilePath);
        const fileName = outputFileNamePath.substring(outputFileNamePath.lastIndexOf('/') + 1);
        const hash = crypto.createHash('md5');
        hash.update(fileContent);
        const md5Hash = hash.digest('hex');
        const fileSizeInBytes = fs.statSync(outputFilePath).size;
        const fileSizeInMB = Number((fileSizeInBytes / (1024 * 1024)).toFixed(2));
        const params = this.getUploadFileParams({realUserName, fileName, config});
        if (isNaN(fileSizeInMB)) {
            return res;
        }
        if (fileSizeInMB < 100) {
            const file = fs.createReadStream(outputFilePath);

            const options = {
                method: 'POST',
                body: createFormData({
                    file,
                    params,
                    token,
                    signature: md5Hash,
                }),
            };

            const uploadRes = await fetch(uploadFilePath, options);

            const jsonData = await uploadRes.json();

            res.onlineResData = jsonData.body.data;
            res.onlineStatus = jsonData.header.status;
            res.onlineFailures = jsonData.header.failures;
        }
        else {
            const splitNum = Math.ceil(fileSizeInMB * 1024 / 92160);
            splitFile(outputFilePath, splitNum);
            // 分片上传 分InitiateMultipartUpload、UploadPart、CompleteMultipartUpload 三个阶段
            const initOptions = {
                method: 'POST',
                body: createFormData({
                    params,
                    token,
                    filename: fileName,
                    signature: md5Hash,
                    step: 'InitiateMultipartUpload',
                }),
            };

            const initRes = await fetch(uploadFilePath, initOptions);
            const initJsonData = await initRes.json();
            if (initJsonData.header.status !== 0) {
                throw new Error('上传文件初始化失败');
            }
            const parts = [];
            const uploadId = initJsonData.body.data[0].uploadId;
            for (let i = 0; i < splitNum; i++) {
                const partNumber = i + 1;
                const file = fs.createReadStream(`${outputFilePath}_${i}`);
                const splitOptions = {
                    method: 'POST',
                    body: createFormData({
                        params,
                        file,
                        token,
                        uploadId,
                        partNumber,
                        step: 'UploadPart',
                    }),
                };
                const splitRes = await fetch(uploadFilePath, splitOptions);
                const splitJsonData = await splitRes.json();
                if (splitJsonData.header.status !== 0) {
                    throw new Error('分片上传应用包失败');
                }
                parts.push({
                    partNumber,
                    ETag: splitJsonData.body.data[0].ETag,
                });
            }
            const completeOptions = {
                method: 'POST',
                body: createFormData({
                    params,
                    parts: JSON.stringify(parts),
                    uploadId,
                    token,
                    filename: fileName,
                    signature: md5Hash,
                    step: 'CompleteMultipartUpload',
                }),
            };
            const completeRes = await fetch(uploadFilePath, completeOptions);
            const completeJsonData = await completeRes.json();
            res.onlineResData = completeJsonData.body.data;
            res.onlineStatus = completeJsonData.header.status;
            res.onlineFailures = completeJsonData.header.failures;
        }

        return res;
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const {query} = this.currentContext;

        const stream = new TaskProgressChunkStream();

        const userDetail = await this.currentUser.requestDetail();
        const realUserName = userDetail && userDetail.name || '未知用户';

        // eslint-disable-next-line @typescript-eslint/init-declarations
        let fileBuffer;
        try {
            const data = getDeployConfig(this.cwd);
            if (data.type === 'message') {
                yield stream.flush(data.content + '\n\n');
                return;
            }
            else {
                fileBuffer = data.value;
            }
        }
        catch (ex) {
            yield stream.flushReplaceLast(ex instanceof Error ? ex.message : `${ex}`);
            return;
        }

        if (!fileBuffer) {
            yield stream.flushReplaceLast('获取配置文件失败 \n\n');
            return;
        }

        yield stream.flush(`获取[配置文件](${configPath})成功，查找配置: ${query} \n\n`);
        const sourceCode = fileBuffer.toString();

        let configs = [];
        try {
            configs = JSON.parse(sourceCode);
        }
        catch (error) {
            yield stream.flush('未找到指定配置，请查看上线配置文件是否配置正确 \n\n');
            return;
        }

        let config: Config = {};
        for (const element of configs) {
            if (element.confName === query) {
                config = element;
                this.currentConfig = config;
                break;
            }
        }

        if (!config.confName) {
            yield stream.flush(`未找到「${query}」的上线配置，请检查上线配置文件配置是否正确 \n\n`);
            return;
        }

        yield stream.flush(`查找指定配置成功：${JSON.stringify(config)} \n\n`);

        // 2、根据配置决定是否执行本地编译
        if (config.buildCmd) {
            yield stream.flush(`执行本地编译命令: ${config.buildCmd} \n\n`);
            try {
                const {stderr} = await executeCommand(config.buildCmd, this.cwd);
                if (stderr) {
                    yield stream.flush(`编译错误日志: ${stderr} \n\n`);
                }
            }
            catch (ex) {
                yield stream.flush(`编译命令执行异常: ${ex}，[编译日志](${buildStdOutPath}) \n\n`);
                return;
            }
        }

        if (!config.output) {
            yield stream.flush(`未找到指定output配置${config.output}，请检查output配置 \n\n`);
            return;
        }

        yield stream.flush(`准备上传output产出文件包: ${config.output} \n\n`);

        // 3、获取本地编译产出和其md5值
        const outputFileNamePath = config.output;
        if (outputFileNamePath.startsWith('/') || outputFileNamePath.startsWith('..')) {
            yield stream.flush('output配置请填写项目内的相对路径: eg: output/cloudnative-consumer.tar.gz \n\n');
            return;
        }

        const outputFilePath = path.resolve(this.cwd, outputFileNamePath);
        if (!fs.existsSync(outputFilePath)) {
            yield stream.flush(`没有找到output产出包: ${config.output}`);
            return;
        }

        // 4、上传文件到jarvis，发起上线任务
        yield stream.flush('开始上传并自动发起上线任务，请稍候... \n\n');
        try {
            const {
                onlineStatus,
                onlineResData,
                onlineFailures,
            } = await this.uploadFile({outputFilePath, outputFileNamePath, realUserName, config});

            if (onlineStatus !== 0) {
                yield stream.flush(
                    `上线接口调用失败，接口错误：${JSON.stringify(onlineFailures || [])} \n\n`
                );
                return;
            }

            if (
                Array.isArray(onlineResData)
                && onlineResData.length > 0
                && !!onlineResData[0].mapId
            ) {
                const getConfig = () => this.currentConfig || config;

                const data = {
                    header: {
                        userid: USER_ID,
                        optid: OPT_ID,
                        token: token,
                    },
                    body: {
                        realUserName,
                        userName: 'Comate',
                        mapId: onlineResData[0].mapId,
                        params: JSON.stringify(getConfig()),
                    },
                };
                const headers = {
                    'Content-Type': 'application/json;charset=UTF-8',
                };
                let envId = 0;
                let envUrl = '';
                /* eslint-disable no-param-reassign */
                for (let i = 0; i < 180; i++) {
                    data.body.params = JSON.stringify(getConfig()); // 每次都需要拿最新的配置
                    const taskDetailResponse = await fetch(
                        'https://api.baidu-int.com/json/sms/service/JarvisMscFileUploadService/getComateTaskDetail',
                        {
                            method: 'POST',
                            headers: headers,
                            body: JSON.stringify(data),
                        }
                    );
                    const taskDetailResponsJson = await taskDetailResponse.json();
                    if (
                        taskDetailResponsJson.header.status === 0
                        && Array.isArray(taskDetailResponsJson.body.data)
                        && taskDetailResponsJson.body.data.length > 0
                    ) {
                        const comateTaskMap = taskDetailResponsJson.body.data[0];
                        this.callbackOnGetDetail(comateTaskMap);
                        const {params = '', addTime, modTime, deployStatus, reason = '-'} = comateTaskMap;
                        const startTime = normalizeTime(addTime);
                        const endTime = normalizeTime(modTime);
                        envId = (parseJson(params, {}) || {}).envId;
                        envUrl = `http://eos.baidu-int.com/#/env/detail?id=${envId}`;
                        if ((!envId || envId <= 0)) {
                            if (this.deployStatus !== 'PEDNING') {
                                yield stream.flush(`发起EOS环境搭建任务成功，环境准备中，开始时间：${startTime} \n\n`);
                                this.deployStatus = 'PEDNING';
                            }
                        }
                        else if (envId > 0 && deployStatus === 'DOING') {
                            if (this.deployStatus !== 'DOING') {
                                yield stream.flush(
                                    `发起EOS环境搭建任务成功，环境部署中，环境地址：[${envId}](${envUrl}), 开始时间：${startTime} \n\n`
                                );
                                this.deployStatus = 'DOING';
                            }
                        }
                        else if (envId > 0 && deployStatus === 'SUCCESS') {
                            yield stream.flush(
                                `EOS环境搭建成功，环境地址：[${envId}](${envUrl}), 结束时间：${endTime} \n\n`
                            );
                            this.deployStatus = 'SUCCESS';
                            break;
                        }
                        else if (envId > 0 && deployStatus === 'FAILED') {
                            yield stream.flush(
                                `EOS环境搭建失败，环境地址：[${envId}](${envUrl}), 结束时间：${endTime}，失败原因：${reason} \n\n`
                            );
                            this.deployStatus = 'FAILED';
                            break;
                        }
                    }
                    await sleep(10000);
                }
                if (this.deployStatus !== 'SUCCESS' && this.deployStatus !== 'FAILED') {
                    yield stream.flush(`EOS环境搭建超时，详情请查看部署任务，环境地址：[${envId}](${envUrl}) \n\n`);
                }
            }
        }
        catch (ex) {
            yield stream.flush(`上线接口调用失败: ${ex instanceof Error ? ex.message : ex} \n\n`);
        }
    }
}
