/* eslint-disable no-useless-escape */
import {
    FunctionParameterDefinition,
    SkillProvider,
    TaskProgressChunk,
    TaskProgressChunkStream,
    TextModel,
} from '@comate/plugin-host';
import defaultPromptTemplate from '../prompts/functionCompute.prompt';

interface Args {
    query: string;
}

function getKeyword(query: string, splitKey = ':') {
    const keywords = query.split(splitKey);
    if (keywords.length > 1 && keywords[0].length < 10) {
        return keywords[0];
    }
    return '';
}

export class FunctionComputeProvider extends SkillProvider<Args> {
    static skillName = 'functionCompute';

    static displayName = '生成代码';

    static description = '函数计算代码生成';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const query = this.currentContext.query;
        const stream = new TaskProgressChunkStream();

        yield stream.flush(
            '温馨提示：函数计算除了生成Higgs代码，还可以生成其他各类语言代码，并且支持代码解释等高级能力。 \n\n'
        );

        yield stream.flush('正在思考中，请耐心等候...');

        const keyword = getKeyword(query, ':') || getKeyword(query, '：');
        let texts = [] as any[];

        if (keyword) {
            const response = await fetch(
                `http://************:8000/openApi/v1/prompt/list?suiteID=&pn=1&size=20&promptName=${keyword}`,
                {
                    method: 'GET',
                    headers: {
                        token: 'zsfXgSzeyJUeqTbf1KtXfUUNjizWhKO6nA0Db/rYDXNHxXdOixFX9kaFhlfRJBDR',
                    },
                }
            );
            const result = await response.json();
            texts = result.data.list.map((item: any) => item.text);
        }
        else {
            texts = await this.retriever.knowledgeFromQuery(query, {
                knowledgeSets: [{
                    uuid: '5d1e55c5-4ecd-424f-890e-1af091563163',
                    type: 'NORMAL',
                }],
            });
        }

        const promptTemplate = await this.config.getAsString('promptTemplate-functionCompute');

        const prompt = this.llm.createPrompt(
            promptTemplate || defaultPromptTemplate,
            {knowledge: texts.join('\n'), query}
        );

        const chunks = this.llm.askForTextStreaming(prompt, {model: TextModel.Default});

        yield stream.flushReplaceLast('');

        for await (const chunk of chunks) {
            yield stream.flush(chunk);
        }
    }
}
