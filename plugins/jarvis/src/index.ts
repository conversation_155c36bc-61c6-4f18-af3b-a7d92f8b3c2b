import {HelpSkillProvider} from './providers/help.js';
import {PluginSetupContext} from '@comate/plugin-host';
import {CustomFallbackProvider} from './providers/fallback.js';
import {FunctionComputeProvider} from './providers/functionCompute.js';
import {JarvisAppDeployProvider} from './providers/jarvisAppDeploy.js';
import {JarvisEosDeployProvider} from './providers/jarvisEosDeploy.js';
import {MarsproHotUpdateProvider} from './providers/marsproHotUpdate.js';
import {HairuoApiProvider} from './providers/hairuoApi.js';
import {ChatSkillProvider} from './providers/chat.js';

export function setup({registry}: PluginSetupContext) {
    registry.registerSkillProvider('jarvis-help', HelpSkillProvider);
    registry.registerSkillProvider('functionCompute', FunctionComputeProvider);
    registry.registerSkillProvider('jarvisAppDeploy', JarvisAppDeployProvider);
    registry.registerSkillProvider('jarvisEosDeploy', JarvisEosDeployProvider);
    registry.registerSkillProvider('marsproHotUpdate', MarsproHotUpdateProvider);
    registry.registerSkillProvider('hairuoApi', HairuoApiProvider);
    registry.registerSkillProvider('jarvisqa', ChatSkillProvider);
    registry.registerFallbackProvider('fallback', CustomFallbackProvider);
}
