import {sleep} from '../utils.js';

export const token = 'e13393ffa785c021e261117beff5a445';
export const USER_ID = 630152;
export const OPT_ID = 630152;

interface TaskBean {
    taskStatus: string;
    taskDetailUrl: string;
    startTime: string;
    endTime: string;
}

interface Config {
    confName?: string;
    buildCmd?: string;
    output?: string;
    deployApp?: string;
}

export interface ComateTaskMap {
    mapId: number;
    taskId: number | null;
    deployStatus?: 'DOING' | 'SUCCESS' | 'FAILED';
    params?: string;
    reason?: string;
    addTime?: string;
    modTime?: string;
    deployApp?: string;
    deployType?: string;
    eosCreate?: boolean;
    fileMd5?: string;
    fileUrl?: string;
}

export function getTaskDetail(
    realUserName: string,
    mapId: string,
    getConfig: () => Config,
    cb?: (val: any) => void,
    checkPollResponse?: (val: ComateTaskMap) => boolean
): Promise<{taskId: number, params?: string}> {
    return new Promise(async (resolve, reject) => {
        const data = {
            header: {
                userid: 630152,
                optid: 630152,
                token: token,
            },
            body: {
                realUserName: realUserName,
                userName: 'Comate',
                mapId: mapId,
                params: JSON.stringify(getConfig()),
            },
        };
        const headers = {
            'Content-Type': 'application/json;charset=UTF-8',
        };
        /* eslint-disable no-param-reassign */
        checkPollResponse = checkPollResponse || (({taskId}) => taskId !== null && taskId > 0);
        for (let i = 0; i < 30; i++) {
            data.body.params = JSON.stringify(getConfig()); // 每次都需要拿最新的配置
            const taskDetailResponse = await fetch(
                'https://api.baidu-int.com/json/sms/service/JarvisMscFileUploadService/getComateTaskDetail',
                {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(data),
                }
            );
            const taskDetailResponsJson = await taskDetailResponse.json();
            if (taskDetailResponsJson.header.status !== 0) {
                // do nothing
            }
            else if (
                Array.isArray(taskDetailResponsJson.body.data)
                && taskDetailResponsJson.body.data.length > 0
            ) {
                const comateTaskMap = taskDetailResponsJson.body.data[0];
                cb && cb(comateTaskMap);
                if (checkPollResponse(comateTaskMap)) {
                    resolve(comateTaskMap);
                }
                else if (comateTaskMap.deployStatus === 'FAILED') {
                    reject(new Error(`${comateTaskMap.reason}`));
                }
            }
            await sleep(10000);
        }
        reject(new Error('未检测到Jarvis上线任务，请进入Jarvis页面查看是否发起'));
    });
}

export function getWorkflowDeployTaskDetail(
    realUserName: string,
    taskId: number,
    maxRunTimes: number
): Promise<TaskBean> {
    return new Promise(async resolve => {
        const data = {
            header: {
                userid: 630152,
                optid: 630152,
                token: token,
            },
            body: {
                paasType: 'eks',
                realUserName: realUserName,
                userName: 'Comate',
                taskId: taskId,
            },
        };
        const headers = {
            'Content-Type': 'application/json;charset=UTF-8',
        };
        let taskBean: TaskBean = {
            taskStatus: '',
            taskDetailUrl: '',
            startTime: '',
            endTime: '',
        };
        for (let i = 0; i < maxRunTimes; i++) {
            const taskDetailResponse = await fetch(
                'https://api.baidu-int.com/json/sms/service/JarvisMscTaskService/getWorkflowDeployTaskDetail',
                {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(data),
                }
            );
            const taskDetailResponsJson = await taskDetailResponse.json();
            if (taskDetailResponsJson.header.status !== 0) {
                // do nothing
            }
            else if (
                Array.isArray(taskDetailResponsJson.body.data)
                && taskDetailResponsJson.body.data.length > 0
            ) {
                const taskMap = taskDetailResponsJson.body.data[0];
                taskBean = {
                    taskStatus: taskMap.taskStatus,
                    taskDetailUrl: taskMap.taskDetailUrl,
                    startTime: taskMap.startTime,
                    endTime: taskMap.endTime,
                };
                if (
                    taskMap.taskStatus === 'SUCCESS'
                    || taskMap.taskStatus === 'CANCELED'
                    || taskMap.taskStatus === 'PAUSING'
                ) {
                    resolve(taskBean);
                }
            }
            await sleep(10000);
        }
        resolve(taskBean);
    });
}
