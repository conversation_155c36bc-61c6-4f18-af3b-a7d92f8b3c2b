import fs from 'node:fs';
import path from 'node:path';
import defaultConfig from './config/default.json';
import childProcess from 'child_process';
import FormData from 'form-data';

interface Config {
    confName?: string;
    buildCmd?: string;
    output?: string;
    deployApp?: string;
}

export const configFolder = './.comate';
export const configPath = `${configFolder}/jarvis.json`;

/**
 * 构建日志文件路径
 */
export const buildStdOutPath = `${configFolder}/jarvis_build_std_out.log`;
const buildStdErrorPath = `${configFolder}/jarvis_build_std_error.log`;
const buildErrorPath = `${configFolder}/jarvis_build_error.log`;

function checkFolderExistsSync(folderPath: string) {
    try {
        return fs.existsSync(folderPath) && fs.statSync(folderPath).isDirectory();
    }
    catch (err) {
        return false;
    }
}

function checkFileExistsSync(filePath: string) {
    try {
        return fs.existsSync(filePath) && fs.statSync(filePath).isFile();
    }
    catch (err) {
        return false;
    }
}

function initConfigFile(cwd: string, mkdir?: boolean) {
    if (mkdir) {
        fs.mkdirSync(path.resolve(cwd, configFolder));
    }
    fs.writeFileSync(path.resolve(cwd, configPath), JSON.stringify(defaultConfig, null, 4));
}

export function getDeployConfig(cwd: string) {
    try {
        const fileBuffer = fs.readFileSync(path.resolve(cwd, configPath));
        return {type: 'file', value: fileBuffer};
    }
    catch (ex: any) {
        const initMessage = '检测到您没有配置Jarvis部署配置，已为您初始化一份Demo配置，'
            + `请参考Demo配置说明进行修改。文件地址：[点击打开配置文件](${configPath})，`
            + '修改并保存后在comate输入配置名后即可开始部署流程。';

        const isFolderExisted = checkFolderExistsSync(path.resolve(cwd, configFolder));
        if (!isFolderExisted) {
            initConfigFile(cwd, true);

            return {type: 'message', content: initMessage};
        }

        const isFileExisted = checkFileExistsSync(path.resolve(cwd, configPath));
        if (!isFileExisted) {
            initConfigFile(cwd);

            return {type: 'message', content: initMessage};
        }

        throw new Error(`获取配置文件失败 ${ex instanceof Error ? ex.message : ex}`);
    }
}

export function splitFile(sourceFile: string, numChunks: number) {
    const fileContent: Buffer = fs.readFileSync(sourceFile);

    const chunkSize: number = 1024 * 1024 * 90;
    for (let i = 0; i < numChunks; i++) {
        const start: number = i * chunkSize;
        const end: number = start + chunkSize;
        const chunkData: Buffer = fileContent.slice(start, end);

        const chunkFileName: string = `${sourceFile}_${i}`;
        fs.writeFileSync(chunkFileName, chunkData);
    }
}

export function createFormData(input: {[key: string]: any}): FormData {
    const form = new FormData();
    for (const key in input) {
        if (input.hasOwnProperty(key)) {
            form.append(key, input[key]);
        }
    }
    form.append('userid', 630152);
    form.append('optid', 630152);
    return form;
}

export function sleep(ms: number): Promise<void> {
    return new Promise<void>(resolve => setTimeout(resolve, ms));
}

export function executeCommand(command: string, cwd: string): Promise<{stdout: string, stderr: string}> {
    return new Promise((resolve, reject) => {
        childProcess.exec(command, {
            maxBuffer: Infinity,
            cwd,
        }, (error, stdout, stderr) => {
            if (stdout) {
                fs.writeFileSync(path.resolve(cwd, buildStdOutPath), stdout);
            }
            if (stderr) {
                fs.writeFileSync(path.resolve(cwd, buildStdErrorPath), stderr);
            }
            if (error) {
                fs.writeFileSync(path.resolve(cwd, buildErrorPath), error?.message || '');
                reject(error);
            }
            else {
                resolve({stdout, stderr});
            }
        });
    });
}

export function overrideConfig(data: Record<string, any>, cwd: string) {
    const absolutePath = path.resolve(cwd, configPath);
    const isFileExisted = checkFileExistsSync(absolutePath);
    if (!isFileExisted) {
        return;
    }
    const fileBuffer = fs.readFileSync(path.resolve(cwd, configPath));
    const config = JSON.parse(fileBuffer.toString());
    const newConfig = config.map((item: Config) => {
        return item.confName === data.confName ? data : item;
    });
    fs.writeFileSync(absolutePath, JSON.stringify(newConfig, null, 4));
}

export function parseJson(str: string, defaultVal?: any) {
    try {
        return JSON.parse(str);
    }
    catch (e) {
        return defaultVal;
    }
}

// 给时间前缀补0使其达到两位数，比如 1 => 01, 12 -> 12
function prefixTime(time: number) {
    return ('00' + time).slice(-2);
}

export function normalizeTime(time?: string) {
    if (!time) {
        return '';
    }
    const date = new Date(time);
    const year = date.getFullYear();
    const month = prefixTime(date.getMonth() + 1);
    const day = prefixTime(date.getDate());
    const hour = prefixTime(date.getHours());
    const minute = prefixTime(date.getMinutes());
    const second = prefixTime(date.getSeconds());
    return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
}
