[{"confName": "『必填』，应用部署时Comate对话的命令，例如: dev", "buildCmd": "『选填』，本地编译打包成 Jarvis应用.tar.gz 的命令，可以参考ci.yml配置填写。这里不填对话时则不执行对应编译打包命令", "output": "『必填』，本地编译的产出包，必须是 Jarvis应用.tar.gz，例如: output/cloudnative-consumer.tar.gz", "deployApp": "『必填』，部署到Jarvis的部署组编排名称，例如：cpdinf-cloudnative-consumer-12345", "desc": "『配置项描述』当前数组配置项对象内容是 Jarvis插件应用部署指令的配置项，应用部署能力详情可参考知识库：https://ku.baidu-int.com/d/CwDE9t_sX-X4v4"}, {"confName": "『必填』，EOS全新搭建时Comate对话的命令，例如: new", "buildCmd": "『选填』，本地编译打包成 Jarvis应用.tar.gz 的命令，可以参考ci.yml配置填写。这里不填对话时则不执行对应编译打包命令", "output": "『必填』，本地编译的产出包，必须是 Jarvis应用.tar.gz，例如: output/thunder.tar.gz", "modules": "『必填』，用户首次搭建EOS环境的模块列表，请填写 1个或多个 Jarvis应用名即可，例如: ['thunder', 'puppet']", "desc": "『配置项描述』当前数组配置项对象内容是 EOS环境全新搭建的配置项，全新搭建能力详情可参考知识库：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/gv0WKdV3ZF/60incnF1Zd/AASfVLdyhiDTsg"}, {"confName": "『必填』，marspro配置热更新时Comate对话的命令，例如: marspro", "configPath": "『必填』，指定本地打包热更新的代码config配置路径，例如：./config", "deployApp": "『必填』，config配置热更新的线下环境地址，请填写部署组编排名称，例如：cpd-marspro-1747030", "desc": "『配置项描述』当前数组配置项对象内容是描述 Marspro配置热更新的配置项，热更新配置详情可以参考知识库：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/gv0WKdV3ZF/60incnF1Zd/AASfVLdyhiDTsg"}]