import {EvalResult, Interpreter, InterpreterEnv, Options, Statement} from './Interpreter.js';

/**
 * 回调配置
 */
interface ActionConfig {
    /**
     * 创建对话后的回调
     */
    afterCreateDialog?: Statement[];
    /**
     * 多轮对话时，除第一次外的触发。之所以区分创建和继续，是为了刘玄场景中截断-继续的时候进行追加。需求场景看下面文档
     * https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/DBLKIO3bMt/FVFo_fU-UJEBGB#anchor-bee08660-8553-11ef-978c-854a8ea32f3c
     */
    afterContinueDialog?: Statement[];
}

/**
 * 提示词相关的前置后置回调
 */
export class PromptActions {
    constructor(private readonly log: (value: string) => void, private readonly actionConfig: ActionConfig) {}
    static of(params: {log: (value: string) => void, actionConfig: ActionConfig}) {
        return new PromptActions(params.log, params.actionConfig);
    }
    static ofEmpty() {
        return new PromptActions(() => {}, {});
    }
    afterCreateDialog(env: InterpreterEnv, options: Options): Promise<EvalResult> {
        const afterCreateDialog = this.actionConfig.afterCreateDialog;
        if (!afterCreateDialog) {
            return Promise.resolve({success: true, length: 0});
        }
        this.log('开始执行 afterCreateDialog 脚本，内容为：' + afterCreateDialog.join(''));
        const interpreter = Interpreter.of({env, log: this.log, options});
        return interpreter.evalBlock(afterCreateDialog);
    }
    afterContinueDialog(env: InterpreterEnv, options: Options): Promise<EvalResult> {
        const afterContinueDialog = this.actionConfig.afterContinueDialog;
        if (!afterContinueDialog) {
            return Promise.resolve({success: true, length: 0});
        }
        this.log('开始执行 afterContinueDialog 脚本，内容为：' + afterContinueDialog.join(''));
        const interpreter = Interpreter.of({env, log: this.log, options});
        return interpreter.evalBlock(afterContinueDialog);
    }
}
