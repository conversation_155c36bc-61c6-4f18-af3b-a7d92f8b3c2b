import {PromptSummaryData} from '../../api/interface.js';
import {Brand} from '../../utils/type.js';
import {PromptActions} from './PromptAction.js';

/** 提示词唯一标识符 */
export type PromptKey = Brand<string, 'PromptKey'>;
export interface Variable {
    key: string;
    value: string;
    type: string;
}
export interface PromptData {
    promptCode: string;
    promptID: number;
    promptName: string;
    spaceCode: string;
    spaceName: string;
    submitStatus: number;
    versionID: number;
    versionName: string;
    versionCreateTime: string;
    text: string | undefined;
    inputType: string;
    variables: Variable[] | undefined;
    modelParams: {
        system?: string;
    };
}
const emptyPromptData: PromptData = {
    text: '',
    inputType: 'text',
    variables: [],
    modelParams: {},
    promptCode: '',
    promptID: 0,
    promptName: '<无提示词>',
    spaceCode: '',
    spaceName: '',
    submitStatus: 0,
    versionID: 0,
    versionName: '',
    versionCreateTime: '',
};
const userInputPromptData: PromptData = {
    text: '{{IDE_QUERY}}',
    inputType: 'text',
    variables: [],
    modelParams: {},
    promptCode: '',
    promptID: 0,
    promptName: '<用户输入作为提示词>',
    spaceCode: '',
    spaceName: '',
    submitStatus: 0,
    versionID: 0,
    versionName: '',
    versionCreateTime: '',
};
/**
 * 宏提示词
 * 包括基础的提示词(prompt)和操作(action)，其为用户提供完整业务功能。
 * 例1 优化代码：提示词 优化{{IDE_CODE}}，操作 AI输出写入当前文件
 * 例2 生成提交：提示词 输出git message{{IDE_CODE}}, 操作 调起 commit.sh
 * 后续可能还需要加快捷键触发等
 */
export class MacroPrompt {
    constructor(private readonly _data: PromptData, private readonly _promptActions: PromptActions) {}
    static of(params: {data: PromptData, promptActions: PromptActions}) {
        return new MacroPrompt(params.data, params.promptActions);
    }
    static ofEmpty() {
        return new MacroPrompt(emptyPromptData, PromptActions.ofEmpty());
    }
    /**
     * 特殊提示词，使用用户输入作为提示词
     */
    static defaultPromptWithUserInput = new MacroPrompt(userInputPromptData, PromptActions.ofEmpty());
    static ofData(data: PromptData) {
        return new MacroPrompt(data, PromptActions.ofEmpty());
    }
    /**
     * 提示词的唯一标识
     * 本地提示词的 ID 都是 0，所以还需要加上 promptName
     */
    get promptKey(): PromptKey {
        return `${this._data.promptID}_${this._data.promptName}` as PromptKey;
    }
    /**
     * 主要给根据示例或者描述生成提示词用
     */
    static ofSimplePanda(params: {text: string, system: string, promptName: string}) {
        return new MacroPrompt({
            ...emptyPromptData,
            promptName: params.promptName,
            text: params.text,
            modelParams: {
                system: params.system,
            },
        }, PromptActions.ofEmpty());
    }
    get data() {
        return this._data;
    }
    get actions() {
        return this._promptActions;
    }
}

export function promptFileName(prompt: PromptSummaryData) {
    return `${prompt.promptName}_${prompt.versionName}-${prompt.spaceName}`;
}
