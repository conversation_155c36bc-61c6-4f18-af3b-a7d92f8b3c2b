import fs from 'node:fs';
import ChildProcess from 'node:child_process';
import {toAbsolutePath} from '../../utils/fileTools.js';
import {injectParamsToText} from '../../providers/interpreter/pathParamsInject.js';

const fsp = fs.promises;
export type EvalResult = {
    success: true;
    length: number;
    log?: string[];
} | {
    success: false;
    error: unknown;
    message: string;
    log?: string[];
};

function shell(command: string) {
    return new Promise((resolve, reject) => {
        ChildProcess.exec(command, (error, stdout, stderr) => {
            if (error) {
                reject(error);
            }
            else if (stderr) {
                reject(new Error(`Shell command error: ${stderr}`));
            }
            else {
                resolve(stdout);
            }
        });
    });
}

export type Statement = string | Statement[];
export interface Options {cwd: string}
export interface InterpreterEnv {
    fileDir: string;
    fileName: string;
    fileExt: string;
    promptName: string;
    spaceName: string;
    formatTime: string;
    aiResponse: string;
    aiResponseFirstCode: string;
}

/**
 * Lisp 风格脚本代码解释器
 */
export class Interpreter {
    constructor(private readonly env: InterpreterEnv, private readonly log: (string: string) => void, private readonly options: Options) {}
    static of(params: {env: InterpreterEnv, log: (string: string) => void, options: Options}) {
        return new Interpreter(params.env, params.log, params.options);
    }
    toString = (value: unknown): string => {
        if (value !== null && value !== undefined) {
            return `${value}`;
        }
        return '';
    };
    private readonly toAbsolutePath = (path: string) => {
        return toAbsolutePath(this.options.cwd, path);
    };
    // 如果结果是相对路径，转换为基于 cwd 的绝对路径
    evalStatement = (statement: Statement): any => {
        // @ts-ignore this.env 类型不对
        const injectEnv = (template: string) => injectParamsToText(template, this.env);
        if (Array.isArray(statement)) {
            const [functionName, ...rest] = statement;
            switch (functionName) {
                case 'writeFile': {
                    const [fileNameTemplate, content] = rest;
                    const fileName = injectEnv(this.toString(fileNameTemplate));
                    const absolutePath = this.toAbsolutePath(fileName);
                    const fileContent = injectEnv(this.toString(content));
                    this.log(`写入文件: ${absolutePath}, 内容: ${fileContent}`);
                    return fsp.writeFile(absolutePath, fileContent);
                }
                case 'appendFile': {
                    const [fileNameTemplate, content] = rest;
                    const fileName = injectEnv(this.toString(fileNameTemplate));
                    const absolutePath = this.toAbsolutePath(fileName);
                    const fileContent = injectEnv(this.toString(content));
                    this.log(`追加文件: ${absolutePath}, 内容: ${fileContent}`);
                    return fsp.appendFile(absolutePath, fileContent);
                }
                case 'shell': {
                    return (async () => {
                        try {
                            const [command, ...args] = rest.map(v => injectEnv(this.toString(v)));
                            const commandStr = ['cd', this.options.cwd, '&&', 'sh', command, ...args].join(' ');
                            this.log(`执行 shell 命令: ${commandStr}`);
                            await shell(commandStr);
                        }
                        catch (err) {
                            const errMsg = err instanceof Error ? err.message : `${err}`;
                            this.log(`shell 命令失败: ${errMsg}`);
                        }
                    })();
                }
            }
        }
        return statement;
    };
    async evalBlock(script: Statement[]): Promise<EvalResult> {
        for (const statement of script) {
            try {
                await this.evalStatement(statement);
            } catch (err) {
                const errMsg = err instanceof Error ? err.message : `${err}`;
                return {
                    success: false,
                    error: err,
                    message: `执行语句失败\n语句: ${statement}\n错误: ${errMsg}`,
                };
            }
        }
        return {success: true, length: script.length};
    }
}
