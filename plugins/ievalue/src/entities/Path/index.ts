import Path from 'path';
import fs from 'fs';
const fsp = fs.promises;

export class RelativePath {
    constructor(private readonly path: string) {}
    static of(params: {path: string}) {
        return new RelativePath(params.path);
    }
    toAbsolutePath(root: string) {
        const path = Path.resolve(root, this.path);
        return AbsolutePath.of({path});
    }
}

export class AbsolutePath {
    constructor(private readonly path: string) {}
    static of(params: {path: string}) {
        return new AbsolutePath(params.path);
    }
}
