import {Brand} from '../../utils/type.js';

export enum ModelName {
    ERNIE_BOT = 'ernie-bot',
    ERNIE_BOT_4_0 = 'ernie-bot-4.0',
    ERNIE_4_0_8K = 'ernie-4.0-8k',
    ERNIE_4_0_TURBO_8K = 'ernie-4.0-turbo-8k',
    ERNIE_3_5_8K = 'ernie-3.5-8k',
    ERNIE_SPEED_8K = 'ernie-speed-8k',
    ERNIE_SPEED_128K = 'ernie-speed-128k',
    ERNIE_LITE_8K = 'ernie-lite-8k',
    GPT_3_5_TURBO = 'gpt-3.5-turbo',
    GPT_3_5_TURBO_16K = 'gpt-3.5-turbo-16k',
    GPT_4 = 'gpt-4',
    GPT_4_1106_PREVIEW = 'gpt-4-1106-preview',
    GPT_4_TURBO_PREVIEW = 'gpt-4-turbo-preview',
    GPT_4_TURBO = 'gpt-4-turbo',
    GPT_4_VISION_PREVIEW = 'gpt-4-vision-preview',
    GPT_4O = 'gpt-4o',
    GPT_4O_2024_08_06 = 'gpt-4o-2024-08-06',
    GPT_4O_MINI = 'gpt-4o-mini',
}
export interface ModelData {
    modelID: number;
    model: string;
    modelType: string;
    modelParams: {
        temperature?: number;
        topP?: number;
        system?: string;
    };
}
/** 模型唯一标识符 */
export type ModelKey = Brand<string, 'ModelKey'>;
export class Model {
    constructor(public data: ModelData) {}
    static of(params: {data: ModelData}) {
        return new Model(params.data);
    }
    static ofName(model: ModelName) {
        return Model.of({
            data: {
                modelID: 0,
                modelType: 'PANDA',
                modelParams: {},
                model,
            },
        });
    }
    get modelKey(): ModelKey {
        return this.data.model as ModelKey;
    }
    get displayName(): string {
        return this.data.model;
    }
}
