/**
 * 执行对象的深复制。
 *
 * @param obj 需要被深复制的对象
 * @returns 深复制后的新对象
 */

export function deepCopy<T>(obj: T): T {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }

    if (obj instanceof Date) {
        return new Date(obj.getTime()) as any;
    }

    if (obj instanceof Array) {
        return obj.map(item => deepCopy(item)) as any;
    }

    if (obj instanceof Object) {
        const copy: Record<string, any> = {};
        Object.keys(obj).forEach(key => {
            copy[key] = deepCopy((obj as Record<string, any>)[key]);
        });
        return copy as T;
    }

    throw new Error('Unable to copy obj! Its type isn\'t supported.');
}
