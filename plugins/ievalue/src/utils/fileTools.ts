import Path from 'node:path';
import fs from 'node:fs';
const fsp = fs.promises;

export function toAbsolutePath(dir: string, path: string): string {
    return Path.isAbsolute(path) ? path : Path.resolve(dir, path);
}

/**
 * 创建文件，如果文件不存在则创建，如果文件已存在则不做任何操作
 * @param filePath 文件路径
 * @param content 文件内容
 * @returns 是否成功创建文件
 */
export async function createFileIfNotExists(filePath: string, content: string | Buffer): Promise<boolean> {
    try {
        // 使用 Path.normalize 来确保路径格式正确
        const normalizedPath = Path.normalize(filePath);
        await fsp.mkdir(Path.dirname(normalizedPath), {recursive: true});

        // 尝试创建文件，如果文件已存在则会抛出错误
        await fsp.writeFile(normalizedPath, content, {flag: 'wx'});
        return true;
    }
    catch (error) {
        if ((error as NodeJS.ErrnoException).code === 'EEXIST') {
            // 文件已存在，不做任何操作
            return false;
        }
        // 其他错误，重新抛出
        throw error;
    }
}

/**
 * 创建或覆盖文件
 * @param filePath 文件路径
 * @param content 文件内容
 * @returns 如果是覆盖现有文件返回true，如果是新建文件返回false
 */
export async function createOrOverwriteFile(filePath: string, content: string): Promise<boolean> {
    try {
        // 使用 Path.normalize 来确保路径格式正确
        const normalizedPath = Path.normalize(filePath);
        await fsp.mkdir(Path.dirname(normalizedPath), {recursive: true});

        // 检查文件是否存在
        const fileExists = await fsp.access(normalizedPath).then(() => true).catch(() => false);

        // 写入文件内容
        await fsp.writeFile(normalizedPath, content);

        // 返回是否为覆盖操作
        return fileExists;
    }
    catch (error) {
        // 处理其他错误
        console.error(`创建或覆盖文件失败: ${filePath}`, error);
        throw error;
    }
}

export async function safeReadFile(filePath: string) {
    if (!fs.existsSync(filePath)) {
        return '';
    }
    return fsp.readFile(filePath, 'utf-8');
}

/**
 * 读取目录内容
 * @param dirPath 目录路径
 * @returns 目录内容列表，文件夹不存在则返回空
 */
export async function readDir(dirPath: string): Promise<string[] | undefined> {
    // 检查目录是否存在
    const dirExists = await fsp.access(dirPath).then(() => true).catch(() => false);

    if (!dirExists) {
        // 如果目录不存在，返回空数组
        return;
    }

    // 读取并返回目录内容
    return await fsp.readdir(dirPath);
}

/**
 * 读取目录内容，如果目录不存在则创建
 * @param dirPath 目录路径
 * @returns 目录内容列表，如果是新创建的目录则返回空数组
 */
export async function readOrCreateDir(dirPath: string): Promise<string[]> {
    // 检查目录是否存在
    const dirExists = await fsp.access(dirPath).then(() => true).catch(() => false);

    // 如果目录不存在，创建它
    if (!dirExists) {
        await fsp.mkdir(dirPath, {recursive: true});
    }

    // 读取并返回目录内容
    return await fsp.readdir(dirPath);
}
