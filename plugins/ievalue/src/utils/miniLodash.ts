export function keyBy<T>(obj: T[], keyFor: (item: T) => string) {
    const result: Record<string, T> = {};
    for (const item of obj) {
        result[keyFor(item)] = item;
    }
    return result;
}

export function mapValues<T, U>(obj: Record<string, T>, fn: (value: T) => U): Record<string, U> {
    const result: Record<string, U> = {};
    for (const key in obj) {
        result[key] = fn(obj[key]);
    }
    return result;
}
