export function parseJsonc(jsoncString: string): any {
    const stripComments = (input: string): string => {
        let inString = false;
        let inComment = false;
        let inMultilineComment = false;
        let newStr = '';
        let char = '';
        let nextChar = '';

        for (let i = 0; i < input.length; i++) {
            char = input[i];
            nextChar = input[i + 1];

            if (!inComment && !inMultilineComment) {
                if (char === '"') {
                    inString = !inString;
                }
            }

            if (!inString) {
                if (!inComment && !inMultilineComment) {
                    if (char === '/' && nextChar === '/') {
                        inComment = true;
                        i++;
                    }
                    else if (char === '/' && nextChar === '*') {
                        inMultilineComment = true;
                        i++;
                    }
                    else {
                        newStr += char;
                    }
                }
                else if (inComment && char === '\n') {
                    inComment = false;
                    newStr += char;
                }
                else if (inMultilineComment && char === '*' && nextChar === '/') {
                    inMultilineComment = false;
                    i++;
                }
            }
            else {
                newStr += char;
            }
        }

        return newStr;
    };

    const strippedJson = stripComments(jsoncString);
    return JSON.parse(strippedJson);
}
