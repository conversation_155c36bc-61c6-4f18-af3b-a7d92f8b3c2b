export function codeblocksInMarkdown(markdown: string): Array<{fileType: string, content: string}> {
    const codeblocks: Array<{fileType: string, content: string}> = [];
    const regex = /```(\w+)?\n([\s\S]*?)```/g;
    let match;

    while ((match = regex.exec(markdown)) !== null) {
        codeblocks.push({
            fileType: match[1] || '',
            content: match[2].trim(),
        });
    }

    return codeblocks;
}
