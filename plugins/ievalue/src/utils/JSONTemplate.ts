import {parseJsonc} from './jsoncParser.js';

/**
 * 带注释和顺序的 JSON 格式化工具
 * @template T 数据类型
 */
export class JSONTemplate<T extends object> {
    private readonly schema: Array<{key: keyof T, comment?: string, blockComment?: string}>;

    /**
     * @param schema 预期格式，包括 key 和对应的注释
     */
    constructor(schema: Array<{key: keyof T, comment?: string, blockComment?: string}>) {
        this.schema = schema;
    }

    /**
     * 格式化对象为带注释的 JSON 字符串
     * @param obj 要格式化的对象
     * @returns 格式化后的 JSON 字符串
     */
    format(obj: T): string {
        const lines: string[] = ['{'];
        const fullSchema: Array<{key: keyof T, comment?: string, blockComment?: string}> = [
            ...this.schema,
            ...Object
                .keys(obj)
                .filter(key => !this.schema.some(item => item.key === key))
                .map(key => ({key: key as keyof T})),
        ];
        fullSchema.forEach(({key, comment, blockComment}, index) => {
            const value = obj[key];
            const formattedValue = JSON.stringify(value);
            if (blockComment) {
                lines.push(`    /** ${blockComment} */`);
            }
            if (comment) {
                lines.push(`    // ${comment}`);
            }
            const valueLine = `    "${String(key)}": ${formattedValue}${index < fullSchema.length - 1 ? ',' : ''}`;
            lines.push(valueLine);
        });
        lines.push('}');
        return lines.join('\n');
    }

    /**
     * 解析带注释的 JSON 字符串
     * @param jsonc 带注释的 JSON 字符串
     * @returns 解析后的对象
     */
    parse(jsonc: string): unknown {
        try {
            return parseJsonc(jsonc);
        }
        catch (error) {
            if (error instanceof Error) {
                throw new Error('无效的 JSONC 格式\n' + jsonc + '\n' + error.message);
            }
            throw new Error('无效的 JSONC 格式');
        }
    }
}
