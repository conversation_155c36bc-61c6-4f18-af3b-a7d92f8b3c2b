import {PluginSetupContext} from '@comate/plugin-host';
import {HelpFallbackProvider} from './providers/help.js';
import {GetPromptProvider} from './providers/getPrompt.js';
import {skills} from './config/skill.js';
import {RunPromptProvider} from './providers/runPrompt.js';
import {RunEB4Provider, RunGPTProvider} from './providers/runSpecialModel.js';
import {GeneratePromptByDescriptionProvider} from './providers/generatePromptByDescription.js';
import {GeneratePromptByExampleProvider} from './providers/generatePromptByExample.js';
import {DiagnosisPromptProvider} from './providers/diagnosisPrompt.js';

export function setup({registry}: PluginSetupContext) {
    registry.registerFallbackProvider('help', HelpFallbackProvider);

    registry.registerSkillProvider(skills.runGPT.name, RunGPTProvider);
    registry.registerSkillProvider(skills.runEB.name, RunEB4Provider);
    registry.registerSkillProvider(skills.runPrompt.name, RunPromptProvider);

    registry.registerSkillProvider(skills.generatePromptByDescription.name, GeneratePromptByDescriptionProvider);
    registry.registerSkillProvider(skills.getPrompt.name, GetPromptProvider);
    registry.registerSkillProvider(skills.generatePromptByExample.name, GeneratePromptByExampleProvider);

    registry.registerSkillProvider(skills.diagnosisPrompt.name, DiagnosisPromptProvider);
}
