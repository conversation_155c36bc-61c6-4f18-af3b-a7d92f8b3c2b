import {ElementChunkStream, FunctionParameterDefinition, TaskProgressChunk} from '@comate/plugin-host';
import {skills} from '../config/skill.js';
import {BasePromptProvider} from './basePrompt/index.js';
import Path from 'node:path';
import fs from 'node:fs';
import {formatTime} from '../utils/time.js';
import {codeblocksInMarkdown} from '../utils/markdown.js';
import {pathForResultCodeExport} from './interpreter/pathParamsInject.js';
import {APIService, SessionID} from './basePrompt/Service/index.js';
import {MacroPrompt, PromptKey} from '../entities/MacroPrompt/index.js';
import {Model, ModelKey} from '../entities/Model/index.js';
import {internalModel} from '../config/internalModel.js';
import {expectNever} from '../utils/type.js';
const fsp = fs.promises;

/* ---- 流程节点 ---- */
enum Step {
    /** 第一步，刚开始，选模型 */
    empty,
    /** 第二步，选模型之后，开始选提示词 */
    modelSelected,
    /** 第三步，选模型和提示词之后，开始创建对话 */
    promptSelected,
    /** 最后，对话已经创建，可以开始多轮 */
    dialogCreated,
}
/**
 * Functional Programming 可以让我们对状态转移进行严格约束，快说：「我爱 FP」
 */
type RunningState =
    | {type: Step.empty}
    | {type: Step.modelSelected, modelKey: ModelKey}
    | {type: Step.promptSelected, modelKey: ModelKey, promptKey: PromptKey}
    | {type: Step.dialogCreated, modelKey: ModelKey, promptKey: PromptKey, sessionID: SessionID};
type StateOfStep<T extends Step> = RunningState & {type: T};
// 请只使用赋值语法，保持对象属性是不变的
let runningState: RunningState = {type: Step.empty};

/* ---- 按钮定义 ---- */
enum ActionKey {
    selectModel = 'comate.ievalue.prompt.selectModel',
    runPrompt = 'comate.ievalue.prompt.runPrompt',
    displayPromptList = 'comate.ievalue.prompt.displayPromptList',
    displayModelList = 'comate.ievalue.prompt.displayModelList',
}
// NOTE: 注意，这里的对象会被序列化，所以我们传 Key 而非复杂对象
type ButtonMapping = {
    [ActionKey.selectModel]: {modelKey: ModelKey};
    [ActionKey.runPrompt]: {promptKey: PromptKey, previousState: StateOfStep<Step.modelSelected>};
    [ActionKey.displayPromptList]: {previousState: StateOfStep<Step.modelSelected>};
    [ActionKey.displayModelList]: {};
};
function commandButton<T extends keyof ButtonMapping>(
    {commandName, data, content}: {commandName: T, data?: ButtonMapping[T], content: string}
) {
    return <command-button commandName={commandName} data={data}>{content}</command-button>;
}

/* ---- 配置 ---- */
const autoRunPromptAfterModelSelected = true;

/**
 * 执行提示词
 */
export class RunPromptProvider extends BasePromptProvider {
    static skillName: string = skills.getPrompt.name;
    static description: string = skills.getPrompt.description;
    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
        required: [],
    };
    private apiService = APIService.of({log: this.debugLog});

    private async loadAllPromptsWithInternal() {
        // const promptList = await this.loadAllPrompts();
        // return [...internalPrompts, ...promptList];
        return this.loadAllPrompts();
    }

    private async initInternalPrompt() {
        // TODO: 初始化模型
        const allModel = await this.loadAllModels();
        const modelSet = new Set(allModel.map(m => m.modelKey));
        const modelNotInit = internalModel.filter(m => !modelSet.has(m.modelKey));
        const saveResult = await Promise.allSettled(modelNotInit.map(m => this.saveModel(m)));
        return saveResult;
    }
    protected async *execute(arg: void): AsyncIterableIterator<TaskProgressChunk> {
        await this.initInternalPrompt();

        const stream = new ElementChunkStream();
        const promptList = await this.loadAllPromptsWithInternal();
        switch (runningState.type) {
            case Step.empty: {
                yield* this.displayModelList();
                return;
            }
            case Step.modelSelected: {
                if (autoRunPromptAfterModelSelected) {
                    runningState = {
                        type: Step.promptSelected,
                        modelKey: runningState.modelKey,
                        promptKey: MacroPrompt.defaultPromptWithUserInput.promptKey,
                    }
                    yield* this.createDialog(runningState)
                } else {
                    yield* this.displayPromptList(runningState);
                }
                return;
            }
            case Step.promptSelected: {
                const prompt = this.getPromptByKey(runningState.promptKey);
                if (!prompt) {
                    yield stream.flush(<p>找不到该提示词</p>);
                    runningState = {type: Step.empty};
                    return;
                }
                yield* this.createDialog(runningState);
                return;
            }
            case Step.dialogCreated: {
                const prompt = this.getPromptByKey(runningState.promptKey);
                if (!prompt) {
                    yield stream.flush(<p>找不到该提示词</p>);
                    runningState = {type: Step.empty};
                    return;
                }
                const query = this.editorState.queryOrSelection;
                if (!query) {
                    runningState = {type: Step.empty};
                    yield* this.displayModelList();
                    return;
                }
                yield* this.continueDialog(runningState);
                return;
            }
            default: {
                // TS 经典花招，确保检查了所有 case 分支
                expectNever(runningState)
            }
        }
    }
    async *handleCommand(commandName: unknown, data: unknown): AsyncIterableIterator<TaskProgressChunk> {
        // interface 我卡不住，所以就卡这里吧
        const params = {commandName, data} as {
            [k in keyof ButtonMapping]: {commandName: k, data: ButtonMapping[k]};
        }[keyof ButtonMapping];
        this.debugLog({object: params});

        const stream = new ElementChunkStream();
        switch (params.commandName) {
            case ActionKey.selectModel: {
                const {modelKey} = params.data;
                runningState = {type: Step.modelSelected, modelKey};
                const model = await this.getModelByKey(modelKey);
                yield stream.flush(<p>已切换模型为：{model?.displayName || ''}</p>);
                yield* this.displayPromptList(runningState)
                return;
            }
            case ActionKey.runPrompt: {
                const {promptKey, previousState} = params.data;
                runningState = {type: Step.promptSelected, modelKey: previousState.modelKey, promptKey};
                if (autoRunPromptAfterModelSelected) {
                    yield* this.createDialog({type: Step.promptSelected, modelKey: previousState.modelKey, promptKey});
                } else {
                    const prompt = await this.getPromptByKey(promptKey);
                    yield stream.flush(<p>
                        已切换提示词为：{prompt?.data.promptName ?? ''}，输入内容让我们开始对话吧。
                    </p>)
                    yield this.additionalInfo({stream});
                }
                return;
            }
            case ActionKey.displayPromptList: {
                yield* this.displayPromptList(params.data.previousState);
                return;
            }
            case ActionKey.displayModelList: {
                yield* this.displayModelList();
                return;
            }
            default: {
                expectNever(params)
            }
        }
    }
    protected async *displayModelList() {
        const allModel = await this.loadAllModels();
        this.debugLog({allModel});
        const stream = new ElementChunkStream();
        yield stream.flush(<h1>请选择要使用的模型</h1>);
        yield stream.flush(
            <ul>
                {allModel.map(model => {
                    return (
                        <li>
                            <flex betweenCentered>
                                <span>
                                    {model.displayName}
                                    （<file-link to={this.pathForModelFile(model)} line={1}>参数</file-link>）
                                </span>

                                {commandButton({
                                    commandName: ActionKey.selectModel,
                                    data: {modelKey: model.modelKey},
                                    content: '选择模型',
                                })}

                            </flex>
                        </li>
                    );
                })}
            </ul>
        );
    }
    protected async *displayPromptList(previousState: RunningState & {type: Step.modelSelected}) {
        const macroPromptList = await this.loadAllPromptsWithInternal();
        const model = await this.getModelByKey(previousState.modelKey);
        const stream = new ElementChunkStream();
        if (macroPromptList.length === 0) {
            yield stream.flush(<p>当前没有可执行的提示词，您可以使用<code>/生成提示词</code>创建，或输入内容直接开始对话</p>);
            return;
        }
        yield stream.flush(<h1>可用提示词如下</h1>);
        yield stream.flush(
            <ul>
                {macroPromptList.map(macroPrompt => {
                    const prompt = macroPrompt.data;
                    return (
                        <li>
                            <flex betweenCentered>
                                {/* <file-link to={this.pathForPromptIEValueFile(prompt)} line={1}>
                                    {prompt.promptName}
                                </file-link> */}
                                <span>{prompt.promptName}</span>
                                {prompt.versionName}-{prompt.spaceName}
                                {commandButton({
                                    commandName: ActionKey.runPrompt,
                                    data: {promptKey: macroPrompt.promptKey, previousState},
                                    content: autoRunPromptAfterModelSelected ? '点击执行' : '点击切换',
                                })}
                            </flex>
                        </li>
                    );
                })}
            </ul>
        );
        yield stream.flush(<p>
            当前模型为<strong>{model?.displayName ?? ''}</strong>，
            您可以<code>选择提示词</code>、<code>直接输入问题</code>或<code>框选编辑区文本</code>来开始对话
        </p>);
    }
    protected async *createDialog(previousState: StateOfStep<Step.promptSelected>) {
        const stream = new ElementChunkStream();
        const prompt = await this.getPromptByKey(previousState.promptKey);
        if (!prompt) {
            yield stream.flush(<p>找不到该提示词</p>);
            return;
        }
        const model = await this.getModelByKey(previousState.modelKey);
        if (!model) {
            yield stream.flush(<p>找不到该模型</p>);
            return;
        }
        const promptData = prompt.data;

        const username = await this.getUserName();
        if (!username) {
            yield stream.flush(<p>请先登录</p>);
            return;
        }
        const apiKey = await this.getAPIKey();
        if (!apiKey) {
            const apiKeyPath = this.getPaths().userSetting;
            yield stream.flush(<p>请先在下面路径中配置API Key</p>);
            yield stream.flush(<file-link to={apiKeyPath} line={1}>{apiKeyPath}</file-link>);
            return;
        }
        const result = await this.apiService.autoDialog({
            model,
            sessionID: undefined,
            prompt: prompt,
            localStates: {
                query: this.editorState.queryOrSelection,
                currentCode: this.editorState.activeFileContent,
            },
            config: {
                username,
                apiKey,
            },
        });
        this.log(result);

        const name = model.displayName;

        // 输出用户提示信息
        if (!result.success) {
            yield stream.flush(<h1>{name}执行失败</h1>);
            yield stream.flush(<markdown>{result.message}</markdown>);
            return;
        }
        yield stream.flush(<h1>{name}执行成功</h1>);
        yield stream.flush(<h2>输入内容为：</h2>);
        yield stream.flush(<markdown>{result.input}</markdown>);
        yield stream.flush(<hr />);
        yield stream.flush(<h2>输出内容为：</h2>);
        yield stream.flush(<markdown>{result.message}</markdown>);

        // 更新 sessionID
        const sessionID = result.sessionID;
        runningState = {type: Step.dialogCreated, promptKey: prompt.promptKey, modelKey: model.modelKey, sessionID};

        // 保存第一个代码块
        yield stream.flush(<hr />);
        const path = this.editorState.activeFilePath;
        if (path) {
            yield* this.saveFirstCode({output: result.output, prompt, stream, path});
        }
        const evalResult = await prompt.actions.afterCreateDialog({
            fileDir: Path.dirname(path),
            fileName: Path.basename(path, Path.extname(path)),
            fileExt: Path.extname(path),
            promptName: promptData.promptName,
            spaceName: promptData.spaceName,
            formatTime: formatTime(new Date()),
            aiResponse: result.output,
            aiResponseFirstCode: (codeblocksInMarkdown(result.output))[0]?.content,
        }, {cwd: this.init.cwd});
        if (!evalResult.success) {
            yield stream.flush(<h2>后置操作执行失败</h2>);
            yield stream.flush(<p>{evalResult.message}</p>);
        } else if (evalResult.length) {
            yield stream.flush(<h2>后置操作执行成功</h2>);
        }
        const info = await this.additionalInfo({stream});
        const buttons = await this.additionalButtons({stream});
        yield stream.flush(<flex betweenCentered>
            {[...buttons, ...info]}
        </flex>);
    }
    protected async *continueDialog(previousState: StateOfStep<Step.dialogCreated>) {
        const stream = new ElementChunkStream();

        const username = await this.getUserName();
        if (!username) {
            yield stream.flush(<p>请先登录</p>);
            return;
        }
        const apiKey = await this.getAPIKey();
        if (!apiKey) {
            const apiKeyPath = this.getPaths().userSetting;
            yield stream.flush(<p>请先在下面路径中配置API Key</p>);
            yield stream.flush(<file-link to={apiKeyPath} line={1}>{apiKeyPath}</file-link>);
            return;
        }
        const model = await this.getModelByKey(previousState.modelKey);
        if (!model) {
            yield stream.flush(<p>找不到该模型</p>);
            return;
        }
        const prompt = await this.getPromptByKey(previousState.promptKey);
        if (!prompt) {
            yield stream.flush(<p>找不到该提示词</p>);
            return;
        }
        const {sessionID} = previousState;

        const result = await this.apiService.autoDialog({
            model,
            prompt: MacroPrompt.ofEmpty(),
            sessionID,
            localStates: {
                query: this.editorState.queryOrSelection,
                currentCode: this.editorState.activeFileContent,
            },
            config: {
                username,
                apiKey,
            },
        });
        this.log(result);

        const promptData = prompt.data;
        const name = model.displayName;

        // 输出用户提示信息
        if (!result.success) {
            yield stream.flush(<h1>{name}执行失败</h1>);
            yield stream.flush(<markdown>{result.message}</markdown>);
            return;
        }
        yield stream.flush(<h1>{name}执行成功</h1>);
        yield stream.flush(<markdown>{result.message}</markdown>);

        // 保存第一个代码块
        yield stream.flush(<hr />);
        const path = this.editorState.activeFilePath;
        if (path) {
            yield* this.saveFirstCode({output: result.output, prompt, stream, path});
        }
        const evalResult = await prompt.actions.afterContinueDialog({
            fileDir: Path.dirname(path),
            fileName: Path.basename(path, Path.extname(path)),
            fileExt: Path.extname(path),
            promptName: promptData.promptName,
            spaceName: promptData.spaceName,
            formatTime: formatTime(new Date()),
            aiResponse: result.output,
            aiResponseFirstCode: (codeblocksInMarkdown(result.output))[0]?.content,
        }, {cwd: this.init.cwd});
        if (!evalResult.success) {
            yield stream.flush(<h2>后置操作执行失败</h2>);
            yield stream.flush(<p>{evalResult.message}</p>);
        } else if (evalResult.length) {
            yield stream.flush(<h2>后置操作执行成功</h2>);
        }
        const info = await this.additionalInfo({stream});
        const buttons = await this.additionalButtons({stream});
        yield stream.flush(<flex betweenCentered>
            {[...buttons, ...info]}
        </flex>);
    }

    private async *saveFirstCode(
        params: {output: string, prompt: MacroPrompt, stream: ElementChunkStream, path: string}
    ) {
        const {prompt, stream, path, output} = params;
        const codeList = codeblocksInMarkdown(output);
        const firstCode = codeList[0];
        const content = firstCode?.content;

        const filePathTemplate = await this.userSetting.getItem('exportFirstCode');
        if (!filePathTemplate) {
            return;
        }
        const exportFirstCode = await pathForResultCodeExport(filePathTemplate, {
            fileDir: Path.dirname(path),
            fileName: Path.basename(path, Path.extname(path)),
            fileExt: Path.extname(path),
            promptName: prompt.data.promptName,
            spaceName: prompt.data.spaceName,
            formatTime: formatTime(new Date()),
        });

        if (content && exportFirstCode) {
            try {
                await fsp.writeFile(exportFirstCode, content);
                yield stream.flush(<p>已将第一个代码块保存到文件：</p>);
                yield stream.flush(<file-link to={exportFirstCode} line={1}>{exportFirstCode}</file-link>);
            }
            catch (error) {
                this.debugLog(`保存第一个代码块失败: ${error}`);
                yield stream.flush(<p>保存第一个代码块失败: {String(error)}</p>);
            }
        }
    }

    async getCurrentPrompt(): Promise<MacroPrompt | undefined> {
        if (runningState.type === Step.empty || runningState.type === Step.modelSelected) {
            return;
        }
        const promptKey = runningState.promptKey;
        const macroPrompt = this.getPromptByKey(promptKey);
        return macroPrompt;
    }
    async getCurrentModel(): Promise<Model | undefined> {
        if (runningState.type === Step.empty) {
            return;
        }
        return this.getModelByKey(runningState.modelKey);
    }
    private async additionalInfo(params: {stream: ElementChunkStream}) {
        const currentPrompt = await this.getCurrentPrompt();
        const currentModel = await this.getCurrentModel();
        const metaPromptPath = currentPrompt && this.pathForPromptMetaFile(currentPrompt);

        const info = [
            metaPromptPath && this.fileExist(metaPromptPath)
            && <file-link to={metaPromptPath} line={1}>提示词模板</file-link>,
            currentModel && <file-link to={this.pathForModelFile(currentModel)} line={1}>模型参数</file-link>
        ]

        return info
    }
    private async additionalButtons(params: {stream: ElementChunkStream}) {
        const currentPrompt = await this.getCurrentPrompt();
        const model = await this.getCurrentModel();
        const buttons = [
            currentPrompt && model && commandButton({
                content: '切换提示词',
                commandName: ActionKey.displayPromptList,
                data: {previousState: {type: Step.modelSelected, modelKey: model.modelKey}},
            }),
            model && commandButton({
                commandName: ActionKey.displayModelList,
                data: {},
                content: '切换模型',
            })
        ]

        return buttons;
    }
}
