import {ElementChunkStream, FunctionParameterDefinition, TaskProgressChunk} from '@comate/plugin-host';
import {BasePromptProvider} from './basePrompt/index.js';
import {skills} from '../config/skill.js';
import {APIService} from './basePrompt/Service/index.js';
import {Information} from '@comate/plugin-shared-internals';
import fs from 'fs';
import Path from 'path';
import {createFileIfNotExists} from '../utils/fileTools.js';
import {formatTime} from '../utils/time.js';
import {MacroPrompt} from '../entities/MacroPrompt/index.js';

const fsp = fs.promises;

type Example = {
    input: string;
    output: string;
};

type ButtonMapping = {
    'comate.ievalue.prompt.createPrompt': {system: string, prompt: string, title: string};
};

function commandButton<T extends keyof ButtonMapping>(
    {commandName, data, content}: {commandName: T, data?: ButtonMapping[T], content: string}
) {
    return <command-button commandName={commandName} data={data}>{content}</command-button>;
}

export class GeneratePromptByExampleProvider extends BasePromptProvider {
    static skillName: string = skills.generatePromptByExample.name;
    static description: string = skills.generatePromptByExample.description;
    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
        required: [],
    };
    private apiService = APIService.of({log: this.debugLog});
    private async getFileExample(file: Information, log: (message: unknown) => void): Promise<Example[]> {
        const absolutePath = Path.resolve(this.projectRoot, file.path || '');
        const ext = Path.extname(absolutePath);
        const fileContent = await fsp.readFile(absolutePath);

        switch (ext) {
            // case '.xlsx': {
            //     const sheet = xlsx.parse(fileContent);
            //     const rows = sheet[0]
            //         .data
            //         // NOTE: 因为第一行是表头
            //         .slice(1);
            //     const examples = rows.map(row => ({
            //         input: row[0] || '',
            //         output: row[1] || '',
            //     }));
            //     return examples;
            // }
            case '.csv': {
                const rows = fileContent
                    .toString()
                    .split('\n')
                    // NOTE: 因为第一行是表头
                    .slice(1);
                const examples = rows.map(row => {
                    const [input, output] = row.split(',');
                    return {
                        input: input || '',
                        output: output || '',
                    };
                });
                return examples;
            }
            default:
                return [];
        }
    }
    createExampleFile = async () => {
        const data = [
            ['问题', '答案'], // 第一行：标题
            ['1 + 1 等于多少？', '2'], // 示例数据
            ['2 + 2 等于多少？', '4'],
            ['3 + 5 等于多少？', '8'],
        ];
        // 将数据转换为工作表
        // const xlsxPath = Path.resolve(this.ievalueRoot, '示例.xlsx');
        const csvPath = Path.resolve(this.ievalueRoot, '示例.csv');

        // const xlsxBuffer = xlsx.build([{name: 'Sheet1', data: data, options: {}}]);
        const csvData = data.map(row => row.join(',')).join('\n');

        // await createFileIfNotExists(xlsxPath, xlsxBuffer);
        await createFileIfNotExists(csvPath, csvData);

        return {
            // xlsxPath,
            csvPath,
        };
    };
    async *showExampleFile(stream: ElementChunkStream) {
        const {csvPath} = await this.createExampleFile();
        yield stream.flush(<p>输入 # 选择文件，支持 .csv 格式</p>);
        yield stream.flush(<p>文件第一行为表头，示例文件请参考</p>);
        yield stream.flush(<file-link to={csvPath} line={1}>{csvPath}</file-link>);
        // yield stream.flush(<file-link to={xlsxPath} line={1}>{xlsxPath}</file-link>);
        // yield stream.flush(<p>xlsx格式可能无法直接用 vscode 打开，请使用excel等软件查看</p>);

    }
    protected async * execute(arg: void): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new ElementChunkStream();
        const selectedKnowledgeList: Information[] = this.retriever.selectedKnowledgeList();
        const files: Information[] = selectedKnowledgeList.filter(item => item.type === 'FILE' || item.type === 'CURRENT_FILE');
        if (files.length === 0) {
            yield* this.showExampleFile(stream);
            return;
        }
        try {
            const examples = await this.getFileExample(files[0], this.debugLog);
            if (examples.length === 0) {
                yield stream.flush(<p>文件中没有示例数据</p>);
                yield* this.showExampleFile(stream);
                return;
            }
            const prompt = await this.apiService.generatePromptByExample({
                examples,
                config: {
                    username: await this.getUserName() || '',
                    apiKey: await this.getAPIKey() || '',
                },
            });
            yield stream.flush(<span>共选中了{files.length}个文件{examples.length}个示例</span>);
            const {result, systemResult} = prompt.data;
            const arr: {value: string, title: string}[] = [
                {title: 'system', value: systemResult},
                {title: 'prompt内容', value: result},
            ];
            // yield stream.flush(<code-block closed={false} language="json">{JSON.stringify(prompt, null, 2)}</code-block>);
            for (const {value, title} of arr) {
                if (value) {
                    yield stream.flush(<span>{title}</span>);
                    yield stream.flush(<code-block closed={false} language="">{value}</code-block>);
                }
            }
            yield stream.flush(commandButton({
                commandName: 'comate.ievalue.prompt.createPrompt',
                data: {
                    system: systemResult,
                    prompt: result,
                    title: `新建提示词(${formatTime(new Date())})`,
                },
                content: '保存提示词',
            }));
        }
        catch (err) {
            yield stream.flush(<code-block closed={false} language="">{JSON.stringify(err, null, 2)}</code-block>);
        }
    }
    async *handleCommand(commandName: unknown, data: unknown): AsyncIterableIterator<TaskProgressChunk> {
        // interface 我卡不住，所以就卡这里吧
        const object = {commandName, data} as {
            [k in keyof ButtonMapping]: {commandName: k, data: ButtonMapping[k]};
        }[keyof ButtonMapping];
        this.debugLog({object});

        switch (object.commandName) {
            case 'comate.ievalue.prompt.createPrompt': {
                const {system, prompt, title} = object.data;
                const macroPrompt = MacroPrompt.ofSimplePanda({
                    text: prompt,
                    system,
                    promptName: title,
                })
                yield* this.savePromptWithStream(macroPrompt)
            }
        }
    }
}
