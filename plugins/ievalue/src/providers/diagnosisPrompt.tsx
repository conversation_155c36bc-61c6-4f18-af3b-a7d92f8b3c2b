import {ElementChunkStream, FunctionParameterDefinition, TaskProgressChunk} from '@comate/plugin-host';
import {BasePromptProvider} from './basePrompt/index.js';
import {skills} from '../config/skill.js';
import {APIService} from './basePrompt/Service/index.js';
import {resultTypeNameMapping} from '../api/diagnosisPrompt.js';

function at<T>(arr: T[], index: number): T | undefined {
    return arr[index];
}
export class DiagnosisPromptProvider extends BasePromptProvider {
    static skillName: string = skills.diagnosisPrompt.name;
    static description: string = skills.diagnosisPrompt.description;
    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
        required: [],
    };
    private apiService = APIService.of({log: this.debugLog});
    protected async *execute(arg: void): AsyncIterableIterator<TaskProgressChunk> {
        const query = this.editorState.queryOrSelection;
        const username = await this.getUserName();
        const stream = new ElementChunkStream();
        if (!username) {
            yield stream.flush(<span>请先登录</span>);
            return;
        }
        if (!query) {
            yield stream.flush(<span>请先输入诊断内容</span>);
            return;
        }
        const result = await this.apiService.diagnosisPrompt({text: query, username});
        // yield stream.flush(<span>{JSON.stringify(result, null, 2)}</span>);
        const msgList = result.data.result ?? [];
        for (const msg of msgList) {
            const {reason, resultSrc, resultTgt, resultFlag, resultType} = msg;
            const resultTypeName = resultTypeNameMapping[resultType];
            const resultFlagName = resultFlag === 0 ? '✅' : '❌';
            yield stream.flush(<h1>{resultTypeName} {resultFlagName}</h1>);
            yield stream.flush(<span>{reason}</span>);

            const arr: {title: string, value: string}[] = [
                {title: '原始语句', value: resultSrc},
                {title: '改进建议', value: resultTgt},
            ]
                .filter(item => item.value);
            for (const {title, value} of arr) {
                yield stream.flush(<h2>{title}</h2>);
                yield stream.flush(<code-block closed={false} language="">{value}</code-block>);
            }
        }
    }
}
