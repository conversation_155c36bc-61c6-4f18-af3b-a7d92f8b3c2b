import {ElementChunkStream, FunctionParameterDefinition, TaskProgressChunk} from '@comate/plugin-host';
import {skills} from '../config/skill.js';
import {BasePromptProvider} from './basePrompt/index.js';
import {apiGetPromptDetail} from '../api/getPromptDetail.js';
import {apiSmartSearchPromptList} from '../api/searchPromptList.js';
import {PromptSummaryData} from '../api/interface.js';
import {MacroPrompt, promptFileName} from '../entities/MacroPrompt/index.js';
import {ResultCode} from './basePrompt/Service/ResultCode.js';
import {PromptActions} from '../entities/MacroPrompt/PromptAction.js';

type ButtonMapping = {
    'comate.ievalue.prompt.setPrompt': PromptSummaryData;
};

function commandButton<T extends keyof ButtonMapping>(
    {commandName, data, content}: {commandName: T, data?: ButtonMapping[T], content: string}
) {
    return <command-button commandName={commandName} data={data}>{content}</command-button>;
}

function parseUserQuery(query: string): {promptNameKeyword: string, versionNameKeyword: string} {
    const parts = query.trim().split(/\s+/).filter(Boolean);
    return {
        promptNameKeyword: parts[0] || '',
        versionNameKeyword: parts[1] || '',
    };
}

/**
 * 获取提示词
 */
export class GetPromptProvider extends BasePromptProvider {
    static skillName: string = skills.getPrompt.name;
    static description: string = skills.getPrompt.description;
    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
        required: [],
    };
    protected async *execute(arg: void): AsyncIterableIterator<TaskProgressChunk> {
        const userQuery = this.editorState.queryOrSelection;
        const {promptNameKeyword, versionNameKeyword} = parseUserQuery(userQuery);

        const stream = new ElementChunkStream();
        const username = await this.getUserName();
        if (!username) {
            return stream.flush(<p>请先登录</p>);
        }

        const data = await apiSmartSearchPromptList({username, promptNameKeyword, versionNameKeyword});
        if (data.code !== ResultCode.ok) {
            yield stream.flush(<h1>获取提示词失败</h1>);
            yield stream.flush(<p>错误代码：{data.code}</p>);
            yield stream.flush(<p>错误原因：{data.msg}</p>);
            yield stream.flush(<p>{data.desc}</p>);
        }
        this.debugLog({data});

        const promptDetails = await Promise.all(data.data.map(async prompt => {
            const detail = await apiGetPromptDetail({
                promptCode: prompt.promptCode,
                versionName: prompt.versionName,
            });
            return detail.data;
        }));
        this.debugLog({promptDetails});

        if (promptDetails.length === 0) {
            yield stream.flush(<p>未找到提示词</p>);
            return;
        }

        yield stream.flush(<h1>提示词如下</h1>);
        yield stream.flush(
            <ul>
                {data.data.map(item => {
                    return (
                        <li>
                            <flex betweenCentered>
                                <strong>{item.promptName}</strong>
                                {item.versionName}-{item.spaceName}

                                {commandButton({
                                    commandName: 'comate.ievalue.prompt.setPrompt',
                                    data: item,
                                    content: `点击获取`,
                                })}
                            </flex>
                        </li>
                    );
                })}
            </ul>
        );
    }

    async *handleCommand<T extends keyof ButtonMapping>(
        commandName: T,
        data: ButtonMapping[T]
    ): AsyncIterableIterator<TaskProgressChunk> {
        this.log({commandName, data});

        switch (commandName) {
            case 'comate.ievalue.prompt.setPrompt':
                yield* this.downloadDetail(data);
                break;
        }
    }

    async *downloadDetail(promptSummary: PromptSummaryData) {
        try {
            const {promptCode, versionName} = promptSummary;
            const detail = await apiGetPromptDetail({promptCode, versionName});
            const prompt = MacroPrompt.of({
                data: detail.data,
                promptActions: PromptActions.ofEmpty()
            })
            const actionType = await this.savePrompt(prompt);
            if (actionType === 'error') {
                return;
            }

            const stream = new ElementChunkStream();
            const iEValueFile = this.pathForPromptSystemFile(prompt);
            yield stream.flush(
                <p>
                    {actionType === 'overwrite' ? '已更新' : '已获取'}{' '}
                    <file-link to={iEValueFile} line={1}>{promptFileName(promptSummary)}</file-link>
                    ，并将 Prompt 模版、自定义变量、调试参数文件下载到本地
                </p>
            );

            const item = detail.data;
            const mateFile = this.pathForPromptMetaFile(prompt);
            yield stream.flush(
                <p>
                    Prompt 模版地址：<file-link to={mateFile} line={1}>{item.promptName}模版</file-link>
                </p>
            );

            yield stream.flush(
                <p>
                    调试并保存后使用 <code>@Prompt /执行提示词</code> 即可获取执行结果
                </p>
            );
        }
        catch (err) {
            const stream = new ElementChunkStream();
            yield stream.flush(<h1>获取 {promptFileName(promptSummary)} 失败</h1>);
            if (err instanceof Error) {
                yield stream.flush(<p>{err.message}</p>);
            }
            this.debugLog(err);
            this.log(err);
        }
    }
}
