/**
 * @file 特殊模型的 provider
 */
import {FunctionParameterDefinition, TaskProgressChunk} from '@comate/plugin-shared-internals';
import {BasePromptProvider} from './basePrompt/index.js';
import {skills} from '../config/skill.js';
import {MacroPrompt} from '../entities/MacroPrompt/index.js';
import {ElementChunkStream, SkillProviderRegistryDescription} from '@comate/plugin-host';
import {APIService, SessionID} from './basePrompt/Service/index.js';
import {Model, ModelName} from '../entities/Model/index.js';

/**
 * 通用模型构造器
 * @param prompt 特殊模型的模版 prompt
 * @returns
 */
function buildProvider(prompt: MacroPrompt, model: Model): SkillProviderRegistryDescription {
    let sessionID: undefined | SessionID = undefined;

    class RunGPTProvider extends BasePromptProvider {
        static skillName: string = skills.runGPT.name;
        static description: string = skills.runGPT.description;
        static parameters: FunctionParameterDefinition = {
            type: 'object',
            properties: {},
            required: [],
        };
        private apiService = APIService.of({log: this.debugLog});
        // private async *initPrompt() {
        //     const allPrompt = await this.loadAllPrompts();
        //     const key = defaultPrompt.promptKey;
        //     const hasCreated = allPrompt.some(p => p.promptKey === key);
        //     // 已经创建过，就不再重复创建，避免覆盖用户的自定义配置
        //     if (!hasCreated) {
        //         return this.savePrompt(defaultPrompt)
        //     }
        // }
        protected async *execute(arg: void): AsyncIterableIterator<TaskProgressChunk> {
            // yield* this.initPrompt();
            // const allPrompt = await this.loadAllPrompts();
            // const prompt = allPrompt.find(p => p.promptKey === defaultPrompt.promptKey);
            const stream = new ElementChunkStream();
            const apiKey = await this.getAPIKey();
            const username = await this.getUserName() || '';
            const input = this.editorState.queryOrSelection;
            if (!apiKey) {
                const apiKeyPath = this.getPaths().userSetting;
                yield stream.flush(<p>请先在下面路径中配置API Key</p>);
                yield stream.flush(<file-link to={apiKeyPath} line={1}>{apiKeyPath}</file-link>);
                return;
            }
            const result = await this.apiService.autoDialog({
                model,
                prompt,
                sessionID,
                localStates: {
                    query: input,
                    currentCode: this.editorState.activeFileContent,
                },
                config: {
                    username,
                    apiKey,
                },
            });
            if (!result.success) {
                yield stream.flush(<h1>执行失败</h1>);
                yield stream.flush(<p>{result.message}</p>);
                return;
            }
            sessionID = result.sessionID;
            yield stream.flush(
                <markdown>
                    {result.message}
                </markdown>
            );
            // yield stream.flush(<hr />);
            // const iEValueFile = this.pathForPromptIEValueFile(prompt);
            // const metaFile = this.pathForPromptMetaFile(prompt);
            // yield stream.flush(<p>
            //     修改提示词模板：
            //     <file-link to={metaFile} line={1}>meta-prompt.md</file-link>
            //     (<a href={USER_DOCS.metaPrompt}>模板文档</a>)
            // </p>);
            // yield stream.flush(<p>修改模型参数：<file-link to={iEValueFile} line={1}>system.jsonc</file-link></p>);
        }
    }
    return RunGPTProvider;
}

export const RunGPTProvider = buildProvider(
    MacroPrompt.ofSimplePanda({
        promptName: '命令gpt-4o-mini',
        text: '{{IDE_QUERY}}',
        system: '',
    }),
    Model.ofName(ModelName.GPT_4O_MINI),
);

export const RunEB4Provider = buildProvider(
    MacroPrompt.ofSimplePanda({
        promptName: '命令eb4',
        text: '{{IDE_QUERY}}',
        system: '',
    }),
    Model.ofName(ModelName.ERNIE_BOT_4_0),
);
