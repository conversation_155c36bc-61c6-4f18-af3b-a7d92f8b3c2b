import {FallbackProvider, StringChunkStream, TaskProgressChunk} from '@comate/plugin-host';
import {config} from '../config/config.js';
import {USER_DOCS} from '../config/doc.js';

export class HelpFallbackProvider extends FallbackProvider {
    static description = '介绍插件能力与开发方式';

    async *handleQuery(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new StringChunkStream();
        yield stream.flush(<h1>插件介绍</h1>);
        yield stream.flush(<p>
            {config.pluginName}插件支持自定义提示词，智能生成、执行、诊断提示词。
        </p>);
        yield stream.flush(<p>
            详情请查看<a href={USER_DOCS.userManual}>用户文档</a>
        </p>);
    }
}
