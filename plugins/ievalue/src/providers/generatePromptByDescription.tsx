import {ElementChunkStream, FunctionParameterDefinition, TaskProgressChunk} from '@comate/plugin-host';
import {BasePromptProvider} from './basePrompt/index.js';
import {skills} from '../config/skill.js';
import {APIService} from './basePrompt/Service/index.js';
import {MacroPrompt} from '../entities/MacroPrompt/index.js';
import {formatTime} from '../utils/time.js';

type ButtonMapping = {
    'comate.ievalue.prompt.createPrompt': {system: string, prompt: string, title: string};
};

function commandButton<T extends keyof ButtonMapping>(
    {commandName, data, content}: {commandName: T, data?: ButtonMapping[T], content: string}
) {
    return <command-button commandName={commandName} data={data}>{content}</command-button>;
}

export class GeneratePromptByDescriptionProvider extends BasePromptProvider {
    static skillName: string = skills.generatePromptByDescription.name;
    static description: string = skills.generatePromptByDescription.description;
    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
        required: [],
    };
    private apiService = APIService.of({log: this.debugLog});
    protected async *execute(arg: void): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new ElementChunkStream();
        const example = '你是一名资深前端，用ts实现下面的功能';
        const rawQuery = this.editorState.queryOrSelection;
        const query = rawQuery || example;
        const isExample = !rawQuery;

        const prompt = await this.apiService.generatePromptByDescription({
            query,
            config: {
                username: await this.getUserName() || '',
                apiKey: await this.getAPIKey() || '',
            },
        });
        try {
            yield stream.flush(<h1>原始描述</h1>);
            yield stream.flush(<span>{isExample ? '[示例]' : ''}{query}</span>);
            yield stream.flush(<h1>生成结果：</h1>);
            const {result, systemResult} = prompt.data;
            const arr: {value: string, title: string}[] = [
                {title: 'system', value: systemResult},
                {title: 'prompt内容', value: result},
            ];
            for (const {value, title} of arr) {
                if (value) {
                    yield stream.flush(<span>{title}</span>);
                    yield stream.flush(<code-block closed={false} language="">{value}</code-block>);
                }
            }
            yield stream.flush(commandButton({
                commandName: 'comate.ievalue.prompt.createPrompt',
                data: {
                    system: systemResult,
                    prompt: result,
                    title: query.slice(0, 10) + `(${formatTime(new Date())})`,
                },
                content: '保存提示词',
            }));
        }
        catch (err) {
            yield stream.flush(<code-block closed={false} language="">{JSON.stringify(err, null, 2)}</code-block>);
        }
    }
    async *handleCommand(commandName: unknown, data: unknown): AsyncIterableIterator<TaskProgressChunk> {
        // interface 我卡不住，所以就卡这里吧
        const object = {commandName, data} as {
            [k in keyof ButtonMapping]: {commandName: k, data: ButtonMapping[k]};
        }[keyof ButtonMapping];
        this.debugLog({object});

        switch (object.commandName) {
            case 'comate.ievalue.prompt.createPrompt': {
                const {system, prompt, title} = object.data;
                const macroPrompt = MacroPrompt.ofSimplePanda({
                    text: prompt,
                    system,
                    promptName: title,
                })
                yield* this.savePromptWithStream(macroPrompt)
            }
        }
    }
}
