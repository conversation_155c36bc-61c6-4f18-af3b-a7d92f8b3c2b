/**
 * @file 路径参数注入
 */
import Path from 'path';
import {readDir, toAbsolutePath} from '../../utils/fileTools.js';

/**
 * 字符串参数的注入，替换掉 {{}} 中的参数
 * @param template 原始模版
 * @param params 参数映射关系
 * @returns
 */
export function injectParamsToText(template: string, params: Record<string, string>) {
    let result = template;
    for (let [key, value] of Object.entries(params)) {
        result = result.replace(`{{${key}}}`, value);
    }
    return result;
}

/**
 * 处理特殊字段 index，这个是一个动态字段，作用是避免文件名重复
 */
export async function injectSpecialParamIndex(template: string) {
    const absolutePath = template;
    const keyword = '{{index}}';
    async function nextIndexFor(absolutePath: string, keyword: string) {
        if (absolutePath.includes(keyword)) {
            const [prefix, suffix] = absolutePath.split(keyword);
            const dir = Path.dirname(absolutePath);
            const files = await readDir(dir) || [];
            const indexList = files
                .map(v => toAbsolutePath(dir, v))
                .filter(v => v.startsWith(prefix) && v.endsWith(suffix))
                .map(v => {
                    const indexStr = v.replace(prefix, '').replace(suffix, '');
                    if (indexStr.match(/^\d+$/)) {
                        return Number(indexStr);
                    }
                    return 0;
                });
            return Math.max(0, ...indexList) + 1;
        }
        return undefined;
    }
    const index = await nextIndexFor(absolutePath, keyword);
    return absolutePath.replace(keyword, index?.toString() || '');
}
export async function pathForResultCodeExport(filePathTemplate: string, params: {
    fileName: string;
    fileExt: string;
    fileDir: string;
    promptName: string;
    spaceName: string;
    formatTime: string;
}) {
    // const filePathTemplate = await this.userSetting.getItem('exportFirstCode');
    if (!filePathTemplate) {
        return undefined;
    }

    // 如果结果是相对路径，转换为基于 cwd 的绝对路径
    const absolutePath = toAbsolutePath(params.fileDir, injectParamsToText(filePathTemplate, params));
    return injectSpecialParamIndex(absolutePath);
}
