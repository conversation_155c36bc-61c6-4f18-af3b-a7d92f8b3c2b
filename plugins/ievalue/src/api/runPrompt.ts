import {config, host} from '../config/config.js';
import {PromptDetailData} from './interface.js';

interface RunPromptParams {
    data: PromptDetailData;
    username: string;
    apiKey: string | undefined;
    /** 对话框输入 */
    input: string | undefined;
    /** 多轮对话会话ID，undefined表示开启新一轮对话 */
    sessionID: string | undefined;
}

export interface PromptResponse {
    data: {
        output: string;
        startTime: string;
        endTime: string;
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
        recordID: number;
        sessionID: string;
    };
    code: number;
    msg: string;
    desc: string;
}

export async function apiRunPrompt(params: RunPromptParams): Promise<PromptResponse> {
    const headers = new Headers({
        token: config.token,
        'User-Agent': 'iAPI/1.0.0 (https://iapi.baidu-int.com)',
        'Content-Type': 'application/json',
    });

    // https://iapi.baidu-int.com/apidoc/shared-df088616-528d-4261-8c9d-7480bd7f26a4/api-4462368
    const requestBody = JSON.stringify({
        promptCode: params.data.promptCode,
        text: params.data.text,
        variables: params.data.variables,
        modelType: params.data.modelType,
        model: params.data.model,
        modelParams: params.data.modelParams,
        username: params.username,
        apiKey: params.apiKey,
        input: params.input,
        sessionID: params.sessionID,
    });

    const requestOptions = {
        method: 'POST',
        headers: headers,
        body: requestBody,
        redirect: 'follow' as RequestRedirect,
    };

    try {
        const response = await fetch(`${host}/openApi/v1/plugin/prompt/run`, requestOptions);
        if (!response.ok) {
            const text = await response.text();
            throw new Error(`网络错误!状态码： ${response.status}。响应内容: ${text}`);
        }
        const result = await response.json();
        return result;
    }
    catch (error) {
        console.error('运行提示词时出错:', error);
        throw error;
    }
}
