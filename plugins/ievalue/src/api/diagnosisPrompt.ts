import {config, host} from '../config/config.js';
import {postRequest} from './api.js';

interface Params {
    username: string; // 添加 username 属性
    text: string;
}

export type ResultType =
    /** 语意 */
    | 'bad_sentence'
    /** 上下文冲突 */
    | 'conflict'
    /** 描述不清晰 */
    | 'unclear';

export const resultTypeNameMapping: {[key in ResultType]: string} = {
    bad_sentence: '语意',
    conflict: '上下文冲突',
    unclear: '描述不清晰',
};

type ResultFlag =
    /** 正确 */
    | 0
    /** 错误 */
    | 1;
interface ApiResponse {
    data: {
        ID: number;
        result: Array<{
            ID: number;
            qualityID: number;
            resultType: ResultType;
            resultFlag: ResultFlag;
            reason: string;
            resultSrc: string;
            resultTgt: string;
            isAccepted: number;
        }>;
    };
    code: number;
    msg: string;
}

export async function apiDiagnosisPrompt(params: Params): Promise<ApiResponse> {
    const result = await postRequest({
        url: `${host}/openApi/v1/plugin/prompt/quality/run`,
        token: config.token,
        data: {
            username: params.username,
            text: params.text,
            // "username": "yuanzhanguang",
            // "text": "<指令>：\n请基于以下大纲，扩展三级标题\"### \"下的内容，每个三级标题下的内容要有2-4个列表罗列出来（每个列表的格式是“- 总结词：详细描述”），同时内容需要丰富、专业、字数多，不要输出任何额外的说明和提示文字，并以markdown格式返回：\n<大纲>：\n# 校园安全\n## 心理健康辅导工作开展途径和方法探讨\n### 建立有效心理咨询机制\n### 关注学生心理问题并提供帮助\n### 组织举办主题活动增进同伴关系\n<扩展结果>："
        },
    });

    return result;
}
