import {config, host} from '../config/config.js';
import {PromptSummaryData} from './interface.js';

interface SearchProps {
    username: string;
    promptNameKeyword?: string;
    versionNameKeyword?: string;
}

interface SearchResponse {
    data: PromptSummaryData[];
    code: number;
    msg: string;
    desc: string;
}

export async function apiSearchPromptList(params: SearchProps): Promise<SearchResponse> {
    const {username, promptNameKeyword = '', versionNameKeyword = ''} = params;
    const myHeaders = new Headers();
    myHeaders.append('token', config.token);
    myHeaders.append('User-Agent', 'iAPI/1.0.0 (https://iapi.baidu-int.com)');

    const requestOptions = {
        method: 'GET',
        headers: myHeaders,
        redirect: 'follow' as RequestRedirect,
    };

    try {
        const response = await fetch(
            `${host}/openApi/v1/plugin/prompt/query?username=${username}&promptNameKeyword=${promptNameKeyword}&versionNameKeyword=${versionNameKeyword}`,
            requestOptions
        );
        const result = await response.json();
        return result;
    }
    catch (error) {
        console.error('搜索提示词列表时出错:', error);
        throw error;
    }
}

import {apiGetPromptList} from './getPromptList.js';

export async function apiSmartSearchPromptList(params: SearchProps): Promise<SearchResponse> {
    const {username, promptNameKeyword, versionNameKeyword} = params;

    // 如果关键词都为空，调用 getPromptList
    if (!promptNameKeyword && !versionNameKeyword) {
        return await apiGetPromptList({username});
    }

    // 否则调用 apiSearchPromptList
    return await apiSearchPromptList(params);
}
