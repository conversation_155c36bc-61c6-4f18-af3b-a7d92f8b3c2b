/**
 * Prompt 的概要信息，是 Detail 接口的子集，用于列表等场景
 */
export interface PromptSummaryData {
    promptCode: string;
    promptID: number;
    promptName: string;
    spaceCode: string;
    spaceName: string;
    submitStatus: number;
    versionID: number;
    versionName: string;
    versionCreateTime: string; // 或者使用 Date 类型，如果需要进一步处理日期
}

export interface DebugParam {
    id: string;
    name: string;
    type: string;
    value: string | number;
}

export interface Variable {
    key: string;
    value: string;
    type: string;
}

/**
 * 完整的提示词信息
 */
export interface PromptDetailData {
    promptCode: string;
    promptID: number;
    promptName: string;
    spaceCode: string;
    spaceName: string;
    submitStatus: number;
    versionID: number;
    versionName: string;
    versionCreateTime: string;
    text: string | undefined;
    inputType: string;
    variables: Variable[] | undefined;
    modelID: number;
    model: string;
    modelType: string;
    modelParams: {
        temperature?: number;
        topP?: number;
        system?: string;
    };
    debugParams: DebugParam[];
}

export const emptyPromptDetailData: PromptDetailData = {
    promptCode: '',
    promptID: 0,
    promptName: '',
    spaceCode: '',
    spaceName: '',
    submitStatus: 0,
    versionID: 0,
    versionName: '',
    versionCreateTime: '',
    inputType: '',
    modelID: 0,
    model: '',
    modelType: '',
    modelParams: {},
    debugParams: [],
    text: undefined,
    variables: undefined,
};
