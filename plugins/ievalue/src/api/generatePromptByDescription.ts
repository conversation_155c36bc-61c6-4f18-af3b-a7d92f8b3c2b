import {config, host} from '../config/config.js';
import {postRequest} from './api.js';

interface Params {
    username: string;
    taskDesc: string;
    examples: Array<{
        input: string;
        output: string;
    }>;
    // 1：一句话任务描述：3：输入输出示例
    templateCode: 1 | 3;
}
/** Request */
export interface Response {
    code: number;
    data: Data;
    desc: string;
    msg: string;
    [property: string]: any;
}

export interface Data {
    /** 创建时间 */
    createTime: string;
    /** 创建人 */
    creator: string;
    /** 完成时间 */
    finishTime: string;
    /** 生成记录ID，recordID */
    ID: number;
    /** 输入参数 */
    inputParams: string;
    /** 是否被采纳，1是0否 */
    isAccepted: number;
    /** 生成结果 */
    result: string;
    /** 生成状态，0：未开始；1：生成中；2：运行成功；3：运行失败 */
    statusCode: number;
    /** 生成的系统人设 */
    systemResult: string;
    /** 模板类型代码，1：一句话任务描述：2：全场景；3：输入输出示例 */
    templateCode: number;
    [property: string]: any;
}
export async function apiGeneratePromptByDescription(params: Params): Promise<Response> {
    const res = postRequest({
        url: `${host}/openApi/v1/plugin/prompt/generate/run`,
        token: config.token,
        data: params,
    });
    return res;
}
