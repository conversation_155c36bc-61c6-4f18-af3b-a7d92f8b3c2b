// 封装一个 POST 请求工具函数
export async function postRequest({url, token, data}: {url: string, token: string, data: any}): Promise<any> {
    // 创建请求头
    const headers = new Headers({
        token: token,
        'User-Agent': 'iAPI/1.0.0 (https://iapi.baidu-int.com)',
        'Content-Type': 'application/json',
    });

    // 将请求数据转换为 JSON 字符串
    const requestBody = JSON.stringify(data);

    // 设置请求选项
    const requestOptions: RequestInit = {
        method: 'POST',
        headers: headers,
        body: requestBody,
        redirect: 'follow' as RequestRedirect,
    };

    // 发送请求并返回响应
    const response = await fetch(url, requestOptions);
    return response.json();
}
