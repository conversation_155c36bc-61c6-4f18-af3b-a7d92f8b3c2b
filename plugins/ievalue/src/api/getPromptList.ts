import {config, host} from '../config/config.js';
import {PromptSummaryData} from './interface.js';

interface Props {
    username: string;
}
interface Response {
    data: PromptSummaryData[];
    code: number;
    msg: string;
    desc: string;
}
export async function apiGetPromptList(params: Props): Promise<Response> {
    const {username} = params;
    const myHeaders = new Headers();
    myHeaders.append('token', config.token);
    myHeaders.append('User-Agent', 'iAPI/1.0.0 (https://iapi.baidu-int.com)');

    const data = await fetch(
        `${host}/openApi/v1/plugin/prompt/recommend?username=${username}`,
        {
            method: 'GET',
            headers: myHeaders,
            redirect: 'follow',
        }
    );
    const result = await data.json();
    return result;
}
