import {config, host} from '../config/config.js';
import {PromptDetailData} from './interface.js';

interface PromptDetailResponse {
    data: PromptDetailData;
    code: number;
    msg: string;
    desc: string;
}

interface PromptDetailProps {
    promptCode: string;
    versionName: string;
}

export async function apiGetPromptDetail(params: PromptDetailProps): Promise<PromptDetailResponse> {
    const {promptCode, versionName} = params;
    const myHeaders = new Headers();
    myHeaders.append('token', config.token);
    myHeaders.append('User-Agent', 'iAPI/1.0.0 (https://iapi.baidu-int.com)');

    const requestOptions = {
        method: 'GET',
        headers: myHeaders,
        redirect: 'follow' as RequestRedirect,
    };

    try {
        const response = await fetch(
            `${host}/openApi/v1/plugin/prompt/detail?promptCode=${promptCode}&versionName=${versionName}`,
            requestOptions
        );
        const result = await response.json();
        return result;
    }
    catch (error) {
        console.error('获取提示词详情时出错:', error);
        throw error;
    }
}
