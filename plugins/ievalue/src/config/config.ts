export enum Env {
    online = 'online',
    dev = 'dev',
}

export const config = {
    pluginName: 'Prompt',
    token: 'G9js+A7jZfZXOhPv2kOMp1E0y71ED3pDGFI7AVRWoUo=',
    outputDir: '.comate/ievalue',

    /** 是否开启调试模式，开启后将打印出更多调试信息 */
    isDebug: true,
    /** 调试日志文件名 */
    debugLogFile: 'debug.log',
    /** 普通日志文件名 */
    generalLogFile: 'general.log',

    /** 当前环境 */
    env: Env.online, // 统一用线上环境
};

export const host = config.env === Env.dev
    ? 'http://ievalue-api-test.baidu-int.com:8030'
    : 'http://ievalue-api.baidu-int.com';
