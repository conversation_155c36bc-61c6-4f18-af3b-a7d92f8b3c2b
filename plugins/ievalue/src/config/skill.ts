export const skills = {
    runGPT: {
        name: 'run-gpt',
        description: '调用 chatgpt',
    },
    runEB: {
        name: 'run-eb',
        description: '调用文心大模型',
    },
    runPrompt: {
        name: 'run-prompt',
        displayName: '执行提示词',
        description: '运行提示词生成程序',
    },
    generatePromptByDescription: {
        name: 'generate-prompt-by-description',
        description: '根据描述生成提示词',
    },
    getPrompt: {
        name: 'get-prompt',
        description: '下载提示词内容到本地',
    },
    // 根据示例生成提示词
    generatePromptByExample: {
        name: 'generate-prompt-by-example',
        description: '根据示例生成提示词',
    },
    diagnosisPrompt: {
        name: 'diagnosis-prompt',
        description: '对提示词进行诊断',
    },
} satisfies {[key: string]: {name: string, description: string, displayName?: string}};
