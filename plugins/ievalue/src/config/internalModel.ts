/**
 * @file 内置模型
 */
import {Model, ModelName} from '../entities/Model/index.js';

const internalEBModels: Model[] = [
    ModelName.ERNIE_BOT,
    ModelName.ERNIE_BOT_4_0,
    ModelName.ERNIE_4_0_8K,
    ModelName.ERNIE_4_0_TURBO_8K,
    ModelName.ERNIE_3_5_8K,
    ModelName.ERNIE_SPEED_8K,
    ModelName.ERNIE_SPEED_128K,
    ModelName.ERNIE_LITE_8K,
].map(v => Model.of({
    data: {
        model: v,
        modelID: 0,
        modelType: 'PANDA',
        modelParams: {
            temperature: 0.8,
            topP: 0.8,
        },
    },
}));

const internalGPTModels: Model[] = [
    ModelName.GPT_3_5_TURBO,
    ModelName.GPT_3_5_TURBO_16K,
    ModelName.GPT_4,
    ModelName.GPT_4_1106_PREVIEW,
    ModelName.GPT_4_TURBO_PREVIEW,
    ModelName.GPT_4_TURBO,
    ModelName.GPT_4_VISION_PREVIEW,
    ModelName.GPT_4O,
    ModelName.GPT_4O_2024_08_06,
    ModelName.GPT_4O_MINI,
].map(v => Model.of({
    data: {
        model: v,
        modelID: 0,
        modelType: 'PANDA',
        modelParams: {
            temperature: 1,
            topP: 1,
        },
    },
}));


export const internalModel: Model[] = [
    ...internalEBModels,
    ...internalGPTModels,
];
