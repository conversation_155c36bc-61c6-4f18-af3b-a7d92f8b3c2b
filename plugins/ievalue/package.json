{"private": true, "name": "@comate-plugin/ievalue", "version": "0.8.0", "type": "module", "scripts": {"clean": "rm -rf dist", "dev": "node_modules/.bin/cpd dev", "build": "node_modules/.bin/tsc && cpd build"}, "dependencies": {"@comate/plugin-shared-internals": "workspace:^"}, "devDependencies": {"@comate/plugin-cli": "^0.9.2", "@comate/plugin-host": "^0.9.2", "typescript": "^5.3.2"}, "comate": {"name": "i<PERSON>ue", "version": "1.0.0", "icon": "./assets/iEValue.svg", "entry": "./dist/index.js", "displayName": "Prompt", "description": "支持自定义提示词，智能生成、执行、诊断提示词", "keyword": [], "capabilities": [{"type": "Fallback", "name": "help", "displayName": "插件介绍", "description": "确认插件成功运行并介绍开发方式"}, {"type": "Skill", "name": "run-gpt", "displayName": "调用GPT-4o-mini", "description": "使用 GPT-4o-mini 生成内容", "placeholder": "请输入你的问题"}, {"type": "Skill", "name": "run-eb", "displayName": "调用EB-4-8k", "description": "使用 EB-4-8k 生成内容", "placeholder": "请输入你的问题"}, {"type": "Skill", "name": "run-prompt", "displayName": "执行提示词", "description": "基于提示词生成内容", "placeholder": "回车查看提示词列表"}, {"type": "Skill", "name": "generate-prompt-by-description", "displayName": "生成提示词（根据描述）", "description": "根据描述生成提示词", "placeholder": "输入或选中描述，回车查看示例"}, {"type": "Skill", "name": "get-prompt", "displayName": "获取提示词", "description": "下载提示词内容到本地", "placeholder": "提示词名称 提示词版本(可选)"}, {"type": "Skill", "name": "generate-prompt-by-example", "displayName": "生成提示词（根据示例）", "description": "根据示例生成提示词", "placeholder": "井号选择文件 #示例.csv ；直接回车查看教程"}, {"type": "Skill", "name": "diagnosis-prompt", "displayName": "提示词诊断", "description": "对提示词进行优化建议", "placeholder": "输入或选中要优化的提示词"}], "configSchema": {"sections": []}}}