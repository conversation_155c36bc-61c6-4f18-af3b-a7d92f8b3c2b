let email = '';
export const setEmail = (name: string) => {
    email = name;
};

let figmaName = '';
export const setFigmaName = (name: string) => {
    figmaName = name;
};

import {exec} from 'child_process';
export const getGitUserName = (): Promise<string> => {
    return new Promise((resolve, reject) => {
        if (email) {
            resolve(email);
        }
        else if (figmaName) {
            resolve(figmaName);
        }
        else {
            exec('git config --global user.email', (error: any, stdout: string, stderr: any) => {
                if (error) {
                    // reject(error);
                    resolve('f2c_test');
                }
                else {
                    const userName = stdout.trim();
                    resolve(userName);
                }
            });
        }
    });
};

export const getGitAddress = (): Promise<string> => {
    return new Promise((resolve, reject) => {
        exec('git remote -v', (error: any, stdout: string, stderr: any) => {
            if (error) {
                resolve('');
            }
            else {
                const reg = /^origin(\s+)(.*)\(/;
                const result = stdout.match(reg);
                if (result && result[2]) {
                    resolve(result[2].trim());
                }
                resolve(stdout);
            }
        });
    });
};
