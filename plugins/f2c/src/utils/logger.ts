export type LoggerType = 'debug' | 'info' | 'warn' | 'error';

const lv = () => process.env.logLevel || 'info';
const logger = {
    debug: (...args: any[]) => ['debug'].includes(lv()) && console.log(...args),
    info: (...args: any[]) => ['debug', 'info'].includes(lv()) && console.log(...args),
    warn: (...args: any[]) => ['debug', 'info', 'warn'].includes(lv()) && console.warn(...args),
    error: (...args: any[]) => ['debug', 'info', 'warn', 'error'].includes(lv()) && console.error(...args),
};

const FgBlack = '\x1b[30m';
const FgRed = '\x1b[31m';
const FgGreen = '\x1b[32m';
const FgYellow = '\x1b[33m';
const FgBlue = '\x1b[34m';
const FgMagenta = '\x1b[35m';
const FgCyan = '\x1b[36m';
const FgWhite = '\x1b[37m';

export const logBlue = (...msg: any[]) => {
    colorLog(FgBlue, ...msg);
};
export const logCyan = (...msg: any[]) => {
    colorLog(FgCyan, ...msg);
};

export const logMagenta = (...msg: any[]) => {
    colorLog(FgMagenta, ...msg);
};

export const logGreen = (...msg: any[]) => {
    colorLog(FgGreen, ...msg);
};

export const logYellow = (...msg: any[]) => {
    colorLog(FgYellow, ...msg);
};
export const logRed = (...msg: any[]) => {
    colorLog(FgRed, ...msg);
};

const colorLog = (color: string, ...msg: any[]) => {
    const arr = new Array(msg.length + 1).fill('%s');
    console.log(`${color}${arr.join(' ')}\x1b[0m`, `[F2C]`, ...msg);
};

export default logger;
