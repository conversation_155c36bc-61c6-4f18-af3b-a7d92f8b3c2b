import {File} from './prettier.js';
import fsPromises from 'fs/promises';
import path from 'path';
import fs from 'fs';
import crypto from 'crypto';
import {Logger} from '@comate/plugin-host';
export const getExtensionFromFilePath = (path: string) => {
    const parts = path.split('.');
    return parts[parts.length - 1];
};

export async function writeFileWithMkdir(filePath: string, data: any) {
    const dirname = path.dirname(filePath);

    // 检查目录是否存在，如果不存在则创建目录
    if (!fs.existsSync(dirname)) {
        fs.mkdirSync(dirname, {recursive: true});
    }

    // 写入文件
    return fsPromises.writeFile(filePath, data);
}
export const writeFiles = async (files: File[], rootDir: string) => {
    return await Promise.all(
        files.map(async ({content, path}) => {
            const uri = rootDir + path;
            if (getExtensionFromFilePath(path) === 'png') {
                return writeFileWithMkdir(uri, Buffer.from(content, 'base64'));
            }
            return writeFileWithMkdir(uri, Buffer.from(content));
        })
    );
};

export function rmDir(dirPath: string) {
    try {
        console.log('目录是否存在', fs.existsSync(dirPath));
        fs.rmdirSync(dirPath, {
            recursive: true,
        });
        // let entries = fs.readdirSync(dirPath, {withFileTypes: true});
        // if (entries.length === 0) {
        //     // 目录为空，直接删除
        //     fs.rmdirSync(dirPath);
        //     return;
        // }
        // for (let entry of entries) {
        //     let fullPath = path.join(dirPath, entry.name);
        //     if (entry.isDirectory()) {
        //         rmDir(fullPath);
        //     }
        //     else {
        //         fs.unlinkSync(fullPath);
        //     }
        // }
    }
    catch {
    }
    // fs.rmdirSync(dirPath);
}

export async function calculateFileHash(filePath: string, algorithm: string = 'md5', logger: Logger): Promise<string> {
    if (!fs.existsSync(filePath)) {
        logger.info(`enter ${filePath}`);
        return '';
    }
    return new Promise((resolve, reject) => {
        logger.info(`calculateFileHash file exisst${filePath}`);
        const hash = crypto.createHash(algorithm);
        const stream = fs.createReadStream(filePath);
        logger.info(`hash ${hash}`);

        stream.on('data', chunk => {
            logger.info(`calculateFileHash data ${chunk}`);
            hash.update(chunk);
        });
        stream.on('end', () => {
            const digest = hash.digest('hex');
            logger.info(`calculateFileHash end ${digest}`);
            resolve(digest);
        });
        stream.on('error', err => {
            logger.error(`calculateFileHash error ${err}`);
            reject(err);
        });
    });
}
