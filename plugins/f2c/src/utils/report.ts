import axios from 'axios';
import {getGitAddress, getGitUserName} from './gitHelper.js';
import {isSupportedExtension} from './prettier.js';
import {ComateWorkFlow} from '../providers/f2c.js';
const url = 'https://statistcs-hdpt.yy.com/reportGeneralStatistics';
interface File {
    content: string;
    path: string;
}

export enum ReportType {
    codeCount = 'f2c_gen_code_count', // 代码生成次数
    lineCount = 'f2c_code_line_count', // 代码生成行数
    aiCount = 'f2c_ai_count',
    copyCount = 'f2c_copy_count', // 使用复制功能次数
    downloadCutCount = 'f2c_download_cut_count', // 使用切图下载功能次数
    loginType = 'f2c_login_type', // 登录方式
    uploadCount = 'f2c_upload_count', // 上传/更新次数
    acceptCount = 'f2c_gen_accept_count', // 保存本地次数
    acceptLineCount = 'f2c_gen_accept_line_count', // 保存本地行数
}

export async function f2cDataReport(type: ReportType, uiframework: string, count: number, expandObj?: any) {
    const userName = await getGitUserName();
    const gitAddr = await getGitAddress();
    const reqData: any = {
        ...expandObj,
        appid: 1,
        dataType: type,
        dim1: userName,
        dim2: gitAddr,
        dim3: uiframework,
        value1: count,
    };
    try {
        await axios.post(url, reqData).then((resp: any) => {
            if (resp?.data && resp.data.result !== 200) {
                console.log('上报失败', resp.data.reason, resp.data.result);
            }
        });
    }
    catch (e: any) {
        console.error(e);
    }
}
export const getLine = (file: File) => {
    let lines = 0;
    const fileExtension = (file.path.match(/\.[^.]+$/)?.[0] || '').replace('.', '');
    if (isSupportedExtension(fileExtension)) {
        const reg = /\n/gi;
        const regResult = file.content.match(reg);
        if (regResult) {
            lines += regResult.length;
        }
    }
    return lines;
};
export const reportLines = (files: File[], workingFlow?: ComateWorkFlow, uiframework?: string) => {
    let lines = 0;
    for (const file of files) {
        const fileExtension = (file.path.match(/\.[^.]+$/)?.[0] || '').replace('.', '');
        if (isSupportedExtension(fileExtension)) {
            const reg = /\n/gi;
            const regResult = file.content.match(reg);
            if (regResult) {
                lines += regResult.length;
            }
        }
    }
    f2cDataReport(ReportType.lineCount, uiframework || 'react', lines, {
        dim4: workingFlow,
    });
};
