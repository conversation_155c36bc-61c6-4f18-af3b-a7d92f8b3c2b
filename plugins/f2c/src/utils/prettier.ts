import * as prettier from 'prettier/standalone';
import * as cssParser from 'prettier/parser-postcss';
import * as babelParser from 'prettier/parser-babel';
import * as htmlParser from 'prettier/parser-html';
import pluginVue from 'prettier-plugin-vue';
export interface File {
    content: string;
    path: string;
}

export const getFileExtension = (file: File) => {
    return getExtensionFromFilePath(file.path);
};

export const getExtensionFromFilePath = (path: string) => {
    const parts = path.split('.');
    return parts[parts.length - 1];
};

export const isSupportedExtension = (extension: string) => {
    return ['tsx', 'ts', 'jsx', 'js', 'css', 'scss', 'less', 'html', 'vue', 'ets'].includes(extension.toLowerCase());
};

export const isFileSupported = (path: string) => {
    return isSupportedExtension(getExtensionFromFilePath(path));
};

export const formatFiles = (files: File[]): File[] => {
    const formated = [...files];

    for (const file of formated) {
        const extension = getFileExtension(file);

        switch (extension) {
            case 'tsx':
            case 'ts':
            case 'jsx':
            case 'js':
                // @ts-ignore
                file.content = prettier.format(file.content, {
                    plugins: [babelParser],
                    parser: 'babel',
                });
                break;
            case 'vue':
                // @ts-ignore
                file.content = prettier.format(file.content, {
                    plugins: [babelParser, pluginVue],
                    parser: 'vue',
                });
                break;
            case 'css':
            case 'scss':
            case 'less':
                // @ts-ignore
                file.content = prettier.format(file.content, {
                    plugins: [cssParser],
                    parser: 'css',
                });
                break;
            case 'html':
                // @ts-ignore
                file.content = prettier.format(file.content, {
                    plugins: [htmlParser],
                    parser: 'html',
                    semi: false,
                    bracketSameLine: true,
                });
                break;
        }
    }

    return formated;
};
