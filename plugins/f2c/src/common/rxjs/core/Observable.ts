import {subscribeSourceFunction, OperatorFunction} from './Opertor.js';
import {pipeFromArray} from './pipe.js';
import {SafeSubscriber, Subscriber} from './Subscriber.js';
import {Subscription} from './Subscription.js';
import {Observer, Subscribable, UnsubscribeLike} from './types.js';
import {isFunction, Symbol_observable} from './util.js';

let ObservableId = 0;
/**
 * 可观察量。
 * 符合观察者接口规范的对象，通常用来传给 observable.subscribe(observer) 方法，
 * 同时 Observable 会调用观察者的 next(value) 提供通知。
 */
export class Observable<T> implements Subscribable<T> {
    id = 0;
    source?: Observable<any>;
    subscribeSource?: subscribeSourceFunction<any, T>;

    static EMPTY = new Observable<never>(subscriber => subscriber.complete());
    constructor(subscribe?: (this: Observable<T>, subscriber: Subscriber<T>) => UnsubscribeLike) {
        this.id = ++ObservableId;
        if (subscribe) {
            this._subscribe = subscribe;
        }
    }

    /** 订阅数据的通知，返回一个可取消的订阅 */
    subscribe(observer?: Partial<Observer<T>>): Subscription;
    /** 订阅数据的通知，返回一个可取消的订阅 */
    subscribe(next: (value: T) => void): Subscription;
    /** 订阅数据的通知，返回一个可取消的订阅 */
    subscribe(next?: (value: T) => void, error?: (error: any) => void, complete?: () => void): Subscription;
    /** 订阅数据的通知，返回一个可取消的订阅 */
    subscribe(
        observerOrNext?: Partial<Observer<T>> | ((value: T) => void),
        error?: (error: any) => void,
        complete?: () => void
    ): Subscription {
        const subscriber = isSubscriber(observerOrNext)
            ? observerOrNext
            : new SafeSubscriber(observerOrNext, error, complete);
        const {subscribeSource, source} = this;
        let unsubscribeLike;
        if (subscribeSource) {
            // 处理运算符链中处理订阅，将运算符提升为
            unsubscribeLike = subscribeSource(source!, subscriber); // eslint-disable-line
        }
        else if (source) {
            // Subject之类的订阅
            unsubscribeLike = this._subscribe(subscriber);
        }
        else {
            // 初始化传入的_subscribe，需要包装调用
            unsubscribeLike = this._trySubscribe(subscriber);
        }
        subscriber.add(unsubscribeLike);
        return subscriber;
    }
    protected _subscribe(subscriber: Subscriber<any>): UnsubscribeLike {
        return this.source?.subscribe(subscriber);
    }

    /** 包装调用传入的_subscribe */
    protected _trySubscribe(sink: Subscriber<T>): UnsubscribeLike {
        try {
            return this._subscribe(sink);
        }
        catch (err) {
            sink.error(err);
        }
    }

    [Symbol_observable]() {
        return this;
    }

    /** 将运算符窜成一条运算符链，返回经过所有运算符顺序处理的可被观察的结果 */
    pipe<A>(op1: OperatorFunction<T, A>): Observable<A>;
    /** 将运算符窜成一条运算符链，返回经过所有运算符顺序处理的可被观察的结果 */
    pipe<A, B>(op1: OperatorFunction<T, A>, op2: OperatorFunction<A, B>): Observable<B>;
    /** 将运算符窜成一条运算符链，返回经过所有运算符顺序处理的可被观察的结果 */
    pipe<A, B, C>(op1: OperatorFunction<T, A>, op2: OperatorFunction<A, B>, op3: OperatorFunction<B, C>): Observable<C>;
    /** 将运算符窜成一条运算符链，返回经过所有运算符顺序处理的可被观察的结果 */
    pipe<A, B, C, D>(
        op1: OperatorFunction<T, A>,
        op2: OperatorFunction<A, B>,
        op3: OperatorFunction<B, C>,
        op4: OperatorFunction<C, D>
    ): Observable<D>;
    /** 将运算符窜成一条运算符链，返回经过所有运算符顺序处理的可被观察的结果 */
    pipe<A, B, C, D, E>(
        op1: OperatorFunction<T, A>,
        op2: OperatorFunction<A, B>,
        op3: OperatorFunction<B, C>,
        op4: OperatorFunction<C, D>,
        op5: OperatorFunction<D, E>
    ): Observable<E>;
    /** 将运算符窜成一条运算符链，返回经过所有运算符顺序处理的可被观察的结果 */
    pipe<A, B, C, D, E, F>(
        op1: OperatorFunction<T, A>,
        op2: OperatorFunction<A, B>,
        op3: OperatorFunction<B, C>,
        op4: OperatorFunction<C, D>,
        op5: OperatorFunction<D, E>,
        op6: OperatorFunction<E, F>
    ): Observable<F>;
    /** 将运算符窜成一条运算符链，返回经过所有运算符顺序处理的可被观察的结果 */
    pipe<A, B, C, D, E, F, G>(
        op1: OperatorFunction<T, A>,
        op2: OperatorFunction<A, B>,
        op3: OperatorFunction<B, C>,
        op4: OperatorFunction<C, D>,
        op5: OperatorFunction<D, E>,
        op6: OperatorFunction<E, F>,
        op7: OperatorFunction<F, G>
    ): Observable<G>;
    /** 将运算符窜成一条运算符链，返回经过所有运算符顺序处理的可被观察的结果 */
    pipe<A, B, C, D, E, F, G, H>(
        op1: OperatorFunction<T, A>,
        op2: OperatorFunction<A, B>,
        op3: OperatorFunction<B, C>,
        op4: OperatorFunction<C, D>,
        op5: OperatorFunction<D, E>,
        op6: OperatorFunction<E, F>,
        op7: OperatorFunction<F, G>,
        op8: OperatorFunction<G, H>
    ): Observable<H>;
    /** 将运算符窜成一条运算符链，返回经过所有运算符顺序处理的可被观察的结果 */
    pipe<A, B, C, D, E, F, G, H, I>(
        op1: OperatorFunction<T, A>,
        op2: OperatorFunction<A, B>,
        op3: OperatorFunction<B, C>,
        op4: OperatorFunction<C, D>,
        op5: OperatorFunction<D, E>,
        op6: OperatorFunction<E, F>,
        op7: OperatorFunction<F, G>,
        op8: OperatorFunction<G, H>,
        op9: OperatorFunction<H, I>
    ): Observable<I>;
    /** 将运算符窜成一条运算符链，返回经过所有运算符顺序处理的可被观察的结果 */
    pipe<A, B, C, D, E, F, G, H, I>(
        op1: OperatorFunction<T, A>,
        op2: OperatorFunction<A, B>,
        op3: OperatorFunction<B, C>,
        op4: OperatorFunction<C, D>,
        op5: OperatorFunction<D, E>,
        op6: OperatorFunction<E, F>,
        op7: OperatorFunction<F, G>,
        op8: OperatorFunction<G, H>,
        op9: OperatorFunction<H, I>,
        ...operations: OperatorFunction<any, any>[]
    ): Observable<unknown>;
    /** 将运算符窜成一条运算符链，返回经过所有运算符顺序处理的可被观察的结果 */
    pipe(...operations: OperatorFunction<any, any>[]): Observable<any> {
        const fn: any = pipeFromArray(operations);
        return fn(this);
    }
}

function isObserver<T>(value: any): value is Observer<T> {
    return value && isFunction(value.next) && isFunction(value.error) && isFunction(value.complete);
}

function isSubscriber<T>(value: any): value is Subscriber<T> {
    return (value && value instanceof Subscriber) || (isObserver(value) && Subscription.isSubscription(value));
}

export type ObservableInputTuple<T> = {
    [K in keyof T]: Observable<T[K]>;
};
