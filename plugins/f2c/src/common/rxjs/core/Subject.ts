import {Observable} from './Observable.js';
import {Subscriber} from './Subscriber.js';
import {Subscription} from './Subscription.js';
import {Observer, SubscriptionLike} from './types.js';
import {arrRemove} from './util.js';

/**
 * 主体是个特殊的可观察量（Observable）。他允许数据被多路广播给多个观察者（Observer）。他像事件发射器一样（EventEmitters）
 * 主体是 可观察量（Observablee）同时也是一个（Observer）. You can subscribe to a
 * 你可以订阅另外一个主体, 和调用 next 传输数据，触发error 和 complete.
 */
export class Subject<T> extends Observable<T> implements SubscriptionLike {
    closed = false;
    observers: Observer<T>[] = [];
    isStopped = false;
    hasError = false;
    thrownError: any = null;

    next(value: T) {
        if (!this.isStopped) {
            const copy = this.observers.slice();
            for (const observer of copy) {
                observer.next(value);
            }
        }
    }

    error(err: any) {
        if (!this.isStopped) {
            this.hasError = this.isStopped = true;
            this.thrownError = err;
            const {observers} = this;
            while (observers.length) {
                observers.shift()?.error(err);
            }
        }
    }

    complete() {
        if (!this.isStopped) {
            this.isStopped = true;
            const {observers} = this;
            while (observers.length) {
                observers.shift()?.complete();
            }
        }
    }

    unsubscribe() {
        this.isStopped = this.closed = true;
        this.observers = [];
    }

    get observed() {
        return this.observers?.length > 0;
    }

    protected _subscribe(subscriber: Subscriber<T>): Subscription {
        this._checkFinalizedStatuses(subscriber);
        return this._innerSubscribe(subscriber);
    }

    protected _innerSubscribe(subscriber: Subscriber<any>) {
        const {hasError, isStopped, observers} = this;
        return hasError || isStopped
            ? Subscriber.EMPTY
            : (observers.push(subscriber), new Subscription(() => arrRemove(observers, subscriber)));
    }

    protected _checkFinalizedStatuses(subscriber: Subscriber<any>) {
        const {hasError, thrownError, isStopped} = this;
        if (hasError) {
            subscriber.error(thrownError);
        }
        else if (isStopped) {
            subscriber.complete();
        }
    }
}
export class BehaviorSubject<T> extends Subject<T> {
    constructor(public value: T) {
        super();
    }

    protected _subscribe(subscriber: Subscriber<T>): Subscription {
        const subscription = super._subscribe(subscriber);
        !subscription.closed && subscriber.next(this.value);
        return subscription;
    }

    next(value: T): void {
        super.next(this.value = value);
    }
}

export class ValueSubject<T> extends Subject<T> {
    preValueNotify = false;
    constructor(public value: T) {
        super();
    }

    // 兼容以前的EventSubject
    subscribeNotify(next: (value: T) => void, preValueNotify = false): Subscription {
        this.preValueNotify = preValueNotify;
        const sub = super.subscribe(next);
        this.preValueNotify = false;
        return sub;
    }

    protected _subscribe(subscriber: Subscriber<T>): Subscription {
        const subscription = super._subscribe(subscriber);
        this.preValueNotify && !subscription.closed && subscriber.next(this.value);
        return subscription;
    }

    next(value: T): void {
        super.next(this.value = value);
    }
}

/** ReadySubject，在read后回调，并取消订阅。已ready时订阅，会立即回调 */
export class ReadySubject extends Subject<boolean> {
    public value = false;

    protected _subscribe(subscriber: Subscriber<boolean>): Subscription {
        const subscription = super._subscribe(subscriber);
        if (this.value) {
            subscriber.next(this.value);
            subscriber.complete(); // 让订阅自动取消
        }
        return subscription;
    }

    next(value: boolean): void {
        if (value) {
            super.next(this.value = value);
            const {observers} = this;
            while (observers.length) {
                observers.shift()?.complete(); // 让订阅自动取消
            }
        }
    }

    setReady() {
        this.next(true);
    }
}
