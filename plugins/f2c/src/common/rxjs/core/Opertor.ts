import {Observable} from './Observable.js';
import {Subscriber} from './Subscriber.js';
import {UnsubscribeLike} from './types.js';

export interface OperatorFunction<T, R> {
    (source: Observable<T>): Observable<R>;
}

export interface subscribeSourceFunction<T, R> {
    (source: Observable<T>, subscriber: Subscriber<R>): UnsubscribeLike;
}

export function createOperator<T>(
    next?: (value: T, liftedSource: Observable<T>, subscriber: Subscriber<T>) => void,
    error?: (error: any, liftedSource: Observable<T>, subscriber: Subscriber<T>) => void,
    complete?: () => void
) {
    return (source: Observable<T>) => {
        const observable = new Observable<T>();
        observable.source = source;
        observable.subscribeSource = (liftedSource: Observable<T>, subscriber: Subscriber<T>) => {
            try {
                return liftedSource.subscribe(
                    liftedSourceValue => {
                        next ? next(liftedSourceValue, liftedSource, subscriber) : subscriber.next(liftedSourceValue);
                    },
                    err => {
                        error ? error(err, liftedSource, subscriber) : subscriber.error(error);
                    },
                    () => {
                        complete ? complete() : subscriber.complete();
                    }
                );
            }
            catch (err) {
                subscriber.error(err);
            }
        };

        return observable;
    };
}

export function timeout<T>(delay: number): OperatorFunction<T, T> {
    return (source: Observable<T>) => {
        const observable = new Observable<T>();
        observable.source = source;
        observable.subscribeSource = (liftedSource: Observable<T>, subscriber: Subscriber<T>) => {
            let timeId: any = setTimeout(() => {
                if (timeId) {
                    clearTimeout(timeId);
                    timeId = 0;
                }
                subscriber.error('err');
            }, delay);
            try {
                return liftedSource.subscribe(
                    liftedSourceValue => {
                        if (timeId) {
                            clearTimeout(timeId);
                            timeId = 0;
                        }
                        subscriber.next(liftedSourceValue);
                    },
                    error => {
                        subscriber.error(error);
                    },
                    () => {
                        subscriber.complete();
                    }
                );
            }
            catch (err) {
                subscriber.error(err);
            }
        };

        return observable;
    };
}
