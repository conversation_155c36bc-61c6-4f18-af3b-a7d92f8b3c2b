import {Observable} from './Observable.js';

export interface HasEventTargetAddRemove<E> {
    addEventListener(type: string, listener: (evt: E) => void, options?: boolean | AddEventListenerOptions): void;
    removeEventListener(type: string, listener: (evt: E) => void, options?: EventListenerOptions | boolean): void;
}
export function fromEvent<T>(
    target: HasEventTargetAddRemove<T> | ArrayLike<HasEventTargetAddRemove<T>>,
    eventName: string
): Observable<T> {
    const eventTarget: any = target;
    const addEventListener = (handler: any) => eventTarget['addEventListener'](eventName, handler);
    const removeEventListener = (handler: any) => eventTarget['removeEventListener'](eventName, handler);
    return new Observable<T>(subscriber => {
        const handler = (...args: any[]) => subscriber.next(1 < args.length ? args : args[0]);
        addEventListener(handler);
        return () => removeEventListener(handler);
    });
}

export function fromEvents<T>(
    target: HasEventTargetAddRemove<T> | ArrayLike<HasEventTargetAddRemove<T>>,
    eventNames: string[]
): Observable<T> {
    const eventTarget: any = target;

    return new Observable<T>(subscriber => {
        const handler = (...args: any[]) => subscriber.next(1 < args.length ? args : args[0]);
        eventNames.forEach(eventName => {
            eventTarget.addEventListener(eventName, handler);
        });
        return () => {
            eventNames.forEach(eventName => {
                eventTarget.removeEventListener(eventName, handler);
            });
        };
    });
}
