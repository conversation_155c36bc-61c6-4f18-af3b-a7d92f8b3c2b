export interface PipeFunction<T, R> {
  (source: T): R
}

export function pipe(...fns: Array<PipeFunction<any, any>>): PipeFunction<any, any> {
  return pipeFromArray(fns)
}

/** 恒等运算,  */
function identity<T>(x: T): T {
  return x
}

export function pipeFromArray<T, R>(fns: Array<PipeFunction<T, R>>): PipeFunction<T, R> {
  if (fns.length === 0) {
    return identity as PipeFunction<any, any>
  }

  if (fns.length === 1) {
    return fns[0]
  }

  return function piped(input: T): R {
    return fns.reduce((prev: any, fn: PipeFunction<T, R>) => fn(prev), input as any)
  }
}
