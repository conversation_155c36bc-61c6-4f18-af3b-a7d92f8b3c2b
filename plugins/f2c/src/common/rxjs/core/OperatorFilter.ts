import {Observable} from './Observable.js';
import {Subscriber} from './Subscriber.js';
import {createOperator, OperatorFunction} from './Opertor.js';

// 转换操作符

/**
 * 订阅后，取数据源的count个值。达到count个后，进入完成状态，自动取消订阅。
 * @param {number} count
 * @return {*}  {OperatorFunction<T, T>}
 */
export function take<T>(count: number): OperatorFunction<T, T> {
    return count <= 0
        ? () => Observable.EMPTY
        : (source: Observable<T>) => {
            const observable = new Observable<T>();
            observable.source = source;
            observable.subscribeSource = (liftedSource: Observable<T>, subscriber: Subscriber<T>) => {
                try {
                    let observedCount = 0;
                    return liftedSource.subscribe(
                        value => {
                            if (++observedCount <= count) {
                                subscriber.next(value);

                                if (count <= observedCount) {
                                    subscriber.complete();
                                }
                            }
                        },
                        error => {
                            subscriber.error(error);
                        },
                        () => {
                            subscriber.complete();
                        }
                    );
                }
                catch (err) {
                    subscriber.error(err);
                }
            };
            return observable;
        };
}

/**
 * 过滤数据源，只有满足predicate的数据才可以通过。类似于 Array.prototype.filter()
 * @param {(value: T, index: number) => boolean} predicate 检测数据源发出的每个值的函数。true就发出值。index 参数是自订阅开始后发送序列的索引，是从 0 开始的。
 * @param {*} [thisArg] 用来决定 predicate 函数中的 this 的值
 * @return {*}  {OperatorFunction<T, T>}
 */
export function filter<T>(predicate: (value: T, index: number) => boolean, thisArg?: any): OperatorFunction<T, T> {
    let index = 0;
    return createOperator((value: T, liftedSource: Observable<T>, subscriber: Subscriber<T>) => {
        predicate.call(thisArg, value, index++) && subscriber.next(value);
    });
}
