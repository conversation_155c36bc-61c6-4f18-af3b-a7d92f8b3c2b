import {config} from './RSConfig.js';

export class UnsubscriptionError extends Error {
    public readonly errors: (Error | string)[];
    constructor(errors: (Error | string)[]) {
        super();
        this.message = errors
            ? `${errors.length} errors occurred during unsubscription:
		${errors.map((err, i) => `${i + 1}) ${err.toString()}`).join('\n  ')}`
            : '';
        this.name = 'UnsubscriptionError';
        this.errors = errors;
    }
}

export function reportUnhandledError(err: any) {
    setTimeout(() => {
        const {onUnhandledError} = config;
        if (onUnhandledError) {
            // 使用方配置的错误处理.
            onUnhandledError(err);
        }
        else {
            throw err;
        }
    });
}
