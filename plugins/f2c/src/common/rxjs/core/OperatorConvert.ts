import {Observable} from './Observable.js';
import {Subscriber} from './Subscriber.js';
import {OperatorFunction, createOperator} from './Opertor.js';

// 转换操作符

/**
 * 投射数据源的每个值
 * @param {(value: T, index: number) => R} project 投射函数, index 参数是自订阅开始后发送序列的索引，是从 0 开始的
 * @return {*}  {OperatorFunction<T, R>}
 */
export function map<T, R>(project: (value: T, index: number) => R): OperatorFunction<T, R> {
    return (source: Observable<T>) => {
        const observable = new Observable<R>();
        observable.source = source;
        observable.subscribeSource = (liftedSource: Observable<T>, subscriber: Subscriber<R>) => {
            try {
                let observedCount = 0;
                return liftedSource.subscribe(
                    liftedSourceValue => {
                        subscriber.next(project(liftedSourceValue, observedCount++));
                    },
                    error => {
                        subscriber.error(error);
                    },
                    () => {
                        subscriber.complete();
                    }
                );
            }
            catch (err) {
                subscriber.error(err);
            }
        };
        return observable;
    };
}

/**
 * 对源数据源使用累积器函数， 返回生成的中间值， 可选的初始值
 * @param {((acc: V | A | S, value: V, index: number) => A)} accumulator 对每个源数据调用的累积器函数
 * @param {S} [seed] 初始值。
 * @return {*}  {(OperatorFunction<V, V | A>)}
 */
export function scan<V, A, S>(
    accumulator: (acc: V | A | S, value: V, index: number) => A,
    seed?: S
): OperatorFunction<V, V | A> {
    let index = 0;
    let cache: any = seed;
    let hasSeed = arguments.length >= 2;
    return createOperator((value: V, liftedSource: Observable<V>, subscriber: Subscriber<V>) => {
        if (hasSeed) {
            cache = accumulator(cache, value, index++);
        }
        else {
            hasSeed = true;
            cache = value;
        }
        subscriber.next(cache);
    });
}
