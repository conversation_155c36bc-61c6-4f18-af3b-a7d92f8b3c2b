/** SUBSCRIPTION INTERFACES */

export interface Unsubscribable {
  unsubscribe(): void
}

export interface SubscriptionLike extends Unsubscribable {
  unsubscribe(): void
  readonly closed: boolean
}

export type UnsubscribeLike = SubscriptionLike | Unsubscribable | (() => void) | void

export interface Observer<T> {
  next: (value: T) => void
  error: (err: any) => void
  complete: () => void
}

export interface Subscribable<T> {
  subscribe(observer: Partial<Observer<T>>): Unsubscribable
}
