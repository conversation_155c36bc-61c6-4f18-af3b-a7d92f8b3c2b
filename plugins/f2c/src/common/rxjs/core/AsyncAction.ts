import {Subscription} from './Subscription.js';

const logOn = false;
export interface SchedulerAction<T> extends Subscription {
    schedule(state?: T, delay?: number): Subscription;
}

export class AsyncAction<T> extends Subscription {
    public intervalId = 0;
    public delay = 0;
    public state?: T = undefined;

    constructor(protected work: (action: SchedulerAction<T>, preState?: T) => void) {
        super();
        this.execute = this.execute.bind(this);
    }

    public schedule(state?: T, delay = 0): Subscription {
        logOn && console.log('schedule state', state, 'delay', delay);
        if (!this.closed) {
            this.state = state;
            if (this.intervalId != 0) {
                this.intervalId = this.recycleAsyncId(this.intervalId, delay);
            }
            this.delay = delay;
            this.intervalId = this.intervalId || this.requestAsyncId(delay);
        }
        else {
            console.warn('schedule closed state', state, 'delay', delay);
        }

        return this;
    }

    protected requestAsyncId(delay = 0): number {
        logOn && console.log('requestAsyncId', delay);
        return window.setInterval(this.execute, delay);
    }

    protected recycleAsyncId(intervalId: number, delay = 0): any {
        if (delay >= 0 && this.delay === delay) {
            return intervalId;
        }
        logOn && console.log('clearInterval', intervalId);
        window.clearInterval(intervalId);
        return 0;
    }

    private execute() {
        if (this.closed) {
            console.warn('executing a cancelled action');
            if (this.intervalId > 0) {
                this.intervalId = this.recycleAsyncId(this.intervalId, -1);
                this.intervalId = 0;
            }
        }
        else {
            try {
                const work = this.work;
                work(this, this.state);
            }
            catch (e) {
                console.warn(e || 'Scheduled action threw falsy error');
                this.unsubscribe();
            }
        }
    }

    unsubscribe() {
        // logOn && console.log('unsubscribe')
        console.log('unsubscribe');
        if (this.intervalId > 0) {
            this.intervalId = this.recycleAsyncId(this.intervalId, -1);
            this.intervalId = 0;
        }
        if (!this.closed) {
            this.work = this.state = undefined!;

            this.delay = 0;
            super.unsubscribe();
        }
    }
}
