import {Observable, ObservableInputTuple} from './Observable.js';
import {Subscriber} from './Subscriber.js';
export function merge<A extends readonly unknown[]>(...args: [...ObservableInputTuple<A>]): Observable<A[number]> {
    const observable = new Observable<unknown>();
    observable.subscribeSource = (liftedSource: Observable<unknown>, subscriber: Subscriber<unknown>) => {
        try {
            args.forEach(observable => {
                const sub = observable.subscribe(
                    value => subscriber.next(value),
                    error => subscriber.error(error),
                    () => subscriber.complete()
                );
                subscriber.add(sub);
            });
            return subscriber;
        }
        catch (err) {
            subscriber.error(err);
        }
    };
    return observable;
}
