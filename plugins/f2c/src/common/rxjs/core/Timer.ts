import {AsyncAction} from './AsyncAction.js';
import {Observable} from './Observable.js';

/** 有两个参数，第一个参数表示到发送第一个值的间隔时间，第二个参数表示从发送第二个参数开始，每发送一个值的间隔时间，如果第二个参数为空则发送第一个参数后，终止，执行complete函数；值是从0开始的计数； */
export function timer(dueTime: number, interval = -1): Observable<number> {
    return new Observable(subscriber => {
        let n = 0;
        return new AsyncAction((action, preState) => {
            if (!subscriber.closed) {
                subscriber.next(n++);

                if (interval < 0) {
                    subscriber.complete();
                }
                else if (!action.closed) {
                    action.schedule(preState, interval);
                }
            }
            return interval;
        })
            .schedule(undefined, dueTime < 0 ? 0 : dueTime);
    });
}

export function interval(interval: number): Observable<number> {
    return timer(interval, interval);
}
