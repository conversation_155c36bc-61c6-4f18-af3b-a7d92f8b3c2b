import {isFunction} from './util.js';
import {Observer, UnsubscribeLike} from './types.js';
import {Subscription} from './Subscription.js';
import {reportUnhandledError} from './error.js';

/* tslint:disable:no-empty */
function EmptyFuntion() {}

/**
 * 消费者.
 * 用于消费Observable的数据。
 * 为了提供类似订阅的特性，如unsubscribe，所有的观察者都被转为消费者。
 * 消费者是一个实现运算符的至关重要的类型，但是他是一个很少对外的API。
 */
export class Subscriber<T> extends Subscription implements Observer<T> {
    protected isStopped = false;
    protected destination?: Subscriber<any> | Observer<any>;

    public static EMPTY = (() => {
        const empty = new Subscriber();
        empty.closed = true;
        empty.next = EmptyFuntion;
        empty.error = defaultErrorHandler;
        empty.complete = EmptyFuntion;
        return empty;
    })();

    constructor(destination?: Subscriber<any> | Observer<any>) {
        super();
        if (destination) {
            this.destination = destination;
            if (Subscription.isSubscription(destination)) {
                destination.add(this);
            }
        }
        else {
            this.destination = Subscriber.EMPTY;
        }
    }

    next(value: T): void {
        if (!this.isStopped) {
            this._next(value);
        }
    }
    error(err?: any): void {
        if (!this.isStopped) {
            this.isStopped = true;
            this._error(err);
        }
    }
    complete(): void {
        if (!this.isStopped) {
            this.isStopped = true;
            this._complete();
        }
    }

    unsubscribe(): void {
        if (!this.closed) {
            this.isStopped = true;
            super.unsubscribe();
            this.destination = undefined;
        }
    }

    protected _next(value: T): void {
        this.destination?.next(value);
    }

    protected _error(err: any): void {
        try {
            this.destination?.error(err);
        }
        finally {
            this.unsubscribe();
        }
    }

    protected _complete(): void {
        try {
            this.destination?.complete();
        }
        finally {
            this.unsubscribe();
        }
    }
}

export class SafeSubscriber<T> extends Subscriber<T> {
    constructor(
        observerOrNext?: Partial<Observer<T>> | ((value: T) => void) | null,
        error?: ((e?: any) => void) | null,
        complete?: (() => void) | null
    ) {
        super();
        let next: ((value: T) => void) | undefined;
        if (isFunction(observerOrNext)) {
            next = observerOrNext;
        }
        else if (observerOrNext) {
            ({next, error, complete} = observerOrNext);
            const context = observerOrNext;
            next = next?.bind(context);
            error = error?.bind(context);
            complete = complete?.bind(context);
        }

        this.destination = {
            next: next ? wrapForErrorHandling(next, this) : EmptyFuntion,
            error: wrapForErrorHandling(error ?? defaultErrorHandler, this),
            complete: complete ? wrapForErrorHandling(complete, this) : EmptyFuntion,
        };
    }
}

function wrapForErrorHandling(handler: (arg?: any) => void, instance: SafeSubscriber<any>) {
    return (...args: any[]) => {
        try {
            handler(...args);
        }
        catch (err) {
            reportUnhandledError(err);
        }
    };
}

function defaultErrorHandler(err: any) {
    throw err;
}
