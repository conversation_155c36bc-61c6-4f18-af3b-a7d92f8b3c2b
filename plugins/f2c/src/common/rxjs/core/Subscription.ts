import {UnsubscriptionError} from './error.js';
import {SubscriptionLike, UnsubscribeLike, Unsubscribable} from './types.js';
import {isFunction, arrRemove} from './util.js';

let SubscriptionID = 0;
/**
 * 订阅，一个可以释放的资源。
 * 通过重要的方法`unsubscribe`, 用于释放被订阅把持的资源.
 * 另外，可以通过 `add()` 将子订阅聚集在一起，当当前订阅被释放时，子订阅也被释放
 */
export class Subscription implements SubscriptionLike {
    public static EMPTY = (() => {
        const empty = new Subscription();
        empty.closed = true;
        return empty;
    })();

    static isSubscription(value: any): value is Subscription {
        return (
            value instanceof Subscription
            || (value && 'closed' in value && isFunction(value.remove) && isFunction(value.add)
                && isFunction(value.unsubscribe))
        );
    }

    public closed = false;

    private _parentage?: Subscription[] | Subscription;
    private _subSubscriptionLikes?: Exclude<UnsubscribeLike, void>[];
    id = 0;
    constructor(private dispose?: () => void) {
        this.id = ++SubscriptionID;
    }

    unsubscribe(): void {
        let errors: any[] | undefined;

        if (!this.closed) {
            this.closed = true;
            const {_parentage} = this;
            if (_parentage) {
                this._parentage = undefined;
                if (Array.isArray(_parentage)) {
                    for (const parent of _parentage) {
                        parent.remove(this);
                    }
                }
                else {
                    _parentage.remove(this);
                }
            }

            const {dispose} = this;
            if (isFunction(dispose)) {
                try {
                    dispose();
                }
                catch (e) {
                    errors = e instanceof UnsubscriptionError ? e.errors : [e];
                }
            }

            const {_subSubscriptionLikes} = this;
            if (_subSubscriptionLikes) {
                this._subSubscriptionLikes = undefined;
                for (const subSubscription of _subSubscriptionLikes) {
                    try {
                        this.disposeSubscription(subSubscription);
                    }
                    catch (err) {
                        errors = errors ?? [];
                        if (err instanceof UnsubscriptionError) {
                            errors = [...errors, ...err.errors];
                        }
                        else {
                            errors.push(err);
                        }
                    }
                }
            }

            if (errors) {
                throw new UnsubscriptionError(errors);
            }
        }
    }

    /**
     * 添加子订阅。当当前订阅被释放时，子订阅也会被释放。
     * 添加子订阅时，如果当前订阅已关闭了，子订阅会立刻执行释放
     */
    add(subSubscription: UnsubscribeLike): void {
        if (subSubscription && subSubscription !== this) {
            if (this.closed) {
                this.disposeSubscription(subSubscription);
            }
            else {
                if (subSubscription instanceof Subscription) {
                    if (subSubscription.closed || subSubscription._hasParent(this)) {
                        return;
                    }
                    subSubscription._addParent(this);
                }
                (this._subSubscriptionLikes = this._subSubscriptionLikes ?? []).push(subSubscription);
            }
        }
    }

    private _hasParent(parent: Subscription) {
        const {_parentage} = this;
        return _parentage === parent || (Array.isArray(_parentage) && _parentage.includes(parent));
    }

    private _addParent(parent: Subscription) {
        const {_parentage} = this;
        this._parentage = Array.isArray(_parentage)
            ? (_parentage.push(parent), _parentage)
            : _parentage
            ? [_parentage, parent]
            : parent;
    }

    private _removeParent(parent: Subscription) {
        const {_parentage} = this;
        if (_parentage === parent) {
            this._parentage = undefined;
        }
        else if (Array.isArray(_parentage)) {
            arrRemove(_parentage, parent);
        }
    }
    remove(sub: Exclude<UnsubscribeLike, void>): void {
        const {_subSubscriptionLikes} = this;
        _subSubscriptionLikes && arrRemove(_subSubscriptionLikes, sub);

        if (sub instanceof Subscription) {
            sub._removeParent(this);
        }
    }
    disposeSubscription(sub: Unsubscribable | (() => void)) {
        if (isFunction(sub)) {
            sub();
        }
        else {
            sub.unsubscribe();
        }
    }
}
