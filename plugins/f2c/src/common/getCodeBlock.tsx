export async function* getCodeBlock(chunks: AsyncIterable<string> | string[]) {
    let str = '';
    yield str;
    for await (const chunk of chunks) {
        str += chunk;
        yield (
            <code-block
                language="scss"
                actions={['insertToFile', 'showFileInsertDiff']}
                insertToFileData={{
                    filePath: 'index.module.scss', // 需要绝对路径
                    position: {line: 1, character: 0}, // 插入位置
                    newText: str, // 插入内容
                }}
                closed={false}
            >
                {str}
            </code-block>
        );
    }
}
