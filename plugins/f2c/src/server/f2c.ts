import {DefaultEventsMap, Server, Socket} from 'socket.io';
import {createServer, type Server as NodeServer} from 'node:http';
import {File, formatFiles} from '../utils/prettier.js';
import {ProviderInit} from '@comate/plugin-host';
import {Subject} from '../common/rxjs/index.js';
// import {writeFiles} from './utils/writeFile.js';
import path from 'node:path';
import logger from '../utils/logger.js';
import {getNameMap, replaceVariableNameWithinFile} from '../services/comateAi.js';
import {rmDir, writeFiles} from '../utils/fileUtil.js';
import {getTempDir, OUTPUT_DIR} from '../providers/f2c.js';
import {reportLines} from '../utils/report.js';
import CallBackLogic, {CodeGenerationStatus} from '../providers/callback/callback.js';
import RegenerateLogic from '../providers/regenerate/regenerate.js';
export const PORT = 48889;

type ProviderInitType = ProviderInit;
export type ServerMessageData = {
    data: {
        files: File[];
        name: string;
        uiframework: string;
        option: Record<string, string>;
    };
    status: string;
    msg: string;
};

export class F2CServer {
    websocketServer?: NodeServer;
    io?: Server = undefined;
    socket!: Socket<DefaultEventsMap, DefaultEventsMap, DefaultEventsMap, any>;
    isServerRunning = false;
    provider?: ProviderInit;
    type: 'genCode' | 'genCodeById' = 'genCode';
    init(provider: ProviderInitType, type?: 'genCode' | 'genCodeById') {
        this.provider = provider;
        this.type = type || 'genCode';
        console.log('[server] 初始化F2CServer', this.provider);
        this.initServer();
    }
    genSuccess$: Subject<any> = new Subject();
    searchSuccess$: Subject<any> = new Subject();
    serverConnected$: Subject<boolean> = new Subject();
    initServer() {
        this.destroy();
        console.log('[server] server destroy', this.io);

        this.websocketServer = createServer();

        this.io = new Server(this.websocketServer, {
            maxHttpBufferSize: 1e8,
            cors: {
                origin: '*',
            },
        });
        if (this.type == 'genCodeById') {
            this.listenIdMethod();
        }
        else {
            this.listen();
        }
        this.isServerRunning = true;
        console.log('[server] server start');
        console.log(this.provider?.config);
    }

    destroy() {
        this.io?.removeAllListeners();
        this.io?.close();
        this.websocketServer?.closeAllConnections();
        this.isServerRunning = false;
        this.io = undefined;
        this.websocketServer = undefined;
    }

    async listen() {
        const timeId = setTimeout(() => {
            clearTimeout(timeId);
            this.serverConnected$.next(false);
        }, 10 * 1000);

        this
            .websocketServer
            ?.listen({port: PORT}, async () => {
                console.log(`[server] server is running at http://127.0.0.1:${PORT}`);
            })
            .on('error', err => {
                this.serverConnected$.next(false);
            });
        this.io?.on('connection', async socket => {
            if (CallBackLogic.impl.codeGenerationStatus == CodeGenerationStatus.Stop) {
                socket.emit('code-generation-stop');
                CallBackLogic.impl.codeGenerationStatus = CodeGenerationStatus.None;
                // 这里destory 那么上面emit的信号有可能收不到
                this.destroy();
                return;
            }
            CallBackLogic.impl.codeGenerationStatus = CodeGenerationStatus.None;
            this.serverConnected$.next(true);
            clearTimeout(timeId);
            socket.emit('pong', {
                // 模拟插件版本比较高
                serverVersion: '2.0.0',
                platform: 'comate',
            });

            // figma传递数据的 callback
            socket.on('word-generation', async function* (str, callback) {
                console.log('[server] 图层名', str, '调用Comate 执行翻译');
            });

            socket.on('code-generation', async (data, callback) => {
                if (CallBackLogic.impl.codeGenerationStatus == CodeGenerationStatus.Stop) {
                    // 在soicket信号来之前就手动停止
                    callback({
                        status: 'error',
                        msg: '生成失败',
                    });
                    this.destroy();
                    return;
                }
                CallBackLogic.impl.codeGenerationCallback = callback;
                CallBackLogic.impl.codeGenerationStatus = CodeGenerationStatus.Start;
                try {
                    console.log(`[F2C] code generation received.`, data);
                    // 使用comate服务 默认开启Ai 暂先支持web框架
                    logger.debug('[F2C] data.cssToNameMap', data.cssToNameMap);
                    await getNameMap(data.cssToNameMap, data.idToClassname);
                    const files = replaceVariableNameWithinFile(data.files, data.idToComponentNames, data.uiframework);
                    // 上报生成行数
                    const mode = data.option?.fileOutPutMode;
                    reportLines(data.files, mode, data.uiframework);
                    if (this.provider?.cwd) {
                        // 生成之前删除临时目录中的文件
                        rmDir(getTempDir(data.name));
                        await writeFiles(formatFiles(files), getTempDir(data.name));
                    }
                    this.genSuccess$.next({
                        status: 'success',
                        data,
                    });
                    if (CallBackLogic.impl.codeGenerationCallback) {
                        CallBackLogic.impl.codeGenerationStatus = CodeGenerationStatus.Finish;
                        CallBackLogic.impl.codeGenerationCallback({
                            status: 'ok',
                        });
                    }
                }
                catch (e: any) {
                    console.error(`[server]`, e);
                    this.genSuccess$.next({
                        status: 'error',
                        msg: '生成失败',
                    });
                    CallBackLogic.impl.codeGenerationStatus = CodeGenerationStatus.Finish;
                    if (CallBackLogic.impl.codeGenerationCallback) {
                        CallBackLogic.impl.codeGenerationCallback({
                            status: 'error',
                            error: e.stack,
                        });
                    }
                }
            });
        });
    }
    async listenIdMethod() {
        const timeId = setTimeout(() => {
            clearTimeout(timeId);
            this.serverConnected$.next(false);
        }, 10 * 1000);

        this
            .websocketServer
            ?.listen({port: PORT}, async () => {
                console.log(`[server] server is running at http://127.0.0.1:${PORT}`);
            })
            .on('error', err => {
                this.serverConnected$.next(false);
            });
        this.io?.on('connection', async socket => {
            if (CallBackLogic.impl.codeGenerationStatus == CodeGenerationStatus.Stop) {
                socket.emit('code-generation-stop');
                CallBackLogic.impl.codeGenerationStatus = CodeGenerationStatus.None;
                // 这里destory 那么上面emit的信号有可能收不到
                this.destroy();
                return;
            }
            CallBackLogic.impl.codeGenerationStatus = CodeGenerationStatus.None;
            clearTimeout(timeId);
            console.log('开始向插件发送节点Id');
            this.serverConnected$.next(true);
            socket.on('node-search-result', (resp, callback) => {
                console.log('node-search-result', resp);

                const {status, data} = resp;
                if (status === 'success') {
                    this.searchSuccess$.next(data);
                }
                else {
                    this.searchSuccess$.next(null);
                }
            });
            socket.on('code-generation', async (data, callback) => {
                if (CallBackLogic.impl.codeGenerationStatus == CodeGenerationStatus.Stop) {
                    // 在soicket信号来之前就手动停止
                    callback({
                        status: 'error',
                        msg: '生成失败',
                    });
                    this.destroy();
                    return;
                }
                CallBackLogic.impl.codeGenerationCallback = callback;
                CallBackLogic.impl.codeGenerationStatus = CodeGenerationStatus.Start;
                try {
                    console.log(`[F2C] code generation received.`, data);
                    // 使用comate服务 默认开启Ai 暂先支持web框架
                    logger.debug('[F2C] data.cssToNameMap', data.cssToNameMap);
                    await getNameMap(data.cssToNameMap, data.idToClassname);
                    const files = replaceVariableNameWithinFile(data.files, data.idToComponentNames, data.uiframework);
                    // 上报生成行数
                    const mode = data.option?.fileOutPutMode;
                    reportLines(data.files, mode, data.uiframework);
                    if (this.provider?.cwd) {
                        // 生成之前删除临时目录中的文件
                        rmDir(getTempDir(data.name));
                        await writeFiles(formatFiles(files), getTempDir(data.name));
                    }
                    this.genSuccess$.next({
                        status: 'success',
                        data,
                    });
                    // if (CallBackLogic.impl.codeGenerationCallback) {
                    //     console.log('codeGenerationCallback_genFinfish');
                    //     CallBackLogic.impl.codeGenerationStatus = CodeGenerationStatus.Finish;
                        callback({
                            status: 'ok',
                        });
                    // }
                }
                catch (e: any) {
                    console.error(`[server]`, e);
                    this.genSuccess$.next({
                        status: 'error',
                        msg: '生成失败',
                    });
                    CallBackLogic.impl.codeGenerationStatus = CodeGenerationStatus.Finish;
                    if (CallBackLogic.impl.codeGenerationCallback) {
                        CallBackLogic.impl.codeGenerationCallback({
                            status: 'error',
                            error: e.stack,
                        });
                    }
                }
            });
            console.log(RegenerateLogic.impl.cacheData.pluginData, 'afterStopGen');
            if (RegenerateLogic.impl.cacheData.pluginData) {
                socket.emit('code-generation-by-id', {
                    data: RegenerateLogic.impl.cacheData.pluginData,
                });
            }
        });
    }
}
