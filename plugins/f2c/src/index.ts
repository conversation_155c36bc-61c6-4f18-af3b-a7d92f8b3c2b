import type {PluginSetupContext} from '@comate/plugin-host';
import {F2CSkillProvider} from './providers/f2c.js';
import {HelpFallbackProvider} from './providers/help.js';
import {GenCodeProvider} from './providers/genCodeById.js';

export function setup({registry}: PluginSetupContext) {
    // const server  = new F2CServer();
    // server.init();
    // registry.registerBackgroundServiceProvider('f2c', F2CServer);
    registry.registerFallbackProvider('default', HelpFallbackProvider);
    registry.registerSkillProvider('genCode', F2CSkillProvider);
}
