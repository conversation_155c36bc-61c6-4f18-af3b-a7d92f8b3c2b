import {FallbackProvider, TaskProgressChunk, StringChunkStream} from '@comate/plugin-host';
import {readFileSync} from 'fs';
import {dirname} from 'path';
import {fileURLToPath} from 'url';
// const DESCRIPTION = `
// '该插件用于 Figma 转 代码，用户需要在 Figma 端安装 YY F2C 插件，并选择 Comate 输出方式生成：：

// * 支持 React, Taro, Vue 等Web 代码生成
// * 支持 AI 代码优化能力

// 如果你需要详细的教程，请阅读文档：https://www.yuque.com/yyf2c/doc/k0156z。
// `;

export class HelpFallbackProvider extends FallbackProvider {
    static description = '能力解释';

    async *handleQuery(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new StringChunkStream();
        const filename = fileURLToPath(import.meta.url);
        const DESCRIPTION = readFileSync(`${dirname(filename)}/help.md`, 'utf8');

        yield stream.flush(DESCRIPTION);
    }
}
