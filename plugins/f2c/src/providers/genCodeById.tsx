import {
    FunctionParameterDefinition,
    ProviderInit,
    StringChunkStream,
    type TaskProgressChunk,
} from '@comate/plugin-host';
import {ServerMessageData} from '../server/f2c.js';
import {firstValueFrom} from '../common/rxjs/index.js';
import {File} from '../utils/prettier.js';
import {description, description_saas} from '../help.js';
import RegenerateLogic from './regenerate/regenerate.js';
import CallBackLogic from './callback/callback.js';
import { ComateWorkFlow, F2CBaseProvider, getTempDir, OUTPUT_DIR } from './f2cBaseProvide.js';
import { Adaptor } from '../services/codeAdaptation/adaptor.js';
import { getParam } from '../utils/urlUtils.js';

export class GenCodeProvider extends F2CBaseProvider {
    _layerInfo = {nodeId: '', name: ''}
    static description = '根据图层Id为我输出代码';
    static skillName = 'genCodeById';
    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };
    readonly cwd: string;

    constructor(init: ProviderInit) {
        super(init);
        this.cwd = init.cwd;
    }

    handleChatCancel(cancleToken: string): void {
        this.logger.info(`handleChatCancel: ${cancleToken}`)
        if (cancleToken == 'stop' && CallBackLogic.impl.codeGenerationCallback) {
            console.log('[provider_cancleToken]:', cancleToken, CallBackLogic.impl.codeGenerationCallback)
            // CallBackLogic.impl.codeGenerationCallback({
            //     status: 'error',
            //     error: '生成失败, 用户取消生成代码',
            // })
            // 停止执行之后 再执行代码 不会起作用了?
            // CallBackLogic.impl.codeGenerationCallback = null
            this.server.destroy()
        }

    }
    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        if (!this.cwd) {
            yield this.elementChunkStream.flush(<p>未检索到可用的工作区，请打开工作区后再进行尝试。</p>);
            return;
        }
        const isRegenerated = this.isRegenerated
        try {
            const data = this.isValidQuery(isRegenerated ? RegenerateLogic.impl.cacheData.pluginData as string: this.currentContext.query.trim())
            // 前置判断是否为合法的figmaNodeId
            if (!data){
                yield this.stringChunkStream.flush(<p>{`输入ID不合法，请检查`}</p>);
                return
            }
            if (!isRegenerated && this.currentContext.query) {
                RegenerateLogic.impl.cacheData.pluginData = data
            }
            console.log('[provider] isRegenerated',  data)
            if (isRegenerated && RegenerateLogic.impl.cacheData.filesData.data?.files?.length > 0) {
                yield this.elementChunkStream.flush(<p>{`代码重新生成成功`}</p>);
                const data = RegenerateLogic.impl.cacheData.filesData
                console.log('当前使用方案:',this.workFlow,RegenerateLogic.impl.cacheData.filesData)
                const workFlow = RegenerateLogic.impl.cacheData.filesData.data.option.fileOutPutMode
                if (data && workFlow=== ComateWorkFlow.Intelligent) {
                    const adaptor = new Adaptor(
                        this.cwd,
                        this.llm,
                        this.logger,
                        this.elementChunkStream,
                        OUTPUT_DIR
                    );
                    if (yield* adaptor.executeAdaptor(data)) {
                        yield this._renderBatchAdoption(data);
                    }
                    return;
                }
                yield this._dealShowCodeGenStatus(data);
                yield this._renderBatchAdoption(data);
                return
            }
            this.server.init(this as any, 'genCodeById');
            yield this.elementChunkStream.flush(<loading>正在建立连接</loading>);
            const connected = await Promise.race([
                firstValueFrom(this.server.serverConnected$),
                new Promise<boolean>(resolve => {
                    setTimeout(() => {
                        resolve(false);
                    }, 7000);
                }),
            ]);
            if (!connected) {
                yield* this._dealTimeOut();
                return;
            }

            yield this.elementChunkStream.flushReplaceLast(<p>正在建立连接</p>);
            yield this.elementChunkStream.flush(<p>{`连接建立${connected ? '成功' : '失败'}`}</p>);
            yield this.elementChunkStream.flush(<loading>{`正在搜索图层,请确保Figma中F2C插件为开启状态`}</loading>);
            this._layerInfo = await Promise.race([
                firstValueFrom(this.server.searchSuccess$),
                new Promise<boolean>(resolve => {
                    setTimeout(() => {
                        resolve(false);
                    }, 30 * 1000);
                }),
            ]);
            if (this._layerInfo){
                yield this.elementChunkStream.flushReplaceLast(<p>{`正在搜索图层,请确保Figma中F2C插件为开启状态`}</p>);
                yield this.elementChunkStream.flush(<loading>{`已找到图层: ${this._layerInfo.name},准备生成代码`}</loading>);
            } else {
                yield this.elementChunkStream.flushReplaceLast(<p>{`正在搜索图层,请确保Figma中F2C插件为开启状态`}</p>);
                yield this.elementChunkStream.flush(<p>{'未找到该id对应图层，请确保输入id正确'}</p>);
                this.server.destroy()
                return
            }
            yield this.elementChunkStream.flushReplaceLast(<p>已找到图层: {this._layerInfo.name},准备生成代码</p>);
            yield this.elementChunkStream.flush(<loading>{`开始生成代码`}</loading>);
            this.serverData = await Promise.race([
                firstValueFrom(this.server.genSuccess$),
                new Promise<boolean>(resolve => {
                    setTimeout(() => {
                        resolve(false);
                    }, 30 * 1000);
                }),
            ]);
            if (!this.serverData) {
                yield* this._dealTimeOut();
                return;
            }
            this.server.destroy();
            yield this.elementChunkStream.flushReplaceLast(<p>{`开始生成代码`}</p>);
            this.logger.info(`serverData start、${JSON.stringify(this.serverData)}`);
            RegenerateLogic.impl.cacheData.filesData  = this.serverData
            if (this.serverData) {
                this.workFlow = this.serverData.data.option.fileOutPutMode as ComateWorkFlow;
            }
            if (this.serverData && this.workFlow === ComateWorkFlow.Intelligent) {
                const adaptor = new Adaptor(
                    this.cwd,
                    this.llm,
                    this.logger,
                    this.elementChunkStream,
                    OUTPUT_DIR
                );
                if (yield* adaptor.executeAdaptor(this.serverData)) {
                    yield this._renderBatchAdoption(this.serverData!);
                }
                return;
            }
            yield this._dealShowCodeGenStatus(this.serverData!);
            yield this._renderBatchAdoption(this.serverData!);
        }
        catch (e) {
            console.log(e);
            yield* this._dealHelp();
            return;
        }

    }

    findValidSubstring(input: string): string | null {
        const pattern = /\b(\d+:\d+)\b/;
        const match = input.match(pattern);
        return match ? match[1] : null;
    }
    isValidQuery(query: any ) {
        try {
            if (Object.prototype.toString.call(query) === '[object Object]' && query.nodeId && query.fileKey) {
                return query
            }
        } catch (error) {

        }
        let res: any = null
        console.log('isValidQuery', query)
        if (query.startsWith('https://www.figma.com')) {
            const regex = /https:\/\/www\.figma\.com\/design\/([^\/]+)/;
            const match = query.match(regex);
            if (match && match[1]) {
                const extractedString = match[1];
                res = {
                    fileKey: extractedString,
                    nodeId: getParam('node-id', query).replace('-', ':')
                }
            return res
            }
        } else {
        // 插件复制
        const regex = /【(.*?)】/g;
        const matches = query.matchAll(regex);
        const result: string[] = [];

        for (const match of matches) {
            if (match[1]) {
                result.push(match[1]);
            }
        }
        if (result && result.length == 4) {
            res = {
                fileKey: result[1],
                nodeId: result[3]
            }
        }
        return res;
        }

    }

}
