import {ServerMessageData} from '../../server/f2c.js';

class RegenerateLogic {
    cacheData: {
        pluginData: Record<string, string> | string;
        filesData: ServerMessageData;
    } = {
        pluginData: {},
        filesData: {} as ServerMessageData,
    };
    private static _instance: RegenerateLogic;
    static get impl() {
        if (!this._instance) {
            this._instance = new RegenerateLogic();
        }
        return this._instance;
    }
}
export default RegenerateLogic;
