import {
    ElementChunkStream,
    FunctionParameterDefinition,
    ProviderInit,
    SkillProvider,
    StringChunkStream,
    type TaskProgressChunk,
} from '@comate/plugin-host';
import {F2CServer, ServerMessageData} from '../server/f2c.js';
import path  from 'path';
import {File} from '../utils/prettier.js';
import {rmDir} from '../utils/fileUtil.js';
import ComateTemplate from '../services/comateTemplate.js';
import os from 'os';
import fs  from 'fs';
import {f2cDataReport, getLine, ReportType} from '../utils/report.js';
import {description, description_saas} from '../help.js';

export enum ComateWorkFlow {
    Basic = 'comate_basic',
    Intelligent = 'comate_intelligent',
}
export const TEMP_DIR = os.tmpdir();
export const OUTPUT_DIR = '.comate-f2c';

export const getTempDir = (dir: string) => {
    return path.join(TEMP_DIR, OUTPUT_DIR, dir);
};
/**
 * F2C基类
 */
export class F2CBaseProvider extends SkillProvider {
    static description = '';
    static skillName = '';
    private _server!: F2CServer;
    private _elementChunkStream!: ElementChunkStream;
    private _stringChunkStream!: StringChunkStream;
    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };
    readonly cwd: string;
    serverData?: ServerMessageData = undefined;
    workFlow?: ComateWorkFlow = undefined;

    constructor(init: ProviderInit) {
        super(init);
        this.cwd = init.cwd;
    }

    get server() {
        if (!this._server) {
            this._server = new F2CServer();
        }
        return this._server;
    }
    get elementChunkStream() {
        if (!this._elementChunkStream) {
            this._elementChunkStream = new ElementChunkStream();
        }
        return this._elementChunkStream;
    }
    get stringChunkStream() {
        if (!this._stringChunkStream) {
            this._stringChunkStream = new StringChunkStream();
        }
        return this._stringChunkStream;
    }

    getOutputDir(dir: string) {
        return path.join(this.cwd, OUTPUT_DIR, dir);
    }
    handleChatCancel(cancleToken: string): void {

    }
    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
    }

    readFile(file: string) {
        return fs.readFileSync(
            path.join(this.getOutputDir(this.serverData?.data.name || ''), file),
            'utf-8'
        );
    }
    async *_dealTimeOut(tips?: string) {
        yield this.elementChunkStream.flushReplaceLast(<p>{tips || '建立连接失败，请在figma插件中重试。'} </p>);
        this.server.destroy();
        return;
    }

    async *_dealHelp() {
        console.log('host11111')

        const host = await this.retriever.hostInfo();
        console.log('host1111122222')
        this.logger.info('isInner', {message: `platform: ${host.platform}`});
        const stream = new StringChunkStream();
        const content = host.platform === 'baidu-saas'
            ? description_saas
            : description;
        yield stream.flush(content);
        return;
    }


    getExtensionFromFilePath(filePath: string) {
        return filePath.split('.').pop() || 'text';
    }

    async *getCodeBlock(chunks: AsyncIterable<string> | string[], file = 'index.module.scss') {
        let str = '';
        yield str;
        for await (const chunk of chunks) {
            str += chunk;
            yield (
                <code-block
                    language={this.getExtensionFromFilePath(file)}
                    actions={['replaceToFile', 'copy', 'showFileInsertDiff']}
                    replaceToFileData={{
                        filePath: path.join(this.getOutputDir(this.serverData?.data.name || ''), file), // 需要绝对路径
                        from: this.readFile(file),
                        to: str, // 插入内容
                        replaceAll: true,
                    }}
                    closed={true}
                >
                    {str}
                </code-block>
            );
        }
    }

    async *_handleCssOptimize() {
        ComateTemplate.impl.init(this as unknown as ProviderInit);
        const chunks = ComateTemplate.impl.askCssReq(this.readFile('/index.module.scss'));
        const stringChunkStream = new StringChunkStream();
        for await (const code of chunks) {
            // for await (const code of this.getCodeBlock(chunks, 'index.module.scss')) {
            // yield this.stringChunkStream.flushReplaceLast(code);
            yield stringChunkStream.flush(code);
        }
    }

    async *_handleReactToVue() {
        ComateTemplate.impl.init(this as unknown as ProviderInit);
        const chunks = ComateTemplate.impl.askReactToVueReq(this.readFile('/index.tsx'));
        const stringChunkStream = new StringChunkStream();
        for await (const code of chunks) {
            // for await (const code of this.getCodeBlock(chunks, 'index.module.scss')) {
            // yield this.stringChunkStream.flushReplaceLast(code);
            yield stringChunkStream.flush(code);
        }
    }

    async *_handleBatchAdoption(data: {files: File[], dirPath: string, name: string, lines: number}) {
        const {files, dirPath, name, lines} = data;
        // 延时删除 防止复制操作还没结束找不到文件
        const options = this.serverData?.data?.option || {};
        // 上报采纳次数与采纳行数到yy报表
        f2cDataReport(ReportType.acceptCount, options.uiframework || 'react', 1, {
            dim4: options.fileOutPutMode || 'comate_basic',
        });
        f2cDataReport(ReportType.acceptLineCount, options.uiframework || 'react', lines, {
            dim4: options.fileOutPutMode || 'comate_basic',
        });

        const that = this;
        // const timeId = setTimeout(() => {
        //     clearTimeout(timeId);
        //     that._deleteTempDir(name);
        // }, 2 * 1000);
        const stream = new ElementChunkStream();
        const filesBlock = files.filter(item => !item.path.startsWith(`/assets`)).map(item => {
            return (
                <p>
                    └───<file-link showIcon line={1} to={`${dirPath}${item.path}`}>
                        {item.path}
                    </file-link>
                </p>
            );
        });

        const text = (
            <p>
                <p>已为你采纳代码：存放目录：{dirPath}</p>
                <br />
                <p>├─{OUTPUT_DIR}</p>
                <p>├──{name}</p>
                <p>{filesBlock}</p>
            </p>
        );
        yield stream.flush(text);
        if (this.workFlow === ComateWorkFlow.Intelligent) {
            return;
        }
    }
    async *handleCommand(commandName: string, data: any): AsyncIterableIterator<TaskProgressChunk> {
        // todo 每执行一次都是新的上下文环境?
        console.log('commandName', commandName);
        console.log('commandNameData', data);

        // const stream = new ElementChunkStream();

        console.log(this.commandContext);

        if (commandName === 'recommend:cssOptimize' && this.readFile('/index.module.scss')) {
            yield* this._handleCssOptimize();
            // yield stream.flushReplaceLast(<p>点击数据为yes</p>);
        }
        else if (commandName === 'recommend:reactToVue' && this.readFile('/index.tsx')) {
            yield* this._handleReactToVue();
        }
        else if (commandName === 'commitMessage:confirmIssue') {
            yield* this._handleBatchAdoption(data);
        }
    }

    _renderBatchAdoption(data: ServerMessageData) {
        this.logger.info(`serverdata filter final ${JSON.stringify(data)}`);
        const tempDir = getTempDir(data.data.name);
        console.log('输入目录', tempDir);
        const outputDir = this.getOutputDir(data.data.name);
        console.log('输出目录', outputDir);
        // getLine
        let lines = 0;
        const files = data.data.files.map(item => {
            lines += getLine(item);
            return {path: item.path};
        });
        return this.elementChunkStream.flush(
            <p>
                <command-button
                    commandName="commitMessage:confirmIssue"
                    action="acceptDir"
                    actionData={{
                        newText: data
                            .data
                            .files
                            .filter(item => !['.png', '.jpg', '.svg'].includes(path.extname(item.path))) // 不上报图片内容
                            .map(item => item.content)
                            .join('\n'),
                        includeHiddenStatsCode: true,
                        from: tempDir,
                        to: outputDir,
                    }}
                    variant="primary"
                    propagation={true}
                    data={{
                        files, // 不上报文件内容，减少进程间通信内容
                        from: tempDir,
                        to: outputDir,
                        dirPath: outputDir,
                        name: data.data.name,
                        lines,
                    }}
                >
                    批量采纳
                </command-button>
                <markdown>
                    {`点击 **批量采纳** 可以将所有已生成文件写入目录\`${outputDir}\`中。\n\n\n`}
                </markdown>
            </p>
        );
    }
    _dealShowCodeGenStatus(data: ServerMessageData) {
        console.log('[provider] code data', data);
        if (data.status === 'success') {
            const name = data.data.name;
            const filesBlock = data
                .data
                ?.files
                .filter(item => !item.path.startsWith(`/assets`))
                .map((item: File) => {
                    return (
                        <p>
                            └──<file-link
                                showIcon
                                line={1}
                                to={getTempDir(`${name}${item.path}`)}
                            >
                                {item.path}
                            </file-link>
                        </p>
                    );
                });

            const text = (
                <p>
                    <p>已为你生成代码:</p>
                    <br />
                    <p>├──{name}</p>
                    <p>{filesBlock}</p>
                </p>
            );
            return this.elementChunkStream.flush(text);
        }
        else {
            return this.elementChunkStream.flush(<p>{data.msg || '很抱歉，代码生成失败'}</p>);
        }
    }
    async _renderRecommendQuestion() {
        const questions = [
            this.serverData?.data.option.cssFramework === 'cssmodules' && {
                title: '请为我优化CSS代码',
                commandName: 'recommend:cssOptimize',
            },
            this.serverData?.data.option.uiFramework === 'react' && {
                title: '请将React代码转换为Vue代码',
                commandName: 'recommend:reactToVue',
            },
        ]
            .filter(item => Boolean(item)) as {title: string, commandName: string}[];

        const jsx = (
            <p>
                {questions.map(item => {
                    return (
                        <command-button
                            commandName={item.commandName}
                            data="no"
                            variant="primary"
                        >
                            {item.title}
                        </command-button>
                    );
                })}
            </p>
        );

        return this.elementChunkStream.flush(jsx);
    }
    _deleteTempDir(componentName: string) {
        const tempDir = getTempDir(componentName);
        console.log('移除目录', tempDir);

        rmDir(tempDir);
    }
}
