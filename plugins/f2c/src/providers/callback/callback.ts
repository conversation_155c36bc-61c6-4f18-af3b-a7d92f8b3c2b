import {DefaultEventsMap, Socket} from 'socket.io';

export enum CodeGenerationStatus {
    Start = 'start',
    Stop = 'stop',
    Finish = 'finish',
    None = 'none',
}
class CallBackLogic {
    codeGenerationStatus = CodeGenerationStatus.None;
    socket: null | Socket<DefaultEventsMap, DefaultEventsMap, DefaultEventsMap, any> = null;
    codeGenerationCallback: ((...params: any) => void) | null = null;
    private static _instance: CallBackLogic;
    static get impl() {
        if (!this._instance) {
            this._instance = new CallBackLogic();
        }
        return this._instance;
    }
}
export default CallBackLogic;
