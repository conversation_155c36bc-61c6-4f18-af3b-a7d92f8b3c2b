import {
    ElementChunkStream,
    FunctionParameterDefinition,
    ProviderInit,
    SkillProvider,
    StringChunkStream,
    type TaskProgressChunk,
} from '@comate/plugin-host';
import {F2CServer, ServerMessageData} from '../server/f2c.js';
import {firstValueFrom} from '../common/rxjs/index.js';
import path, {dirname} from 'path';
import {File} from '../utils/prettier.js';
import {rmDir, writeFiles} from '../utils/fileUtil.js';
import ComateTemplate from '../services/comateTemplate.js';
import {getCodeBlock} from '../common/getCodeBlock.js';
import os from 'os';
import fs, {readFileSync} from 'fs';
import {fileURLToPath} from 'url';
import {Adaptor} from '../services/codeAdaptation/adaptor.js';
import {f2cDataReport, getLine, ReportType} from '../utils/report.js';
import {description, description_saas} from '../help.js';
import RegenerateLogic from './regenerate/regenerate.js';
import CallBackLogic, { CodeGenerationStatus } from './callback/callback.js';
import { F2CBaseProvider } from './f2cBaseProvide.js';
import { GenCodeProvider } from './genCodeById.js';

export enum ComateWorkFlow {
    Basic = 'comate_basic',
    Intelligent = 'comate_intelligent',
}
export const TEMP_DIR = os.tmpdir();
export const OUTPUT_DIR = '.comate-f2c';

export const getTempDir = (dir: string) => {
    return path.join(TEMP_DIR, OUTPUT_DIR, dir);
};

export class F2CSkillProvider extends F2CBaseProvider {
    static description = '根据图层名称、UI框架类型为我输出代码';
    static skillName = 'genCode';
    readonly cwd: string;
    genCodeById: GenCodeProvider;

    constructor(init: ProviderInit) {
        super(init);
        this.cwd = init.cwd;
        this.genCodeById = new GenCodeProvider(init)
    }

    handleChatCancel(cancleToken: string): void {
        this.logger.info(`handleChatCancel: ${cancleToken}`)
        if (cancleToken == 'stop') {
            // CallBackLogic.impl.codeGenerationStatus = CodeGenerationStatus.Stop
            if (CallBackLogic.impl.socket) {
                CallBackLogic.impl.socket.emit('code-generation-stop')
            }
            // 这里有可能codeGenerationCallback还没有赋值，无法给插件回调，插件只能触发超时
            if (CallBackLogic.impl.codeGenerationCallback) {
                CallBackLogic.impl.codeGenerationCallback({
                    status: 'error',
                    error: '生成失败, 用户取消生成代码',
                })
            }
            // 停止执行之后 再执行代码 不会起作用了?
            CallBackLogic.impl.codeGenerationCallback = null
            this.server.destroy()
        }

    }
    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        if (!this.cwd) {
            yield this.elementChunkStream.flush(<p>未检索到可用的工作区，请打开工作区后再进行尝试。</p>);
            return;
        }
        const isRegenerated = this.isRegenerated
        ComateTemplate.impl.init(this as unknown as ProviderInit);
        try {
            // 如果有query 必然不是从f2c启动的任务。 则走通过id的交互。
            if(this.currentContext.query){
                yield *this.genCodeById.execute()
                return;
            }
            // 无连接时也有currentContext.data, 通过有没有method字段兜底处理一下。
            const data = isRegenerated ? RegenerateLogic.impl.cacheData.pluginData : JSON.parse(decodeURIComponent(this.currentContext.data));
            if (!isRegenerated) {
                RegenerateLogic.impl.cacheData.pluginData = data
            }
            console.log('[provider] isRegenerated', isRegenerated, data, RegenerateLogic.impl.cacheData)
            console.log('[provider] currentContext', this.currentContext.data);
            if (!data.method) {
                yield* this._dealHelp();
                return;
            }
            this.workFlow = data.method;
            // mock data
            this.logger.info(`mockdata ${JSON.stringify(data)}`);
            console.log('[provider] currentContext.data', data);
            if (data.mockData) {
                await writeFiles(data.mockData.data.files, getTempDir(data.mockData.data.name));
                yield this._dealShowCodeGenStatus(data.mockData);
                yield this._renderBatchAdoption(data.mockData);
                yield this._renderRecommendQuestion();
                return;
            }
        }
        catch (e) {
            console.log(e);
            yield* this._dealHelp();
            return;
        }
        if (isRegenerated) {
            // 如果文件不完整
            yield this.elementChunkStream.flush(<p>{`代码重新生成成功`}</p>);
            const data = RegenerateLogic.impl.cacheData.filesData
            if (data && this.workFlow === ComateWorkFlow.Intelligent) {
                const adaptor = new Adaptor(
                    this.cwd,
                    this.llm,
                    this.logger,
                    this.elementChunkStream,
                    OUTPUT_DIR
                );
                if (yield* adaptor.executeAdaptor(data)) {
                    yield this._renderBatchAdoption(data);
                }
                return;
            }
            yield this._dealShowCodeGenStatus(data);
            yield this._renderBatchAdoption(data);
            return
        }
        this.server.init(this as any);
        yield this.elementChunkStream.flush(<loading>正在建立连接</loading>);
        const connected = await Promise.race([
            firstValueFrom(this.server.serverConnected$),
            new Promise<boolean>(resolve => {
                setTimeout(() => {
                    resolve(false);
                }, 7000);
            }),
        ]);
        if (!connected) {
            yield* this._dealTimeOut();
            return;
        }

        yield this.elementChunkStream.flushReplaceLast(<p>正在建立连接</p>);
        yield this.elementChunkStream.flush(<p>{`连接建立${connected ? '成功' : '失败'}`}</p>);
        yield this.elementChunkStream.flush(<loading>{`开始生成代码`}</loading>);
        this.serverData = await Promise.race([
            firstValueFrom(this.server.genSuccess$),
            new Promise<boolean>(resolve => {
                setTimeout(() => {
                    resolve(false);
                }, 30 * 1000);
            }),
        ]);
        if (!this.serverData) {
            yield* this._dealTimeOut();
            return;
        }
        this.server.destroy();
        yield this.elementChunkStream.flushReplaceLast(<p>{`开始生成代码`}</p>);
        this.logger.info(`serverData start、${JSON.stringify(this.serverData)}`);
        RegenerateLogic.impl.cacheData.filesData  = this.serverData
        if (this.serverData && this.workFlow === ComateWorkFlow.Intelligent) {
            const adaptor = new Adaptor(
                this.cwd,
                this.llm,
                this.logger,
                this.elementChunkStream,
                OUTPUT_DIR
            );
            if (yield* adaptor.executeAdaptor(this.serverData)) {
                yield this._renderBatchAdoption(this.serverData!);
            }
            return;
        }
        yield this._dealShowCodeGenStatus(this.serverData!);
        yield this._renderBatchAdoption(this.serverData!);
    }
}
