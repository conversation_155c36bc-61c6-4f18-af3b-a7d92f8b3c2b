import logger, {logCyan} from '../utils/logger.js';
import ComateTemplate from './comateTemplate.js';
let nameMapRes: any = {};
let defaults = 0;

const fileExtensionList = [
    '.tsx',
    '.ts',
    '.h',
    '.scss',
    '.m',
    '.swift',
    '.kt',
    '.xml',
    '.java',
    '.jsx',
    '.js',
    '.less',
    '.vue',
];
const nameReplaceKeyRegExp = /f2c_bg_|f2c_|__img|__seg\d+/g;

const nameReplaceKeyRegExp2 = /f2c_bg_|__img|__seg\d+/g;
export interface File {
    content: string;
    path: string;
}
export const getNameMap = async (cssToNameMap: Record<string, string>, idToClassnameMap: Record<string, string>) => {
    logger.info('[F2C] 正在使用Comate语义化分析中...');
    await generateByArray(cssToNameMap, idToClassnameMap);
    return nameMapRes;
};

const generateByArray = async (cssToNameMap: Record<string, string>, idToClassnameMap: any) => {
    const kMaxCount = 6;
    const promiseList = [];
    let count = -1;
    const regex = /^(bg|var|list|item|module|tabList|tabPane|tabSlider)(_|-)/;
    let valArray: Array<string> = [];
    nameMapRes = {};
    console.log('generateByArray', cssToNameMap);

    Object.keys(cssToNameMap).forEach(key => {
        let value = cssToNameMap[key];
        nameMapRes[key] = '';
        if (value) {
            while (regex.test(value)) {
                value = value.replace(regex, '');
            }
            if (count < kMaxCount) {
                valArray.push(value.length >= 20 ? value.slice(0, 20) : value);
                count++;
            }
            else {
                valArray = [];
                count = 0;
                valArray.push(value.length >= 20 ? value.slice(0, 20) : value);
            }
            if (count == kMaxCount) {
                promiseList.push(getAIResp(JSON.stringify(valArray)));
            }
        }
    });
    if (count != -1 && count != kMaxCount) {
        promiseList.push(getAIResp(JSON.stringify(valArray)));
    }
    const valueList: string[] = [];
    let index = 0;
    let defaultNum = 0;
    await Promise.all(promiseList).then(values => {
        const totalValues: Array<string> = [];
        values.forEach(subVals => {
            let valArr: Array<string> = [];
            try {
                valArr = JSON.parse(subVals);
            }
            catch (e) {
                valArr = [];
            }
            if (Array.isArray(valArr)) {
                valArr.forEach(val => {
                    totalValues.push(val);
                });
            }
        });
        console.log('[F2C]返回1', nameMapRes);

        Object.keys(nameMapRes).forEach(key => {
            if (cssToNameMap[key] && idToClassnameMap[key]) {
                let itemValue = totalValues[index] || '';
                // console.log(key, ': ', itemValue)

                // 数字开头则增加前缀
                const startWithNumReg = /^\d/;
                if (startWithNumReg.test(itemValue)) {
                    itemValue = `number_${itemValue}`;
                }

                itemValue = itemValue?.replace(/ /g, '')?.replace(/[\W]/g, '_');

                if (itemValue.length == 0 || itemValue.startsWith('_')) {
                    // itemValue = `${kDefaultName}_${itemValue}${defaults}`
                    itemValue = idToClassnameMap[key];
                    defaults++;
                }

                const regex = /(Group|group|Frame|frame|Rectangle|rectangle)(\s*)(\d+)$/i;
                if (regex.test(itemValue)) {
                    logger.debug('正则替换', itemValue);
                    itemValue = idToClassnameMap[key];
                }

                if (valueList.includes(itemValue)) {
                    logger.debug('存在相同value', itemValue);
                    // itemValue = `${itemValue}_${defaultNum}`
                    if (itemValue == idToClassnameMap[key]) {
                        itemValue = `${idToClassnameMap[key]}_${defaultNum}`;
                    }
                    else {
                        itemValue = idToClassnameMap[key];
                    }
                    defaultNum++;
                }

                valueList.push(itemValue);
                nameMapRes[key] = itemValue;
                index++;
            }
            else {
                nameMapRes[key] = key;
            }
        });
    });
    console.log('[F2C]返回2', nameMapRes);
};

const getAIResp = async (str: string): Promise<string> => {
    logger.debug('输入===', str);

    let answer = '{}';
    answer = {data: {answer: ''}}.data.answer.replace(/\n/g, '');
    const begin = answer.lastIndexOf('[');
    const end = answer.lastIndexOf(']');
    if (begin == -1) {
        answer = '[]';
    }
    else if (begin !== 0) {
        answer = answer.slice(begin, end + 1);
    }
    try {
        if (ComateTemplate.impl.provider) {
            console.log('[F2C] Comate输入', str);
            const res = await ComateTemplate.impl.askCodeReq(str);
            console.log('[F2C] Comate返回结果', res);
            answer = res.replace(/\n/g, '');
            console.log('ComateTemplate_res', res);
            const begin = answer.lastIndexOf('[');
            const end = answer.lastIndexOf(']');
            if (begin == -1) {
                answer = '[]';
            }
            else if (begin !== 0) {
                answer = answer.slice(begin, end + 1);
            }
            console.log('[F2C] Comate分析结果', answer);
        }
    }
    catch (error) {
        answer = '{}';
        logger.info('[F2C] Comate语义化失败', error);
    }
    return answer;
};

export function isSupportedExtension(fileExtension: string): boolean {
    const supportedExtensions = ['.ts', '.tsx', '.swift', '.h', '.m', '.xml', '.java', '.kt'];
    return supportedExtensions.includes(fileExtension);
}
export const replaceVariableNameWithinFile = (
    files: File[],
    idToComponentNames: Record<string, Array<string>> = {},
    uiframework: string,
    nameMap: Record<string, string> = nameMapRes
): File[] => {
    logCyan('[F2C] 组件命名语义化 idToComponentNames', JSON.stringify(idToComponentNames), uiframework);
    for (const file of files) {
        const fileExtension = file.path.match(/\.[^.]+$/)?.[0];
        if (fileExtension && fileExtensionList.includes(fileExtension)) {
            let content: string = file.content;
            Object.entries(nameMap).forEach(([oldName, newName]) => {
                const ajustedName = oldName.replace(nameReplaceKeyRegExp, '');
                // logCyan(`组件命名语义化 ajustedName:${ajustedName} oldName:${oldName} newName:${newName}`)
                if (isSupportedExtension(fileExtension)) {
                    const componentNames = idToComponentNames[ajustedName];
                    if (componentNames && componentNames.length > 0) {
                        componentNames.forEach(oldCmponentName => {
                            const newComponentName = oldCmponentName.replace(
                                ajustedName,
                                newName.charAt(0).toUpperCase() + newName.slice(1)
                            );
                            logCyan('[F2C] 组件命名语义化', oldCmponentName, '=>', newComponentName);
                            content = content.replaceAll(oldCmponentName, newComponentName);
                        });
                    }
                }
                // `${textNode.id}__seg${styledTextSegmentIndex}`
                content = content.replaceAll(
                    new RegExp(`${oldName.replace(nameReplaceKeyRegExp2, '')}|${ajustedName}`, 'ig'),
                    newName
                );
            });
            file.content = content;
        }
        const pathFlags = uiframework === 'android' ? 'ig' : 'g';
        let newpath: string = file.path;
        // console.log(`组件命名语义化 oldPath:${newpath}`)
        Object.entries(nameMap).forEach(([oldName, newName]) => {
            const ajustedName = oldName.replace(nameReplaceKeyRegExp, '');
            const componentNames = idToComponentNames[ajustedName];
            // logCyan(`组件命名语义化 path ajustedName:${ajustedName} oldName:${oldName} newName:${newName}
            //  componentNames:${componentNames}`)
            if (componentNames && componentNames.length > 0) {
                componentNames.forEach(oldCmponentName => {
                    const newComponentName = oldCmponentName.replace(
                        ajustedName,
                        newName.charAt(0).toUpperCase() + newName.slice(1)
                    );
                    newpath = newpath.replaceAll(oldCmponentName, newComponentName);
                });
            }
            newpath = newpath.replaceAll(
                new RegExp(`${oldName.replace(nameReplaceKeyRegExp2, '')}|${ajustedName}`, pathFlags),
                newName
            );
        });
        file.path = newpath;
        // console.log(`组件命名语义化 newpath:${newpath}`)
    }
    logger.info('[F2C] 完成变量语义化替换');
    return files;
};
