import {ElementChunkStream, Logger, SuppotingLlm, TaskProgressChunk, TextModel} from '@comate/plugin-host';
import path from 'path';
import fs from 'fs';
import {calculateFileHash} from '../../utils/fileUtil.js';
import {promptForComponents, promptForProjectInfo} from './promptTemplate.js';
import {findComponents, extractFirstJsonFromMarkdown, findReadme, parseMarkdownToFiles} from './utils.js';
import {File} from '../comateAi.js';
import {writeFile} from 'fs/promises';
import {getTempDir} from '../../providers/f2c.js';
import {ServerMessageData} from '../../server/f2c.js';
export const DEFAULT_META: Meta = {
    packageJson: {
        filePath: '',
        hash: '',
    },
    readme: {
        filePath: '',
        hash: '',
    },
    technologyStack: {},
};
export interface Meta {
    packageJson: {
        filePath: string;
        hash: string;
    };
    readme: {
        filePath: string;
        hash: string;
    };
    technologyStack: any;
}

export class Adaptor {
    cwd: string;
    llm: SuppotingLlm;
    logger: Logger;
    stream: ElementChunkStream;
    metaPath: string;
    meta: Meta = DEFAULT_META;
    packageJsonPath: string;
    readmePath: string;
    demo: string;
    convertedFiles: File[];

    constructor(cwd: string, llm: SuppotingLlm, logger: any, stream: ElementChunkStream, outputDir: string) {
        this.cwd = cwd;
        this.llm = llm;
        this.logger = logger;
        this.stream = stream;
        this.metaPath = path.resolve(this.cwd, outputDir, 'meta.json');
        if (!fs.existsSync(path.resolve(this.cwd, outputDir))) {
            fs.mkdirSync(path.resolve(this.cwd, outputDir));
        }
        this.packageJsonPath = path.resolve(this.cwd, 'package.json');
        this.readmePath = findReadme(this.cwd);
        this.demo = '';
        this.convertedFiles = [];
    }
    // 转换器启动方法 返回boolean表示是否成功执行
    async *executeAdaptor(serverData: ServerMessageData): AsyncGenerator<TaskProgressChunk, boolean, unknown> {
        yield this.stream.flush(<loading>{`开始为项目进行智能适配，请稍后...`}</loading>);
        if (!fs.existsSync(this.packageJsonPath)) {
            yield this.stream.flushReplaceLast(<p>智能适配失败，目录结构不符合要求，未检测到pacakge.json。</p>);
            return false;
        }
        try {
            await this.indexMeta();
            await this.indexDemoComponent();
            const inputs = serverData.data.files.filter(item => !item.path.startsWith(`/assets`));
            this.logger.info(`f2c serverdata filter ${JSON.stringify(inputs)}`);
            this.logger.info(`f2c gettempdir ${getTempDir(serverData!.data.name)}`);
            for await (const input of inputs) {
                this.logger.info(`f2c input ${input.path}`);
                yield* this.runAdaptor(input, getTempDir(serverData!.data.name));
                yield this.stream.flush(<p>{''}</p>);
            }
            serverData!.data.files = serverData.data.files.filter(item => item.path.startsWith(`/assets`));
            serverData!.data.files.push(...this.convertedFiles);
            return true;
        }
        catch (error: any) {
            this.logger.error(JSON.stringify(error));
            yield this.stream.flushReplaceLast(<p>代码适配中出现异常。{error.message}</p>);
            return false;
        }
    }

    /**
     * @param input 待转换的文件路径
     * @param draftDir 输出目录，用于存放转换后的文件
     * @returns 异步生成器，用于输出处理后的代码块
     */
    async *runAdaptor(input: File, draftDir: string) {
        const prompt = this.llm.createPrompt(
            promptForComponents,
            {demo: this.demo, input: input.content, tec: this.getKnowledge()}
        );
        const chunks = this.llm.askForTextStreaming(prompt, {model: TextModel.ErnieBot4Turbo128});
        let res = '';
        for await (const chunk of chunks) {
            res += chunk;
            yield this.stream.flushReplaceLast(<markdown>{res}</markdown>);
        }

        const files = parseMarkdownToFiles(res);
        this.convertedFiles.push(...files);
        // 清理中间文件再写入 保证生成采纳行正确
        fs.unlinkSync(path.join(draftDir, input.path));
        for (const file of files) {
            this.logger.info(`writing ${path.join(draftDir, file.path)}`);
            await writeFile(path.join(draftDir, file.path), file.content, 'utf-8');
        }

    }

    async indexDemoComponent() {
        const components = await findComponents(this.cwd, this.logger);
        const selectedComponent = components[Math.floor(Math.random() * components.length)];
        if (!selectedComponent) {
            return '';
        }
        const fileEntries = Object.entries(selectedComponent.files);
        this.logger.info(`f2c getDemo ${fileEntries}`);
        this.demo = selectedComponent
            .files
            .map((path: string) => {
                const content = fs.readFileSync(path);
                return `\`\`\`${path.split('.').pop()}\n${content}\n\`\`\``;
            })
            .join('\n');
    }

    getKnowledge(): string {
        try {
            const meta = fs.readFileSync(this.metaPath, 'utf-8');
            const knowledge: Meta = JSON.parse(meta);

            return JSON.stringify(knowledge.technologyStack);
        }
        catch {
            return JSON.stringify(DEFAULT_META.technologyStack);
        }
    }
    async indexMeta() {
        if (!fs.existsSync(this.metaPath)) {
            await this.updateMeta();
        }
        const metaInfo = fs.readFileSync(this.metaPath, 'utf-8');
        try {
            const info: Meta = JSON.parse(metaInfo);
            if (!info.packageJson || !info.readme || !info.technologyStack) {
                this.updateMeta();
            }
            this.logger.info('CheckMeta', {
                pacakgeJsonPath: this.packageJsonPath,
                readmePath: this.readmePath,
            });
            const md5ForPackageJson = await calculateFileHash(this.packageJsonPath, 'md5', this.logger);
            const md5ForReadme = await calculateFileHash(this.readmePath, 'md5', this.logger);
            if (md5ForPackageJson !== info.packageJson.hash || md5ForReadme !== info.readme.hash) {
                await this.updateMeta();
            }
        }
        catch (e) {
            await this.updateMeta();
        }
    }

    async updateMeta() {
        this.logger.info('Updatemeta', {
            path: this.packageJsonPath,
        });
        const md5ForPackageJson = await calculateFileHash(this.packageJsonPath, 'md5', this.logger);
        this.logger.info(`updateMeta ${md5ForPackageJson}`);
        const md5ForReadme = await calculateFileHash(this.readmePath, 'md5', this.logger);
        this.logger.info(`updateMeta ${md5ForReadme}`);
        const res = {
            packageJson: {
                filePath: this.packageJsonPath,
                hash: md5ForPackageJson,
            },
            readme: {
                filePath: this.readmePath,
                hash: md5ForReadme,
            },
        };
        const packageJson = fs.readFileSync(this.packageJsonPath, 'utf-8');
        const readme = fs.existsSync(this.readmePath) ? fs.readFileSync(this.readmePath, 'utf-8') : '';

        const prompt = this.llm.createPrompt(
            promptForProjectInfo,
            {pac: packageJson, readme: readme}
        );

        const tec = await this.llm.askForCode(prompt, {model: TextModel.ErnieBot4Turbo128});
        this.logger.info(`parse tec  ${tec}`);
        try {
            const technologyStack = JSON.parse(extractFirstJsonFromMarkdown(tec));
            this.logger.info(`parse technology  ${technologyStack}`);
            fs.writeFileSync(
                this.metaPath,
                JSON.stringify(
                    {
                        ...this.meta,
                        ...res,
                        technologyStack: {
                            ...technologyStack,
                        },
                    },
                    null,
                    4
                )
            );
        }
        catch {
            this.logger.error(`parse technology stack error`);
            throw new Error('parse technology stack error');
        }
    }
}
