import {Logger} from '@comate/plugin-host';
import {File} from '../comateAi.js';
import * as fs from 'fs';
import * as path from 'path';

async function findComponentPaths(rootDir: string): Promise<string[]> {
    const results: string[] = [];
    const queue: string[] = [rootDir];

    // 忽略的目录
    const ignorePatterns = new Set([
        'node_modules',
        '.git',
        'dist',
        'build',
        'coverage',
        '__tests__',
        '__snapshots__',
    ]);
    // 是否在 components 目录下
    let isInComponentsDir = false;
    while (queue.length > 0) {
        const currentDir = queue.shift()!;
        try {
            // 使用 withFileTypes 选项，减少 stat 调用
            const entries = await fs.promises.readdir(currentDir, {withFileTypes: true});
            isInComponentsDir = currentDir.includes('/components');

            for (const entry of entries) {
                const fullPath = path.join(currentDir, entry.name);

                if (entry.isDirectory()) {
                    // 如果是目录，且不在忽略列表中，则加入队列

                    if (!ignorePatterns.has(entry.name)) {
                        console.log(entry.name);
                        queue.push(fullPath);
                    }
                }
                else if (entry.isFile() && isInComponentsDir) {
                    // 如果是文件，且在 components 目录下，则添加到结果中
                    results.push(fullPath);
                }
            }
        }
        catch (error) {
            console.error(`Error reading directory ${currentDir}:`, error);
        }
    }

    return results;
}
export const findComponents = async (dir: string, logger: Logger) => {
    const res = await findComponentPaths(dir);
    logger.info(`findcomponents ${JSON.stringify(res)}`);
    const componentsMap = new Map();
    const extname = [
        '.js',
        '.ts',
        '.jsx',
        '.tsx', // JavaScript 及其 TypeScript 变体，常用于 React 等框架的组件
        '.vue', // Vue.js 组件文件
        '.html', // 虽然 HTML 本身不是组件文件，但在某些框架或库中可能作为模板使用
        '.css', // 标准的 CSS 文件
        '.scss', // Sass 的 SCSS 语法文件
        '.sass', // Sass 的缩进语法文件（尽管在实际项目中 SCSS 语法更为常见）
        '.less', // LESS 文件
        '.styl',
        '.stylus',
    ];
    for (const filePath of res as string[]) {
        // 分组：同一个组件文件放到一起
        const key = getGroupKey(filePath);
        logger.info(`findcomponents key${JSON.stringify(key)}`);
        logger.info(`findcomponents extname${path.extname(filePath)}`);
        if (!extname.includes(path.extname(filePath))) {
            continue;
        }
        if (!componentsMap.has(key)) {
            componentsMap.set(key, []);
        }

        componentsMap.get(key).push(filePath);
        logger.info(`findcomponents push ${filePath}`);
        //  console.log(componentsMap)
    }
    logger.info(`findcomponents beforemap ${JSON.stringify(Array.from(componentsMap.entries()))}`);
    // 转换为数组形式
    const results = Array.from(componentsMap.entries()).map(([key, files]) => ({
        componentName: path.basename(key),
        files,
    }));
    // console.log(results)
    logger.info(`findcomponents res${JSON.stringify(results)}`);
    return results || [];
};

export const getGroupKey = (filePath: string) => {
    const fileNameWithoutExt = path.basename(filePath, path.extname(filePath));
    const fileDir = path.dirname(filePath);
    return path.resolve(fileDir, fileNameWithoutExt.replace(/-/g, '').toLocaleLowerCase());
};

export const findReadme = (dir: string) => {
    const entries = fs.readdirSync(dir, {withFileTypes: true});
    for (const entry of entries) {
        if (!entry.isFile()) {
            continue;
        }
        const filePath = path.join(dir, entry.name);
        const featureFileReg = /^(readme\.md)$/i;
        if (featureFileReg.test(entry.name)) {
            return filePath;
        }
    }
    return '';
};

export function extractFirstJsonFromMarkdown(markdownText: string): string {
    // 正则表达式匹配以 ```json 开头，以 ``` 结尾的代码块
    const regex = /```json\n([\s\S]*?)\n```/;
    const match = regex.exec(markdownText);

    // 如果找到匹配项，返回捕获组中的 JSON 内容（去除首尾空白字符）
    if (match) {
        return match[1].trim();
    }
    return '';
}

export function parseMarkdownToFiles(markdown: string): File[] {
    const codeBlockRegex = /\`\`\`([a-zA-Z]*)\n([\s\S]*?)\n\`\`\`/g;
    const codeBlocks: File[] = [];

    let match;
    while ((match = codeBlockRegex.exec(markdown)) !== null) {
        const content = match[2].trim(); // 代码内容
        const firstLine = content.split('\n')[0]; // 取第一行的注释作为文件名 如果没有// 则将第一行内容作为文件名
        const nameMatch = firstLine.match(/\{([^}]+)\}/);
        if (!nameMatch) {
            throw new Error('文件名解析异常。');
        }
        const path = `/${nameMatch[1]}`;

        codeBlocks.push({content, path});
    }

    return codeBlocks;
}
