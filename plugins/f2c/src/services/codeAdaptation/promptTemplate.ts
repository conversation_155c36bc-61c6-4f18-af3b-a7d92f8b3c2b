export const promptForProjectInfo =
    `你是一个顶级前端开发者，你的任务是根据提供的【package.json】文件和【README.md】，识别项目使用的技术栈。需要提取的技术栈包括并不限于：
- 框架：明确指定使用的框架（如React, Vue, Angular等）。
- 语言：明确是JavaScript (JS) 还是 TypeScript (TS)。
- CSS方案：明确使用的CSS技术（如CSS-in-JS, less，saas等）。
- 图片资源引用方式：图片等资源引入的方式（如import require 等）。
- 命名规范：包括变量、函数、组件等的命名约定。
- 其他相关技术和工具类库的使用：如Redux, Axios, Lodash等。

你需要严格遵循以下规则：
1. 你要严格按照示例用json格式返回结果，不要输出任何其他内容，以下是一个示例：
{"框架": "React", "语言": "TypeScript", "css方案": "less","图片资源引用方式："", "命名规范": "", "其他相关技术和工具类库": ""}
2. 如果你无法明确识别出某个技术，对应字段的值为空字符串。

【pacakge.json】：
\`\`\`
{{pac}}
\`\`\`
【README.md】:
{{readme}}
`;
export const promptForComponents =
    `你是一个顶级前端开发者，你善于按照一定的规范和提供的示例改写前端代码。请以【示例代码】为例对【待改写代码】进行改写，使得改写后的代码与【示例代码】有一样的技术栈和代码风格。其中【示例代码】是一个前端组件的示例代码，【待改写代码】是需要你改写的原始代码。你必须严格遵循下面的步骤进行完整的思考。

第一步：你需要根据【示例代码】判断改写后代码需要使用的“项目技术栈”，包括并不限于以下几点：

-   框架：明确项目使用的框架,如React类组件（类组件或函数组件）, Vue3（vue2或vue3）, Angular 等。
-   语言：明确是 JavaScript (JS) 还是 TypeScript (TS)。
-   CSS 方案：明确示例代码使用的 CSS 技术（注意判断使用行内样式还是外部样式表）。
-   图片等资源引用方式 (import, require 等)。
-   命名规范：包括变量、函数、组件等的命名约定。
-   其他相关技术和工具类库：如 Redux, Axios, Lodash 等。

第二步：你需要判断“项目技术栈”中是否有明确的框架、语言与 css 方案，如果没有则设置“项目技术栈”如下{{tec}}

第三步，你需要仔细分析【示例代码】得到“项目代码风格”，包括但不限于命名规范、缩进方式、注释风格、组件结构、代码分割等方面，并尽量保持一致的风格进行改写。如果没有则设置"项目代码风格"为空。

第四步，你需要根据“项目技术栈”以及“项目代码风格”对【待改写代码】进行代码改写，代码改写时你应该注意下面这几点：

1. 改写时必须尽量参考【示例代码】，同时参考技术栈{{tec}}。如果你认为他们之间有明显冲突，则以【示例代码】为准。
2. 在改写过程中，你需要注意错误处理、边界情况的考虑以及性能优化等方面的最佳实践，确保改写后的代码不仅风格一致，而且质量可靠、性能优异。
3. 你知道示例代码和待生成的代码在业务逻辑上可能没有关联，你应该借鉴示例代码的代码规范与风格而不是代码逻辑。
4. 你应该保证代码转换前后在视觉效果上不应该有任何变化
5. 特别注意：如果需要将其他css方案适配为tailwind，无论示例代码中的风格如何，你必须使用tailwind中的自定义值，如"w-[28px]"、"h-[30px]"、"p-[28px]"等，而不是"w-2"、"h-3"、"p-2"，注意他们之间的区别，从而确保样式的一致性。


【示例代码】： {{demo}}
【待改写代码】：{{input}}

输出结果时你应该注意以下几点：
1. 仅输出改写后的代码，不要输出任何其他内容。
2. 返回的代码内容应该用markdown代码块包裹，并在第一行用注释说明文件名，将文件名放在括号中。以下是一个代码块示例：
\`\`\`javascript\n// {index.js}\n代码内容\n\`\`\`
3. 如果有多段代码，用多个markdown代码块包裹并返回，不要用注释的形式写在同一个文件里。
`;
