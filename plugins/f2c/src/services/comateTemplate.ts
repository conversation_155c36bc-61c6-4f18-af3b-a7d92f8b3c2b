import {ProviderInit, TextModel} from '@comate/plugin-host';
import {TextModelOptions} from '../../../../packages/client/dist/internals/llm.js';
import {ModelOptions} from '../../../../packages/shared-internals/dist/bridge/llm.js';

class ComateTemplate {
    private static _instance: ComateTemplate;
    private codePrompt!: string;
    private cssPrompt!: string;
    private modelConfig = this.getModelConfig();
    static get impl() {
        if (!this._instance) {
            this._instance = new ComateTemplate();
        }
        return this._instance;
    }
    provider!: ProviderInit;
    public init(provider: ProviderInit, prompt?: string) {
        this.provider = provider;
        this.codePrompt = prompt || this.getCodePrompt();
        this.cssPrompt = prompt || this.getCssPrompt();
    }

    private getModelConfig() {
        // * @param openMaxOutput 是否开启最大输出
        // * @param temperature 较高的数值会使输出更加随机，而较低的数值会使其更加集中和确定，建议该参数和Top p只设置1个
        // * @param topP 影响输出文本的多样性，取值越大，生成文本的多样性越强，建议该参数和Temperature只设置1个
        // * @param penaltyScore 通过对已生成的token增加惩罚，减少重复生成的现象。说明：值越大表示惩罚越大，默认1.0，取值范围：[1.0, 2.0]
        return {
            model: TextModel.Default,
            modelOptions: {
                // openMaxOutput: true,
                // topP: 0.75,
                // temperature: 0.2,
                // penaltyScore: 1.0,
            } as ModelOptions,
        } as TextModelOptions;
    }

    private getCodePrompt() {
        return `As an excellent programmer, I would like your assistance in defining variable names for each element in a text list, following proper coding conventions and standards.
                * Step 1: Translate to 4 English words using abbreviations, keep numbers, and convert the first letter to lowercase.
                * Step 2: Remove non-alphanumeric characters from the variable names.
                * Step 3: If any variable name starts with a number, add the prefix "num".
                * Step 4: If the name contains more than 20 characters, truncate it to the first 20 characters.
                * Step 5: Check all variable names. If there are duplicates, append a number to the end.
                * this is a case:
                    question: ["+33.56万 游戏币/荣誉值","我的主页/游戏区/我的动态", "通知",“通知”, "25"]
                    answer: ["num3356GameHonor", "myGameTrends", "notice", "notice1", "num25"].
                  my question is:
                  {{query}}
                  I just need the answer,don't return anything except answer
                `;
    }

    private getCssPrompt() {
        return `
                你是一位专业的SASS工程师，擅长根据已有的CSS代码转换为SCSS 代码，并使用SASS语法进行优化。

                在回答时，请遵循以下指导：
                - 直接使用代码回答问题，不需要使用"当然"、"好的"等开场白。
                - 回答仅包含代码片段，不需要任何多余的文字或多余的代码。
                - 可以使用mixin复用css中的重复样式，例如 flex，font的属性集合
                - 为我请输出最少行数的SCSS代码结果
                用户的问题是：
                请帮我优化这段 CSS 代码，使其更加简洁、高效。
                {{query}}

                根据以上信息提供专业、准确的回答。
            `;
    }

    private getReactToVuePrompt() {
        return `
                你是一位专业的前端开发的Vue工程师，擅长将React代码转换为Vue 代码。

                在回答时，请遵循以下指导：
                - 直接使用代码回答问题，不需要使用"当然"、"好的"等开场白。
                - 回答仅包含代码片段，不需要任何多余的文字或多余的代码。
                用户的问题是：
                请帮我将以下这段代码转换为Vue代码，并使用Vue3.0的代码风格。
                {{query}}

                根据以上信息提供专业、准确的回答。
            `;
    }

    async askCodeReq(data: string) {
        const prompt = this.provider.llm.createPrompt(this.codePrompt, {query: data});
        return await this.provider.llm.askForText(
            prompt,
            this.modelConfig
        );
    }
    askCssReq(query: string) {
        const prompt = this.provider.llm.createPrompt(this.cssPrompt, {query});
        return this.provider.llm.askForTextStreaming(
            prompt,
            this.modelConfig
        );
    }

    askReactToVueReq(query: string) {
        const prompt = this.provider.llm.createPrompt(this.getReactToVuePrompt(), {query});
        return this.provider.llm.askForTextStreaming(
            prompt,
            this.modelConfig
        );
    }
}
export default ComateTemplate;
