# 我的Comate插件

需求卡片：YY-JY-7874

## mock调试

1. 拉取 plugin-f2c 分支，启动vscode 调试
2. 复制以下链接到浏览器打开
> 该链接传递mock 数据，跳过 Figma 的F2C 流程，方便调试
vscode://baidu.comate/open-comate-plus?query=为我输出图层【mock图层】的代码。&plugin=f2c&command=genCode&data={"name":"24年度tab_normal","uiFramework":"react","mockData":{"status":"success","data":{"name":"mock图层","files":[{"path":"/index.tsx","content":"const Mock = () => {};export default Mock"},{"path":"/index.module.scss","content":".xx {}"},{"path":"/assets/1.png","content":""}]}}}
