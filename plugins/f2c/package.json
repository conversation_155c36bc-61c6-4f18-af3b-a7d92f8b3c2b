{"private": true, "name": "@comate-plugin/f2c", "version": "0.8.0", "type": "module", "scripts": {"clean": "rm -rf dist", "dev": "cpd dev", "build": "tsc && cpd build", "watch": "nodemon"}, "dependencies": {"prettier": "2.8.4", "prettier-plugin-vue": "^1.1.6", "socket.io": "^4.8.1", "axios": "^1.7.2"}, "devDependencies": {"@comate/plugin-cli": "^0.9.2", "@comate/plugin-host": "^0.9.2", "@types/prettier": "2.7.3", "nodemon": "^3.1.7", "typescript": "^5.3.2"}, "comate": {"name": "f2c", "version": "1.0.0", "icon": "./assets/icon.svg", "entry": "./dist/index.js", "displayName": "F2C x Comate", "description": "D2C 能力插件", "keyword": [], "capabilities": [{"type": "Fallback", "name": "default", "displayName": "插件介绍", "description": "插件能力介绍"}, {"type": "Skill", "name": "genCode", "displayName": "生成代码", "description": "展示生成代码", "placeholder": "支持输入figma链接，或在figma插件中直接生成代码"}], "configSchema": {"sections": []}}}