import {HelpSkillProvider} from './providers/help.js';
import {PluginSetupContext} from '@comate/plugin-host';
import {SAScanProvider} from './providers/saScanProvider.js';
import {OPGeneratorProvider} from './providers/opGeneratorProvider.js';
import {FeedSaScanProvider} from './providers/feedSaScanProvider.js';
import {OPSelfTestProvider} from './providers/opSelfTestProvider.js';
import {UDFGeneratorProvider} from './providers/udfGeneratorProvider.js';
import {ADSGeneratorProvider} from './providers/adsGeneratorProvider.js';
import {CTSProvider} from './providers/ctsProvider.js';
import {ReqCodeGeneratorProvider} from './providers/reqCodeGeneratorProvider.js';
import {conflictResolutionProvider} from './providers/conflictResolutionProvider.js';
import {AmisWebGen} from './codeGenerator/amisWebGen.js';
import {GRGenerator} from './codeGenerator/grGenerator.js';
import {TransGenerator} from './codeGenerator/transGenerator.js';
import {GdpWebGen} from './codeGenerator/gdpWebGen.js';
import {HarmonyGen} from './codeGenerator/harmonyGenerator.js';
import {MegIDA} from './providers/megIDA.js';

export function setup({registry}: PluginSetupContext) {
    registry.registerSkillProvider('devaux-help', HelpSkillProvider);
    registry.registerSkillProvider('SAScan', SAScanProvider);
    registry.registerSkillProvider('OPGenerator', OPGeneratorProvider);
    registry.registerSkillProvider('FeedSaScan', FeedSaScanProvider);
    registry.registerSkillProvider('OPSelfTest', OPSelfTestProvider);
    registry.registerSkillProvider('UDFGenerator', UDFGeneratorProvider);
    registry.registerSkillProvider('ADSGenerator', ADSGeneratorProvider);
    registry.registerSkillProvider('CTS', CTSProvider);
    registry.registerSkillProvider('ReqCodeGenerator', ReqCodeGeneratorProvider);
    registry.registerSkillProvider('ConflictResolution', conflictResolutionProvider);
    registry.registerSkillProvider('AmisWebGen', AmisWebGen);
    registry.registerSkillProvider('GRGenerator', GRGenerator);
    registry.registerSkillProvider('TransGenerator', TransGenerator);
    registry.registerSkillProvider('GdpWebGen', GdpWebGen);
    registry.registerSkillProvider('HarmonyGenerator', HarmonyGen);
    registry.registerSkillProvider('MegIDA', MegIDA);
}
