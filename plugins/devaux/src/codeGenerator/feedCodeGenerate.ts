import {commonHeadersConfig, kirinToken, kirinUserName, WenXinMessage} from '../consts/configs.js';
import {ProgressInfo} from '../consts/interface.js';
import {fetchUrl, fetchStreamUrl} from '../utils/fetch.js';
import {kirinIsSameSessionURL, ErnieBot4URL, kirinCodeGenerateURL} from '../consts/urls.js';
import {trimCustom, sleep, getReaderStr} from '../utils/util.js';
import {CommonStatus} from '../consts/status.js';
import {startKirinAsyncStrategy, getKirinCodeGenerateAsyncStrategyResult, kirinReturnCheck} from '../utils/kirin.js';
import {renderTemplate} from '../utils/util.js';
import {
    taskNotGenerateErrorInfo,
    send2HiGroupFailMessage,
    taskExecuteErrorInfo,
    callWenXinErrorInfo,
} from '../consts/errorInfo.js';
import {feedStrategyHiGroupNumber, userServiceHiGroupNumber} from '../consts/urls.js';
import {sendMessage2Hi, getReuqestModelStream, updateChunk2Qadc, flushRes2Qadc} from '../utils/util.js';
import {ModelType} from '../consts/modelType.js';

export interface feedCodeGenerateParameters {
    userName: string;
    query: string;
    code: string;
    codePath: string;
    qAndAListStr: string;
    codeConf: any;
    codeConfFilePath: string;
    tableName: 'devaux_multiple_rounds';
    wboxTaskID: string;
}

async function sendFailMessage2Hi(
    codeGeneratedParams: feedCodeGenerateParameters,
    taskID: string,
    failMessage: string,
    hiGroupNumber = feedStrategyHiGroupNumber
) {
    const sendMessage = renderTemplate(send2HiGroupFailMessage, {
        taskID: taskID,
        userName: codeGeneratedParams.userName,
        query: codeGeneratedParams.query,
        conf: codeGeneratedParams.codeConf,
        failMessage: failMessage,
    });
    await sendMessage2Hi(
        hiGroupNumber,
        sendMessage
    );
}

async function isContinuationOfPrevious(url: string, headers: any, data: any) {
    try {
        const result = await fetchUrl(url, headers, data);
        if (!result) {
            return {isSameSession: false, sessionAccessToken: null, qAndAListStr: null};
        }

        const resultJsonObj = JSON.parse(result.result.response);
        if (result.message === 'success' && result.status && resultJsonObj.is_same_session) {
            return {
                isSameSession: resultJsonObj.is_same_session,
                sessionAccessToken: resultJsonObj.access_token,
                qAndAListStr: resultJsonObj.q_and_a_list_str,
            };
        }

        return {isSameSession: false, sessionAccessToken: null, qAndAListStr: null};
    }
    // eslint-disable-next-line
    catch (e) {
    }
    return {isSameSession: false, sessionAccessToken: null, qAndAListStr: null};
}

async function callWenXinWithSameSession(codeGeneratedParams: feedCodeGenerateParameters, accessToken: string) {
    try {
        const url = `${ErnieBot4URL}?access_token=${accessToken}`;
        const qAndAList = JSON.parse(codeGeneratedParams.qAndAListStr);
        const payload: {messages: WenXinMessage[], stream: boolean, max_output_tokens: number} = {
            messages: [],
            stream: true,
            // eslint-disable-next-line
            max_output_tokens: 2048,
        };

        if (qAndAList.length < 2) {
            return null;
        }

        payload.messages.push({
            role: 'user',
            content: qAndAList[0],
        });

        payload.messages.push({
            role: 'assistant',
            content: qAndAList[qAndAList.length - 1],
        });

        payload.messages.push({
            role: 'user',
            content: codeGeneratedParams.query,
        });

        return await fetchStreamUrl(url, commonHeadersConfig, payload);
    }
    // eslint-disable-next-line
    catch (e) {
    }
    return null;
}

// eslint-disable-next-line
async function* sameSessionDeal(
    codeGeneratedParams: feedCodeGenerateParameters,
    sessionAccessToken: string
): AsyncIterableIterator<ProgressInfo> {
    const modelReader = await callWenXinWithSameSession(codeGeneratedParams, sessionAccessToken);
    if (!modelReader) {
        return;
    }
    const decoder = new TextDecoder(); // 默认情况下使用 UTF-8 编码
    let chunkStr = '';
    try {
        yield {type: 'message', message: '\n\n'};
        while (true) {
            await sleep(0.5 * 1000);
            const {done, value} = await modelReader.read();
            if (done) {
                break;
            }
            const valueStr = decoder.decode(value);
            const valueStrList = valueStr.split('\n');
            for (let valueStrItem of valueStrList) {
                // eslint-disable-next-line
                if (valueStrItem.trim().length === 0) {
                    continue;
                }
                // eslint-disable-next-line
                if (!valueStrItem.trim().startsWith('data:')) {
                    continue;
                }
                valueStrItem = trimCustom(valueStrItem, 'data: ');
                // 将 Uint8Array 转换为字符串
                // eslint-disable-next-line
                try {
                    const chunkString = JSON.parse(valueStrItem).result;
                    yield {type: 'message', message: chunkString};
                    chunkStr += chunkString;
                }
                catch (e) {
                    continue;
                }
            }
        }
        const qAndAList: string[] = JSON.parse(codeGeneratedParams.qAndAListStr);
        qAndAList.push(codeGeneratedParams.query);
        qAndAList.push(chunkStr);
        await flushRes2Qadc([
            {
                codeConf: codeGeneratedParams.codeConf,
                codeConfFilePath: codeGeneratedParams.codeConfFilePath,
                qAndAList: JSON.stringify(qAndAList),
                userName: codeGeneratedParams.userName,
            },
        ]);
    }
    catch (error) {
        yield {type: 'status', value: CommonStatus.FAIL};
        return;
    }
    finally {
        modelReader.releaseLock();
    }
    yield {type: 'status', value: CommonStatus.FAIL};
}

// eslint-disable-next-line
export async function* feedCodeGenerateProcess(
    codeGeneratedParams: feedCodeGenerateParameters
): AsyncIterableIterator<ProgressInfo> {
    // let isSuccess = false;
    try {
        const {isSameSession, sessionAccessToken, qAndAListStr} = await isContinuationOfPrevious(
            kirinIsSameSessionURL,
            commonHeadersConfig,
            {
                parameter: JSON.stringify({
                    // eslint-disable-next-line
                    table_name: codeGeneratedParams.tableName,
                    query: codeGeneratedParams.query,
                    codeConf: codeGeneratedParams.codeConf,
                    // eslint-disable-next-line
                    codeConfFilePath: codeGeneratedParams.codeConfFilePath,
                    // eslint-disable-next-line
                    user_name: codeGeneratedParams.userName,
                }),
                name: kirinUserName,
                token: kirinToken,
            }
        );

        if (isSameSession) {
            // eslint-disable-next-line
            codeGeneratedParams.qAndAListStr = qAndAListStr;
            for await (const data of sameSessionDeal(codeGeneratedParams, sessionAccessToken)) {
                // eslint-disable-next-line
                if (data.type === 'message') {
                    yield {type: 'message', message: data.message};
                }
                else if (data.type === 'status') {
                    yield {type: 'status', value: data.value};
                    return;
                }
                else {
                    yield {type: 'flushReplace', message: data.message};
                }
            }
            return;
        }

        const kirinTaskID = await startKirinAsyncStrategy(kirinCodeGenerateURL, {
            parameter: JSON.stringify({
                requirement: codeGeneratedParams.query,
                conf: codeGeneratedParams.codeConf,
                // eslint-disable-next-line
                file_path: codeGeneratedParams.codeConfFilePath,
                // eslint-disable-next-line
                rd_name: codeGeneratedParams.userName,
            }),
            name: kirinUserName,
            token: kirinToken,
        });

        yield {type: 'message', message: '解析您的目标，我将根据您提供的输入\n\n'};
        yield {type: 'message', message: '- 生成配置：\n\n\n'};
        yield {type: 'message', message: `${codeGeneratedParams.codeConf}\n\n`};
        yield {type: 'message', message: '- 需求描述：\n\n\n'};
        yield {type: 'message', message: `${codeGeneratedParams.query}\n\n`};
        yield {type: 'message', message: '解析您的目标，启动生成，全程预估30s，请耐心等待......\n\n'};

        for (let i = 25; i > 0; i = i - 5) {
            yield {type: 'flushReplace', message: '解析您的目标，启动生成，全程预估' + i + 's，请耐心等待......\n\n'};
            await sleep(5 * 1000);
        }

        yield {type: 'flushReplace', message: '\n\n'};

        if (!kirinTaskID) {
            const failMessage = renderTemplate(taskNotGenerateErrorInfo, {groupID: userServiceHiGroupNumber});
            yield {type: 'message', message: `${failMessage}`};
            const sendMessage = renderTemplate(send2HiGroupFailMessage, {
                taskID: kirinTaskID,
                userName: codeGeneratedParams.userName,
                query: codeGeneratedParams.query,
                conf: codeGeneratedParams.codeConf,
                failMessage: failMessage,
            });
            await sendMessage2Hi(
                feedStrategyHiGroupNumber,
                sendMessage
            );
            return;
        }

        const kirinRes = await getKirinCodeGenerateAsyncStrategyResult(kirinTaskID);
        for await (const data of kirinReturnCheck(kirinRes, kirinTaskID)) {
            if (data.type === 'message' && data.message) {
                yield {type: 'message', message: `${data.message}`};
                const sendMessage = renderTemplate(send2HiGroupFailMessage, {
                    taskID: kirinTaskID,
                    userName: codeGeneratedParams.userName,
                    query: codeGeneratedParams.query,
                    conf: codeGeneratedParams.codeConf,
                    failMessage: data.message,
                });
                await sendMessage2Hi(
                    feedStrategyHiGroupNumber,
                    sendMessage
                );
                return;
            }
        }

        const modelReader = await getReuqestModelStream(kirinRes.accessToken, kirinRes.result, kirinRes.models);

        if (!modelReader) {
            const failMessage = renderTemplate(callWenXinErrorInfo, {
                groupID: userServiceHiGroupNumber,
                taskID: kirinTaskID,
            });
            yield {type: 'flushReplace', message: `${failMessage}`};
            await sendFailMessage2Hi(codeGeneratedParams, kirinTaskID, failMessage);
            return;
        }

        let chunkStr = '';
        try {
            yield {type: 'flushReplace', message: '\n\n'};
            if (kirinRes.hint && kirinRes.hint.length) {
                yield {type: 'flushReplace', message: `${kirinRes.hint}\n\n`};
            }
            for await (const data of getReaderStr(modelReader, ModelType.WENXIN)) {
                if (data.type === 'message') {
                    yield {type: 'message', message: data.message};
                    chunkStr += data.message;
                }
            }
            await updateChunk2Qadc(chunkStr, kirinTaskID);
            await flushRes2Qadc([
                {
                    codeConf: codeGeneratedParams.codeConf,
                    codeConfFilePath: codeGeneratedParams.codeConfFilePath,
                    qAndAList: JSON.stringify([kirinRes.result, chunkStr]),
                    userName: codeGeneratedParams.userName,
                },
            ]);
        }
        catch (error) {
            const failMessage = renderTemplate(taskExecuteErrorInfo, {
                groupID: userServiceHiGroupNumber,
                taskID: kirinTaskID,
            });
            yield {type: 'flushReplace', message: `${failMessage}`};
            await sendFailMessage2Hi(codeGeneratedParams, kirinTaskID, failMessage);
        }
        finally {
            modelReader.releaseLock();
            await updateChunk2Qadc(chunkStr, kirinTaskID);
        }
        // isSuccess = true;
        await sendFailMessage2Hi(codeGeneratedParams, kirinTaskID, 'success');
    }
    catch (e) {
        yield {type: 'message', message: '\n\n'};
        const failMessage = renderTemplate(taskExecuteErrorInfo, {
            groupID: userServiceHiGroupNumber,
            taskID: codeGeneratedParams.wboxTaskID,
        });
        yield {type: 'flushReplace', message: `${failMessage}`};
        await sendFailMessage2Hi(codeGeneratedParams, codeGeneratedParams.wboxTaskID, failMessage);
    }
}
