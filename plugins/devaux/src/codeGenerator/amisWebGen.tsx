import {
    FunctionParameterDefinition,
    SkillProvider,
    ProviderInit,
    TaskProgressChunk,
    UserDetail,
    ElementChunkStream,
} from '@comate/plugin-host';

import {DrawElement} from '@comate/plugin-shared-internals';

import {GitUtil} from '../utils/git.js';
import {wboxCallBack, wboxRegister} from '../utils/wbox.js';
import {getReaderStr, renderTemplate, flushRes2Qadc, parsePathLists} from '../utils/util.js';
import {ModelType} from '../consts/modelType.js';
import {taskExecuteErrorInfo, taskExecuteTimeoutErroInfo} from '../consts/errorInfo.js';
import {fetchStreamUrl} from '../utils/fetch.js';
import {commonHeadersConfig, kirinToken, kirinUserName} from '../consts/configs.js';
import {userServiceHiGroupNumber, kirinAmisWebGenURL} from '../consts/urls.js';
import {getKirinSyncRes} from '../utils/kirin.js';
import {FeedStrategyStatus} from '../consts/status.js';
interface Args {
    query: string;
}

export class AmisWebGen extends SkillProvider<Args> {
    static skillName = 'amisWebGen';

    static displayName = '代码生成';

    static description = 'amis代码生成功能';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    private readonly git: GitUtil;
    private readonly cwd: string;

    constructor(init: ProviderInit) {
        super(init);
        this.git = new GitUtil(init.cwd);
        this.cwd = init.cwd;
    }

    // eslint-disable-next-line
    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new ElementChunkStream();
        let user: false | UserDetail = false;
        let taskId: number = 0;
        let isSuccess = false;
        try {
            user = await this.currentUser.requestDetail();
            const moduleName = await this.git.getModuleName(this.cwd);
            // wbox平台注册本次扫描任务
            taskId = await wboxRegister(user, 'amisWebGen', this.currentContext.query, this.git);
            if (this.currentContext.query.trim() === '') {
                yield stream.flush(<p>需求描述不可为空，请添加需求描述</p>);
                isSuccess = true;
                return;
            }

            const {status, kirinTaskID, kirinResponse} = await getKirinSyncRes(kirinAmisWebGenURL, {
                parameter: JSON.stringify({
                    requirement: this.currentContext.query,
                    conf: this.currentContext.selectedCode,
                    conf_file: this.currentContext.activeFilePath,
                    scene: 'page_generate',
                    // eslint-disable-next-line
                    rd: user ? user.name : 'unknown',
                    moduleName: moduleName,
                }),
                name: kirinUserName,
                token: kirinToken,
            });

            if (status === FeedStrategyStatus.ERROR_STRATEGY_FAIL) {
                const failMessage = renderTemplate(taskExecuteErrorInfo, {
                    groupID: userServiceHiGroupNumber,
                    taskID: kirinTaskID,
                });
                yield stream.flush(<p>{failMessage}</p>);
                return;
            }
            else if (status === FeedStrategyStatus.ERROR_TIMEOUT) {
                const failMessage = renderTemplate(taskExecuteTimeoutErroInfo, {
                    groupID: userServiceHiGroupNumber,
                    taskID: kirinTaskID,
                });
                yield stream.flush(<p>{failMessage}</p>);
                return;
            }

            if (kirinResponse.length === 0) {
                const failMessage = renderTemplate(taskExecuteErrorInfo, {
                    groupID: userServiceHiGroupNumber,
                    taskID: kirinTaskID,
                });
                yield stream.flushReplaceLast(<p>{failMessage}</p>);
                return;
            }

            let chunkStr = '';
            let result: DrawElement[] = [];
            for (const [index, kirinRes] of kirinResponse.entries()) {
                let singleChunkStr = '';
                let modelReader: ReadableStreamDefaultReader<Uint8Array> | null | undefined = null;
                const models = kirinRes.models;
                if (!models.url) {
                    if (kirinRes.add_before && kirinRes.add_before.length !== 0) {
                        result = result.concat(await parsePathLists(kirinRes.add_before, this.cwd, kirinRes.file_path));
                        if (kirinRes.add_after && kirinRes.add_after.length !== 0) {
                            result.push(<markdown>{kirinRes.add_before}</markdown>);
                        }
                    }
                    continue;
                }
                else if (models.url.includes('deepseek') || models.url.includes('10.55.119.216')) {
                    modelReader = await fetchStreamUrl(models.url, models.headers, models.data, models.method);
                }
                else {
                    modelReader = await fetchStreamUrl(
                        `${models.url}?access_token=${models.params.access_token}`,
                        commonHeadersConfig,
                        models.data,
                        models.method
                    );
                }

                if (!modelReader) {
                    const failMessage = renderTemplate(taskExecuteErrorInfo, {
                        groupID: userServiceHiGroupNumber,
                        taskID: kirinTaskID,
                    });
                    yield stream.flushReplaceLast(<p>{failMessage}</p>);
                    return;
                }

                if (kirinRes.add_before && kirinRes.add_before.length !== 0) {
                    result.push(<markdown>{kirinRes.add_before}</markdown>);
                    yield stream.flush(<markdown>{kirinRes.add_before}</markdown>);
                }

                try {
                    let modelType: ModelType = ModelType.WENXIN;

                    if (models.url.includes('deepseek') || models.url.includes('10.55.119.216')) {
                        modelType = ModelType.DEEPSEEK;
                    }
                    else {
                        modelType = ModelType.WENXIN;
                    }

                    let isFirstFlush = true;
                    for await (const data of getReaderStr(modelReader, modelType)) {
                        if (data.message !== null) {
                            chunkStr += data.message;
                            singleChunkStr += data.message;
                        }
                        if (isFirstFlush) {
                            yield stream.flush(<markdown>{singleChunkStr}</markdown>);
                            isFirstFlush = false;
                        }
                        else {
                            yield stream.flushReplaceLast(<markdown>{singleChunkStr}</markdown>);
                        }
                    }

                    singleChunkStr += '\n\n';

                    result = result.concat(await parsePathLists(singleChunkStr, this.cwd, kirinRes.file_path));

                    yield stream.flush(<br></br>);
                    // await updateChunk2Qadc(chunkStr, kirinTaskID, 'amis_gen_record');
                }
                catch (error) {
                    const failMessage = renderTemplate(taskExecuteErrorInfo, {
                        groupID: userServiceHiGroupNumber,
                        taskID: kirinTaskID,
                    });
                    yield stream.flush(<p>{failMessage}</p>);
                }
                finally {
                    modelReader.releaseLock();
                    await flushRes2Qadc([{
                        kirin_id: kirinTaskID,
                        order_id: `${kirinTaskID}_${index}`,
                        model: JSON.stringify(kirinRes.models),
                        add_before: kirinRes.add_before,
                        add_after: kirinRes.add_after,
                        result: singleChunkStr,
                        rd_name: user ? user.name : 'unknown',
                        module: moduleName,
                    }], 'code_gen_record');
                    if (kirinRes.add_after && kirinRes.add_after.length !== 0) {
                        result.push(<markdown>{kirinRes.add_after}</markdown>);
                        yield stream.flush(<markdown>{kirinRes.add_after}</markdown>);
                    }
                }
            }

            yield stream.flushReplaceAll(result);
            isSuccess = true;
        }
        catch (e) {
            const failMessage = renderTemplate(taskExecuteErrorInfo, {
                groupID: userServiceHiGroupNumber,
                taskID: taskId,
            });
            yield stream.flush(<p>{failMessage}</p>);
        }
        finally {
            await wboxCallBack({
                taskId: taskId ? taskId : 0,
                status: isSuccess ? 2 : 1,
                caller: user ? user.name : 'unknown',
            });
        }
    }
}
