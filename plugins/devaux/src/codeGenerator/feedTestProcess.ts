import {qadcHeadersConfig} from '../consts/configs.js';
import {CommonStatus, FeedStrategyStatus} from '../consts/status.js';
import {qadcSelectURL, qadcSelectCodeGenStageInfoURL} from '../consts/urls.js';
import {fetchUrl} from '../utils/fetch.js';
import {startKirinAsyncStrategy} from '../utils/kirin.js';
import {kirinFeedTestURL, feedStrategyHiGroupNumber} from '../consts/urls.js';
import {sendMessage2Hi, sleep} from '../utils/util.js';
import {ProgressInfo} from '../consts/interface.js';

export interface feedTestParameters {
    userName: string;
    sample: string;
    code: string;
    codePath: string;
    qAndAListStr: string;
    codeConf: any;
    codeConfFilePath: string;
    module: string | null;
}

async function lastQueryRes(headers: any, data: any) {
    try {
        const result = await fetchUrl(qadcSelectURL, headers, data);
        if (result && result.message === 'success' && result.code === 0) {
            return result.data.details[0];
        }
        return null;
    }
    // eslint-disable-next-line
    catch (e) {
    }
    return null;
}

async function queryResult(hopeStatus: string, data: any) {
    let running = true;
    const timeout = 720 * 1000; // 180s

    const timer = setTimeout(() => {
        running = false;
    }, timeout);
    try {
        // eslint-disable-next-line
        while (running) {
            await sleep(1 * 1000);
            const result = await fetchUrl(qadcSelectCodeGenStageInfoURL, qadcHeadersConfig, data);
            if (!result) {
                continue;
            }

            // eslint-disable-next-line
            if (result.code !== 0 || result.data.total <= 0) {
                continue;
            }

            // eslint-disable-next-line
            const source = result.data.records[0]._source;
            // eslint-disable-next-line
            if (source.length === 0 || Number(source[0]) < Number(hopeStatus[0])) {
                continue;
            }

            return {status: FeedStrategyStatus.SUCCESS, message: source.msg};
        }
    }
    // eslint-disable-next-line
    catch (e) {
        return {status: FeedStrategyStatus.ERROR_STRATEGY_FAIL, message: '策略执行异常，插件中止进程'};
    }
    finally {
        clearTimeout(timer);
    }

    return {status: FeedStrategyStatus.ERROR_TIMEOUT, message: '阶段任务超时，插件中止进程'};
}

// eslint-disable-next-line
export async function* feedTestProcess(testParameters: feedTestParameters): AsyncIterableIterator<ProgressInfo> {
    const sample = testParameters.sample.substring(6);
    const code = '```cpp\n' + testParameters.code + '\n```';
    const codePath = testParameters.codePath;
    const userName = testParameters.userName;
    const module = testParameters.module;

    const recentRes = await lastQueryRes(
        qadcHeadersConfig,
        {
            // eslint-disable-next-line
            sql: `select * from "devaux_multiple_rounds" where userName='${userName}' ORDER BY "@timestamp" DESC LIMIT 1`,
        }
    );

    if (!recentRes) {
        return;
    }

    const codeConf = recentRes.codeConf;
    const codeConfFilePath = recentRes.codeConfFilePath;
    // eslint-disable-next-line
    testParameters.codeConf = codeConf;
    // eslint-disable-next-line
    testParameters.codeConfFilePath = codeConfFilePath;

    if (recentRes.qAndAList) {
        try {
            const firstPrompt = JSON.parse(recentRes.qAndAList)[0];
            const kirinTaskId = await startKirinAsyncStrategy(kirinFeedTestURL, {
                parameter: JSON.stringify({
                    prompt: firstPrompt,
                    code: code,
                    // eslint-disable-next-line
                    code_path: codePath,
                    sample: sample,
                    rd: userName,
                    module: module,
                    conf: codeConf,
                    // eslint-disable-next-line
                    conf_path: codeConfFilePath,
                }),
                name: 'zhaomingming04',
                token: 'e33cc284830b4ba98a46e878afc16114',
            });

            let codeHasError = true;
            for (let i = 3; i <= 5; ++i) {
                // eslint-disable-next-line
                switch (i) {
                    case 3:
                        yield {type: 'message', message: '* 1. 正在检测代码异常……'};
                        break;
                    case 4:
                        // eslint-disable-next-line
                        if (codeHasError) {
                            yield {type: 'message', message: '* 2. 正在修复代码……'};
                        }
                        break;
                    case 5:
                        // eslint-disable-next-line
                        if (codeHasError) {
                            yield {type: 'message', message: '* 3. 正在执行自动化测试……'};
                        }
                        else {
                            yield {type: 'message', message: '* 2. 正在执行自动化测试……'};
                        }
                        break;
                    default:
                }
                // eslint-disable-next-line
                if (i === 4 && !codeHasError) {
                    continue;
                }
                const {status, message} = await queryResult(
                    i.toString(),
                    {
                        // eslint-disable-next-line
                        sql: `select * from "code_gen_stage_info" where kirin_task_id='${kirinTaskId}' and stage like '${i.toString()}%' order by stage desc`,
                    }
                );
                // eslint-disable-next-line
                if (status === FeedStrategyStatus.SUCCESS) {
                    // eslint-disable-next-line
                    if (i === 3 && message.length === 0) {
                        codeHasError = false;
                    }
                    yield {type: 'message', message: '\n\n'};
                    // eslint-disable-next-line
                    if ((i === 3 || i === 4 || i === 5) && message.length !== 0) {
                        yield {type: 'message', message: message};
                        yield {type: 'message', message: '\n\n'};
                    }
                }
                else {
                    yield {type: 'message', message: '\n\n'};
                    yield {type: 'message', message: message};
                    yield {type: 'message', message: '\n\n'};
                    await sendMessage2Hi(feedStrategyHiGroupNumber, message);
                    yield {type: 'status', value: CommonStatus.FAIL};
                }
            }
        }
        catch (err) {
            yield {type: 'status', value: CommonStatus.FAIL};
        }
    }
    else {
        yield {type: 'status', value: CommonStatus.FAIL};
    }
}
