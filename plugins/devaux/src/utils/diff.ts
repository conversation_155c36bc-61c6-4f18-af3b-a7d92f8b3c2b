import {GitUtil} from './git.js';
import {FileSystem} from '@comate/plugin-host';

function mapToObject(map: Map<any, any>): any {
    const obj: any = {};
    for (const [key, value] of map) {
        if (value instanceof Map) {
            obj[key] = mapToObject(value); // 递归转换子Map
        }
        else {
            obj[key] = value; // 直接赋值
        }
    }
    return obj;
}

export async function getCtsDiffMsg(gitUtil: GitUtil, fs: FileSystem) {
    try {
        const result = await gitUtil.getCtsChangedInfos(fs);
        const nestedObject = mapToObject(result);
        const jsonString = JSON.stringify(nestedObject, null, 2);
        return jsonString;
    }
    catch (e) {
        return '';
    }
}
