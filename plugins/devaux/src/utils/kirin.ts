import {fetchUrl} from './fetch.js';
import {commonHeadersConfig} from '../consts/configs.js';
import {kirinAsynResCallBackURL, userServiceHiGroupNumber} from '../consts/urls.js';
import {FeedStrategyStatus} from '../consts/status.js';
import {getJsonValue, renderTemplate, sleep} from './util.js';
import {taskExecuteErrorInfo, taskExecuteTimeoutErroInfo} from '../consts/errorInfo.js';

export interface kirinAsynResStruct {
    status: FeedStrategyStatus;
    result: string | null;
    accessToken: string | null;
    hint: string | null;
    message: string | null;
    intent: string | null;
    models: any | null;
}

export interface kirinResBaseStruct {
    models: any | null;
    file_path: string[];
    add_before: string | null;
    add_after: string | null;
    download_url: string | null;
}

export interface kirinSyncResStruct {
    status: FeedStrategyStatus;
    kirinTaskID: string;
    kirinResponse: kirinResBaseStruct[];
    message: string | null;
}

export interface kirinAsyncResStruct {
    status: FeedStrategyStatus;
    kirinResponse: kirinResBaseStruct[];
    message: string | null;
}

export async function startKirinAsyncStrategy(url: string, data: any) {
    const result = await fetchUrl(url, commonHeadersConfig, data);
    if (result && result.message === 'success' && result.status) {
        return result.result.taskId;
    }
    return null;
}

export async function getKirinSyncRes(url: string, data: any): Promise<kirinSyncResStruct> {
    let running = true;
    const timeout = 180 * 1000; // 180s

    const timer = setTimeout(() => {
        running = false;
    }, timeout);

    try {
        // eslint-disable-next-line
        while (running) {
            await sleep(1 * 1000);
            const result = await fetchUrl(url, commonHeadersConfig, data, 'POST');

            if (!result) {
                continue;
            }

            if (result.status && result.result.taskStatus === 3) {
                // eslint-disable-next-line
                try {
                    const resultJsonObj = JSON.parse(result.result.response);
                    // eslint-disable-next-line
                    if (resultJsonObj.status === 0) {
                        return {
                            status: FeedStrategyStatus.SUCCESS,
                            kirinTaskID: result.result.taskId,
                            kirinResponse: resultJsonObj.response,
                            message: '',
                        };
                    }
                    else {
                        return {
                            status: FeedStrategyStatus.ERROR_STRATEGY_FAIL,
                            kirinTaskID: result.result.taskId,
                            kirinResponse: [],
                            message: resultJsonObj.message,
                        };
                    }
                }
                catch (e) {
                    return {
                        status: FeedStrategyStatus.ERROR_STRATEGY_FAIL,
                        kirinTaskID: result.result.taskId,
                        kirinResponse: [],
                        message: '',
                    };
                }
            }
            if (result.status && result.result.taskStatus === 4) {
                return {
                    status: FeedStrategyStatus.ERROR_STRATEGY_FAIL,
                    kirinTaskID: result.result.taskId,
                    kirinResponse: [],
                    message: '',
                };
            }
        }
    }
    // eslint-disable-next-line
    catch (e) {
    }
    finally {
        clearTimeout(timer);
    }
    return {
        status: FeedStrategyStatus.ERROR_TIMEOUT,
        kirinTaskID: '-1',
        kirinResponse: [],
        message: '',
    };
}

export async function getKirinSyncGDPPathList(url: string, data: any) {
    const timeout = 20 * 1000; // 20s
    const endTime = Date.now() + timeout;
    // eslint-disable-next-line
    while (Date.now() < endTime) {
        await sleep(1 * 1000);
        const result = await fetchUrl(url, commonHeadersConfig, data, 'POST');

        // 0-排队中 1-新建 2-执行中 3-执行成功 4-执行失败 5-强制终止
        if (!result || result.result.taskStatus <= 2) {
            continue;
        }

        if (result.status && result.result.taskStatus === 3) {
            // eslint-disable-next-line
            const resultJsonObj = JSON.parse(result.result.response);
            // eslint-disable-next-line
            if (resultJsonObj.status === 0) {
                return {
                    status: FeedStrategyStatus.SUCCESS,
                    kirinTaskID: result.result.taskId,
                    PathResponse: resultJsonObj.response.path_list,
                };
            }
            else {
                return {
                    status: FeedStrategyStatus.ERROR_STRATEGY_FAIL,
                    kirinTaskID: result.result.taskId,
                    PathResponse: null,
                };
            }
        }

        if (result.status && result.result.taskStatus === 4) {
            return {
                status: FeedStrategyStatus.ERROR_STRATEGY_FAIL,
                kirinTaskID: result.result.taskId,
                PathResponse: null,
            };
        }
    }
    return {
        status: FeedStrategyStatus.ERROR_TIMEOUT,
        kirinTaskID: '-1',
        PathResponse: null,
    };
}

export async function getKirinAsyncStrategyResult(taskId: string): Promise<kirinAsyncResStruct> {
    const timeout = 180 * 1000; // 180s
    const endTime = Date.now() + timeout;

    while (Date.now() < endTime) {
        await sleep(1 * 1000);
        const result = await fetchUrl(`https://kirin.baidu-int.com/api/task/${taskId}`, commonHeadersConfig, {}, 'GET');

        if (!result || result.result.taskStatus <= 2) {
            continue;
        }

        if (result.status && result.result.taskStatus === 4) {
            return {
                status: FeedStrategyStatus.ERROR_STRATEGY_FAIL,
                kirinResponse: [],
                message: result.result.stdout,
            };
        }

        if (result.status && result.result.taskStatus === 3) {
            const resultJsonObj = JSON.parse(result.result.response);
            if (resultJsonObj.status === 0) {
                return {
                    status: FeedStrategyStatus.SUCCESS,
                    kirinResponse: resultJsonObj.response,
                    message: '',
                };
            }
            else {
                return {
                    status: FeedStrategyStatus.ERROR_STRATEGY_FAIL,
                    kirinResponse: [],
                    message: resultJsonObj.message,
                };
            }
        }
    }

    return {
        status: FeedStrategyStatus.ERROR_TIMEOUT,
        kirinResponse: [],
        message: '',
    };
}

export async function getKirinCodeGenerateAsyncStrategyResult(taskId: string): Promise<kirinAsynResStruct> {
    let running = true;
    const timeout = 180 * 1000; // 180s

    const timer = setTimeout(() => {
        running = false;
    }, timeout);

    try {
        // eslint-disable-next-line
        while (running) {
            await sleep(1 * 1000);
            const result = await fetchUrl(`${kirinAsynResCallBackURL}/${taskId}`, commonHeadersConfig, {}, 'GET');

            if (!result) {
                continue;
            }

            if (result.status && result.result.taskStatus === 3) {
                // eslint-disable-next-line
                try {
                    const resultJsonObj = JSON.parse(result.result.response);
                    // eslint-disable-next-line
                    if (resultJsonObj.status === 0) {
                        return {
                            status: FeedStrategyStatus.SUCCESS,
                            result: getJsonValue(resultJsonObj.response, 'prompt'),
                            accessToken: getJsonValue(resultJsonObj.response, 'access_token'),
                            hint: getJsonValue(resultJsonObj.response, 'hint'),
                            message: getJsonValue(resultJsonObj.response, 'message'),
                            intent: getJsonValue(resultJsonObj.response, 'intent'),
                            models: getJsonValue(resultJsonObj.response, 'models'),
                        };
                    }
                    else {
                        return {
                            status: FeedStrategyStatus.ERROR_STRATEGY_FAIL,
                            result: getJsonValue(resultJsonObj.response, 'prompt'),
                            accessToken: getJsonValue(resultJsonObj.response, 'access_token'),
                            hint: getJsonValue(resultJsonObj.response, 'hint'),
                            message: getJsonValue(resultJsonObj, 'message'),
                            intent: getJsonValue(resultJsonObj.response, 'intent'),
                            models: getJsonValue(resultJsonObj.response, 'models'),
                        };
                    }
                }
                catch (e) {
                    return {
                        status: FeedStrategyStatus.ERROR_STRATEGY_FAIL,
                        result: null,
                        accessToken: null,
                        hint: null,
                        message: null,
                        intent: null,
                        models: null,
                    };
                }
            }
            if (result.status && result.result.taskStatus === 4) {
                return {
                    status: FeedStrategyStatus.ERROR_STRATEGY_FAIL,
                    result: null,
                    accessToken: null,
                    hint: null,
                    message: null,
                    intent: null,
                    models: null,
                };
            }
        }
    }
    // eslint-disable-next-line
    catch (e) {
    }
    finally {
        clearTimeout(timer);
    }
    return {
        status: FeedStrategyStatus.ERROR_TIMEOUT,
        result: null,
        accessToken: null,
        hint: null,
        message: null,
        intent: null,
        models: null,
    };
}

export function* kirinReturnCheck(kirinRes: kirinAsynResStruct, kirinTaskID: string) {
    if (kirinRes.status === FeedStrategyStatus.ERROR_STRATEGY_FAIL && !kirinRes.message) {
        const failMessage = renderTemplate(taskExecuteErrorInfo, {
            groupID: userServiceHiGroupNumber,
            taskID: kirinTaskID,
        });
        yield {type: 'message', message: failMessage};
        return;
    }
    else if (kirinRes.status === FeedStrategyStatus.ERROR_STRATEGY_FAIL) {
        const failMessage = kirinRes.message
            ? kirinRes.message
            : renderTemplate(taskExecuteErrorInfo, {groupID: userServiceHiGroupNumber, taskID: kirinTaskID});
        yield {type: 'message', message: failMessage};
        return;
    }
    else if (kirinRes.status === FeedStrategyStatus.ERROR_TIMEOUT) {
        const failMessage = renderTemplate(taskExecuteTimeoutErroInfo, {
            groupID: userServiceHiGroupNumber,
            taskID: kirinTaskID,
        });
        yield {type: 'message', message: failMessage};
        return;
    }
    yield {type: 'status', value: FeedStrategyStatus.SUCCESS};
}
