/* eslint-disable max-depth */
import {IncomingMessage} from 'http';
import * as vscode from 'vscode';
import {splitWithMaxsplit, splitLines, mapToJson} from './util.js';

export class SSEProcessor<T> {
    error: boolean = false;
    errorMsg: string = '';

    constructor(
        private readonly body: IncomingMessage,
        private readonly cancellationToken?: vscode.CancellationToken
    ) {}

    async *read() {
        let chunk = '';
        const decoder = new TextDecoder();
        try {
            for await (const data of this.body) {
                if (this.maybeCancel()) {
                    return;
                }
                const decodedData = decoder.decode(data, {
                    stream: true,
                });

                for (const line of splitLines(decodedData, true)) {
                    chunk += line;
                    if (chunk.endsWith('\r\r') || chunk.endsWith('\n\n') || chunk.endsWith('\r\n\r\n')) {
                        yield chunk;
                        chunk = '';
                    }
                }
            }
        }
        catch (e) {
            return;
        }

        if (chunk) {
            yield chunk;
        }
    }

    // eslint-disable-next-line
    async *processSSE(): AsyncGenerator<T, void, void> {
        try {
            for await (const chunk of this.read()) {
                const event = new Map<string, string>();
                for (const line of chunk.split(/\r?\n|\r/)) {
                    // eslint-disable-next-line
                    if (!line.trim() || line.startsWith(':')) {
                        continue;
                    }
                    const data = splitWithMaxsplit(line, ':', 1);
                    const field = data[0];
                    let value = '';

                    // eslint-disable-next-line
                    if (field !== 'id' && field !== 'event' && field !== 'data' && field !== 'retry') {
                        continue;
                    }

                    // eslint-disable-next-line
                    if (data.length > 1) {
                        // eslint-disable-next-line
                        if (data[1].startsWith(' ')) {
                            value = data[1].trimStart();
                        }
                        else {
                            value = data[1];
                        }
                    }
                    else {
                        value = '';
                    }

                    // eslint-disable-next-line
                    if (field === 'data') {
                        // eslint-disable-next-line
                        if (event.has('data')) {
                            let tmpData = event.get('data');
                            event.set('data', tmpData += value + '\n');
                        }
                        else {
                            event.set('data', value + '\n');
                        }
                    }
                    else {
                        event.set(field, value);
                    }

                    const tmpData = event.get('data');
                    // eslint-disable-next-line
                    if (!tmpData || tmpData.length === 0) {
                        continue;
                    }

                    if (tmpData.endsWith('\n')) {
                        event.set('data', tmpData.substring(0, tmpData.length - 1));
                    }

                    if (!event.has('event')) {
                        event.set('event', 'message');
                    }

                    try {
                        const jsonData = JSON.stringify(mapToJson(event));
                        const res = JSON.parse(jsonData);
                        event.clear();
                        yield res;
                    }
                    catch (e: any) {
                        this.cancel();
                        this.error = true;
                        this.errorMsg = line;
                        return;
                    }
                }
            }
        }
        catch (e: any) {
            if (this.maybeCancel()) {
                return;
            }
            throw e;
        }
    }

    maybeCancel() {
        const cancel = !!this.cancellationToken?.isCancellationRequested;
        if (cancel) {
            this.cancel();
        }
        return cancel;
    }

    cancel() {
        this.body.destroy();
    }
}
