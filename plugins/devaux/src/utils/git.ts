import {diffLines, Change} from 'diff';
import {SimpleGit, simpleGit} from 'simple-git';
import {FileSystem} from '@comate/plugin-host';
import {getExtName, getDirname} from './fs.js';
import {getFunctionDeclareStr} from './treeSitter.js';
import fs from 'node:fs/promises';
import path from 'path';
import Parser from 'web-tree-sitter';
import {languageExtMap} from '../consts/languageParser.js';

export class GitUtil {
    readonly git: SimpleGit;

    constructor(cwd: string) {
        this.git = simpleGit(cwd);
    }

    async filterDiffFile(filePathList: string[]) {
        const fileterFilePath = filePathList.filter(filePath => {
            if (filePath.startsWith('env/test')) {
                return true;
            }

            if (
                filePath.endsWith('.cpp') || filePath.endsWith('.h')
                || filePath.endsWith('.hpp') || filePath.endsWith('.cxx')
            ) {
                return true;
            }

            if (
                filePath.endsWith('.cpp') || filePath.endsWith('.h')
                || filePath.endsWith('.hpp') || filePath.endsWith('.cxx')
            ) {
                return true;
            }

            return false;
        });

        return fileterFilePath;
    }

    // eslint-disable-next-line
    async getCtsChangedInfos(fs: FileSystem) {
        const statusSummary = await this.git.status();
        const createFiles = await this.filterDiffFile(statusSummary.created);
        const modifiedFiles = await this.filterDiffFile(statusSummary.modified);
        const deletedFies = await this.filterDiffFile(statusSummary.deleted);
        const notAddedFiels = await this.filterDiffFile(statusSummary.not_added);

        const result: Map<string, any> = new Map();
        for (const changedInfo of modifiedFiles) {
            let functionInfo: string[] = [];
            const fileContent = (await fs.readFile(changedInfo, 'utf8')).toString();
            const {changedLineNumbers, deletedLineNumbers} = await this.getModifiedLinesOfFile(
                changedInfo,
                fileContent
            );

            try {
                // 获取函数相关信息
                if (getExtName(changedInfo) === '.cpp' || getExtName(changedInfo) === '.cxx') {
                    const dirname = getDirname();
                    // wasm 需要，不加会报错
                    // eslint-disable-next-line
                    global.__dirname = dirname;
                    const wasmFile = path.resolve(dirname, 'tree-sitter.wasm');
                    await Parser.init({
                        locateFile() {
                            return wasmFile;
                        },
                    });

                    const parser = new Parser();
                    const parserWasm = languageExtMap.get('.cpp');
                    // eslint-disable-next-line
                    if (parserWasm) {
                        const Lang = await Parser.Language.load(parserWasm);
                        parser.setLanguage(Lang);
                    }

                    const tree = parser.parse(fileContent);
                    functionInfo = getFunctionDeclareStr(tree.rootNode, Array.from(changedLineNumbers));
                }
            }
            // eslint-disable-next-line
            catch (e) {
            }

            const changedContent: string[] = [];
            const deletedContent: string[] = [];
            const newFileContent = fileContent.split('\n');
            for (const lineNumber of changedLineNumbers) {
                changedContent.push(newFileContent[lineNumber - 1]);
            }
            const oldContent = (await this.git.show(['HEAD:' + changedInfo])).split('\n');
            for (const lineNumber of deletedLineNumbers) {
                deletedContent.push(oldContent[lineNumber - 1]);
            }

            if (functionInfo.length === 0) {
                result.set(changedInfo, {
                    // eslint-disable-next-line
                    diff_block: [{
                        content: {
                            change: changedContent,
                            delete: deletedContent,
                        },
                    }],
                });
            }
            else {
                result.set(changedInfo, {
                    functions: functionInfo,
                    // eslint-disable-next-line
                    diff_block: [{
                        content: {
                            change: changedContent,
                            delete: deletedContent,
                        },
                    }],
                });
            }
        }

        for (const createFile of createFiles) {
            let functionInfo: string[] = [];
            const fileContent = (await fs.readFile(createFile, 'utf8')).toString().split('\n');
            try {
                // 获取函数相关信息
                if (getExtName(createFile) === '.cpp' || getExtName(createFile) === '.cxx') {
                    const dirname = getDirname();
                    // wasm 需要，不加会报错
                    // eslint-disable-next-line
                    global.__dirname = dirname;
                    const wasmFile = path.resolve(dirname, 'tree-sitter.wasm');
                    await Parser.init({
                        locateFile() {
                            return wasmFile;
                        },
                    });

                    const parser = new Parser();
                    const parserWasm = languageExtMap.get('.cpp');
                    // eslint-disable-next-line
                    if (parserWasm) {
                        const Lang = await Parser.Language.load(parserWasm);
                        parser.setLanguage(Lang);
                    }

                    const tree = parser.parse((await fs.readFile(createFile, 'utf8')).toString());
                    functionInfo = getFunctionDeclareStr(
                        tree.rootNode,
                        Array.from({length: fileContent.length}, (_, i) => i + 1)
                    );
                }
            }
            // eslint-disable-next-line
            catch (e) {
            }

            if (functionInfo.length === 0) {
                result.set(createFile, {
                    // eslint-disable-next-line
                    diff_block: [{
                        content: {
                            change: fileContent,
                            delete: [],
                        },
                    }],
                });
            }
            else {
                result.set(createFile, {
                    functions: functionInfo,
                    // eslint-disable-next-line
                    diff_block: [{
                        content: {
                            change: fileContent,
                            delete: [],
                        },
                    }],
                });
            }
        }

        for (const notAddFile of notAddedFiels) {
            const fileContent = (await fs.readFile(notAddFile, 'utf8')).toString().split('\n');
            result.set(notAddFile, {
                // eslint-disable-next-line
                diff_block: [{
                    content: {
                        change: fileContent,
                        delete: [],
                    },
                }],
            });
        }

        for (const deleteFile of deletedFies) {
            const fileContent = (await this.git.show(['HEAD:' + deleteFile])).split('\n');
            result.set(deleteFile, {
                // eslint-disable-next-line
                diff_block: [{
                    content: {
                        change: [],
                        delete: fileContent,
                    },
                }],
            });
        }

        return result;
    }

    async getModifiedLinesOfFile(activeFilePath: string, fileContent: string) {
        try {
            const beforeContent = await this.git.show(['HEAD:' + activeFilePath]);
            const changes = diffLines(beforeContent, fileContent);

            const changedLineNumbers: Set<number> = new Set();
            const deletedLineNumbers: Set<number> = new Set();
            const addedLines: number[] = [];
            const removedLines: number[] = [];

            let oldLineNum = 1;
            let newLineNum = 1;
            changes
                .forEach((part: Change) => {
                    if (part.added) {
                        for (let i = 0; i < part.count!; i++) {
                            const lineNumber = newLineNum;
                            changedLineNumbers.add(lineNumber);
                            addedLines.push(lineNumber);
                            newLineNum++;
                        }
                    }
                    else if (part.removed) {
                        for (let i = 0; i < part.count!; i++) {
                            const lineNumber = oldLineNum;
                            deletedLineNumbers.add(lineNumber);
                            removedLines.push(lineNumber);
                            oldLineNum++;
                        }
                    }
                    else {
                        oldLineNum += part.count!;
                        newLineNum += part.count!;
                    }
                });
            return {changedLineNumbers: changedLineNumbers, deletedLineNumbers: deletedLineNumbers};
        }
        catch (e) {
            return {changedLineNumbers: new Set<number>(), deletedLineNumbers: new Set<number>()};
        }
    }

    async getDiffForFile(file: string) {
        const diff = await this.git.diff(['HEAD', '--', file]);
        return diff;
    }

    async getGoModModuleName(dir: string): Promise<string> {
        try {
            const goModFilePath = await fs.stat(path.join(dir, 'go.mod'));
            if (goModFilePath.isFile()) {
                const goModFileContent = await fs.readFile(path.join(dir, 'go.mod'), {encoding: 'utf8'});
                const regex = /module\s+icode\.baidu\.com\/(baidu\/[a-zA-Z0-9\-_/]+)/;
                const match = regex.exec(goModFileContent);
                return match ? match[1] : 'unknown';
            }
            return 'unknown';
        }
        catch (e) {
            return 'unknown';
        }
    }

    async getModuleName(dir: string = '.'): Promise<string> {
        try {
            const configs = await this.git.listConfig();
            const remoteUrl = configs.values['.git/config']['remote.origin.url'];
            const regex = /baidu\/[^/]+\/[^/]+(?!.*baidu\/[^/]+\/[^/]+)/;
            let matches: RegExpMatchArray | null = null;
            if (typeof remoteUrl === 'string') {
                matches = regex.exec(remoteUrl);
            }
            else if (Array.isArray(remoteUrl) && remoteUrl.length !== 0) {
                matches = regex.exec(remoteUrl[0]);
            }

            if (matches && matches.length > 0) {
                return matches[0];
            }
            else {
                // 如果没有匹配到，返回 null
                return this.getGoModModuleName(dir);
            }
        }
        catch (e) {
            return this.getGoModModuleName(dir);
        }
    }

    async getStagedDiffContent() {
        const diff = await this.git.diff(['--cached']);
        return diff;
    }

    async getUnstagedDiffContent() {
        const diff = await this.git.diff();
        return diff;
    }

    async getCommitId() {
        const logSummary = await this.git.log({n: 1});
        return logSummary.latest?.hash;
    }

    async getBranch() {
        const branchSummary = await this.git.branchLocal();
        return branchSummary.current;
    }
}
