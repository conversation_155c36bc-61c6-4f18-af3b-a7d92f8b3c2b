import {GitUtil} from '../utils/git.js';
import {UserDetail} from '@comate/plugin-shared-internals';

const WBOX_HOST = 'http://wbox.baidu-int.com';

const WBOX_API: Map<string, string> = new Map([
    ['register', '/api/task'],
    ['callback', '/api/task'],
    ['bugReport', '/api/bugs/add'],
    ['prompts', '/api/regular/prompt'],
    ['bugRefuse', '/api/bug/mark/sign'],
]);

export const languageExtWboxToolMap: Map<string, number> = new Map([
    ['.cpp', 113],
    ['.go', 113],
]);

async function wboxRegisterInner(data: any) {
    try {
        const response = await fetch(WBOX_HOST + WBOX_API.get('register'), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
        });

        const result = await response.json();
        if (result.code === 0) {
            return result.data.id;
        }
        else {
            throw new Error(result.msg);
        }
    }
    catch (error) {
        return 0;
    }
}

export async function wboxRegister(user: UserDetail | false, toolName: string, query: string, git: GitUtil) {
    const agileModuleName = await git.getModuleName();
    const taskId = await wboxRegisterInner({
        taskType: 4,
        agileModuleName: agileModuleName ? agileModuleName : 'unknown',
        toolId: 113,
        agilePipelineTriggerUser: user ? user.name : 'unknown',
        agileCheckinAuthor: user ? user.name : 'unknown',
        runMode: query === 'all' ? 1 : 2,
        stage: toolName,
    });
    return taskId;
}

export async function wboxGetPrompts(data: any) {
    const queryParams = new URLSearchParams(data);
    const response = await fetch(WBOX_HOST + WBOX_API.get('prompts') + `?${queryParams}`);

    const result = await response.json();
    if (result.code === 0) {
        return {
            accessToken: result.data.accessToken,
            prompts: result.data.prompts,
        };
    }
    return {
        accessToken: undefined,
        prompts: undefined,
    };
}

export async function wboxCallBack(data: any): Promise<string> {
    const response = await fetch(WBOX_HOST + WBOX_API.get('callback'), {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
    });

    const result = await response.json();
    if (result.code === 0) {
        return result.data.task_report_url;
    }

    return '';
}

export async function wboxBugReport(data: any) {
    const response = await fetch(WBOX_HOST + WBOX_API.get('bugReport'), {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
    });

    const result = await response.json();
    if (result.code === 0) {
        return result.data.addBugNum;
    }

    return {};
}

export async function wboxRequestModelResult(modelUrl: string, promptStr: string) {
    return [modelUrl, promptStr];
}

export async function wboxBugRefuse(data: any) {
    try {
        const response = await fetch(WBOX_HOST + WBOX_API.get('bugRefuse'), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
        });

        const result = await response.json();
        if (result.code === 0) {
            return {};
        }
    }
    // eslint-disable-next-line
    catch {
    }

    return {};
}
