import path from 'node:path';
import {fileURLToPath} from 'node:url';
import arrayOutOfBoundsTemplate from '../prompts/arrayOutOfBounds.prompt';
import didiByZeroTemplate from '../prompts/divideByZero.prompt';

export const getDirname = () => {
    const filename = fileURLToPath(import.meta.url);
    return path.dirname(filename);
};

export const getExtName = (filePath: string) => {
    return path.extname(filePath);
};

export const getTemplateStr = (promptName: string) => {
    if (promptName === 'arrayOutOfBounds') {
        return arrayOutOfBoundsTemplate;
    }
    else if (promptName === 'divideByZero') {
        return didiByZeroTemplate;
    }
    return '';
};
