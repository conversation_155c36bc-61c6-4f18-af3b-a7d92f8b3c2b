import {Tree, SyntaxNode} from 'web-tree-sitter';

export function getFunctionBody(tree: Tree, diffLines: number[]) {
    const result = new Map<string, string>();
    const functionNodes = tree.rootNode.descendantsOfType('function_declaration');
    // 遍历函数节点
    functionNodes.forEach(functionNode => {
        // 获取函数体节点
        if (!functionNode) {
            return;
        }

        const functionNameNode = functionNode.childForFieldName('name');
        if (!functionNameNode) {
            return;
        }

        const functionBodyNode = functionNode.childForFieldName('body');
        if (!functionBodyNode) {
            return;
        }

        if (diffLines.length !== 0) {
            if (!result.get(functionNameNode.text)) {
                diffLines.forEach(line => {
                    if (line >= functionBodyNode.startPosition.row && line <= functionBodyNode.endPosition.row) {
                        result.set(functionNameNode.text + ':' + functionNode.startPosition.row, functionBodyNode.text);
                    }
                });
            }
        }
        else if (!result.get(functionNameNode.text)) {
            result.set(functionNameNode.text + ':' + functionNode.startPosition.row, functionBodyNode.text);
        }
    });
    return result;
}

export function getArrayOutOfBoundsItems(tree: Tree, lineContent: string[], diffLines: number[]) {
    const result = new Map<string, Map<string, string | any[]>>();
    const functionNodes = tree.rootNode.descendantsOfType('function_declaration');
    // 遍历函数节点
    functionNodes.forEach(functionNode => {
        // 获取函数体节点
        if (!functionNode) {
            return;
        }

        const functionBodyNode = functionNode.lastChild;
        if (!functionBodyNode) {
            return;
        }

        // 获取函数内所有的index表达式
        const indexExpressions = functionNode.descendantsOfType('index_expression');
        /* eslint-disable max-statements */
        /* eslint-disable complexity */
        indexExpressions.forEach(node => {
            let indexStr = '';
            if (!node) {
                return;
            }

            // 判断是否是diff行
            let isDiffLine = false;
            for (let i = node.startPosition.row; i <= node.endPosition.row; i++) {
                if (diffLines.includes(i)) {
                    isDiffLine = true;
                    break;
                }
            }
            if (!isDiffLine) {
                return;
            }

            // 获取访问的目标节点（数组或切片）
            const targetNode = node.child(0);
            if (!targetNode) {
                return;
            } // 检查目标节点是否存在

            // 获取访问的索引
            const indexNode = node.child(1);
            if (!indexNode) {
                return;
            } // 检查索引节点是否存在
            const stack = [];
            for (let i = 1; i < node.childCount; i++) {
                const childNode = node.child(i);
                if (!childNode) {
                    continue;
                }
                if (childNode.text === '[') {
                    stack.push('[');
                }
                else if (childNode.text === ']') {
                    stack.pop();
                }
                else {
                    indexStr += childNode.text;
                }

                if (stack.length === 0) {
                    break;
                }
            }

            if (stack.length) {
                indexStr = '';
            }
            else {
                const functionNameNode = functionNode.child(0);
                if (!functionNameNode) {
                    return;
                }
                if (!result.has(functionNameNode.text)) {
                    const functionFeature = new Map<string, string | any[]>();
                    functionFeature.set('function_body', functionBodyNode.text);
                    const errorFeatureList = new Array<Map<string, string>>();
                    const errorFeature = new Map<string, string>();
                    errorFeature.set('error_line', lineContent[node.startPosition.row - 1]);
                    errorFeature.set('token_index', indexStr);
                    errorFeature.set('token', targetNode.text);
                    errorFeatureList.push(errorFeature);
                    functionFeature.set('error_feature', errorFeatureList);
                    result.set(functionNameNode.text, functionFeature);
                }
            }
        });
    });
    return result;
}

export function getFunctionDeclareStr(
    node: SyntaxNode,
    diffLines: number[],
    namespaceStack: string[] = [],
    functionNodeSet: Set<SyntaxNode> = new Set()
): string[] {
    let functions: string[] = [];
    if (node.type === 'namespace_definition') {
        const namespaceNameNode = node.namedChildren.find(child => child.type === 'identifier'); // 查找命名空间名节点
        if (namespaceNameNode) {
            const namespaceName = namespaceNameNode.text;
            namespaceStack.push(namespaceName);

            // 递归处理命名空间内部
            for (const child of node.namedChildren) {
                functions = functions.concat(getFunctionDeclareStr(child, diffLines, namespaceStack, functionNodeSet));
            }

            namespaceStack.pop(); // 处理完命名空间后弹出
        }
    }
    else if (node.type === 'function_definition') {
        // 获取函数签名
        const functionNameNode = node.childForFieldName('declarator');

        if (functionNameNode) {
            if (!functionNodeSet.has(node)) {
                functionNodeSet.add(node);
                const functionName = functionNameNode.text;
                const namespacePrefix = namespaceStack.length > 0 ? '::' + namespaceStack.join('::') + '::' : '::';

                const startLine = node.startPosition.row + 1; // 行号从 0 开始，所以 +1
                const endLine = node.endPosition.row + 1; // 行号从 0 开始，所以 +1

                const definition = `${namespacePrefix}${functionName}`;
                // 创建一个 Set 来存储数组中的元素
                const numSet = new Set(diffLines);

                // 遍历区间 [a, b] 内的每个数字，检查它是否存在于 Set 中
                // eslint-disable-next-line
                for (let i = startLine; i <= endLine; i++) {
                    // eslint-disable-next-line
                    if (numSet.has(i)) {
                        const funcSet = new Set(functions);
                        // eslint-disable-next-line
                        if (!funcSet.has(definition)) {
                            functions.push(definition);
                        }
                    }
                }
            }
        }
    }

    // 递归处理子节点
    for (const child of node.namedChildren) {
        functions = functions.concat(getFunctionDeclareStr(child, diffLines, namespaceStack.slice(), functionNodeSet));
    }
    return functions;
}
