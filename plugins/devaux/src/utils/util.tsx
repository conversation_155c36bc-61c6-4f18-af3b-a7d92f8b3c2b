import path from 'path';
import fs from 'node:fs/promises';
import {constants} from 'fs/promises';
import {GitDirStatus} from '../consts/status.js';
import {fetchStreamUrl, fetchUrl} from './fetch.js';
import {feedStrategyHiRobotUrl, qadcUpdatePrefixURL, kirinGDPFilePathURL} from '../consts/urls.js';
import {commonHeadersConfig, qadcHeadersConfig, kirinToken, kirinUserName} from '../consts/configs.js';
import {getKirinSyncGDPPathList} from '../utils/kirin.js';
import {ErnieBot4URL} from '../consts/urls.js';
import {ModelType} from '../consts/modelType.js';
import {DrawElement} from '@comate/plugin-shared-internals';
import {spawnSync} from 'child_process';
import * as Fs from 'fs';

export function jsonToMap(jsonStr: string): Map<string, string> {
    const obj: any = JSON.parse(jsonStr);
    const map: Map<string, string> = new Map();

    // 将对象的每个属性添加到 Map 对象中
    for (const key of Object.keys(obj)) {
        map.set(key, obj[key]);
    }

    return map;
}

export function mapToJson(mapObj: Map<string, string>) {
    const obj: {[key: string]: string} = {};
    for (let [k, v] of mapObj) {
        obj[k] = v;
    }
    return obj;
}

export function trimCustom(str: string, target: string): string {
    const targetLength = target.length;
    let start = 0;
    let end = str.length;

    // 寻找左边需要去除的字符串子串的起始位置
    while (str.startsWith(target, start)) {
        start += targetLength;
    }

    // 寻找右边需要去除的字符串子串的结束位置
    while (str.endsWith(target, end)) {
        end -= targetLength;
    }

    // 返回去除指定字符串子串后的子字符串
    return str.slice(start, end);
}

export function dealModelResultStr(result: string) {
    // eslint-disable-next-line
    result = trimCustom(result, '```');
    // eslint-disable-next-line
    result = trimCustom(result, 'json');
    if (!result || result.length === 0) {
        return null;
    }
    try {
        const resultJsonObj = JSON.parse(result);
        return resultJsonObj;
    }
    catch (e) {
        return null;
    }
}

export function formatDuration(duration: number): string {
    if (duration >= 60000) {
        // 如果耗时超过1分钟，则用1分钟作为单位，并保留两位小数
        return (duration / 60000).toFixed(2) + ' min';
    }
    else if (duration >= 1000) {
        // 如果耗时超过1秒，则用秒作为单位，并保留两位小数
        return (duration / 1000).toFixed(2) + ' s';
    }
    else {
        // 否则，用毫秒作为单位，并保留两位小数
        return duration.toFixed(2) + ' ms';
    }
}

export function countOccurrences(mainStr: string, subStr: string): number {
    const regex = new RegExp(subStr, 'g');
    const matches = mainStr.match(regex);
    return matches ? matches.length : 0;
}

export function countOccurrencesNotFollowedBy(mainStr: string, subStr1: string, subStr2: string): number {
    const regex = new RegExp(`${subStr1}(?!${subStr2})`, 'g');
    const matches = mainStr.match(regex);
    return matches ? matches.length : 0;
}

export function sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
}

export function getRelativePath(filePath: string, workspacePath: string): string {
    let fileRelativePath = filePath;
    if (workspacePath.length >= filePath.length) {
        return fileRelativePath;
    }
    if (path.isAbsolute(filePath)) {
        fileRelativePath = path.relative(workspacePath, filePath);
    }
    return fileRelativePath;
}

export async function getGitUtilObj(filePath: string) {
    let currentPath = path.dirname(filePath);
    let findGit = false;
    // 逐级向上遍历父目录
    // eslint-disable-next-line
    while (true) {
        try {
            const gitFs = await fs.stat(path.join(currentPath, '.git'));
            if (gitFs.isDirectory()) {
                findGit = true;
            }
        }
        // eslint-disable-next-line
        catch (e) {
        }

        if (findGit) {
            // 检查当前目录是否不可读
            try {
                await fs.access(currentPath, fs.constants.R_OK);
            }
            catch (err) {
                return {gitDirStatus: GitDirStatus.ERROR_NOT_READ_PERMISSION, gitDir: null};
            }
            return {gitDirStatus: GitDirStatus.SUCCESS, gitDir: currentPath};
        }

        // 如果已经到达根目录，则退出循环
        if (currentPath === path.dirname(currentPath)) {
            return {gitDirStatus: GitDirStatus.ERROR_NOT_GIT_REPO, gitDir: null};
        }
        // 继续向上查找
        currentPath = path.dirname(currentPath);
    }
}

export async function sendMessage2Hi(groupid: number, message: string) {
    try {
        const payload = {
            message: {
                header: {
                    toid: [groupid],
                },
                body: [{content: message, type: 'TEXT'}, {atall: true, type: 'AT'}],
            },
        };
        await fetchUrl(feedStrategyHiRobotUrl, commonHeadersConfig, payload);
    }
    // eslint-disable-next-line
    catch (e) {
    }
}

export async function getReuqestModelStream(
    accessToken: string | null,
    promptStr: string | null,
    kirinModels: Record<string, any> | null = null
) {
    if (!accessToken) {
        return null;
    }

    try {
        if (kirinModels === null) {
            const url = `${ErnieBot4URL}?access_token=${accessToken}`;
            const payload = {
                messages: [
                    {
                        role: 'user',
                        content: promptStr,
                    },
                ],
                stream: true,
                // eslint-disable-next-line
                max_output_tokens: 2048,
            };

            return await fetchStreamUrl(url, commonHeadersConfig, payload);
        }
        else {
            let url = `${ErnieBot4URL}`;
            if (kirinModels.url) {
                url = kirinModels.url;
            }

            // eslint-disable-next-line
            let max_output_tokens = 2048;
            if (kirinModels.max_output_token) {
                // eslint-disable-next-line
                max_output_tokens = kirinModels.max_output_token;
            }

            let temperature = 0.8;
            if (kirinModels.temperature) {
                temperature = kirinModels.temperature;
            }

            // eslint-disable-next-line
            let top_p = 0.8;
            if (kirinModels.top_p) {
                // eslint-disable-next-line
                top_p = kirinModels.top_p;
            }

            url = `${url}?access_token=${accessToken}`;

            const payload = {
                messages: [
                    {
                        role: 'user',
                        content: promptStr,
                    },
                ],
                stream: true,
                // eslint-disable-next-line
                max_output_tokens: max_output_tokens,
                temperature: temperature,
                // eslint-disable-next-line
                top_p: top_p,
            };
            return await fetchStreamUrl(url, commonHeadersConfig, payload);
        }
    }
    // eslint-disable-next-line
    catch (e) {
    }
    return null;
}

// eslint-disable-next-line
export async function* getReaderStr(modelReader: ReadableStreamDefaultReader<Uint8Array>, modelType: ModelType) {
    if (!modelReader) {
        return;
    }

    try {
        const decoder = new TextDecoder(); // 默认情况下使用 UTF-8 编码
        while (true) {
            const {done, value} = await modelReader.read();
            if (done) {
                break;
            }
            const valueStr = decoder.decode(value);
            const valueStrList = valueStr.split('\n');

            for (let valueStrItem of valueStrList) {
                // eslint-disable-next-line
                if (valueStrItem.trim().length === 0) {
                    continue;
                }
                // eslint-disable-next-line
                if (!valueStrItem.trim().startsWith('data:')) {
                    continue;
                }
                valueStrItem = trimCustom(valueStrItem, 'data: ');
                // eslint-disable-next-line
                try {
                    let chunkString = '';
                    // eslint-disable-next-line
                    if (modelType === ModelType.WENXIN) {
                        chunkString = JSON.parse(valueStrItem).result;
                    }
                    else if (modelType === ModelType.DEEPSEEK) {
                        chunkString = JSON.parse(valueStrItem).choices[0].delta.content;
                    }
                    yield {type: 'message', message: chunkString};
                }
                catch (e) {
                    continue;
                }
            }
        }
    }
    // eslint-disable-next-line
    catch (e) {
    }
}

export async function updateChunk2Qadc(
    chunkStr: string,
    keyValue: string,
    tableName = 'fea_gen_record',
    keyName = 'task_id'
) {
    try {
        let tryTimes = 3;
        while (tryTimes) {
            const url = qadcUpdatePrefixURL + '/' + tableName + '/' + keyName;

            const response = await fetch(url, {
                method: 'POST',
                headers: qadcHeadersConfig,
                // eslint-disable-next-line
                body: JSON.stringify([{[keyName]: keyValue, logic_code: chunkStr}]),
            });
            const responseObj = await response.json();
            if (responseObj.code === 0) {
                return;
            }
            else {
                --tryTimes;
                await sleep(1.5 * 1000);
            }
        }
    }
    // eslint-disable-next-line
    catch {
    }
}

export async function flushRes2Qadc(data: any, tableName = 'devaux_multiple_rounds') {
    const qadcInsertUrl = `http://qadc.baidu-int.com/api/insert/${tableName}`;
    const response = await fetch(qadcInsertUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            name: 'zhaomingming04',
            token: 'f0aa0153a8f24ad3ac199e8251e1bfd2',
        },
        body: JSON.stringify(data),
    });

    const result = await response.json();
    if (response.ok && result.message === 'success' && result.code === 0) {
        return {
            status: true,
        };
    }

    return {status: false};
}

export function renderTemplate(promptTemplate: string, args?: Record<string, unknown>) {
    if (args) {
        return promptTemplate.replaceAll(
            /\{\{(\w+)\}\}/g,
            (match, key) => {
                const replacement = args[key];
                return replacement === undefined ? match : String(replacement);
            }
        );
    }

    return promptTemplate;
}

export function getJsonValue(obj: Record<string, any>, key: string): any {
    if (obj === null) {
        return null;
    }
    return key in obj ? obj[key] : null;
}

export function removeLeftmostSubstring(str: string, substring: string): string {
    // 检查字符串是否以特定子串开头
    if (str.startsWith(substring)) {
        // 如果是，使用 slice 方法去掉最左侧的子串
        return str.slice(substring.length);
    }

    // 如果字符串不以子串开头，返回原字符串
    return str;
}

export async function checkFileExists(filePath: string): Promise<boolean> {
    try {
        await fs.access(filePath, constants.W_OK);
        return true;
    }
    catch (e) {
        return false;
    }
}

export function removeTrailingNumbers(str: string): {cleanedString: string, trailingNumbers: string | null} {
    // 使用正则表达式匹配末尾的数字
    const match = /\d+$/.exec(str);

    // 如果找到末尾数字，移除它并返回
    if (match) {
        const trailingNumbers = match[0]; // 获取末尾的数字
        const cleanedString = str.replace(/\d+$/, ''); // 去掉末尾的数字
        return {cleanedString, trailingNumbers};
    }

    // 如果没有找到末尾数字，返回原字符串和null
    return {cleanedString: str, trailingNumbers: null};
}

export async function parsePathLists(markdown: string, cwd: string, FilePath: string[]) {
    if (FilePath && FilePath.length === countCodeBlocks(markdown)) {
        // 如果后端返回file_path(相对路径)
        const absoluteFilePaths = FilePath.map(filePath => {
            if (filePath === 'skip') {
                return 'skip'; // 跳过
            }
            return path.join(cwd, filePath); // 拼接成绝对路径
        });

        return await extractCodeBlocks(markdown, absoluteFilePaths);
    }
    else {
        // 否则从模型返回中解析代码块, 返回绝对路径
        const {PathResponse} = await getKirinSyncGDPPathList(kirinGDPFilePathURL, {
            parameter: JSON.stringify({
                model_res: markdown,
                repository: cwd,
            }),
            name: kirinUserName,
            token: kirinToken,
        });
        if (PathResponse.length === countCodeBlocks(markdown)) {
            return await extractCodeBlocks(markdown, PathResponse);
        }
        else {
            return <markdown>{markdown}</markdown>;
        }
    }
}

export async function extractCodeBlocks(markdown: string, codeFilePath: string[]) {
    const codeBlockRegex = /```(\w+)?\n(?!\n)([\s\S]*?)```/g;
    let lastIndex = 0;
    let match;
    let codeBlockIndex = 0;
    let result: DrawElement[] = [];

    while ((match = codeBlockRegex.exec(markdown)) !== null) {
        const [fullMatch, language, code] = match;
        let filePath = codeFilePath[codeBlockIndex];

        // 添加非代码块的部分
        if (lastIndex < match.index) {
            result.push(<markdown>{markdown.slice(lastIndex, match.index)}</markdown>);
        }

        // 跳过未提取出路径的代码块
        if (filePath === '' || filePath === 'skip') {
            result.push(<markdown>{fullMatch}</markdown>);
            ++codeBlockIndex;
            lastIndex = match.index + fullMatch.length;
            continue;
        }

        if (await checkFileExists(filePath)) {
            // 如果目标文件存在
            result.push(
                // 自定义的代码块 JSX 元素
                <code-block
                    language={language}
                    insertToFileData={{
                        filePath: filePath,
                        position: {line: 1, character: 0},
                        newText: code.trim(),
                        metadata: {needsConfirmation: true},
                    }}
                    actions={['viewFile', 'copy', 'accept']}
                    closed={false}
                >
                    {code.trim()}
                </code-block>
            );
        }
        else {
            // 如果目标文件不存在，则新建文件到路径
            try {
                result.push(
                    // 自定义的代码块 JSX 元素
                    <code-block
                        language={language}
                        insertToFileData={{
                            filePath: filePath,
                            position: {line: 1, character: 0},
                            newText: code.trim(),
                            metadata: {needsConfirmation: true},
                        }}
                        actions={['newFile', 'copy', 'accept']}
                        closed={false}
                    >
                        {code.trim()}
                    </code-block>
                );
            }
            catch (e) {
                result.push(<markdown>{fullMatch}</markdown>);
            }
        }

        ++codeBlockIndex;

        // 更新索引
        lastIndex = match.index + fullMatch.length;
    }

    // 添加剩余部分
    if (lastIndex < markdown.length) {
        result.push(<markdown>{markdown.slice(lastIndex)}</markdown>);
    }

    return result;
}

export function countCodeBlocks(markdown: string): number {
    // 正则表达式匹配代码块，``` 后跟语言标识（可选），然后是代码内容，最后是 ```
    const codeBlockRegex = /```[\s\S]*?```/g;

    // 使用正则表达式匹配所有的代码块，并返回匹配到的数量
    const matches = markdown.match(codeBlockRegex);

    // 如果匹配到了，返回匹配的数量，否则返回 0
    return matches ? matches.length : 0;
}

export function splitWithMaxsplit(text: string, sep: string, maxsplit: number): string[] {
    const parts: string[] = [];
    let startIndex = 0;
    let splitCount = 0;

    while (splitCount < maxsplit) {
        const sepIndex = text.indexOf(sep, startIndex);
        if (sepIndex === -1) {
            break;
        }
        parts.push(text.slice(startIndex, sepIndex));
        startIndex = sepIndex + sep.length;
        splitCount++;
    }

    parts.push(text.slice(startIndex));
    return parts;
}

export function splitLines(text: string, keepends: boolean = false): string[] {
    const lines = text.split(/\r?\n/);
    if (keepends) {
        return lines.map((line, index) => {
            if (index < lines.length - 1) {
                return line + '\n';
            }
            return line;
        });
    }
    return lines;
}

/**
 * 下载并解压 tar.gz 文件
 *
 * @param url 要下载的 tar.gz 文件的 URL
 * @param dir 目标目录
 */
export async function downloadAndExtractTarGz(url: string, dir: string) {
    const tempDir = path.join(dir, '.src_temp');
    const tempPath = path.join(tempDir, 'temp.tar.gz');
    const baseMediaDir = path.join(dir, 'src/main/resources/base/media');
    const darkMediaDir = path.join(dir, 'src/main/resources/dark/media');
    const xmlDir = path.join(dir, 'lib-paywall/src/main/res');

    try {
        // 创建临时目录
        if (!Fs.existsSync(tempDir)) {
            Fs.mkdirSync(tempDir, {recursive: true});
        }

        // 下载压缩包
        const res = await fetch(url);
        if (!res.ok) {
            return {success: false, status: `代码库图片下载失败: ${res.status} ${res.statusText}`};
        }

        // 使用 pipeline 处理流
        const arrayBuffer = await res.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);
        Fs.writeFileSync(tempPath, buffer);

        // 确保目标目录存在
        [baseMediaDir, darkMediaDir].forEach(dir => {
            if (!Fs.existsSync(dir)) {
                Fs.mkdirSync(dir, {recursive: true});
            }
        });

        // 使用异步的 spawn 而不是 spawnSync
        const tar = spawnSync('tar', ['-xzf', tempPath, '-C', tempDir]);

        if (tar.status !== 0) {
            return {success: false, status: `解压文件失败: ${tar.stderr}`};
        }

        // 处理文件复制
        if (Fs.existsSync(xmlDir)) {
            const dirs = Fs.readdirSync(xmlDir);
            for (const dirName of dirs) {
                const sourcePath = path.join(xmlDir, dirName);
                if (Fs.statSync(sourcePath).isDirectory()) {
                    const targetDir = dirName.includes('night') ? darkMediaDir : baseMediaDir;
                    const files = Fs.readdirSync(sourcePath);
                    for (const file of files) {
                        const sourceFile = path.join(sourcePath, file);
                        const targetFile = path.join(targetDir, file);
                        Fs.copyFileSync(sourceFile, targetFile);
                    }
                }
            }
        }

        return {success: true, status: ''};
    }
    catch (error) {
        return {success: false, status: `代码库图片下载解压失败: ${error}`};
    }
    finally {
        // 清理临时文件
        if (Fs.existsSync(tempDir)) {
            Fs.rmSync(tempDir, {recursive: true, force: true});
        }
    }
}


export async function getSessionTokens() {
    const response = await fetch('http://10.143.161.42:8002/get_session_token', {
        method: 'POST',
    });

    if (response.status === 200) {
        const result = await response.json();
        return result;
    }

    return null;
}