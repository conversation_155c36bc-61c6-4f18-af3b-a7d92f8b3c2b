您好，我是您的研发效能助手DevAux，我可以为您进行静态代码扫描，算子代码生成，算子自测等功能，期间有使用问题欢迎添加如流群"3939419"咨询。

##### SA

- 修改代码之后，直接回车，稍等片刻即可暴露代码可能存在的风

##### FeedScanSA

- feed专用的SA，与上述SA使用方法一样

##### OPGenerator

- feed专用的算子代码生成功能
- 选中conf文件中的某一行
- 在query框中输入需求描述
- 回车，稍等片刻即可返回算子代码
- 算子生成之后，可以继续对话，请帮助我修改....，请帮助我优化....，在...基础上....来继续优化算子
- 如果算子生成OK之后，可以把生成代码粘贴到对应的文件中，并选中，然后在输入框中输入【测试配置】xxxxx来开始算子自测

##### OPSelfTest

- feed算自测功能，用来测试算子代码并生成测试报告
- 选中conf文件中的某一行
- 在query框中输入sample的afs地址
- 回车，稍等片刻即可返回测试报告

##### ADSGenerator

- 商业ADS专用的算子代码生成功能，使用方法可参考OPGenerator

##### UDFGenerator

- 商业UDF专用的算子代码生成功能
- 打开conf文件
- 在query框中输入需求描述
- 回车，稍等片刻即可返回算子代码

##### AisudaWeb代码生成

- 根据需求描述生成Amis代码
- 直接输入需求回车即可
