import {
    FunctionParameterDefinition,
    SkillProvider,
    TaskProgressChunk,
    ElementChunkStream,
    StringChunkStream,
} from '@comate/plugin-host';
import {
    axiosInstance,
    SSEProcessor,
} from '@comate/plugin-shared-internals';

/**
 * 请求的host
 */
const host = 'http://*************:8200';

/**
 * 存放通过规则解析后的清单列表项
 * 全局变量
 */
let itemArrAll = [] as Array<string>;

/**
 * 存放获取的icode代码库信息
 * 全局变量
 */
let icodeAll = '';

/**
 * 存放当前语言
 * 全局变量
 */
let languageAll = '';

/**
 * 存放流式返回的uuid
 * 全局变量
 */
let uuidAll = '';

export class ReqCodeGeneratorProvider extends SkillProvider {
    static skillName = 'reqCodeGeneratorProvider';
    static displayName: '需求代码生成';
    static description = '需求代码生成';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
        required: [],
    };

    /**
     * 调用流式服务
     *
     * @param params 请求参数
     * @returns 返回一个 AsyncGenerator<any> 类型的 Promise，表示流式处理的结果
     * @throws 当请求发生错误时，将记录错误信息并返回一个空对象
     */
    async callStreaming(params: any): Promise<AsyncGenerator<any>> {
        try {
            const response = await axiosInstance(host + '/api/1.0/ai_comate/query', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                responseType: 'stream',
                data: JSON.stringify(params),
            });
            const processor = new SSEProcessor(response.data);
            return processor.processSSE();
        }
        catch (error) {
            this.logger.error('callErnieBotStreaming error', error as Record<string, any> | undefined);
            return {} as any;
        }
    }

    /**
     * 获取知识库UUID
     *
     * @param icodeRepository icode仓库名称
     * @returns 返回一个Promise，解析后为字符串类型的知识库UUID
     */
    async getKnowledgeUuid(icodeRepository: string): Promise<string> {
        try {
            const response = await fetch(host + '/api/1.0/ai_comate/get_knowledge_set?name=' + icodeRepository, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
            });
            const result = await response.json();

            if (result.status === 0) {
                return result.data.uuid;
            }
            return '';
        }
        catch (error) {
            this.logger.error('getKnowledgeUuid error', error as Record<string, any> | undefined);
            return '' as any;
        }
    }

    /**
     * 从给定的icode地址中解析仓库名
     *
     * @param {string} gitUrl - Git地址，例如 'ssh://<EMAIL>:8235/baidu/ep-qa/fanstest-llm'
     * @returns {string} - 解析出的仓库名，如果解析失败则返回空字符串
     */
    async getRepositoryName(gitUrl: string | null): Promise<string> {
        if (null == gitUrl) {
            return '';
        }
        // 使用正则表达式解析仓库名
        const regex = /ssh:\/\/[^@]+@[^\/]+\/([^\s]+)/;
        const match = gitUrl.match(regex);

        // 如果匹配成功，返回捕获的仓库名，否则返回空字符串
        if (match && match[1]) {
            return match[1];
        }
        else {
            return '';
        }
    }

    /**
     * 格式化 Markdown 字符串
     *
     * @param input 输入的 Markdown 字符串
     * @returns 返回格式化后的 Markdown 字符串
     */
    async formatMarkdownString(input: string) {
        // 将输入字符串按行分割
        const lines = input.split('\n') as string[];

        // 过滤掉空行和制表符
        const filteredLines = lines.map(line => line.replace(/^\s*|\t/g, ''));

        // 将处理过的行重新组合成字符串
        return filteredLines.join('\n');
    }

    /**
     * 提交反馈
     *
     * @param feedback 反馈内容
     * @param type 反馈类型
     * @param uuid 用户唯一标识
     * @returns 返回反馈提交结果，若成功则返回结果数据，否则返回空字符串
     */
    async submitFeedback(feedback: string, type: string, uuid: string): Promise<string> {
        try {
            const param = {
                value: feedback,
                type: type,
                uuid: uuid,
            };
            const response = await fetch(host + '/api/1.0/ai_comate/update_query_info', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(param),
            });
            const result = await response.json();

            if (result.status === 0) {
                return result.data;
            }
            return '';
        }
        catch (error) {
            this.logger.error('submitFeedback error', error as Record<string, any> | undefined);
            return '' as any;
        }
    }

    /**
     * 暂停执行指定的毫秒数
     *
     * @param ms 暂停的毫秒数
     * @returns 返回一个Promise，当指定的毫秒数过去后，该Promise将被解析
     */
    sleep(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 执行代码生成任务，并返回一个包含任务进度分块的异步迭代器。
     *
     * @returns 返回一个包含 TaskProgressChunk 类型元素的异步迭代器。
     */
    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const streamElement = new ElementChunkStream();
        const stream = new StringChunkStream();
        const userQuery = this.currentContext.query;
        const repository = await this.retriever.repositoryInfo();
        // 默认语言
        const language = this.currentContext.activeFileLanguage ?? 'python';
        languageAll = language;

        yield streamElement.flush(
            <p>
                <loading>请稍候......</loading>
            </p>
        );

        // 获取知识集
        let icodeRepository = '';
        let knowledgeUuid = '';
        let knowledgeTextArr = [] as string[];
        if (repository.versionControl === 'git') {
            icodeRepository = await this.getRepositoryName(repository.repositoryUrl);
            knowledgeUuid = await this.getKnowledgeUuid(icodeRepository);

            // 如果有找到知识集 则使用知识库
            if (knowledgeUuid.length > 0) {
                knowledgeTextArr = await this.retriever.knowledgeFromQuery(userQuery, {
                    knowledgeSets: [{
                        uuid: knowledgeUuid,
                        type: 'NORMAL',
                    }],
                });
            }
        }
        icodeAll = icodeRepository;

        // 获取代码生成参数
        const params = {
            icode: icodeRepository,
            query: userQuery,
            // "icode": 'baidu/jady/middlepage-server',
            // "query":"根据wenku中间页生成一个tieba的中间页清单",
            language: language,
            filePaths: [],
            fileContents: [],
            knowledgeContents: knowledgeTextArr,
        };

        let text = '';
        const chunks = await this.callStreaming(params);
        for await (const chunk of chunks) {
            uuidAll = chunk.uuid;
            if (chunk?.is_stream) {
                yield stream.flush(chunk.text);
            }
            else {
                text += chunk.text;

                yield streamElement.flushReplaceLast(
                    <p>
                        <loading>生成中......</loading>
                        <markdown>{text}</markdown>
                    </p>
                );
            }
        }

        if (text.length > 0) {
            // 说明是清单 不是流式输出
            // 这里应该对清单做特殊处理 输出按钮

            // 取消loading文字
            yield streamElement.flushReplaceLast(<></>);

            // 根据自定义的规则进行渲染
            let textRaw = text;
            let header = '';
            let res = [] as string[];
            let tail = '';
            for (let index = 1; index < 100; index++) {
                let arr = text.split(index + '. ') as string[];
                if (arr.length === 1) {
                    // 兜底最后结尾总结去除
                    arr = text.split('。\n\n');
                    // 不管有没有拆 都把第一个元素放到res里面
                    res.push(arr[0]);
                    // 拆成功 就把尾巴放进来
                    if (arr.length > 1) {
                        tail = arr[1];
                    }
                    break;
                }
                if (index === 1) {
                    header = arr[0];
                }
                else {
                    res.push(arr[0]);
                }
                text = arr[1];
            }
            itemArrAll = res;
            // 自定义解析结束
            // 开始渲染
            yield streamElement.flush(
                <p>
                    <strong>{header}</strong>
                </p>
            );
            for (let index = 0; index < res.length; index++) {
                const element = res[index];
                yield streamElement.flush(
                    <markdown>
                        {await this.formatMarkdownString('清单' + String(index + 1) + ' ' + element)}
                    </markdown>
                );
                yield streamElement.flush(
                    <button-group>
                        <command-button
                            commandName={'commitMessage:confirmIssue,' + index}
                            data={'handle'}
                            replyText={'正在执行 清单' + String(index + 1) + ' \n\n' + res[index]}
                        >
                            {'去执行清单' + String(index + 1)}
                        </command-button>
                    </button-group>
                );
            }
            yield streamElement.flush(<p>{tail}</p>);
            yield streamElement.flush(<br></br>);
            yield streamElement.flush(<br></br>);

            yield streamElement.flush(<p>您对本次结果的评价：</p>);

            yield streamElement.flush(
                <button-group>
                    <command-button
                        commandName="commitMessage:confirmIssueYes"
                        data={uuidAll}
                        replyText="我要点赞"
                    >
                        点赞
                    </command-button>
                    <command-button
                        commandName="commitMessage:confirmIssueNo"
                        data={uuidAll}
                        replyText="我要点踩"
                    >
                        点踩
                    </command-button>
                </button-group>
            );
        }
    }

    /**
     * 承接点击事件 处理命令并返回异步迭代器，生成任务进度块
     *
     * @param commandName 命令名称
     * @param data 附加数据
     * @returns 异步迭代器，生成 TaskProgressChunk 类型的任务进度块
     */
    async *handleCommand(commandName: string, data: any): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new StringChunkStream();

        if (data === 'handle') {
            let index = Number(commandName.split(',')[1]);

            // 开启新一轮对话
            const params = {
                icode: icodeAll,
                query: itemArrAll[index],
                oriQuery: this.currentContext.query,
                language: languageAll,
                filePaths: [],
                fileContents: [],
                knowledgeContents: [],
            };

            const chunks = await this.callStreaming(params);
            for await (const chunk of chunks) {
                yield stream.flush(chunk.text);
            }
        }
        else {
            if (commandName === 'commitMessage:confirmIssueYes') {
                await this.submitFeedback('1', 'zan', data);
                yield stream.flush(<p>感谢您的点赞</p>);
            }
            else {
                await this.submitFeedback('1', 'unlike', data);
                yield stream.flush(<p>感谢您的点踩，我们会继续改进</p>);
            }
        }
    }
}
