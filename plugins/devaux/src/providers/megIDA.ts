/* eslint-disable camelcase */
import {
    FunctionParameterDefinition,
    SkillProvider,
    ProviderInit,
    TaskProgressChunk,
    UserDetail,
    StringChunkStream,
} from '@comate/plugin-host';

import {GitUtil} from '../utils/git.js';
import {wboxCallBack, wboxRegister} from '../utils/wbox.js';
import {MegIDAChatDelivery} from '../consts/urls.js';
import {SSEProcessor} from '../utils/sseProcessor.js';
import axios from 'axios';
interface Args {
    query: string;
}

export interface Event {
    event: string;
    data: string;
}

export class MegIDA extends SkillProvider<Args> {
    static skillName = 'MegIDA';

    static displayName = 'MEG智能交付助手';

    static description = 'MEG智能交付助手';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    private readonly git: GitUtil;

    constructor(init: ProviderInit) {
        super(init);

        this.git = new GitUtil(init.cwd);
    }

    // eslint-disable-next-line
    async *requestChatDelivery(
        stream: StringChunkStream,
        header: any,
        data: any
    ): AsyncIterableIterator<TaskProgressChunk> {
        try {
            // eslint-disable-next-line
            const response = await axios.post(
                MegIDAChatDelivery,
                data,
                {
                    headers: header,
                    responseType: 'stream', // 设置响应类型为流
                }
            );
            const processor = new SSEProcessor<Event>(response.data);
            for await (const data of processor.processSSE()) {
                if (data.event === 'finish') {
                    return;
                }
                try {
                    const jsonObj = JSON.parse(data.data);
                    // eslint-disable-next-line
                    for (const obj of jsonObj) {
                        // eslint-disable-next-line
                        if (data.event === 'message') {
                            yield stream.flush(obj.contents.text);
                        }
                        else if (data.event === 'error') {
                            yield stream.flush(jsonObj.get('msg'));
                        }
                    }
                }
                // eslint-disable-next-line
                catch (e) {
                }
            }
        }
        // eslint-disable-next-line
        catch (e) {
        }
    }

    // eslint-disable-next-line
    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new StringChunkStream();
        const isSuccess = false;
        let wboxTaskID = 0;
        let user: false | UserDetail = false;
        try {
            user = await this.currentUser.requestDetail();
            const moduleName = await this.git.getModuleName();
            let urlData: any = {};
            if (this.currentContext.data && this.currentContext.data.length > 0) {
                try {
                    urlData = JSON.parse(this.currentContext.data);
                }
                catch (e) {
                    urlData = {};
                }
            }

            // wbox平台注册本次扫描任务
            wboxTaskID = await wboxRegister(user, 'MegIDA', this.currentContext.query, this.git);
            yield* this.requestChatDelivery(stream, {
                'Content-Type': 'application/json',
                'X-Devops-User': btoa(unescape(encodeURIComponent(user ? user.name : 'unknown'))),
                Accept: 'text/event-stream',
            }, {
                query: this.currentContext.query,
                dev_aux_params: {
                    icode_path: moduleName ? moduleName : 'unknown',
                    query_idx: urlData.query_idx ? urlData.query_idx : 0,
                },
                data: {
                    plugin: urlData.plugin ? urlData.plugin : 'unknown',
                    action: urlData.action ? urlData.action : 'unknown',
                    origin_dialog_id: urlData.origin_dialog_id ? urlData.origin_dialog_id : 'unknown',
                },
            });
        }
        // eslint-disable-next-line
        catch (e) {
        }
        finally {
            await wboxCallBack({
                taskId: wboxTaskID ? wboxTaskID : 0,
                status: isSuccess ? 2 : 1,
                caller: user ? user.name : 'unknown',
            });
        }
    }
}
