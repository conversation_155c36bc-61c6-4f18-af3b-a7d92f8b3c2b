/* eslint-disable no-useless-escape */
import {
    FunctionParameterDefinition,
    ProviderInit,
    SkillProvider,
    TaskProgressChunk,
    ElementChunkStream,
} from '@comate/plugin-host';

import {formatPrompt} from '@comate/plugin-shared-internals';
import {Md5} from 'ts-md5';
import {Tree} from 'web-tree-sitter';
import {getFunctionBody, languageExtMap} from '../consts/languageParser.js';
import {WboxStatus, GitDirStatus} from '../consts/status.js';
import {getGitUtilObj} from '../utils/util.js';
import {getExtName} from '../utils/fs.js';
import {GitUtil} from '../utils/git.js';
import {dealModelResultStr, formatDuration} from '../utils/util.js';
import {
    languageExtWboxToolMap,
    wboxBugReport,
    wboxCallBack,
    wboxGetPrompts,
    wboxRegister,
    wboxBugRefuse,
} from '../utils/wbox.js';
import {BasicScanParser} from './saBasicScan.js';
import {currentPromptIdSet} from '../consts/prompt.js';
import path from 'path';

interface Args {
    query: string;
}

interface requestValue {
    modelApiUrl: string;
    promptStr: string;
    promptTemplate: {attribute: any, title: any, prompt: any, promptId: any};
    functionName: string;
}

interface bugInfo {
    bugIndex: number;
    bugPromptTitle: string;
    bugFilePath: string;
    bugLineNumber: number;
    bugReason: string;
    bugLocation: string; // for md5sum compute
}

interface bugRefuseInfo {
    bugSign: string;
    markReason: string;
    status: number;
}

export class SAScanProvider extends SkillProvider<Args> {
    static skillName = 'SAScan';

    static displayName = '代码缺陷检测';

    static description = '检查代码中可能存在的缺陷';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    static flowMessage = '，可添加如流群(3939419)咨询';

    private git: GitUtil;
    private readonly cwd: string;

    constructor(init: ProviderInit) {
        super(init);

        this.cwd = init.cwd;

        this.git = new GitUtil(init.cwd);
    }

    async preCheck(ext: string) {
        // 进行相关前置校验
        const languageBinding = languageExtMap.get(ext.toString());
        if (!languageBinding) {
            return WboxStatus.ERROR_LANGUAGE_NOT_SUPPORT;
        }
        return WboxStatus.SUCCESS;
    }

    async getFileGrammarTree(ext: string) {
        const tree = await BasicScanParser.getCurrentFileTreeSitterInstance(
            this.currentContext.activeFileName,
            this.currentContext.activeFileContent,
            ext
        );
        if (!tree) {
            return {status: WboxStatus.ERROR_GARAMMAR_PARSE_FAIL, tree: tree};
        }
        return {status: WboxStatus.SUCCESS, tree: tree};
    }

    async diffProcess(gitDir: string) {
        let changedInfo: Set<number> = new Set<number>();
        let activeFilePath = path.join(this.cwd, this.currentContext.activeFilePath);
        if (activeFilePath.startsWith(gitDir)) {
            activeFilePath = activeFilePath.substring(gitDir.length);
            if (activeFilePath.startsWith('/')) {
                activeFilePath = activeFilePath.substring(1);
            }
        }
        if (this.currentContext.query.trim() === 'diff' || this.currentContext.query.length === 0) {
            const diffInfo = await this.git.getDiffForFile(activeFilePath);
            if (diffInfo.length === 0) {
                return {diffStatus: WboxStatus.OK_NOT_GIT_PROJECT, diffLines: changedInfo};
            }
            const {changedLineNumbers} = await this.git.getModifiedLinesOfFile(
                activeFilePath,
                this.currentContext.activeFileContent
            );
            changedInfo = changedLineNumbers;
            if (changedInfo.size === 0) {
                return {diffStatus: WboxStatus.OK_NO_DIFF, diffLines: changedInfo};
            }
        }
        else if (this.currentContext.query.trim() !== 'all') {
            return {diffStatus: WboxStatus.OK_ORDER_NOT_SUPPORT, diffLines: changedInfo};
        }
        return {diffStatus: WboxStatus.SUCCESS, diffLines: changedInfo};
    }

    async getFunctionFeature(tree: Tree, diffLines: Set<number>, ext: string) {
        const {functionBodyResult, functionPromptResult} = getFunctionBody(tree, diffLines, ext);
        if (functionBodyResult.size === 0) {
            return {
                getFeatureStatus: WboxStatus.ERROR_GET_FUNCTION_FEATURE_FAIL,
                functionBodyResult: functionBodyResult,
                functionPromptResult: functionPromptResult,
            };
        }
        return {
            getFeatureStatus: WboxStatus.SUCCESS,
            functionBodyResult: functionBodyResult,
            functionPromptResult: functionPromptResult,
        };
    }

    async getWboxPromptTemplates(ext: string) {
        const toolId = languageExtWboxToolMap.get(ext);
        if (!toolId) {
            return {getPromptStatus: WboxStatus.OK_CAN_NOT_FIND_PROMPTS, prompts: []};
        }

        const {accessToken, prompts} = await wboxGetPrompts({
            toolId: toolId,
        });
        if (!prompts) {
            return {
                accessToken: accessToken,
                getPromptStatus: WboxStatus.OK_CAN_NOT_FIND_PROMPTS,
                prompts: [],
            };
        }
        const promptsInfo = [];
        for (const prompt of prompts) {
            if (prompt.status === 1) {
                promptsInfo.push({
                    attribute: prompt.attribute.length === 0 ? '' : prompt.attribute,
                    title: prompt.title,
                    prompt: prompt.prompt,
                    promptId: prompt.id,
                });
            }
        }
        return {
            accessToken: accessToken,
            getPromptStatus: WboxStatus.SUCCESS,
            prompts: promptsInfo,
        };
    }

    // eslint-disable-next-line
    async requestModelResult(requestList: requestValue[], diffLines: number[]) {
        const result = [];
        // eslint-disable-next-line
        const requestPromiseList = [];
        const requestItemList = [];
        for (const requestItem of requestList) {
            requestPromiseList.push(fetch(requestItem.modelApiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    messages: [{
                        role: 'user',
                        content: requestItem.promptStr,
                    }],
                    temperature: 0.3,
                    // eslint-disable-next-line
                    response_format: 'json_object',
                }),
            }));

            requestItemList.push(requestItem);

            if (requestPromiseList.length === 2) {
                const responseList = await Promise.all(requestPromiseList);
                for (let j = 0; j < requestPromiseList.length; ++j) {
                    const response = responseList[j];
                    // eslint-disable-next-line
                    if (response.ok) {
                        let resultJsonObj = await response.json();
                        // eslint-disable-next-line
                        if (!resultJsonObj.result) {
                            continue;
                        }
                        resultJsonObj = dealModelResultStr(resultJsonObj.result);
                        // eslint-disable-next-line
                        if (
                            resultJsonObj && (resultJsonObj.label === 1 || resultJsonObj.label === '1')
                            && diffLines.length !== 0 && !diffLines.includes(Number(resultJsonObj.line_number))
                        ) {
                            continue;
                        }
                        // eslint-disable-next-line
                        if (resultJsonObj && (resultJsonObj.label === 1 || resultJsonObj.label === '1')) {
                            const fileLabel = '[' + this.currentContext.activeFilePath + ':'
                                + Number(resultJsonObj.line_number) + ']('
                                + this.currentContext.activeFilePath + ':'
                                + Number(resultJsonObj.line_number) + ')';
                            result.push({
                                reason: resultJsonObj.reason,
                                fileLabel: fileLabel,
                                location: this.currentContext.activeFilePath + ':'
                                    + requestItemList[j].functionName.split(':')[1],
                                title: requestItemList[j].promptTemplate.title,
                                promptId: requestItemList[j].promptTemplate.promptId,
                                errorLineContent: resultJsonObj.error_line_content,
                            });
                        }
                    }
                }
                // eslint-disable-next-line
                requestPromiseList.splice(0, requestPromiseList.length);
                requestItemList.splice(0, requestItemList.length);
            }
        }
        return {reqStatus: WboxStatus.SUCCESS, result: result};
    }

    // eslint-disable-next-line
    async constructPrompt(
        promptTemplates: Array<{attribute: any, title: any, prompt: any, promptId: any}>,
        // eslint-disable-next-line
        functionBodyResult: Map<string, string>,
        functionPromptResult: Map<string, Set<number>>,
        accessToken: string
    ) {
        const requestList: requestValue[] = [];
        // eslint-disable-next-line
        for (const [functionName, functionBody] of functionBodyResult) {
            const functionBodyStr = (functionBody.split('\n'))
                .map((line, index) => (index + Number(functionName.split(':')[1]) + 1) + `: ${line}`)
                .join('\n');
            for (const promptTemplate of promptTemplates) {
                try {
                    const functionPromptResultSet = functionPromptResult.get(functionName);
                    // eslint-disable-next-line
                    if (!functionPromptResultSet) {
                        break;
                    }

                    // eslint-disable-next-line
                    if (
                        !functionPromptResultSet.has(promptTemplate.promptId)
                        && currentPromptIdSet.has(promptTemplate.promptId)
                    ) {
                        continue;
                    }

                    const attribute = promptTemplate.attribute;
                    // eslint-disable-next-line
                    if (attribute) {
                        const attrJsonObj = JSON.parse(attribute);
                        const modelApiUrl = attrJsonObj.modelApiUrl + `?access_token=${accessToken}`;
                        const promptStr = formatPrompt(promptTemplate.prompt, {
                            // eslint-disable-next-line
                            function_body: functionBodyStr,
                        });
                        requestList.push({
                            modelApiUrl: modelApiUrl,
                            promptStr: promptStr,
                            promptTemplate: promptTemplate,
                            functionName: functionName,
                        });
                    }
                }
                catch (e) {
                    continue;
                }
            }
        }

        return requestList;
    }

    // eslint-disable-next-line
    async getRequestModelResult(
        requestPromiseList: Array<Promise<Response>>,
        requestItemList: requestValue[],
        diffLines: Set<number>,
        bugNumber: number
    ) {
        let bugNumberTmp = bugNumber;
        const resultList: any[] = [];
        const bugResultList: bugInfo[] = [];
        const responseList = await Promise.all(requestPromiseList);
        for (let j = 0; j < requestPromiseList.length; ++j) {
            const response = responseList[j];
            // eslint-disable-next-line
            if (response.ok) {
                let resultJsonObj = await response.json();
                // eslint-disable-next-line
                if (!resultJsonObj.result) {
                    continue;
                }
                resultJsonObj = dealModelResultStr(resultJsonObj.result);
                // eslint-disable-next-line
                if (
                    resultJsonObj && (resultJsonObj.label === 1 || resultJsonObj.label === '1')
                    && diffLines.size !== 0 && !diffLines.has(Number(resultJsonObj.line_number))
                ) {
                    continue;
                }
                // eslint-disable-next-line
                if (resultJsonObj && (resultJsonObj.label === 1 || resultJsonObj.label === '1')) {
                    const fileLabel = '[' + this.currentContext.activeFilePath + ':'
                        + Number(resultJsonObj.line_number) + ']('
                        + this.currentContext.activeFilePath + ':'
                        + Number(resultJsonObj.line_number) + ')';
                    ++bugNumberTmp;
                    bugResultList.push({
                        bugIndex: bugNumberTmp,
                        bugFilePath: this.currentContext.activeFilePath,
                        bugLineNumber: Number(resultJsonObj.line_number),
                        bugPromptTitle: requestItemList[j].promptTemplate.title,
                        bugReason: resultJsonObj.reason.replace('\n', ' '),
                        bugLocation: this.currentContext.activeFilePath + ':'
                            + requestItemList[j].functionName.split(':')[1],
                    });
                    resultList.push({
                        reason: resultJsonObj.reason,
                        fileLabel: fileLabel,
                        location: this.currentContext.activeFilePath + ':'
                            + requestItemList[j].functionName.split(':')[1],
                        title: requestItemList[j].promptTemplate.title,
                        promptId: requestItemList[j].promptTemplate.promptId,
                        errorLineContent: resultJsonObj.error_line_content,
                    });
                }
            }
        }
        return {resultList: resultList, bugResultList: bugResultList};
    }

    async *handleCommand(commandName: string, data: any): AsyncIterableIterator<TaskProgressChunk> {
        try {
            const stream = new ElementChunkStream();
            if (commandName === 'bug:refuse') {
                await wboxBugRefuse(data);
                yield stream.flush(<p>已将该bug标记为误告</p>);
            }
        }
        // eslint-disable-next-line
        catch {
        }
    }

    /* eslint-disable max-statements */
    /* eslint-disable complexity */
    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new ElementChunkStream();
        const user = await this.currentUser.requestDetail();
        const requestFs = await this.requestWorkspaceFileSystem();
        if (!requestFs) {
            yield stream.flush(<p>无法获取文件系统权限${SAScanProvider.flowMessage}</p>);
            return;
        }

        const {gitDirStatus, gitDir} = await getGitUtilObj(path.join(this.cwd, this.currentContext.activeFilePath));
        if (gitDirStatus === GitDirStatus.ERROR_NOT_READ_PERMISSION) {
            yield stream.flush(<p>当前git工程目录不可读{SAScanProvider.flowMessage}</p>);
            return;
        }
        else if (gitDirStatus === GitDirStatus.ERROR_NOT_GIT_REPO) {
            yield stream.flush(<p>当前不是git工程{SAScanProvider.flowMessage}</p>);
            return;
        }
        else if (!gitDir) {
            yield stream.flush(<p>无法找到包含.git文件夹的路径{SAScanProvider.flowMessage}</p>);
            return;
        }

        this.git = new GitUtil(gitDir);

        let isSuccess = false;
        // wbox平台注册本次扫描任务
        const taskId = await wboxRegister(user, 'SA', this.currentContext.query, this.git);

        try {
            // 获取文件扩展名
            const ext = getExtName(this.currentContext.activeFileName);

            // 进行相关前置校验
            yield stream.flush(<h4>&gt; 思考过程</h4>);
            if (await this.preCheck(ext) !== WboxStatus.SUCCESS) {
                yield stream.flushReplaceLast(
                    <ul>
                        <li>当前只支持GO/C++语言的缺陷检查</li>
                    </ul>
                );
                isSuccess = true;
                return;
            }

            // 解析当前active文件的语法
            const {status, tree} = await this.getFileGrammarTree(ext);
            if (status !== WboxStatus.SUCCESS || tree === null) {
                yield stream.flushReplaceLast(
                    <ul>
                        <li>无法解析文件, 请联系相关负责人</li>
                    </ul>
                );
                isSuccess = false;
                return;
            }

            // 判断本次是diff模式还是all模式
            // 如果是diff模式，则获取diff行list
            const {diffStatus, diffLines} = await this.diffProcess(gitDir);
            switch (diffStatus) {
                case WboxStatus.OK_NO_DIFF:
                    isSuccess = true;
                    yield stream.flush(
                        <ul>
                            <li>未修改任何代码</li>
                        </ul>
                    );
                    return;
                case WboxStatus.OK_NOT_GIT_PROJECT:
                    isSuccess = true;
                    yield stream.flush(
                        <ul>
                            <li>未修改任何代码或当前不是git工程</li>
                        </ul>
                    );
                    return;
                case WboxStatus.OK_ORDER_NOT_SUPPORT:
                    isSuccess = true;
                    yield stream.flush(<p>不支持${this.currentContext.query}指令，当前只支持diff/all模式</p>);
                    return;
            }

            // 通过tree sitter提取代码相关特征数据
            yield stream.flush(
                <ul>
                    <li>正在提取变更方法信息</li>
                </ul>
            );
            const getCodeFeatureStartTime = performance.now();
            const {getFeatureStatus, functionBodyResult, functionPromptResult} = await this.getFunctionFeature(
                tree,
                diffLines,
                ext
            );
            if (getFeatureStatus === WboxStatus.ERROR_GET_FUNCTION_FEATURE_FAIL || !functionBodyResult) {
                yield stream.flushReplaceLast(
                    <ul>
                        <li>没有提取到有变更方法......</li>
                    </ul>
                );
                isSuccess = false;
                return;
            }

            const getCodeFeatureEndTime = performance.now();
            let consumeTime = formatDuration(getCodeFeatureEndTime - getCodeFeatureStartTime);
            yield stream.flushReplaceLast(
                <ul>
                    <li>正在提取变更方法信息，数量{functionBodyResult.size}个......{consumeTime}</li>
                </ul>
            );

            // 拼接prompt
            yield stream.flush(
                <ul>
                    <li>正在获取Prompt，数量</li>
                </ul>
            );
            const getPromptStartTime = performance.now();
            const {accessToken, getPromptStatus, prompts} = await this.getWboxPromptTemplates(ext);
            if (getPromptStatus !== WboxStatus.SUCCESS) {
                yield stream.flush(
                    <ul>
                        <li>未查询到该语言的prompt，请联系相关负责人</li>
                    </ul>
                );
                isSuccess = false;
                return;
            }
            const getPromptEndTime = performance.now();
            consumeTime = formatDuration(getPromptEndTime - getPromptStartTime);
            yield stream.flushReplaceLast(
                <ul>
                    <li>正在获取Prompt，数量{prompts.length}个......{consumeTime}</li>
                </ul>
            );

            // 请求文心模型
            yield stream.flush(
                <ul>
                    <li>正在构建Prompt.......</li>
                </ul>
            );
            const constructPromptStartTime = performance.now();
            const requestList = await this.constructPrompt(
                prompts,
                functionBodyResult ? functionBodyResult : new Map<string, string>(),
                functionPromptResult ? functionPromptResult : new Map<string, Set<number>>(),
                accessToken
            );
            const constructPromptEndTime = performance.now();
            consumeTime = formatDuration(constructPromptEndTime - constructPromptStartTime);
            yield stream.flushReplaceLast(
                <ul>
                    <li>正在构建Prompt.......{consumeTime}</li>
                </ul>
            );

            if (requestList.length === 0) {
                isSuccess = true;
                yield stream.flush(
                    <ul>
                        <li>未发现风险......</li>
                    </ul>
                );
                return;
            }

            yield stream.flush(
                <ul>
                    <li>模型努力判断中，请稍候......</li>
                </ul>
            );
            // 渲染扫描结果
            yield stream.flush(<h4>&gt; 检查结果</h4>);
            // const {result} = await this.requestModelResult(requestList, diffLines);
            let result: any[] = [];
            const requestPromiseList = [];
            const requestItemList = [];
            let bugNumber = 0;
            let isFirstBug = true;
            for (const requestItem of requestList) {
                requestPromiseList.push(fetch(requestItem.modelApiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        messages: [{
                            role: 'user',
                            content: requestItem.promptStr,
                        }],
                        temperature: 0.3,
                        // eslint-disable-next-line
                        response_format: 'json_object',
                    }),
                }));

                requestItemList.push(requestItem);

                if (requestPromiseList.length === 2) {
                    const {resultList, bugResultList} = await this.getRequestModelResult(
                        requestPromiseList,
                        requestItemList,
                        diffLines,
                        bugNumber
                    );

                    bugNumber += bugResultList.length;
                    // eslint-disable-next-line
                    for (const bugMsg of bugResultList) {
                        // eslint-disable-next-line
                        if (!isFirstBug) {
                            yield stream.flush(<p>---------------------------------------------------------</p>);
                        }
                        isFirstBug = false;
                        yield stream.flush(
                            <p>
                                <alert-tag level="critical">严重</alert-tag> {bugMsg.bugIndex}.{bugMsg.bugPromptTitle}
                            </p>
                        );
                        yield stream.flush(
                            <p>
                                - 风险位置：<file-link to={bugMsg.bugFilePath} line={bugMsg.bugLineNumber}>
                                    {bugMsg.bugFilePath}:{bugMsg.bugLineNumber}
                                </file-link>
                            </p>
                        );
                        yield stream.flush(
                            <p>
                                - 判定理由：<code>{bugMsg.bugReason}</code>
                            </p>
                        );

                        const bugData: bugRefuseInfo = {
                            bugSign: Md5.hashStr(bugMsg.bugLocation + bugMsg.bugPromptTitle + bugMsg.bugReason),
                            markReason: '点击拒绝按钮拒绝',
                            status: 2,
                        };

                        yield stream.flush(
                            <command-button
                                commandName="bug:refuse"
                                data={bugData}
                                replyText="标记误告"
                                variant="primary"
                            >
                                标记误告
                            </command-button>
                        );
                    }
                    result = result.concat(resultList);
                    // eslint-disable-next-line
                    requestPromiseList.splice(0, requestPromiseList.length);
                    requestItemList.splice(0, requestItemList.length);
                }
            }

            if (requestPromiseList.length !== 0) {
                const {resultList, bugResultList} = await this.getRequestModelResult(
                    requestPromiseList,
                    requestItemList,
                    diffLines,
                    bugNumber
                );
                bugNumber += bugResultList.length;
                for (const bugMsg of bugResultList) {
                    yield stream.flush(<p>---------------------------------------------------------</p>);
                    yield stream.flush(
                        <p>
                            <alert-tag level="critical">严重</alert-tag> {bugMsg.bugIndex}.{bugMsg.bugPromptTitle}
                        </p>
                    );
                    yield stream.flush(
                        <p>
                            - 风险位置：<file-link to={bugMsg.bugFilePath} line={bugMsg.bugLineNumber}>
                                {bugMsg.bugFilePath}:{bugMsg.bugLineNumber}
                            </file-link>
                        </p>
                    );
                    yield stream.flush(
                        <p>
                            - 判定理由：<code>{bugMsg.bugReason}</code>
                        </p>
                    );

                    const bugData: bugRefuseInfo = {
                        bugSign: Md5.hashStr(bugMsg.bugLocation + bugMsg.bugPromptTitle + bugMsg.bugReason),
                        markReason: '点击拒绝按钮拒绝',
                        status: 2,
                    };

                    yield stream.flush(
                        <command-button
                            commandName="bug:refuse"
                            data={bugData}
                            replyText="标记误告"
                            variant="primary"
                        >
                            标记误告
                        </command-button>
                    );
                }
                result = result.concat(resultList);
                // eslint-disable-next-line
                requestPromiseList.splice(0, requestPromiseList.length);
                requestItemList.splice(0, requestItemList.length);
            }

            if (result.length === 0) {
                yield stream.flush(
                    <ul>
                        <li>未发现风险</li>
                    </ul>
                );
            }
            else {
                const bugResult = [];
                for (const modelResult of result.values()) {
                    bugResult.push({
                        taskId: taskId,
                        bugSource: 0,
                        bugReason: modelResult.reason,
                        bugContext: modelResult.errorLineContent,
                        bugSelfSign: Md5.hashStr(
                            modelResult.location
                                + modelResult.title + modelResult.reason
                        ),
                        codeFile: modelResult.location,
                        codeErrorLines: modelResult.errorLineContent,
                        bugUser: user ? user.name : 'unknown',
                        regularId: modelResult.promptId,
                        subTool: 'aisa',
                    });
                }

                await wboxBugReport(bugResult);
                isSuccess = true;
            }
        }
        catch (e) {
            yield stream.flush(
                <ul>
                    <li>扫描失败，请联系技术支持，可添加如流群(3939419)咨询</li>
                </ul>
            );
        }
        finally {
            await wboxCallBack({
                taskId: taskId ? taskId : 0,
                status: isSuccess ? 2 : 1,
                caller: user ? user.name : 'unknown',
            });
        }
    }
}
