/* eslint-disable no-useless-escape */
import {
    FunctionParameterDefinition,
    SkillProvider,
    TaskProgressChunk,
    ProviderInit,
    ElementChunkStream,
} from '@comate/plugin-host';

import {FeedStrategyStatus} from '../consts/status.js';
import {GitUtil} from '../utils/git.js';
import {BosUtil} from '../utils/bos.js';
import {formatDuration, getSessionTokens} from '../utils/util.js';
import {wboxCallBack, wboxRegister} from '../utils/wbox.js';

interface Args {
    query: string;
}

interface riskInfo {
    id: number;
    message: string;
    bugInfo: feedBugRefuseInfo;
}

interface feedBugRefuseInfo {
    module: string;
    branch: string;
    commit: string;
    task_name: string;
    fields: {
        feedback_status: boolean;
        feedback_info: string;
    };
}

export class FeedSaScanProvider extends SkillProvider<Args> {
    static skillName = 'feedSaScan';

    static displayName = 'feed代码缺陷检测';

    static description = 'feed代码缺陷检测';

    static kirinStrategyUrl = 'http://kirin.baidu-int.com/api/tool/213840';

    static kirinResultUrl = 'http://kirin.baidu-int.com/api/task/';

    static flowMessage = '可添加如流群(9801008)咨询具体事项';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    private readonly git: GitUtil;

    constructor(init: ProviderInit) {
        super(init);

        this.git = new GitUtil(init.cwd);
    }

    sleep(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async callKirinAsyncStrategy(data: any) {
        const response = await fetch(FeedSaScanProvider.kirinStrategyUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
        });

        const result = await response.json();
        if (response.ok && result.message === 'success' && result.status) {
            return result.result.taskId;
        }

        return null;
    }

    async getKirinAsyncStrategyResult(taskId: number) {
        let running = true;
        const timeout = 180 * 1000; // 180s

        const timer = setTimeout(() => {
            running = false;
        }, timeout);

        try {
            // eslint-disable-next-line
            while (running) {
                const response = await fetch(FeedSaScanProvider.kirinResultUrl + taskId, {
                    method: 'GET',
                });

                const result = await response.json();

                if (response.ok && result.status && result.result.taskStatus === 3) {
                    // eslint-disable-next-line
                    try {
                        const resultJsonObj = JSON.parse(result.result.response);
                        // eslint-disable-next-line
                        if (resultJsonObj.status === 0) {
                            return {
                                status: FeedStrategyStatus.SUCCESS,
                                riskNum: resultJsonObj.risk_num,
                                result: resultJsonObj.result,
                                message: null,
                            };
                        }
                        else {
                            return {
                                status: FeedStrategyStatus.ERROR_STRATEGY_FAIL,
                                riskNum: 0,
                                result: null,
                                message: resultJsonObj.message,
                            };
                        }
                    }
                    catch (e) {
                        return {
                            status: FeedStrategyStatus.ERROR_STRATEGY_FAIL,
                            riskNum: 0,
                            result: null,
                            message: null,
                        };
                    }
                }

                if (response.ok && result.status && result.result.taskStatus === 4) {
                    return {status: FeedStrategyStatus.ERROR_STRATEGY_FAIL, riskNum: 0, result: null, message: null};
                }

                await this.sleep(1 * 1000);
            }
        }
        finally {
            clearTimeout(timer);
        }

        return {status: FeedStrategyStatus.ERROR_TIMEOUT, riskNum: 0, result: null, message: null};
    }

    async uploadDiffToBos(stagedDiff: string, unstagedDiff: string, commitId: string, timestamp: string) {
        const bosStsSession = await getSessionTokens();
        if (!bosStsSession) {
            throw new Error('can not get bos sts session');
        }
        const bosStsClient : BosUtil = new BosUtil(
            bosStsSession.data.access_key_id,
            bosStsSession.data.secret_access_key,
            'https://qep-public.bj.bcebos.com',
            bosStsSession.data.session_token
        );
        await bosStsClient.putObjFromStr(
            'qep-public',
            'feed-qa/feed_risk_check/' + timestamp + '-' + commitId + '-stagedDiff.patch',
            stagedDiff
        );

        await bosStsClient.putObjFromStr(
            'qep-public',
            'feed-qa/feed_risk_check/' + timestamp + '-' + commitId + '-unstagedDiff.patch',
            unstagedDiff
        );
    }

    async feedBugRefuse(data: any) {
        try {
            const response = await fetch('https://rankservice.baidu-int.com/white_api/update_white_data', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data),
            });
            const responseObj = await response.json();
            return;
        }
        // eslint-disable-next-line
        catch {
        }
        return;
    }

    async *handleCommand(commandName: string, data: any): AsyncIterableIterator<TaskProgressChunk> {
        try {
            if (commandName === 'bug:refuse') {
                const stream = new ElementChunkStream();
                await this.feedBugRefuse(data);
                yield stream.flush(<p>已将该bug标记为误告</p>);
            }
        }
        // eslint-disable-next-line
        catch {
        }
    }

    // eslint-disable-next-line
    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new ElementChunkStream();
        const user = await this.currentUser.requestDetail();
        let isSuccess = false;
        // wbox平台注册本次扫描任务
        const taskId = await wboxRegister(user, 'feedSaScan', this.currentContext.query, this.git);
        try {
            const fs = await this.requestWorkspaceFileSystem();
            if (!fs) {
                yield stream.flush(<p>获取文件权限失败，无法进行扫描....</p>);
                return;
            }
            const user = await this.currentUser.requestDetail();
            const moduleName = await this.git.getModuleName();
            const branch = await this.git.getBranch();
            const commitId = await this.git.getCommitId();

            const stagedDiff = await this.git.getStagedDiffContent();
            const unStagedDiff = await this.git.getUnstagedDiffContent();
            const timestampString: string = new Date().getTime().toString();

            yield stream.flush(<h4>&gt; 思考过程</h4>);

            yield stream.flush(
                <ul>
                    <li>获取diff内容......</li>
                </ul>
            );
            const getDiffStartTime = performance.now();
            await this.uploadDiffToBos(
                stagedDiff,
                unStagedDiff,
                commitId ? commitId.trim() : 'unknown',
                timestampString
            );
            const getDiffEndTime = performance.now();
            let consumeTime = formatDuration(getDiffEndTime - getDiffStartTime);
            yield stream.flushReplaceLast(
                <ul>
                    <li>获取diff内容......{consumeTime}</li>
                </ul>
            );

            yield stream.flush(
                <ul>
                    <li>开始调用策略......</li>
                </ul>
            );
            const callKirinAsyncStrategyStartTime = performance.now();
            const kirinTaskId = await this.callKirinAsyncStrategy({
                parameter: JSON.stringify({
                    module: moduleName ? moduleName.trim() : 'unknown',
                    branch: branch ? branch.trim() : 'unknown',
                    // eslint-disable-next-line
                    commitId: commitId ? commitId.trim() : 'unknown',
                    // eslint-disable-next-line
                    patch_file: timestampString + '-' + (commitId ? commitId.trim() : 'unknown'),
                    // eslint-disable-next-line
                    username: user ? user.name : 'unknown',
                    // eslint-disable-next-line
                    task_id: taskId,
                }),
                name: 'zhaomingming04',
                token: 'e33cc284830b4ba98a46e878afc16114',
            });
            const callKirinAsyncStrategyEndTime = performance.now();
            consumeTime = formatDuration(callKirinAsyncStrategyEndTime - callKirinAsyncStrategyStartTime);
            yield stream.flush(
                <ul>
                    <li>开始调用策略......{consumeTime}</li>
                </ul>
            );

            if (!kirinTaskId) {
                yield stream.flush(
                    <ul>
                        <li>调用策略失败，无法进行扫描....</li>
                    </ul>
                );
                return;
            }

            yield stream.flush(
                <ul>
                    <li>调用策略成功，开始等待结果......</li>
                </ul>
            );
            const callKirinResultStartTime = performance.now();
            const {status, result, message} = await this.getKirinAsyncStrategyResult(kirinTaskId);
            const callKirinResultEndTime = performance.now();
            consumeTime = formatDuration(callKirinResultEndTime - callKirinResultStartTime);
            yield stream.flush(
                <ul>
                    <li>调用策略成功，开始等待结果......{consumeTime}</li>
                </ul>
            );
            if (status === FeedStrategyStatus.SUCCESS) {
                yield stream.flush(<h4>&gt; 检测结果</h4>);
                if (result.length === 0) {
                    yield stream.flush(
                        <ul>
                            <li>没有风险，恭喜你！</li>
                        </ul>
                    );
                    isSuccess = true;
                    return;
                }

                let index = 1;
                for (const resultItem of result) {
                    if (resultItem.risk_data.length === 0) {
                        continue;
                    }
                    yield stream.flush(
                        <h5>
                            风险类型{index}: {resultItem.risk_name} <alert-tag level="critical">严重</alert-tag>
                        </h5>
                    );
                    yield stream.flush(
                        <ul>
                            <li>风险描述: {resultItem.risk_reason}</li>
                        </ul>
                    );
                    yield stream.flush(
                        <ul>
                            <li>
                                修复建议: <markdown>{resultItem.fix_references}</markdown>
                            </li>
                        </ul>
                    );
                    const riskInfo: riskInfo[] = [];
                    let riskDatIndex = 1;
                    // eslint-disable-next-line
                    for (const riskData of resultItem.risk_data) {
                        let riskMsg = `文件: [${riskData.risk_file}](${riskData.risk_file}:${
                            riskData.risk_lineno[0]
                        }) <br> 行号: [${riskData.risk_lineno}]`;
                        if (riskData.risk_info) {
                            riskMsg += `<br> 风险信息: ${riskData.risk_info}`;
                        }

                        riskInfo.push({
                            id: riskDatIndex,
                            message: riskMsg,
                            bugInfo: {
                                module: moduleName ? moduleName : 'unknown',
                                branch: branch ? branch : 'unknonw',
                                commit: commitId ? commitId + '#' + taskId : 'unknown',
                                task_name: resultItem.risk_name,
                                fields: {
                                    feedback_status: false,
                                    feedback_info: resultItem.risk_name + '#' + riskData.risk_file,
                                },
                            },
                        });
                        ++riskDatIndex;
                    }
                    yield stream.flush(
                        <table>
                            <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>风险信息</th>
                                    <th>备注</th>
                                </tr>
                            </thead>
                            <tbody>
                                {riskInfo.map((row, index) => (
                                    <tr>
                                        <td>{row.id}</td>
                                        <td>
                                            <markdown>{row.message}</markdown>
                                        </td>
                                        <td>
                                            <command-button
                                                commandName="bug:refuse"
                                                data={row.bugInfo}
                                                replyText="标记误告"
                                                variant="primary"
                                            >
                                                误告
                                            </command-button>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    );
                    ++index;
                }
                isSuccess = true;
            }
            // eslint-disable-next-line
            else if (!message) {
                yield stream.flush(
                    <ul>
                        <li>调用策略失败，无法进行扫描....</li>
                    </ul>
                );
            }
            else {
                yield stream.flush(
                    <ul>
                        <li>{message}</li>
                    </ul>
                );
            }
        }
        catch (e) {
            yield stream.flush(
                <ul>
                    <li>调用策略失败，无法进行扫描....</li>
                </ul>
            );
        }
        finally {
            await wboxCallBack({
                taskId: taskId ? taskId : 0,
                status: isSuccess ? 2 : 1,
                caller: user ? user.name : 'unknown',
            });
        }
    }
}
