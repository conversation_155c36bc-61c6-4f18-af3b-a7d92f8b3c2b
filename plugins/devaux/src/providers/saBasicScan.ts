/* eslint-disable no-useless-escape */
import path from 'node:path';
import Parser from 'web-tree-sitter';
import {languageExtMap} from '../consts/languageParser.js';
import {getDirname, getExtName} from '../utils/fs.js';

export class BasicScanParser {
    static languageParser = new Map<string, Parser>();

    static async getActiveFileExt(activeFilePath: string): Promise<string> {
        const ext = getExtName(activeFilePath);
        return ext;
    }

    static async getParser(activeFileExt: string) {
        if (this.languageParser.get(activeFileExt)) {
            return this.languageParser.get(activeFileExt);
        }

        const dirname = getDirname();
        const wasmFile = path.resolve(dirname, 'tree-sitter.wasm');
        await Parser.init({
            locateFile() {
                return wasmFile;
            },
        });

        const parser = new Parser();
        const parserWasm = languageExtMap.get(activeFileExt);
        if (!parserWasm) {
            return null;
        }
        const Lang = await Parser.Language.load(parserWasm);
        parser.setLanguage(Lang);

        this.languageParser.set(activeFileExt, parser);
        return parser;
    }

    static async getCurrentFileTreeSitterInstance(activeFilePath: string, codeContent: string, ext: string) {
        try {
            const dirname = getDirname();
            // wasm 需要，不加会报错
            // eslint-disable-next-line
            global.__dirname = dirname;

            const languageBinding = languageExtMap.get(ext);
            if (languageBinding) {
                const parser = await BasicScanParser.getParser(ext);
                if (!parser) {
                    return null;
                }
                const tree = parser.parse(codeContent);
                return tree;
            }
            return null;
        }
        catch (ex) {
            // eslint-disable-next-line
            console.log(ex);
            return null;
        }
    }

    async getCurrentFileTreeSitter(activeFilePath: string, codeContent: string, ext: string) {
        const tree = await BasicScanParser.getCurrentFileTreeSitterInstance(activeFilePath, codeContent, ext);
        return tree;
    }
}
