import {
    FunctionParameterDefinition,
    SkillProvider,
    ProviderInit,
    TaskProgressChunk,
    UserDetail,
    ElementChunkStream,
} from '@comate/plugin-host';
import path from 'path';
import {GitUtil} from '../utils/git.js';
import * as fs from 'fs';
import {wboxCallBack, wboxRegister} from '../utils/wbox.js';
import {renderTemplate} from '../utils/util.js';
import {taskExecuteErrorInfo, taskExecuteTimeoutErroInfo} from '../consts/errorInfo.js';
import {kirinToken, kirinUserName} from '../consts/configs.js';
import {userServiceHiGroupNumber, kirinAsyncWebGenURL} from '../consts/urls.js';
import {
    startKirinAsyncStrategy,
    getKirinAsyncStrategyResult,
} from '../utils/kirin.js';
import {FeedStrategyStatus} from '../consts/status.js';
interface Args {
    query: string;
}

export class conflictResolutionProvider extends SkillProvider<Args> {
    static skillName = 'conflictResolution';

    static displayName = '冲突解决';

    static description = '代码冲突解决功能';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    private readonly git: GitUtil;
    private readonly cwd: string;

    constructor(init: ProviderInit) {
        super(init);
        this.git = new GitUtil(init.cwd);
        this.cwd = init.cwd;
    }

    // eslint-disable-next-line
    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new ElementChunkStream();
        let user: false | UserDetail = false;
        let taskId: number = 0;
        let isSuccess = false;
        try {
            user = await this.currentUser.requestDetail();
            const moduleName = await this.git.getModuleName(this.cwd);
            // wbox平台注册本次扫描任务
            taskId = await wboxRegister(user, 'confilctResolution', this.currentContext.query, this.git);
            if (this.currentContext.query.trim() === '') {
                yield stream.flush(<p>需求描述不可为空，请添加需求描述</p>);
                isSuccess = true;
                return;
            }

            // 获取当前打开文件的内容
            const current_file = path.resolve(this.cwd, this.currentContext.activeFilePath);
            const current_content = fs.readFileSync(current_file, 'utf-8');

            const kirinTaskID = await startKirinAsyncStrategy(kirinAsyncWebGenURL, {
                parameter: JSON.stringify({
                    requirement: this.currentContext.query,
                    conf: current_content,
                    conf_file: this.currentContext.activeFilePath,
                    moduleName: moduleName,
                    rd: user ? user.name : 'unknown',
                    scene: 'lite_ios',
                    // eslint-disable-next-line
                }),
                name: kirinUserName,
                token: kirinToken,
            });

            const {status, kirinResponse, message} = await getKirinAsyncStrategyResult(kirinTaskID);

            if (status === FeedStrategyStatus.ERROR_STRATEGY_FAIL) {
                const failMessage = renderTemplate(taskExecuteErrorInfo, {
                    groupID: userServiceHiGroupNumber,
                    taskID: kirinTaskID,
                });
                yield stream.flush(<p>{failMessage}</p>);
                if (message) {
                    yield stream.flush(<p>{message}</p>);
                }
                return;
            }
            else if (status === FeedStrategyStatus.ERROR_TIMEOUT) {
                const failMessage = renderTemplate(taskExecuteTimeoutErroInfo, {
                    groupID: userServiceHiGroupNumber,
                    taskID: kirinTaskID,
                });
                yield stream.flush(<p>{failMessage}</p>);
                return;
            }

            if (kirinResponse.length === 0) {
                const failMessage = renderTemplate(taskExecuteErrorInfo, {
                    groupID: userServiceHiGroupNumber,
                    taskID: kirinTaskID,
                });
                yield stream.flushReplaceLast(<p>{failMessage}</p>);
                return;
            }

            for (const [index, kirinRes] of kirinResponse.entries()) {
                const models = kirinRes.models;
                if (!models.url) {
                    if (kirinRes.add_before && kirinRes.add_before.length !== 0) {
                        const codeBlockRegex = /```(\w+)?\s*([\s\S]*?)\s*```/g;
                        let matches = kirinRes.add_before.matchAll(codeBlockRegex);
                        for (const match of matches) {
                            const language = match[1] || ''; // 提取语言
                            const code = match[2] ? match[2].trim() : ''; // 提取代码内容

                            yield stream.flush(
                                <code-block
                                    language={language}
                                    replaceToFileData={{
                                        filePath: current_file,
                                        from: current_content,
                                        to: code,
                                        replaceAll: true,
                                    }}
                                    actions={['accept', 'copy', 'showFileReplaceDiff', 'newFile']}
                                    closed={false}
                                >
                                    {code}
                                </code-block>
                            );
                        }
                    }
                    if (kirinRes.add_after && kirinRes.add_after.length !== 0) {
                        yield stream.flush(<markdown>{kirinRes.add_after}</markdown>);
                    }
                }

                yield stream.flush(<br></br>);
            }

            isSuccess = true;
        }
        catch (e: any) {
            const failMessage = renderTemplate(taskExecuteErrorInfo, {
                groupID: userServiceHiGroupNumber,
                taskID: taskId,
            });
            yield stream.flush(<p>{e.toString()}</p>);
            yield stream.flush(<p>{failMessage}</p>);
        }
        finally {
            await wboxCallBack({
                taskId: taskId ? taskId : 0,
                status: isSuccess ? 2 : 1,
                caller: user ? user.name : 'unknown',
            });
        }
    }
}
