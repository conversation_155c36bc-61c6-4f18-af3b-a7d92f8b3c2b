/* eslint-disable no-useless-escape */
import {
    FunctionParameterDefinition,
    SkillProvider,
    ProviderInit,
    TaskProgressChunk,
    StringChunkStream,
} from '@comate/plugin-host';

import {spawnSync} from 'child_process';
import {FeedStrategyStatus} from '../consts/status.js';
import {GitUtil} from '../utils/git.js';
import {wboxRegister, wboxCallBack} from '../utils/wbox.js';
import {BosUtil} from '../utils/bos.js';
import path from 'path';
import {sleep} from '../utils/util.js';
import {formatDuration} from '../utils/util.js';
import {getGitUtilObj, getSessionTokens} from '../utils/util.js';
import {GitDirStatus} from '../consts/status.js';
interface Args {
    query: string;
}

export class OPSelfTestProvider extends SkillProvider<Args> {
    static skillName = 'opSelfTest';

    static displayName = '算子自测';

    static description = 'feed算子自测';

    static caseFilterKirinStrategyUrl = 'http://kirin.baidu-int.com/api/tool/213877';

    static testKirinStrategyUrl = 'http://kirin.baidu-int.com/api/tool/213880';

    static KirinResultUrl = 'http://kirin.baidu-int.com/api/task/';

    static flowMessage = '可添加如流群(9801008)咨询具体事项';

    static hiRobotUrl = 'http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=db9fc843e25092e3a1b1bd10e4c05eb84';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    private readonly cwd: string;

    private gitDir: string = '';

    private git: GitUtil;

    constructor(init: ProviderInit) {
        super(init);

        this.git = new GitUtil(init.cwd);

        this.cwd = init.cwd;
    }

    sleep(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async callKirinAsyncStrategy(data: any, kirinUrl: string) {
        const response = await fetch(kirinUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
        });

        const result = await response.json();
        if (response.ok && result.message === 'success' && result.status) {
            return result.result.taskId;
        }

        return null;
    }

    async callKirinSyncStrategy(data: any, kirinUrl: string) {
        try {
            const response = await fetch(kirinUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data),
            });
            const result = await response.json();
            if (response.ok && result.message === 'success' && result.status) {
                return {syncResult: JSON.parse(result.result.response), syncTaskId: result.result.taskId};
            }
        }
        catch (e) {
            return {syncResult: null, syncTaskId: null};
        }

        return {syncResult: null, syncTaskId: null};
    }

    async getKirinAsyncStrategyResult(taskId: number, kirinResultUrl: string) {
        let running = true;
        const timeout = 600 * 1000; // 10min

        const timer = setTimeout(() => {
            running = false;
        }, timeout);

        try {
            // eslint-disable-next-line
            while (running) {
                const response = await fetch(kirinResultUrl + taskId, {
                    method: 'GET',
                });

                const result = await response.json();

                if (response.ok && result.status && result.result.taskStatus === 3) {
                    // eslint-disable-next-line
                    try {
                        const resultJsonObj = JSON.parse(result.result.response);
                        // eslint-disable-next-line
                        if (resultJsonObj.status) {
                            return {
                                status: FeedStrategyStatus.SUCCESS,
                                resultJsonObj: resultJsonObj,
                                message: null,
                            };
                        }
                        else {
                            return {
                                status: FeedStrategyStatus.ERROR_STRATEGY_FAIL,
                                resultJsonObj: null,
                                message: resultJsonObj.message,
                            };
                        }
                    }
                    catch (e) {
                        return {status: FeedStrategyStatus.ERROR_STRATEGY_FAIL, resultJsonObj: null, message: null};
                    }
                }

                if (response.ok && result.status && result.result.taskStatus === 4) {
                    return {status: FeedStrategyStatus.ERROR_STRATEGY_FAIL, resultJsonObj: null, message: null};
                }

                await this.sleep(1 * 1000);
            }
        }
        finally {
            clearTimeout(timer);
        }

        return {status: FeedStrategyStatus.ERROR_TIMEOUT, resultJsonObj: null, message: null};
    }

    async update2Qadc(data: any, isInsert = false) {
        let tryTimes = 3;
        try {
            while (tryTimes) {
                let url = 'http://qadc.baidu-int.com/api/insert/feed_fe_self_test_record';
                if (!isInsert) {
                    url = 'http://qadc.baidu-int.com/api/update/feed_fe_self_test_record/task_id';
                }

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        name: 'zhaomingming04',
                        token: 'f0aa0153a8f24ad3ac199e8251e1bfd2',
                    },
                    // eslint-disable-next-line
                    body: JSON.stringify([data]),
                });
                const responseObj = await response.json();
                if (responseObj.code === 0) {
                    return;
                }
                else {
                    --tryTimes;
                    await this.sleep(2 * 1000);
                }
            }
        }
        // eslint-disable-next-line
        catch {
        }
    }

    async sendMessage2Hi(message: string) {
        try {
            const payload = {
                message: {
                    header: {
                        toid: [9849201],
                    },
                    body: [{content: message, type: 'TEXT'}, {atall: true, type: 'AT'}],
                },
            };
            await fetch(OPSelfTestProvider.hiRobotUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(payload),
            });
        }
        // eslint-disable-next-line
        catch (e) {
        }
    }

    // eslint-disable-next-line
    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new StringChunkStream();
        const user = await this.currentUser.requestDetail();
        const userName = user ? user.name : 'unknown';
        const fs = await this.requestFullDiskFileSystem();
        if (!fs) {
            return;
        }

        let taskId = 0;
        let isSuccess = false;
        let reportUrl = '';
        const startTime = performance.now();
        let activeFilePath = '';
        let activeFileContent = '';
        try {
            const {gitDirStatus, gitDir} = await getGitUtilObj(path.join(this.cwd, this.currentContext.activeFilePath));
            if (gitDirStatus === GitDirStatus.ERROR_NOT_READ_PERMISSION) {
                yield stream.flush(`当前git工程目录不可读${OPSelfTestProvider.flowMessage}`);
                return;
            }
            else if (gitDirStatus === GitDirStatus.ERROR_NOT_GIT_REPO) {
                yield stream.flush(`当前不是git工程${OPSelfTestProvider.flowMessage}`);
                return;
            }
            else if (!gitDir) {
                yield stream.flush(`无法找到包含.git文件夹的路径${OPSelfTestProvider.flowMessage}`);
                return;
            }
            this.git = new GitUtil(gitDir);
            this.gitDir = gitDir;
            // wbox平台注册本次扫描任务
            taskId = await wboxRegister(user, 'opSelfTest', this.currentContext.query, this.git);
            activeFilePath = path.relative(this.gitDir, path.join(this.cwd, this.currentContext.activeFilePath));

            if (this.currentContext.query.length === 0) {
                yield stream.flush('sample地址不可为空，请添加sample地址， ' + OPSelfTestProvider.flowMessage + '\n\n');
                isSuccess = true;
                return;
            }

            if (this.currentContext.selectedCode.length === 0) {
                yield stream.flush('需要选中配置文件内容，请修改重试，' + OPSelfTestProvider.flowMessage + '\n\n');
                isSuccess = true;
                return;
            }

            const isMultipleLine = this.currentContext.selectedCode.split('\n').length >= 2;
            if (isMultipleLine) {
                activeFileContent = this.currentContext.selectedCode;
            }
            else {
                activeFileContent = this.currentContext.activeFileLineContent;
            }

            const caseFilterStartime = performance.now();
            // eslint-disable-next-line
            const caseFiltertaskID = await this.callKirinAsyncStrategy({
                parameter: JSON.stringify({
                    sample: this.currentContext.query.trim(),
                }),
                name: 'zhaomingming04',
                token: 'e33cc284830b4ba98a46e878afc16114',
            }, OPSelfTestProvider.caseFilterKirinStrategyUrl);
            yield stream.flush(
                // eslint-disable-next-line
                '* 正在执行本地编译命令：bcloud build --targets=\'feed-extract\' \
                    --no-release.bcloud > compile.log 2>&1，请稍后......'
            );
            const bcloudBuildStartTime = performance.now();
            let result = spawnSync(
                `cd ${this.gitDir} && bcloud build --targets='feed-extract' --no-release.bcloud > compile.log 2>&1`,
                {shell: true, encoding: 'utf-8'}
            );
            const bcloudBuildEndTime = performance.now();
            await this.update2Qadc({
                // eslint-disable-next-line
                task_id: taskId,
                // eslint-disable-next-line
                compile_dur: Math.round((bcloudBuildEndTime - bcloudBuildStartTime) / 1000),
                // eslint-disable-next-line
                compile_status: !(result.error || result.status !== 0),
                // eslint-disable-next-line
                rd_name: user ? user.name : 'unknown',
                conf: activeFileContent,
                // eslint-disable-next-line
                sample_path: this.currentContext.query,
                // eslint-disable-next-line
                file_path: activeFilePath,
            }, true);
            if (result.error || result.status !== 0) {
                const failMessage = '* 编译失败：编译失败，编译日志位置：'
                    + path.join(this.gitDir, 'compile.log') + ', ' + OPSelfTestProvider.flowMessage + '\n\n';
                yield stream.flush('\n\n');
                yield stream.flush(failMessage);

                const bosStsSession = await getSessionTokens();
                if (!bosStsSession) {
                    throw new Error('can not get bos sts session');
                }
                const bosStsClient: BosUtil = new BosUtil(
                    bosStsSession.data.access_key_id,
                    bosStsSession.data.secret_access_key,
                    'https://qep-public.bj.bcebos.com',
                    bosStsSession.data.session_token
                );
                await bosStsClient.putObjFromFile(
                    'qe-public',
                    'feed-qa/fe_compile_log/' + taskId + '.log',
                    path.join(this.gitDir, 'compile.log')
                );

                await this.sendMessage2Hi(
                    // eslint-disable-next-line
                    'case_filter_taskid:[' + caseFiltertaskID + '],' + `rd:[${userName}],` + 'query:[' + this
                        .currentContext
                        .query
                        + '],'
                        + 'conf:[' + this
                        .currentContext
                        .activeFileLineContent
                        // eslint-disable-next-line
                        + '], ' + 'fail message:[' + failMessage + ']'
                );
                return;
            }
            yield stream.flush(formatDuration(bcloudBuildEndTime - bcloudBuildStartTime) + '\n\n');

            yield stream.flush('* 上传中：编译成功，正在上传output产出包......');
            const uploadBosStartTime = performance.now();
            const compressPackageName = taskId + '.tar.gz';
            result = spawnSync(
                `cd ${this.gitDir} && tar -czvf ${compressPackageName} output`,
                {shell: true, encoding: 'utf-8'}
            );
            if (result.error) {
                const failMessage = '* 打包产出失败， taskId:' + caseFiltertaskID + ', '
                    + OPSelfTestProvider.flowMessage + '\n\n';
                yield stream.flush('\n\n');
                yield stream.flush(failMessage);
                await this.sendMessage2Hi(
                    // eslint-disable-next-line
                    'case_filter_taskid:[' + caseFiltertaskID + '],'
                        // eslint-disable-next-line
                        + `rd:[${userName}],` + 'query:[' + this.currentContext.query + '],'
                        + 'conf:[' + this
                        .currentContext
                        .activeFileLineContent
                        // eslint-disable-next-line
                        + '], ' + 'fail message:[' + failMessage + ']'
                );
                return;
            }

            const bosStsSession = await getSessionTokens();
            if (!bosStsSession) {
                throw new Error('can not get bos sts session');
            }
            const bosStsClient: BosUtil = new BosUtil(
                bosStsSession.data.access_key_id,
                bosStsSession.data.secret_access_key,
                'https://qep-public.bj.bcebos.com',
                bosStsSession.data.session_token
            );

            await bosStsClient
                .putObjFromFile(
                    'qep-public',
                    `feed-qa/fe_compile_output/${compressPackageName}`,
                    path.join(this.gitDir, compressPackageName)
                );
            const uploadBosEndTime = performance.now();

            yield stream.flush(formatDuration(uploadBosEndTime - uploadBosStartTime) + '\n\n');
            spawnSync(
                `cd ${this.gitDir} && rm -rf  ${compressPackageName}`,
                {shell: true, encoding: 'utf-8'}
            );

            yield stream.flush('* 上传成功，开始执行算子自测流程......\n\n');
            // eslint-disable-next-line
            const {status, resultJsonObj, message} = await this.getKirinAsyncStrategyResult(
                caseFiltertaskID,
                OPSelfTestProvider.KirinResultUrl
            );
            const caseFilterEndime = performance.now();

            if (status !== FeedStrategyStatus.SUCCESS) {
                const failMessage = `* 任务失败：${message}, case filter taskId:
                    ${caseFiltertaskID}, ` + OPSelfTestProvider.flowMessage + '\n\n';
                yield stream.flush(failMessage);
                await this.update2Qadc({
                    // eslint-disable-next-line
                    task_id: taskId,
                    // eslint-disable-next-line
                    case_select_status: false,
                    // eslint-disable-next-line
                    case_select_dur: Math.round((caseFilterEndime - caseFilterStartime) / 1000),
                    // eslint-disable-next-line
                    upload_bos_status: true,
                    // eslint-disable-next-line
                    upload_bos_dur: uploadBosEndTime - uploadBosStartTime,
                    // eslint-disable-next-line
                    bos_address: `feed-qa/fe_compile_output/${compressPackageName}`,
                });

                await this.sendMessage2Hi(
                    // eslint-disable-next-line
                    'case_filter_taskid:[' + caseFiltertaskID + '],'
                        // eslint-disable-next-line
                        + `rd:[${userName}],` + 'query:[' + this.currentContext.query + '],'
                        + 'conf:[' + this
                        .currentContext
                        .activeFileLineContent
                        // eslint-disable-next-line
                        + '], ' + 'fail message:[' + failMessage + ']'
                );
                return;
            }

            let isFirstFlush = true;
            let running = true;
            const timeout = 600 * 1000; // 10min

            const timer = setTimeout(() => {
                running = false;
            }, timeout);
            const testStartime = performance.now();
            let testCaseTaskId = 0;
            try {
                // eslint-disable-next-line
                while (running) {
                    const {syncResult, syncTaskId} = await this.callKirinSyncStrategy({
                        parameter: JSON.stringify({
                            stageBuildId: resultJsonObj.stageBuildId,
                            pipelineBuildId: resultJsonObj.pipelineBuildId,
                            // eslint-disable-next-line
                            task_id: taskId,
                            // eslint-disable-next-line
                            file_path: activeFilePath,
                            conf: activeFileContent.trim(),
                            // eslint-disable-next-line
                            bos_address: `feed-qa/fe_compile_output/${compressPackageName}`,
                        }),
                        name: 'zhaomingming04',
                        token: 'e33cc284830b4ba98a46e878afc16114',
                    }, OPSelfTestProvider.testKirinStrategyUrl);
                    testCaseTaskId = syncTaskId;

                    // eslint-disable-next-line
                    if (!syncResult) {
                        const failMessage = `* 任务失败：调用算子自测流水线失败，case filter taskId:${testCaseTaskId}, `
                            + OPSelfTestProvider.flowMessage + '\n\n';
                        yield stream.flush(failMessage);
                        await this.sendMessage2Hi(
                            // eslint-disable-next-line
                            'case_filter_taskid:[' + caseFiltertaskID + '], test_case_taskid:[' + testCaseTaskId + '],'
                                // eslint-disable-next-line
                                + `rd:[${userName}],` + 'query:[' + this.currentContext.query + '],'
                                + 'conf:[' + this
                                .currentContext
                                .activeFileLineContent
                                // eslint-disable-next-line
                                + '], ' + 'fail message:[' + failMessage + ']'
                        );
                        return;
                    }

                    // eslint-disable-next-line
                    if (syncResult.status === 1) {
                        // eslint-disable-next-line
                        if (isFirstFlush) {
                            yield stream.flush(
                                '* ' + syncResult.message + '\n\n'
                            );
                        }
                        else {
                            yield stream.flushReplaceLast(
                                '* ' + syncResult.message + '\n\n'
                            );
                        }
                        isFirstFlush = false;
                    }
                    else if (syncResult.status === 2) {
                        const failMessage = '* ' + syncResult.message
                            // eslint-disable-next-line
                            + `，case test taskId:${syncTaskId}, ${OPSelfTestProvider.flowMessage}` + '\n\n';
                        yield stream.flush(failMessage);
                        await this.sendMessage2Hi(
                            // eslint-disable-next-line
                            'case_filter_taskid:[' + caseFiltertaskID + '], test_case_taskid:[' + testCaseTaskId + '],'
                                // eslint-disable-next-line
                                + `rd:[${userName}],` + 'query:[' + this.currentContext.query + '],'
                                + 'conf:[' + this
                                .currentContext
                                .activeFileLineContent
                                // eslint-disable-next-line
                                + '], ' + 'fail message:[' + failMessage + ']'
                        );
                        return;
                    }
                    else if (syncResult.status === 0) {
                        const message = syncResult.message.substring(
                            0,
                            syncResult.message.indexOf(syncResult.report_url)
                        );
                        reportUrl = syncResult.report_url;
                        yield stream.flush(
                            `* ${message}[测试报告链接](${syncResult.report_url})\n\n`
                        );
                        isSuccess = true;
                        await this.sendMessage2Hi(
                            // eslint-disable-next-line
                            'case_filter_taskid:[' + caseFiltertaskID + '], test_case_taskid:[' + testCaseTaskId + '],'
                                // eslint-disable-next-line
                                + `rd:[${userName}],` + 'query:[' + this.currentContext.query + '],'
                                + 'conf:[' + this
                                .currentContext
                                .activeFileLineContent
                                // eslint-disable-next-line
                                + '], ' + 'success'
                        );
                        return;
                    }
                    await sleep(3 * 1000);
                }
            }
            finally {
                clearTimeout(timer);
                const testEndime = performance.now();
                const endime = performance.now();
                await this.update2Qadc({
                    // eslint-disable-next-line
                    task_id: taskId,
                    // eslint-disable-next-line
                    self_test_status: isSuccess,
                    // eslint-disable-next-line
                    self_test_dur: Math.round((testEndime - testStartime) / 1000),
                    // eslint-disable-next-line
                    case_select_status: true,
                    // eslint-disable-next-line
                    case_select_dur: Math.round((caseFilterEndime - caseFilterStartime) / 1000),
                    stageBuildId: resultJsonObj.stageBuildId,
                    pipelineBuildId: resultJsonObj.pipelineBuildId,
                    // eslint-disable-next-line
                    upload_bos_status: true,
                    // eslint-disable-next-line
                    upload_bos_dur: Math.round((uploadBosEndTime - uploadBosStartTime) / 1000),
                    // eslint-disable-next-line
                    bos_address: `feed-qa/fe_compile_output/${compressPackageName}`,
                    // eslint-disable-next-line
                    report_url: reportUrl,
                    // eslint-disable-next-line
                    total_dur: Math.round((endime - startTime) / 1000),
                });
            }
            if (!running) {
                const failMessage = '任务超时，请重试生成，也' + OPSelfTestProvider.flowMessage + '，taskid:' + taskId;
                yield stream.flush('\n\n');
                yield stream.flush(failMessage);
                await this.sendMessage2Hi(
                    // eslint-disable-next-line
                    'case_filter_taskid:[' + caseFiltertaskID + '], test_case_taskid:[' + testCaseTaskId + '],'
                        // eslint-disable-next-line
                        + `rd:[${userName}],` + 'query:[' + this.currentContext.query + '],'
                        + 'conf:[' + this
                        .currentContext
                        .activeFileLineContent
                        // eslint-disable-next-line
                        + '], ' + 'fail message:[' + failMessage + ']'
                );
            }
        }
        catch (e) {
            const failMessage = '任务失败，请重试生成，也' + OPSelfTestProvider.flowMessage + '，taskid:' + taskId;
            yield stream.flush('\n\n');
            yield stream.flush(failMessage);
            await this.sendMessage2Hi(
                // eslint-disable-next-line
                'taskid:[' + taskId + '],' + `rd:[${userName}],` + 'query:[' + this.currentContext.query + '],'
                    + 'conf:[' + this
                    .currentContext
                    .activeFileLineContent
                    // eslint-disable-next-line
                    + '], ' + 'fail message:[' + failMessage + ']'
            );
        }
        finally {
            if (isSuccess) {
                spawnSync(
                    'rm -rf compile_log',
                    {shell: true, encoding: 'utf-8'}
                );
            }

            await wboxCallBack({
                taskId: taskId ? taskId : 0,
                status: isSuccess ? 2 : 1,
                caller: user ? user.name : 'unknown',
            });
        }
    }
}
