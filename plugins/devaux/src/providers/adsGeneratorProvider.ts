/* eslint-disable no-useless-escape */
import {
    FunctionParameterDefinition,
    SkillProvider,
    ProviderInit,
    TaskProgressChunk,
    StringChunkStream,
} from '@comate/plugin-host';

import {renderTemplate, getReuqestModelStream, getReaderStr, updateChunk2Qadc} from '../utils/util.js';
import {GitUtil} from '../utils/git.js';
import {wboxCallBack, wboxRegister} from '../utils/wbox.js';
import {getKirinCodeGenerateAsyncStrategyResult, kirinReturnCheck, startKirinAsyncStrategy} from '../utils/kirin.js';
import {kirinADSCodeGenerateURL, userServiceHiGroupNumber} from '../consts/urls.js';
import {kirinToken, kirinUserName} from '../consts/configs.js';
import {taskExecuteErrorInfo} from '../consts/errorInfo.js';
import {sleep} from '../utils/util.js';
import {ModelType} from '../consts/modelType.js';
interface Args {
    query: string;
}

export class ADSGeneratorProvider extends SkillProvider<Args> {
    static skillName = 'adsGenerator';

    static displayName = '商业算子生成';

    static description = '商业算子生成';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    private readonly git: GitUtil;

    constructor(init: ProviderInit) {
        super(init);

        this.git = new GitUtil(init.cwd);
    }
    // eslint-disable-next-line
    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new StringChunkStream();
        const user = await this.currentUser.requestDetail();
        let isSuccess = false;
        // wbox平台注册本次扫描任务
        const taskId = await wboxRegister(user, 'adsGenerator', this.currentContext.query, this.git);

        try {
            if (this.currentContext.query.length === 0) {
                yield stream.flush('需求描述不可为空，请添加需求描述');
                isSuccess = true;
                return;
            }

            const fs = await this.requestWorkspaceFileSystem();
            if (!fs) {
                yield stream.flush('未授权文件读取权限');
                isSuccess = true;
                return;
            }

            const kirinTaskID = await startKirinAsyncStrategy(kirinADSCodeGenerateURL, {
                parameter: JSON.stringify({
                    requirement: this.currentContext.query,
                    conf: this.currentContext.selectedCode.trimEnd(),
                    // eslint-disable-next-line
                    file_path: this.currentContext.activeFilePath,
                    // eslint-disable-next-line
                    rd_name: user ? user.name : 'unknown',
                }),
                name: kirinUserName,
                token: kirinToken,
            });

            yield stream.flush('解析您的目标，我将根据您提供的输入\n\n');
            yield stream.flush('- 生成配置：\n\n\n');
            yield stream.flush(`${this.currentContext.activeFileContent}\n\n`);
            yield stream.flush('- 需求描述：\n\n\n');
            yield stream.flush(this.currentContext.query.trim() + '\n\n');

            yield stream.flush('解析您的目标，启动生成，全程预估30s，请耐心等待......\n\n');

            for (let i = 25; i > 0; i = i - 5) {
                yield stream.flushReplaceLast('解析您的目标，启动生成，全程预估' + i + 's，请耐心等待......\n\n');
                await sleep(5 * 1000);
            }

            if (!kirinTaskID) {
                const failMessage = renderTemplate(taskExecuteErrorInfo, {
                    groupID: userServiceHiGroupNumber,
                    taskID: kirinTaskID,
                });
                yield stream.flushReplaceLast(failMessage);
                return;
            }

            const kirinRes = await getKirinCodeGenerateAsyncStrategyResult(kirinTaskID);
            for await (const data of kirinReturnCheck(kirinRes, kirinTaskID)) {
                if (data.type === 'message') {
                    yield stream.flush(data.message);
                    return;
                }
            }

            const modelReader = await getReuqestModelStream(kirinRes.accessToken, kirinRes.result);

            if (!modelReader) {
                const failMessage = renderTemplate(taskExecuteErrorInfo, {
                    groupID: userServiceHiGroupNumber,
                    taskID: kirinTaskID,
                });
                yield stream.flushReplaceLast(failMessage);
                return;
            }

            let chunkStr = '';
            try {
                for await (const data of getReaderStr(modelReader, ModelType.WENXIN)) {
                    // eslint-disable-next-line
                    if (data.type === 'message') {
                        yield stream.flush(data.message);
                        chunkStr += data.message;
                    }
                }
                await updateChunk2Qadc(chunkStr, kirinTaskID, 'shangye_code_gen');
            }
            catch (error) {
                const failMessage = renderTemplate(taskExecuteErrorInfo, {
                    groupID: userServiceHiGroupNumber,
                    taskID: kirinTaskID,
                });
                yield stream.flushReplaceLast(failMessage);
            }
            finally {
                modelReader.releaseLock();
                await updateChunk2Qadc(chunkStr, kirinTaskID, 'shangye_code_gen');
            }
            isSuccess = true;
        }
        catch (e) {
            const failMessage = renderTemplate(taskExecuteErrorInfo, {
                groupID: userServiceHiGroupNumber,
                taskID: taskId,
            });
            yield stream.flushReplaceLast(`\n\n${failMessage}`);
        }
        finally {
            await wboxCallBack({
                taskId: taskId ? taskId : 0,
                status: isSuccess ? 2 : 1,
                caller: user ? user.name : 'unknown',
            });
        }
    }
}
