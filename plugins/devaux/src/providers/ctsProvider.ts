/* eslint-disable no-useless-escape */
import {
    FunctionParameterDefinition,
    SkillProvider,
    ProviderInit,
    TaskProgressChunk,
    StringChunkStream,
} from '@comate/plugin-host';

import {FeedStrategyStatus} from '../consts/status.js';
import {GitUtil} from '../utils/git.js';
import {getCtsDiffMsg} from '../utils/diff.js';
import {BosUtil} from '../utils/bos.js';
import {wboxRegister, wboxCallBack} from '../utils/wbox.js';
import {getSessionTokens} from '../utils/util.js';
import {spawnSync} from 'child_process';

interface Args {
    query: string;
}

interface QadcData {
    // eslint-disable-next-line
    task_id: string;
    // eslint-disable-next-line
    rd_name: string;
    // eslint-disable-next-line
    module_name: string;
    // eslint-disable-next-line
    change_code: string;
    // eslint-disable-next-line
    logic_code: string;
    // eslint-disable-next-line
    conf_code: string;
    // eslint-disable-next-line
    data_code: string;
    // eslint-disable-next-line
    rd_code: string;
    feedback: boolean;
}

export class CTSProvider extends SkillProvider<Args> {
    static skillName = 'cts';

    static displayName = '商业cts';

    static description = '商业cts功能';

    static kirinStrategyUrl = 'http://kirin.baidu-int.com/api/tool/213805';

    static kirinResultUrl = 'http://kirin.baidu-int.com/api/task/';

    static flowMessage = '可添加如流群(9856501)咨询具体事项';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    private readonly git: GitUtil;
    private readonly cwd: string;

    constructor(init: ProviderInit) {
        super(init);

        this.git = new GitUtil(init.cwd);
        this.cwd = init.cwd;
    }

    sleep(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async callKirinAsyncStrategy(data: any) {
        const response = await fetch(CTSProvider.kirinStrategyUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
        });

        const result = await response.json();
        if (response.ok && result.message === 'success' && result.status) {
            return result.result.taskId;
        }

        return null;
    }

    async updateChunk2Qadc(data: any) {
        try {
            let tryTimes = 3;
            while (tryTimes) {
                const qadcUrl = 'http://qadc.baidu-int.com/api/insert/cts_gen_record';

                const response = await fetch(qadcUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        name: 'zhaomingming04',
                        token: 'f0aa0153a8f24ad3ac199e8251e1bfd2',
                    },
                    // eslint-disable-next-line
                    body: JSON.stringify([data]),
                });
                const responseObj = await response.json();
                if (responseObj.code === 0) {
                    return;
                }
                else {
                    --tryTimes;
                    await this.sleep(1.5 * 1000);
                }
            }
        }
        // eslint-disable-next-line
        catch {
        }
    }

    async getKirinAsyncStrategyResult(taskId: number) {
        let running = true;
        const timeout = 720 * 1000; // 720s

        const timer = setTimeout(() => {
            running = false;
        }, timeout);

        try {
            // eslint-disable-next-line
            while (running) {
                const response = await fetch(CTSProvider.kirinResultUrl + taskId, {
                    method: 'GET',
                });

                const result = await response.json();

                if (response.ok && result.status && result.result.taskStatus === 3) {
                    // eslint-disable-next-line
                    try {
                        const resultJsonObj = JSON.parse(result.result.response);
                        // eslint-disable-next-line
                        if (resultJsonObj.status === 0) {
                            return {
                                status: FeedStrategyStatus.SUCCESS,
                                result: resultJsonObj,
                            };
                        }
                        else {
                            return {
                                status: FeedStrategyStatus.ERROR_STRATEGY_FAIL,
                                result: null,
                            };
                        }
                    }
                    catch (e) {
                        return {status: FeedStrategyStatus.ERROR_STRATEGY_FAIL, result: null};
                    }
                }

                if (response.ok && result.status && result.result.taskStatus === 4) {
                    return {status: FeedStrategyStatus.ERROR_STRATEGY_FAIL, result: null};
                }

                await this.sleep(1 * 1000);
            }
        }
        finally {
            clearTimeout(timer);
        }

        return {status: FeedStrategyStatus.ERROR_TIMEOUT, result: null};
    }

    // eslint-disable-next-line
    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new StringChunkStream();
        const user = await this.currentUser.requestDetail();
        const userName = user ? user.name : 'unknown';
        let isSuccess = false;
        const qadcData: QadcData = {
            // eslint-disable-next-line
            task_id: '',
            // eslint-disable-next-line
            rd_name: userName,
            // eslint-disable-next-line
            module_name: '',
            // eslint-disable-next-line
            change_code: '',
            // eslint-disable-next-line
            logic_code: '',
            // eslint-disable-next-line
            conf_code: '',
            // eslint-disable-next-line
            data_code: '',
            // eslint-disable-next-line
            rd_code: '',
            feedback: false,
        };
        // wbox平台注册本次扫描任务
        const wboxTaskId = await wboxRegister(user, 'cts', this.currentContext.query, this.git);
        try {
            yield stream.flush('<h4> > 模型思考中</h4>\n\n');
            const module = await this.git.getModuleName();
            const moduleName = module ? module : 'unknown';
            // eslint-disable-next-line
            qadcData.module_name = moduleName;
            const fs = await this.requestWorkspaceFileSystem();
            if (!fs) {
                yield stream.flush('当前工作区没有文件系统，无法生成代码');
                return;
            }

            yield stream.flush('* 解析代码diff相关数据......\n\n');
            const rs = await getCtsDiffMsg(this.git, fs);
            if (rs.length === 0) {
                yield stream.flush('暂未找到diff信息，请确保有代码修改, 或' + CTSProvider.flowMessage);
                return;
            }

            yield stream.flush('* 代码特征数据上传BOS中......\n\n');
            const bosStsSession = await getSessionTokens();
            if (!bosStsSession) {
                throw new Error('can not get bos sts session');
            }
            const bosStsClient: BosUtil = new BosUtil(
                bosStsSession.data.access_key_id,
                bosStsSession.data.secret_access_key,
                'https://qep-public.bj.bcebos.com',
                bosStsSession.data.session_token
            );

            await bosStsClient.putObjFromStr('qep-public', `ecom-qa/cts/${wboxTaskId}.json`, rs);
            const diffInfoDonwloadUrl = await bosStsClient.generatePresignedUrl(
                'qep-public',
                `ecom-qa/cts/${wboxTaskId}.json`
            );
            // eslint-disable-next-line
            qadcData.change_code = diffInfoDonwloadUrl;

            const kirinTask = await this.callKirinAsyncStrategy({
                parameter: JSON.stringify({
                    // eslint-disable-next-line
                    diff_datas: diffInfoDonwloadUrl,
                    // eslint-disable-next-line
                    module_path: moduleName,
                    // eslint-disable-next-line
                    rd_name: userName,
                    query: this.currentContext.query,
                }),
                name: 'zhaomingming04',
                token: 'e33cc284830b4ba98a46e878afc16114',
            });
            // eslint-disable-next-line
            qadcData.task_id = kirinTask;

            yield stream.flush('* 模型结果生成中，请耐心等待......\n\n');
            const kirinRes = await this.getKirinAsyncStrategyResult(kirinTask);

            if (!kirinRes.result) {
                yield stream.flush(`策略执行失败，请重试，或${CTSProvider.flowMessage}`);
                return;
            }

            if (kirinRes.result.status !== 0) {
                yield stream.flush(
                    `策略执行失败，错误信息：${kirinRes.result.error_info}, 请重试，或${CTSProvider.flowMessage}`
                );
                return;
            }

            // eslint-disable-next-line
            qadcData.conf_code = kirinRes.result.conf_diff_cmd;
            // eslint-disable-next-line
            qadcData.data_code = kirinRes.result.data_diff_cmd;

            if (kirinRes.result.LLM_output && kirinRes.result.LLM_output.length !== 0) {
                yield stream.flush('* 模型生成结果:\n\n');
                yield stream.flush(`${kirinRes.result.LLM_output}\n\n`);
                // eslint-disable-next-line
                qadcData.logic_code = kirinRes.result.LLM_output;
            }

            yield stream.flush('* 执行配置文件写入中......\n\n');
            if (kirinRes.result.conf_diff_cmd.length !== 0) {
                const shellRes = spawnSync(
                    // eslint-disable-next-line
                    `cd ${this.cwd} && ${kirinRes.result.conf_diff_cmd}`,
                    {shell: true, encoding: 'utf-8'}
                );

                if (shellRes.status) {
                    yield stream.flush(`conf diff cmd fail:${shellRes.stderr}\n\n`);
                    return;
                }
            }

            yield stream.flush('* 执行数据文件写入中......\n\n');
            if (kirinRes.result.data_diff_cmd.length !== 0) {
                const shellRes = spawnSync(
                    `cd ${this.cwd} && ${kirinRes.result.data_diff_cmd}`,
                    {shell: true, encoding: 'utf-8'}
                );

                if (shellRes.status) {
                    yield stream.flush(`data diff cmd fail:${shellRes.stderr}\n\n`);
                    return;
                }
            }

            isSuccess = true;
            yield stream.flush('任务完成');
        }
        // eslint-disable-next-line
        catch (e) {
            yield stream.flush(`exception:${e}`);
        }
        finally {
            // eslint-disable-next-line

            if (qadcData.task_id.length !== 0) {
                await this.updateChunk2Qadc(qadcData);
            }
            await wboxCallBack({
                taskId: wboxTaskId ? wboxTaskId : 0,
                status: isSuccess ? 2 : 1,
                caller: userName,
            });
        }
    }
}
