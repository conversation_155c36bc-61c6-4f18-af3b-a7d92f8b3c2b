/* eslint-disable no-useless-escape */
import {
    FunctionParameterDefinition,
    SkillProvider,
    ProviderInit,
    TaskProgressChunk,
    StringChunkStream,
} from '@comate/plugin-host';

import {GitUtil} from '../utils/git.js';
import {feedCodeGenerateParameters, feedCodeGenerateProcess} from '../codeGenerator/feedCodeGenerate.js';
import {feedTestParameters, feedTestProcess} from '../codeGenerator/feedTestProcess.js';
import {wboxCallBack, wboxRegister} from '../utils/wbox.js';

interface Args {
    query: string;
}

export class OPGeneratorProvider extends SkillProvider<Args> {
    static skillName = 'opGenerator';

    static displayName = '算子生成';

    static description = 'feed算子生成功能';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    private readonly git: GitUtil;

    constructor(init: ProviderInit) {
        super(init);

        this.git = new GitUtil(init.cwd);
    }

    // eslint-disable-next-line
    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new StringChunkStream();
        const user = await this.currentUser.requestDetail();
        const userName = user ? user.name : 'unknown';
        const module = await this.git.getModuleName();
        let isSuccess = false;
        // wbox平台注册本次扫描任务
        const taskId = await wboxRegister(user, 'feedCodeGenerate', this.currentContext.query, this.git);
        const codeGeneratedParams: feedCodeGenerateParameters = {
            userName: userName,
            query: this.currentContext.query,
            code: '',
            codePath: '',
            qAndAListStr: '',
            codeConf: this.currentContext.selectedCode.endsWith('\n')
                ? this.currentContext.selectedCode.trimEnd()
                : this.currentContext.activeFileLineContent,
            codeConfFilePath: this.currentContext.activeFilePath,
            tableName: 'devaux_multiple_rounds',
            wboxTaskID: taskId,
        };
        const codeTestParams: feedTestParameters = {
            userName: userName,
            sample: this.currentContext.query,
            code: this.currentContext.selectedCode,
            codePath: this.currentContext.activeFilePath,
            qAndAListStr: '',
            codeConf: {},
            codeConfFilePath: '',
            module: module,
        };

        try {
            if (this.currentContext.query.length === 0) {
                yield stream.flush('需求描述不可为空，请添加需求描述');
                isSuccess = true;
                return;
            }

            if (this.currentContext.selectedCode.length === 0) {
                yield stream.flush('需要选中内容，请修改重试');
                isSuccess = true;
                return;
            }

            // eslint-disable-next-line
            if (!this.currentContext.query.startsWith('【测试配置】')) {
                for await (const data of feedCodeGenerateProcess(codeGeneratedParams)) {
                    // eslint-disable-next-line
                    if (data.type === 'status') {
                        return;
                    }
                    else if (data.type === 'message') {
                        yield stream.flush(data.message);
                    }
                    else {
                        yield stream.flushReplaceLast(data.message);
                    }
                }
                isSuccess = true;
            }
            else {
                for await (const data of feedTestProcess(codeTestParams)) {
                    if (data.type === 'status') {
                        return;
                    }
                    else if (data.type === 'message') {
                        yield stream.flush(data.message);
                    }
                    else {
                        yield stream.flushReplaceLast(data.message);
                    }
                }
                isSuccess = true;
            }
        }
        // eslint-disable-next-line
        catch (e) {
            yield stream.flush(String(e));
        }
        finally {
            await wboxCallBack({
                taskId: taskId ? taskId : 0,
                status: isSuccess ? 2 : 1,
                caller: userName,
            });
        }
    }
}
