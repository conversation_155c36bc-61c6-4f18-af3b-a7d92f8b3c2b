import path from 'node:path';
import {Tree} from 'web-tree-sitter';
import {getDirname} from '../utils/fs.js';

export function getCppFunctionBody(tree: Tree, diffLines: Set<number>) {
    const functionBodyResult = new Map<string, string>();
    const functionPromptResult = new Map<string, Set<number>>();

    // eslint-disable-next-line
    tree.rootNode.descendantsOfType('function_definition').forEach(functionNode => {
        // 获取函数名
        const functionNameNode = functionNode
            .namedChildren
            .find(
                child => child.type === 'function_declarator'
            )
            ?.namedChildren
            .find(child => child.type === 'identifier');
        if (!functionNameNode) {
            return;
        }

        functionPromptResult.set(functionNameNode.text + ':' + functionNode.startPosition.row, new Set<number>());

        if (functionNode.descendantsOfType('subscript_expression').length > 0) {
            functionPromptResult.get(functionNameNode.text + ':' + functionNode.startPosition.row)?.add(1460);
        }

        if (functionNode.descendantsOfType('pointer_expression').length > 0) {
            functionPromptResult.get(functionNameNode.text + ':' + functionNode.startPosition.row)?.add(1465);
        }

        if (
            functionNode.descendantsOfType('for_statement').length > 0
            || functionNode.descendantsOfType('while_statement').length > 0
        ) {
            functionPromptResult.get(functionNameNode.text + ':' + functionNode.startPosition.row)?.add(1466);
        }

        if (functionNode.descendantsOfType('binary_expression').length > 0) {
            for (const binaryExpress of functionNode.descendantsOfType('binary_expression')) {
                binaryExpress.children.forEach(node => {
                    if (node.text === '/') {
                        functionPromptResult.get(functionNameNode.text + ':' + functionNode.startPosition.row)?.add(
                            1461
                        );
                    }
                });
            }
        }
        const functionName = functionNameNode.text;

        // 获取函数体
        const functionBodyNode = functionNode.namedChildren.find(child => child.type === 'compound_statement');
        if (!functionBodyNode) {
            return;
        }
        if (!functionBodyResult.get(functionNameNode.text)) {
            if (diffLines.size === 0) {
                functionBodyResult.set(functionName + ':' + functionNode.startPosition.row, functionBodyNode.text);
            }
            else {
                diffLines.forEach(line => {
                    if (line >= functionNode.startPosition.row && line <= functionBodyNode.endPosition.row) {
                        functionBodyResult.set(
                            functionName + ':' + functionNode.startPosition.row,
                            functionBodyNode.text
                        );
                    }
                });
            }
        }
    });
    return {functionBodyResult: functionBodyResult, functionPromptResult: functionPromptResult};
}

export function getGolangFunctionBody(tree: Tree, diffLines: Set<number>) {
    const functionBodyResult = new Map<string, string>();
    const functionPromptResult = new Map<string, Set<number>>();
    const functionNodes = tree.rootNode.descendantsOfType('function_declaration');
    // 遍历函数节点
    // eslint-disable-next-line
    functionNodes.forEach(functionNode => {
        // 获取函数体节点
        if (!functionNode) {
            return;
        }

        const functionNameNode = functionNode.childForFieldName('name');
        if (!functionNameNode) {
            return;
        }

        const functionBodyNode = functionNode.childForFieldName('body');
        if (!functionBodyNode) {
            return;
        }
        functionPromptResult.set(functionNameNode.text + ':' + functionNode.startPosition.row, new Set<number>());

        if (functionNode.descendantsOfType('index_expression').length > 0) {
            functionPromptResult.get(functionNameNode.text + ':' + functionNode.startPosition.row)?.add(1460);
        }

        if (functionNode.descendantsOfType('pointer_type').length > 0) {
            functionPromptResult.get(functionNameNode.text + ':' + functionNode.startPosition.row)?.add(1465);
        }

        if (
            functionNode.descendantsOfType('for_statement').length > 0
            || functionNode.descendantsOfType('while_statement').length > 0
        ) {
            functionPromptResult.get(functionNameNode.text + ':' + functionNode.startPosition.row)?.add(1466);
        }

        if (functionNode.descendantsOfType('binary_expression').length > 0) {
            for (const binaryExpress of functionNode.descendantsOfType('binary_expression')) {
                if (binaryExpress.childCount === 3 && binaryExpress.child(1)?.type === '/') {
                    functionPromptResult.get(functionNameNode.text + ':' + functionNode.startPosition.row)?.add(1461);
                }
            }
        }

        if (!functionBodyResult.get(functionNameNode.text)) {
            if (diffLines.size === 0) {
                functionBodyResult.set(
                    functionNameNode.text + ':' + functionNode.startPosition.row,
                    functionBodyNode.text
                );
            }
            else {
                diffLines.forEach(line => {
                    if (line >= functionBodyNode.startPosition.row && line <= functionBodyNode.endPosition.row) {
                        functionBodyResult.set(
                            functionNameNode.text + ':' + functionNode.startPosition.row,
                            functionBodyNode.text
                        );
                    }
                });
            }
        }
    });
    return {functionBodyResult: functionBodyResult, functionPromptResult: functionPromptResult};
}

export const languageExtMap: Map<string, string> = new Map([
    ['.go', path.join(getDirname(), 'static', 'tree-sitter-go.wasm')],
    ['.cpp', path.join(getDirname(), 'static', 'tree-sitter-cpp.wasm')],
]);

type getFunctionBodyFunc = (tree: Tree, diffLines: Set<number>) => {
    functionBodyResult: Map<string, string>;
    functionPromptResult: Map<string, Set<number>>;
};
export const languageParser: Map<string, getFunctionBodyFunc> = new Map<string, getFunctionBodyFunc>([
    ['.go', getGolangFunctionBody],
    ['.cpp', getCppFunctionBody],
]);

export function getFunctionBody(tree: Tree, diffLines: Set<number>, ext: string) {
    const parseFunc = languageParser.get(ext);
    const functionBodyResult = new Map<string, string>();
    const functionPromptResult = new Map<string, Set<number>>();
    if (parseFunc === undefined) {
        return {functionBodyResult: functionBodyResult, functionPromptResult: functionPromptResult};
    }
    else {
        const {functionBodyResult, functionPromptResult} = parseFunc(tree, diffLines);
        return {functionBodyResult: functionBodyResult, functionPromptResult: functionPromptResult};
    }
}

export function getArrayOutOfBoundsItems(tree: Tree, lineContent: string[], diffLines: number[]) {
    const result = new Map<string, Map<string, string | any[]>>();
    const functionNodes = tree.rootNode.descendantsOfType('function_declaration');
    // 遍历函数节点
    functionNodes.forEach(functionNode => {
        // 获取函数体节点
        if (!functionNode) {
            return;
        }

        const functionBodyNode = functionNode.lastChild;
        if (!functionBodyNode) {
            return;
        }

        // 获取函数内所有的index表达式
        const indexExpressions = functionNode.descendantsOfType('index_expression');
        /* eslint-disable max-statements */
        /* eslint-disable complexity */
        indexExpressions.forEach(node => {
            let indexStr = '';
            if (!node) {
                return;
            }

            // 判断是否是diff行
            let isDiffLine = false;
            for (let i = node.startPosition.row; i <= node.endPosition.row; i++) {
                if (diffLines.includes(i)) {
                    isDiffLine = true;
                    break;
                }
            }
            if (!isDiffLine) {
                return;
            }

            // 获取访问的目标节点（数组或切片）
            const targetNode = node.child(0);
            if (!targetNode) {
                return;
            } // 检查目标节点是否存在

            // 获取访问的索引
            const indexNode = node.child(1);
            if (!indexNode) {
                return;
            } // 检查索引节点是否存在
            const stack = [];
            for (let i = 1; i < node.childCount; i++) {
                const childNode = node.child(i);
                if (!childNode) {
                    continue;
                }
                if (childNode.text === '[') {
                    stack.push('[');
                }
                else if (childNode.text === ']') {
                    stack.pop();
                }
                else {
                    indexStr += childNode.text;
                }

                if (stack.length === 0) {
                    break;
                }
            }

            if (stack.length) {
                indexStr = '';
            }
            else {
                const functionNameNode = functionNode.child(0);
                if (!functionNameNode) {
                    return;
                }
                if (!result.has(functionNameNode.text)) {
                    const functionFeature = new Map<string, string | any[]>();
                    functionFeature.set('function_body', functionBodyNode.text);
                    const errorFeatureList = new Array<Map<string, string>>();
                    const errorFeature = new Map<string, string>();
                    errorFeature.set('error_line', lineContent[node.startPosition.row - 1]);
                    errorFeature.set('token_index', indexStr);
                    errorFeature.set('token', targetNode.text);
                    errorFeatureList.push(errorFeature);
                    functionFeature.set('error_feature', errorFeatureList);
                    result.set(functionNameNode.text, functionFeature);
                }
            }
        });
    });
    return result;
}
