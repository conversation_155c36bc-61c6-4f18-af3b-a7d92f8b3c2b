export const qadcPrefixURL = 'http://qadc.baidu-int.com/api';
export const qadcSelectURL = `${qadcPrefixURL}/query`;
export const qadcCodeGenStageInfoTableName = 'code_gen_stage_info';
export const qadcSelectCodeGenStageInfoURL = `${qadcSelectURL}/${qadcCodeGenStageInfoTableName}`;
export const qadcUpdatePrefixURL = `${qadcPrefixURL}/update`;

export const kirinPrefixURL = 'http://kirin.baidu-int.com/api';
export const kirinToolPrefixURL = `${kirinPrefixURL}/tool`;
export const kirinAsynResCallBackURL = `${kirinPrefixURL}/task`;

export const kirinFeedTestToolID = '213911';
export const kirinFeedTestURL = `${kirinToolPrefixURL}/${kirinFeedTestToolID}`;

export const kirinIsSameSessionToolID = '214058';
export const kirinIsSameSessionURL = `${kirinToolPrefixURL}/${kirinIsSameSessionToolID}`;

export const kirinCodeGenerateToolID = '213827';
export const kirinCodeGenerateURL = `${kirinToolPrefixURL}/${kirinCodeGenerateToolID}`;

export const kirinGDPCodeGenerateToolID = '213857';
export const kirinGDPCodeGenerateURL = `${kirinToolPrefixURL}/${kirinGDPCodeGenerateToolID}`;

export const kirinUDFCodeGenerateToolID = '213890';
export const kirinUDFCodeGenerateURL = `${kirinToolPrefixURL}/${kirinUDFCodeGenerateToolID}`;

export const kirinADSCodeGenerateToolID = '213920';
export const kirinADSCodeGenerateURL = `${kirinToolPrefixURL}/${kirinADSCodeGenerateToolID}`;

// 通用代码生成服务
export const kirinGeneralGenToolID = '214250';
export const kirinAmisWebGenURL = `${kirinToolPrefixURL}/${kirinGeneralGenToolID}`;
export const kirinTiebaInterfaceGenURL = `${kirinToolPrefixURL}/${kirinGeneralGenToolID}`;

// 通用代码生成服务（异步）
export const kirinAsyncGeneralGenToolID = '214292';
export const kirinAsyncWebGenURL = `${kirinToolPrefixURL}/${kirinAsyncGeneralGenToolID}`;

// 文件路径解析服务
export const kirinGDPFilePathToolID = '214146';
export const kirinGDPFilePathURL = `${kirinToolPrefixURL}/${kirinGDPFilePathToolID}`;

export const kirinGRGeneratorToolID = '214145';
export const kirinGRGeneratorURL = `${kirinToolPrefixURL}/${kirinGRGeneratorToolID}`;

export const kirinTransferToolID = '214147';
export const kirinTransferURL = `${kirinToolPrefixURL}/${kirinTransferToolID}`;

export const kirinHarmonyGenerateToolID = '214334';
export const kirinHarmonyGenerateURL = `${kirinToolPrefixURL}/${kirinHarmonyGenerateToolID}`;

export const MegIDAChatDelivery = 'https://feedqa.baidu-int.com/feedchat/rest/feed-chat/v1/dev_aux';
export const MegIDAChatDeliveryTestURL = 'http://*************:8107/rest/feed-chat/v1/dev_aux';

export const hiRobotPrefixUrl = 'http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token';
export const userServiceHiGroupNumber = 9801008;

export const feedStrategyHiGroupNumber = 9806174;
export const feedStrategyHiRobotToken = 'dd2c0a6ee1094d0cf6f288b0bbc48448c';
export const feedStrategyHiRobotUrl = `${hiRobotPrefixUrl}=${feedStrategyHiRobotToken}`;

export const ErnieBot4URL = 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions_pro';
