export const taskNotGenerateErrorInfo = '生成算子代码失败, taskId未生成, 请重试生成, 也可添加如流群({{groupID}})咨询';
export const taskExecuteErrorInfo = '生成算子代码失败，请重试生成, 也可添加如流群({{groupID}})咨询, taskid:{{taskID}}';
export const taskExecuteTimeoutErroInfo
    = '生成算子代码超时，请重试生成, 也可添加如流群({{groupID}})咨询, taskid:{{taskID}}';
export const callWenXinErrorInfo = '请求文心失败，请重试生成, 也可添加如流群({{groupID}})咨询, taskid:{{taskID}}';
export const send2HiGroupFailMessage
    = 'taskid:[{{taskID}}], rd:[{{userName}}], query:[{{query}}], conf:[{{conf}}], message:[{{failMessage}}]';
