{"private": true, "name": "@comate-plugin/devaux", "version": "0.0.1", "type": "module", "scripts": {"clean": "rm -rf dist", "dev": "cpd dev", "build": "tsc && cpd build && cp static/tree-sitter.wasm dist/ && cp -r static dist/", "lint": "eslint --max-warnings=0 src --fix"}, "dependencies": {"@baiducloud/sdk": "1.0.1-beta.3", "@comate/plugin-cli": "^0.9.2", "@comate/plugin-host": "workspace:^", "@comate/plugin-shared-internals": "^0.9.2", "@types/diff": "^5.0.9", "@types/node": "18.15.0", "axios": "^1.7.8", "diff": "^5.2.0", "simple-git": "^3.23.0", "ts-md5": "^1.3.1", "typescript": "^5.3.2", "web-tree-sitter": "^0.20.8"}, "comate": {"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "icon": "./assets/icon.svg", "entry": "./dist/index.js", "displayName": "DevAux", "description": "研发过程智能辅助工具", "keyword": ["DevAux"], "capabilities": [{"type": "Skill", "name": "devaux-help", "displayName": "插件介绍", "description": "插件介绍", "placeholder": "无需输入内容，回车获得插件介绍"}, {"type": "Skill", "name": "GdpWebGen", "displayName": "GDP代码生成", "description": "GDP代码生成", "placeholder": ""}, {"type": "Skill", "name": "MegIDA", "displayName": "MEG智能交付助手", "description": "MEG智能交付助手", "placeholder": ""}, {"type": "Skill", "name": "ConflictResolution", "displayName": "代码冲突解决", "description": "代码冲突解决", "placeholder": "打开冲突文件，输入iOS"}, {"type": "Skill", "name": "HarmonyGenerator", "displayName": "鸿蒙代码生成", "description": "鸿蒙代码生成", "placeholder": ""}, {"type": "Skill", "name": "OPGenerator", "displayName": "OPGenerator", "description": "feed算子生成", "placeholder": "选中代码，输入需求描述，回车"}, {"type": "Skill", "name": "OPSelfTest", "displayName": "OPSelfTest", "description": "算子自测", "placeholder": "选择自测conf，填写sample地址，回车"}, {"type": "Skill", "name": "AmisWebGen", "displayName": "AisudaWeb代码生成", "description": "AisudaWeb代码生成", "placeholder": ""}, {"type": "Skill", "name": "GRGenerator", "displayName": "GRGenerator", "description": "feed分人群策略生成", "placeholder": ""}, {"type": "Skill", "name": "TransGenerator", "displayName": "TransGenerator", "description": "feed转换函数生成", "placeholder": "选中代码，输入需求描述，回车"}, {"type": "Skill", "name": "ReqCodeGenerator", "displayName": "需求代码生成", "description": "需求代码生成", "placeholder": "输入需求"}, {"type": "Skill", "name": "CTS", "displayName": "CTS", "description": "商业CTS功能", "placeholder": "直接回车即可"}, {"type": "Skill", "name": "UDFGenerator", "displayName": "UDFGenerator", "description": "udf算子生成", "placeholder": "打开conf文件，输入需求描述，回车"}, {"type": "Skill", "name": "ADSGenerator", "displayName": "ADSGenerator", "description": "ads算子生成", "placeholder": "选中代码，输入需求描述，回车"}, {"type": "Skill", "name": "FeedSaScan", "displayName": "FeedSaScan", "description": "feed sa扫描", "placeholder": "直接回车即可进行代码缺陷检测"}, {"type": "Skill", "name": "SAScan", "displayName": "SA", "description": "静态代码缺陷检测", "placeholder": "直接回车即可进行代码缺陷检测"}], "configSchema": {"sections": []}}}