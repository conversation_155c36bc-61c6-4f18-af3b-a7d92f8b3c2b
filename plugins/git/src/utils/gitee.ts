/**
 * @api https://gitee.com/api/v5/swagger#/postV5ReposOwnerIssues
 */
import {Issue} from '../interface.js';
import {GitHostUtil} from './githost.js';

interface GiteeIssue {
    number: string;
    title: string;
}

export class GiteeUtil implements GitHostUtil {
    constructor(private readonly accessToken: string, private readonly owner: string, private readonly repo: string) {
    }

    async fetchIssues(): Promise<Issue[]> {
        const giteeUrl = `https://gitee.com/api/v5/repos/${this.owner}/${this.repo}/issues`
            + `?access_token=${this.accessToken}&state=open&sort=created&direction=desc&page=1&per_page=20`;

        const response = await fetch(giteeUrl, {
            headers: {
                'Content-Type': 'application/json;charset=UTF-8',
            },
        });

        if (response.ok) {
            const data = await response.json();

            return data.map((v: GiteeIssue) => ({id: v.number, title: v.title}));
        }

        return [];
    }

    async createIssue(title: string, body: string) {
        const response = await fetch(`https://gitee.com/api/v5/repos/${this.owner}/issues`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json;charset=UTF-8',
            },
            body: JSON.stringify({
                // eslint-disable-next-line camelcase
                access_token: this.accessToken,
                repo: this.repo,
                title,
                body,
            }),
        });

        const data = await response.json();

        return {id: data.number, title: data.title, url: data.html_url};
    }
}
