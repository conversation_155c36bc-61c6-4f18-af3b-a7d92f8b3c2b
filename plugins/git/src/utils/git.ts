import {SimpleGit, simpleGit} from 'simple-git';

export interface GitFileStatus {
    index: string;
    path: string;
}

export class GitUtil {
    private readonly git: SimpleGit;

    constructor(cwd: string) {
        this.git = simpleGit(cwd);
    }

    async getChangedFiles(): Promise<GitFileStatus[]> {
        const status = await this.git.status();
        return status.files.map(v => ({index: v.index, path: v.path}));
    }

    async getDiffForFile(file: string) {
        const diff = await this.git.diff(['HEAD', '--', file]);
        return diff;
    }

    async getHistoryCommits() {
        const history = await this.git.log({maxCount: 10, format: {message: '%s'}});
        return history.all.map(v => v.message);
    }

    async readFileAtRevision(revision: string, file: string) {
        const result = await this.git.show(`${revision}:${file}`);
        return result;
    }
}
