/**
 * @api https://github.com/jdalrymple/gitbeaker/blob/main/packages/rest/README.md#usage
 */
import {Gitlab, IssueSchema} from '@gitbeaker/rest';
import {Issue} from '../interface.js';
import {GitHostUtil} from './githost.js';

export class GitLabUtil implements GitHostUtil {
    private readonly api: InstanceType<typeof Gitlab>;

    constructor(private readonly accessToken: string, private readonly owner: string, private readonly repo: string) {
        this.api = new Gitlab({token: this.accessToken});
    }

    async fetchIssues(): Promise<Issue[]> {
        const data = await this.api.Issues.all({
            perPage: 20,
            page: 1,
            state: 'opened',
            projectId: `${this.owner}/${this.repo}`,
        });

        return data.map(v => ({id: String(v.iid), title: v.title}));
    }

    async createIssue(title: string, body: string) {
        const data = await this.api.Issues.create(
            `${this.owner}/${this.repo}`,
            title,
            {
                description: body,
            }
        ) as IssueSchema;

        return {id: data.iid, title: data.title, url: data.web_url};
    }
}
