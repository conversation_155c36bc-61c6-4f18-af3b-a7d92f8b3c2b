import {Octokit} from 'octokit';
import {Issue} from '../interface.js';
import {GitHostUtil} from './githost.js';

export class GitHubUtil implements GitHostUtil {
    private readonly kit: Octokit;

    constructor(private readonly accessToken: string, private readonly owner: string, private readonly repo: string) {
        this.kit = new Octokit({auth: this.accessToken});
    }

    async fetchIssues(): Promise<Issue[]> {
        const query = {
            owner: this.owner,
            repo: this.repo,
            state: 'open',
            // eslint-disable-next-line camelcase
            per_page: 20,
        } as const;
        const {data} = await this.kit.rest.issues.listForRepo(query);

        return data.map(v => ({id: String(v.number), title: v.title}));
    }

    async createIssue(title: string, body: string) {
        const params = {
            owner: this.owner,
            repo: this.repo,
            title,
            body,
        };
        const {data} = await this.kit.rest.issues.create(params);
        return {id: data.number, title: data.title, url: data.html_url};
    }
}
