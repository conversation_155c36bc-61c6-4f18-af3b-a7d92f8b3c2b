import {FileSystem, InformationRetriever, SuppotingLlm} from '@comate/plugin-host';
import describeProjectPromptTemplate from '../prompts/describeProject.prompt';
import explainFilePromptTemplate from '../prompts/explainFile.prompt';
import explainFileChangePromptTemplate from '../prompts/explainFileChange.prompt';
import {GitFileStatus, GitUtil} from './git.js';
import {FileModification, ProjectInfo} from '../interface.js';
import {GitHostType, GitHostUtil} from './githost.js';
import {getGitHostInfo, getGitHostUtil} from './githost.js';

interface FileChange {
    type: 'change' | 'new' | 'delete';
    content: string;
}

interface FileChangeExplainOptions extends ProjectInfo, FileChange {
    file: string;
}

export interface AccessToken {
    [GitHostType.GitHub]: string;
    [GitHostType.Gitee]: string;
    [GitHostType.GitLab]: string;
}

export interface ContextInit {
    git: GitUtil;
    accessToken: AccessToken;
    retriever: InformationRetriever;
    llm: SuppotingLlm;
    fileSystem: FileSystem;
}

export interface GitContext {
    projectInfo: ProjectInfo;
    modifications: string;
    gitHostUtil?: GitHostUtil;
}

export interface MessageProgress {
    type: 'message';
    content: string;
}

export interface ContextProgress {
    type: 'context';
    value: GitContext;
}

type ProgressInfo = MessageProgress | ContextProgress;

async function describeProject(projectName: string, {llm, fileSystem}: ContextInit) {
    try {
        const readme = await fileSystem.readFile('README.md', 'utf-8');
        const prompt = llm.createPrompt(
            describeProjectPromptTemplate,
            {projectName, readme: readme.replaceAll('`', '\\`')}
        );
        const description = await llm.askForText(prompt);
        return description;
    }
    catch {
        const prompt = llm.createPrompt(
            '当前项目为一个Git项目，仓库名称为 {{projectName}} ，请生成小于100个字的内容描述这个项目的作用。',
            {projectName}
        );
        const description = await llm.askForText(prompt);
        return description;
    }
}

async function getFileChange(file: GitFileStatus, {fileSystem, git}: ContextInit): Promise<FileChange> {
    if (file.index === 'D') {
        const content = await git.readFileAtRevision('HEAD', file.path);
        return {type: 'delete', content};
    }
    if (file.index === '?' || file.index === 'A') {
        // TODO: Git返回的路径不一定和当前工作台路径匹配
        const content = await fileSystem.readFile(file.path, 'utf-8');
        return {type: 'new', content};
    }
    else {
        const diff = await git.getDiffForFile(file.path);
        return {type: 'change', content: diff};
    }
}

async function explainFileChange(options: FileChangeExplainOptions, {llm}: ContextInit) {
    const prompt = llm.createPrompt(
        options.type === 'new' ? explainFilePromptTemplate : explainFileChangePromptTemplate,
        options
    );
    const explaination = await llm.askForText(prompt);
    // TODO: 可能这里还是独立一个提示词给删除类更好？
    return options.type === 'delete' ? `删除了该文件，原文件${explaination}` : explaination;
}

export async function* createContext(options: ContextInit): AsyncIterableIterator<ProgressInfo> {
    const {retriever, git, accessToken} = options;

    const repository = await retriever.repositoryInfo();

    if (repository.versionControl !== 'git' || !repository.repositoryUrl) {
        throw new Error('看起来您的当前项目并不是一个Git仓库。');
    }

    const files = await git.getChangedFiles();

    if (!files.length) {
        throw new Error('本项目没有文件改动，请检查!');
    }

    const gitHostInfo = getGitHostInfo(repository.repositoryUrl);
    const gitHostUtil = getGitHostUtil(gitHostInfo, accessToken);

    if (!gitHostUtil) {
        yield {type: 'message', content: `可能不支持的Git平台：${gitHostInfo.type}`};
    }

    const projectName = `${gitHostInfo.user}/${gitHostInfo.project}`;
    yield {type: 'message', content: '正在分析本项目的功能用途……'};
    const projectDescription = await describeProject(projectName, options);
    const info: ProjectInfo = {
        repo: gitHostInfo.project,
        owner: gitHostInfo.user,
        projectName,
        projectDescription,
        projectHost: gitHostInfo.type,
    };

    const modifications: FileModification[] = [];

    for (const file of files) {
        const change = await getFileChange(file, options);

        if (!change.content.trim()) {
            continue;
        }

        // TODO: 改成JSX（几个调用它的插件要先改），文件名用`<code>`包裹起来
        if (change.content.length > 3000) {
            yield {type: 'message', content: `文件${file.path}的内容过长，跳过分析该文件。`};
        }
        else {
            yield {type: 'message', content: `正在分析文件${file.path}的修改内容。`};
        }

        const explain = await explainFileChange({...info, file: file.path, ...change}, options);
        modifications.push({file: file.path, explain});
    }

    const context: GitContext = {
        projectInfo: info,
        gitHostUtil,
        modifications: modifications.map(v => `- ${v.file}：${v.explain}`).join('\n'),
    };

    yield {type: 'context', value: context};
}
