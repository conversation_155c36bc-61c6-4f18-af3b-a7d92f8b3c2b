import hostedGitInfo from 'hosted-git-info';
import {Issue} from '../interface.js';
import {GitHubUtil} from './github.js';
import {GiteeUtil} from './gitee.js';
import {GitLabUtil} from './gitlab.js';
import {AccessToken} from './context.js';

export enum GitHostType {
    GitHub = 'github',
    GitLab = 'gitlab',
    Gitee = 'gitee',
    Unknown = 'unknown',
}

export interface GitHostUtil {
    /**
     * 获取仓库中所有open的issue
     *
     * @param
     * @returns issues列表
     */
    fetchIssues(): Promise<Issue[]>;
    /**
     * 创建issue
     *
     * @param title 标题 body 内容
     * @returns 创建的issue信息
     */
    createIssue(title: string, body: string): Promise<{id: number, title: string, url: string}>;
}

interface GitHostInfo {
    project: string;
    user: string;
    type: GitHostType | string;
}

function getCommonGitHostInfo(repositoryUrl: string, type: string): GitHostInfo {
    const gitRegex = /([^/:]+\/[^/]+)\.git$/;
    const match = (gitRegex.exec(repositoryUrl))?.[1].split('/');
    return {
        project: match?.[1] || '',
        user: match?.[0] || '',
        type,
    };
}

export function getGitHostInfo(repositoryUrl: string): GitHostInfo {
    let gitHostInfo = hostedGitInfo.fromUrl(repositoryUrl) as GitHostInfo;

    if (!gitHostInfo) {
        gitHostInfo = repositoryUrl.includes(GitHostType.Gitee)
            ? getCommonGitHostInfo(repositoryUrl, GitHostType.Gitee)
            : getCommonGitHostInfo(repositoryUrl, GitHostType.Unknown);
    }
    return gitHostInfo;
}

export function getGitHostUtil(gitHost: GitHostInfo, accessToken: AccessToken) {
    const {type, user: owner, project: repo} = gitHost;
    // eslint-disable-next-line @typescript-eslint/init-declarations
    let gitHostUtil: GitHostUtil | undefined;
    switch (type) {
        case GitHostType.GitHub:
            gitHostUtil = new GitHubUtil(accessToken.github, owner, repo);
            break;
        case GitHostType.Gitee:
            gitHostUtil = new GiteeUtil(accessToken.gitee, owner, repo);
            break;
        case GitHostType.GitLab:
            gitHostUtil = new GitLabUtil(accessToken.gitlab, owner, repo);
            break;
        default:
            gitHostUtil = undefined;
    }
    return gitHostUtil;
}
