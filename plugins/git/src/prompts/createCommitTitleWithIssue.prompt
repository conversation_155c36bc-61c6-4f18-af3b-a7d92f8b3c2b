当前开发的项目名称为“{{projectName}}”，项目描述如下：

```
{{projectDescription}}
```

当前项目修改过的文件以及修改内容如下：

{{modifications}}

以下JSON表示当前修改关联的Issue：

{{issue}}

以下是一些历史的提交记录，请参考它们的格式来生成提交信息：

{{history}}

现在需要使用`git commit`提交本次修改，请为我生成提交的信息，满足以下要求：

- 使用中文
- 小于20个字
- 不包含换行
- 符合历史提交信息的格式
- 历史提交有关联需求的话，生成的提交信息中也用相似的格式包含需求的ID，关联的形式要与历史提交信息相同（例如括号与`#`等元素）
- 在提交信息中的各元素中保留一个空格，以提高可读性
- 如果历史提交信息中有无法确定的内容元素，不要生成该元素

按照以下JSON格式给出响应，不要输出任何非JSON格式的内容

```json
{
    "message": "小于20字的提交信息"
}
```
