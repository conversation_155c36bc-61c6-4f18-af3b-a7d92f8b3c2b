import {HelpSkillProvider} from './providers/help.js';
import {PluginSetupContext} from '@comate/plugin-host';
import {CommitMessageSkillProvider} from './providers/commitMessage.js';
import {CreateIssueSkillProvider} from './providers/issue.js';
import {CustomFallbackProvider} from './providers/fallback.js';

export function setup({registry}: PluginSetupContext) {
    registry.registerSkillProvider('git-help', HelpSkillProvider);
    registry.registerSkillProvider('commitMessage', CommitMessageSkillProvider);
    registry.registerSkillProvider('createIssue', CreateIssueSkillProvider);
    registry.registerFallbackProvider('fallback', CustomFallbackProvider);
}
