/* eslint-disable complexity */
/* eslint-disable max-statements */
import {
    ElementChunkStream,
    FunctionParameterDefinition,
    ProviderInit,
    SkillProvider,
    TaskProgressChunk,
    TextModel,
} from '@comate/plugin-host';
import selectIssuePromptTemplate from '../prompts/selectIssue.prompt';
import createCommitTitleWithIssuePromptTemplate from '../prompts/createCommitTitleWithIssue.prompt';
import createCommitTitleNoIssuePromptTemplate from '../prompts/createCommitTitleNoIssue.prompt';
import {GitUtil} from '../utils/git.js';
import {Issue, ProjectInfo} from '../interface.js';
import {GitHostType, GitHostUtil} from '../utils/githost.js';
import {ContextInit, GitContext, createContext} from '../utils/context.js';

interface SelectIssueOptions extends ProjectInfo {
    owner: string;
    repo: string;
    modifications: string;
    gitHostUtil?: GitHostUtil;
}

interface CreateOptions extends ProjectInfo {
    modifications: string;
    historyCommits: string[];
    issue: Issue | null;
}

interface State {
    context: GitContext | null;
}

export class CommitMessageSkillProvider extends SkillProvider {
    static skillName = 'createCommitMessage';

    static displayName = '生成提交';

    static description = '生成Git提交信息';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    private readonly git: GitUtil;

    private createOptions: CreateOptions | null = null;

    constructor(init: ProviderInit) {
        super(init);

        this.git = new GitUtil(init.cwd);
    }

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new ElementChunkStream();
        const fileSystem = await this.requestWorkspaceFileSystem();

        if (!fileSystem) {
            yield stream.flush(<p>插件需要读取项目内文件来生成提交信息。</p>);
            return;
        }

        const githubAccessToken = await this.config.getAsString('githubAccessToken');
        const giteeAccessToken = await this.config.getAsString('giteeAccessToken');
        const gitlabAccessToken = await this.config.getAsString('gitlabAccessToken');

        const init: ContextInit = {
            git: this.git,
            retriever: this.retriever,
            llm: this.llm,
            fileSystem,
            accessToken: {
                [GitHostType.GitHub]: githubAccessToken,
                [GitHostType.Gitee]: giteeAccessToken,
                [GitHostType.GitLab]: gitlabAccessToken,
            },
        };

        const state: State = {
            context: null,
        };

        try {
            for await (const data of createContext(init)) {
                if (data.type === 'message') {
                    yield stream.flush(<p>{data.content}</p>);
                }
                else {
                    state.context = data.value;
                }
            }
        }
        catch (ex) {
            yield stream.flushReplaceLast(<p>{ex instanceof Error ? ex.message : `${ex}`}</p>);
        }

        if (!state.context) {
            return;
        }

        const {projectInfo, modifications, gitHostUtil} = state.context;

        const historyCommits = await this.git.getHistoryCommits();
        let issue: Issue | null = null;

        if (gitHostUtil) {
            yield stream.flush(<p>正在获取项目中的Issue列表……</p>);
            issue = await this.selectIssue({...projectInfo, modifications, gitHostUtil});
        }

        this.createOptions = {...projectInfo, modifications, historyCommits, issue};

        if (issue) {
            yield stream.flush(
                <p>
                    建议采用<code>#{issue.id} {issue.title}</code>与当前提交绑定
                </p>
            );
            yield stream.flush(
                <button-group>
                    <command-button
                        commandName="commitMessage:confirmIssue"
                        data="no"
                        replyText="我不要关联Issue"
                    >
                        不关联Issue
                    </command-button>
                    <command-button
                        commandName="commitMessage:confirmIssue"
                        data="yes"
                        replyText="可以，就用这个Issue生成提交信息"
                        variant="primary"
                    >
                        使用该Issue
                    </command-button>
                </button-group>
            );
            return;
        }

        yield* this.continueCreateCommitMessage(stream);
    }

    // 承接点击事件
    async *handleCommand(commandName: string, data: any): AsyncIterableIterator<TaskProgressChunk> {
        console.log('commandName', commandName);
        console.log('commandNameData', data);

        const stream = new ElementChunkStream();

        if (commandName === 'commitMessage:confirmIssue') {
            if (data === 'no') {
                this.createOptions!.issue = null;
                yield stream.flushReplaceLast(<p>不需要</p>);
                return;
            }
            yield* this.continueCreateCommitMessage(stream);
        }
    }

    private async *continueCreateCommitMessage(stream: ElementChunkStream): AsyncIterableIterator<TaskProgressChunk> {
        yield stream.flush(<p>正在生成提交信息……</p>);

        const title = await this.createCommitTitle(this.createOptions!);

        if (!title) {
            yield stream.flush(<p>未正确生成提交信息，请重试</p>);
            return;
        }

        yield stream.flush(<p>以下是建议的提交信息：</p>);
        yield stream.flush(
            <code-block
                closed
                actions={['copy']}
            >
                {title}
            </code-block>
        );

        yield stream.flush(<p>你也可以直接使用下方的Shell命令提交代码：</p>);
        yield stream.flush(
            <code-block
                closed
                language="shell"
                actions={['insertIntoTerminal', 'copy']}
            >
                git commit -m '{title}'
            </code-block>
        );
    }

    private async selectIssue(options: SelectIssueOptions) {
        try {
            const issues = await options.gitHostUtil?.fetchIssues() || [];

            if (!issues.length) {
                return null;
            }

            const prompt = this.llm.createPrompt(
                selectIssuePromptTemplate,
                {
                    projectName: options.projectName,
                    projectDescription: options.projectDescription,
                    modifications: options.modifications,
                    issues: JSON.stringify([...issues, {id: '-1', title: '没有合适的标题'}].map(v => v.title), null, 2),
                }
            );
            const result = await this.llm.askForText(prompt, {model: TextModel.ErnieBot4});
            const jsonString = result.replace(/^```\w+/, '').replace(/```\s*$/, '');
            const value = JSON.parse(jsonString);
            if (value?.title) {
                const issue = issues.find(v => v.title === value.title);
                return issue && issue.id !== '-1' ? issue : null;
            }
            if (typeof value?.index === 'number') {
                const issue = issues.at(value.index);
                // TODO: 确认标题内容足够相似
                return issue && issue.id !== '-1' ? issue : null;
            }
            return null;
        }
        catch {
            return null;
        }
    }

    private async createCommitTitle(options: CreateOptions) {
        const prompt = this.llm.createPrompt(
            options.issue ? createCommitTitleWithIssuePromptTemplate : createCommitTitleNoIssuePromptTemplate,
            {
                projectName: options.projectName,
                projectDescription: options.projectDescription,
                modifications: options.modifications,
                history: options.historyCommits.map(v => `- ${v}`).join('\n'),
                issue: JSON.stringify(options.issue, null, 2),
            }
        );
        const result = await this.llm.askForText(prompt, {model: TextModel.ErnieBot4});
        const jsonString = result.replace(/^```\w+/, '').replace(/```\s*$/, '');
        try {
            const value = JSON.parse(jsonString);
            if (typeof value?.message === 'string') {
                return this.beautifyIssueTitle(value.message);
            }
        }
        catch {
            // 后续统一处理异常情况
        }

        return this.beautifyIssueTitle(result.split('\n')[0]);
    }

    private beautifyIssueTitle(title: string) {
        const result = {title};
        const matchIssue = /\(#\d+\)/.exec(result.title);
        if (matchIssue) {
            const headingCharacter = result.title[matchIssue.index - 1];
            if (headingCharacter !== ' ') {
                result.title = result.title.slice(0, matchIssue.index) + ' ' + result.title.slice(matchIssue.index);
            }
        }
        result.title = result.title.replaceAll(/:(\S)/g, ': $1');
        return result.title;
    }
}
