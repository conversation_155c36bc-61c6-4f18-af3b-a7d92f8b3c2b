/* eslint-disable complexity */
import {
    FunctionParameterDefinition,
    ProviderInit,
    SkillProvider,
    TaskProgressChunk,
    TaskProgressChunkStream,
} from '@comate/plugin-host';
import {GitUtil} from '../utils/git.js';
import createIssueTitlePrompt from '../prompts/createIssueTitle.prompt';
import createIssueBodyPrompt from '../prompts/createIssueBody.prompt';
import {ContextInit, GitContext, createContext} from '../utils/context.js';
import {GitHostType} from '../utils/githost.js';

interface State {
    context: GitContext | null;
}

export class CreateIssueSkillProvider extends SkillProvider {
    static skillName = 'createIssue';

    static displayName = '创建Issue';

    static description = '通过代码的修改创建Git Issue';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    private readonly git: GitUtil;

    constructor(init: ProviderInit) {
        super(init);

        this.git = new GitUtil(init.cwd);
    }

    // eslint-disable-next-line max-statements
    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new TaskProgressChunkStream();
        const fileSystem = await this.requestWorkspaceFileSystem();

        if (!fileSystem) {
            yield stream.flushReplaceLast('插件需要读取项目内文件来生成提交信息。');
            return;
        }

        const githubAccessToken = await this.config.getAsString('githubAccessToken');
        const giteeAccessToken = await this.config.getAsString('giteeAccessToken');
        const gitlabAccessToken = await this.config.getAsString('gitlabAccessToken');

        const init: ContextInit = {
            git: this.git,
            retriever: this.retriever,
            llm: this.llm,
            fileSystem,
            accessToken: {
                [GitHostType.GitHub]: githubAccessToken,
                [GitHostType.Gitee]: giteeAccessToken,
                [GitHostType.GitLab]: gitlabAccessToken,
            },
        };
        const state: State = {
            context: null,
        };

        try {
            for await (const data of createContext(init)) {
                if (data.type === 'message') {
                    yield stream.flush(data.content + '\n\n');
                }
                else {
                    state.context = data.value;
                }
            }
        }
        catch (ex) {
            yield stream.flushReplaceLast(ex instanceof Error ? ex.message : `${ex}`);
        }

        if (!state.context) {
            return;
        }

        const {projectInfo, modifications, gitHostUtil} = state.context;
        const createIssuePrompt = this.llm.createPrompt(
            createIssueTitlePrompt,
            {
                ...projectInfo,
                modifications,
            }
        );
        const title = await this.llm.askForText(createIssuePrompt);
        yield stream.flush('建议标题：\n\n');
        yield stream.flush(`> ${title}\n\n`);

        yield stream.flush('正在生成Issue内容……\n\n');
        const createBodyPrompt = this.llm.createPrompt(
            createIssueBodyPrompt,
            {
                ...projectInfo,
                modifications,
            }
        );
        const body = await this.llm.askForText(createBodyPrompt);
        yield stream.flush('建议内容：\n\n');
        yield stream.flush(`${body.split('\n').map((v: string) => '> ' + v).join('\n')}\n\n`);

        // TODO: 这里应该要有个按钮让用户确认
        yield stream.flush(`正在代码库${projectInfo.owner}/${projectInfo.repo}创建Issue……\n\n`);
        try {
            const created = await gitHostUtil?.createIssue(title, body);

            if (created?.id) {
                yield stream.flush(`已成功创建[#${created.id} ${created.title}](${created.url})\n\n`);
            }
            else {
                yield stream.flush('创建issue失败，请检查是否为支持的平台和插件相关token配置\n\n');
            }
        }
        catch (ex) {
            yield stream.flush('创建issue失败，请检查是否为支持的平台和插件相关token配置\n\n');
        }
    }
}
