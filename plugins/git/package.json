{"private": true, "name": "@comate-plugin/git", "version": "0.9.2", "type": "module", "scripts": {"clean": "rm -rf dist", "dev": "cpd dev", "build": "tsc && cpd build", "lint": "eslint --max-warnings=0 src --fix"}, "dependencies": {"@gitbeaker/rest": "^39.34.3", "hosted-git-info": "^7.0.1", "octokit": "^3.1.2", "simple-git": "^3.22.0"}, "devDependencies": {"@comate/plugin-cli": "^0.9.2", "@comate/plugin-host": "^0.9.2", "@types/hosted-git-info": "^3.0.5", "eslint": "^8.54.0", "typescript": "^5.3.3"}, "comate": {"name": "git", "version": "1.0.0", "icon": "./assets/git.svg", "entry": "./dist/index.js", "displayName": "Git", "description": "支持创建GitHub、Gitee、GitLab Issues、生成提交信息等", "keyword": [], "capabilities": [{"type": "Skill", "name": "git-help", "displayName": "插件介绍", "description": "插件介绍", "placeholder": "无需输入内容，回车获得插件介绍"}, {"type": "Skill", "name": "commitMessage", "displayName": "生成提交", "description": "使用大模型为你生成Git提交信息", "placeholder": "无需输入内容，回车为你生成提交信息"}, {"type": "Skill", "name": "createIssue", "displayName": "创建Issue", "description": "通过代码的修改创建Git Issue", "placeholder": "无需输入内容，回车为你生成一个Issue"}, {"type": "Fallback", "name": "fallback", "displayName": "兜底", "description": "兜底"}], "configSchema": {"sections": [{"title": "应用授权", "properties": {"githubAccessToken": {"type": "string", "title": "GitHub Access Token", "description": "输入你的GitHub Personal Access Token，请确保有创建Issue的权限", "default": ""}, "giteeAccessToken": {"type": "string", "title": "Gitee Access Token", "description": "输入你的Gitee Personal Access Token，请确保有创建Issue的权限", "default": ""}, "gitlabAccessToken": {"type": "string", "title": "GitLab Access Token ", "description": "输入你的GitLab Personal Access Token，请确保有创建Issue的权限", "default": ""}}, "required": []}]}}}