{"private": true, "name": "@comate-plugin/workcard", "version": "0.8.0", "type": "module", "scripts": {"clean": "rm -rf dist", "dev": "cpd dev", "build": "tsc && cpd build"}, "dependencies": {}, "devDependencies": {"@comate/plugin-cli": "^0.9.2", "@comate/plugin-host": "^0.9.2", "typescript": "^5.3.2"}, "comate": {"name": "workcard", "version": "1.0.0", "icon": "./assets/favicon.png", "entry": "./dist/index.js", "displayName": "智能工作卡", "description": "支持自动生成接入智能工作卡代码，包括发卡，回调，更新接口对象构建，加解密等方法", "keyword": [], "capabilities": [{"type": "Skill", "name": "workcard-help", "displayName": "插件介绍", "description": "确认插件成功运行并介绍开发方式", "placeholder": "无需输入内容，回车获得插件介绍"}, {"type": "Skill", "name": "buildParamForUserData", "displayName": "生成UserData对象", "description": "构建卡片发送对象", "placeholder": "请输入卡片模板ID"}, {"type": "Skill", "name": "buildParamForSendCard", "displayName": "生成发卡请求入参&返回值对象", "description": "生成发卡请求入参&返回值对象", "placeholder": "请输入卡片模板ID"}, {"type": "Skill", "name": "buildParamForModifyCard", "displayName": "生成卡片修改请求对象", "description": "生成卡片修改请求对象", "placeholder": "请输入卡片模板ID"}, {"type": "Skill", "name": "buildParamForCallbackRequest", "displayName": "生成回调请求入参&返回值对象", "description": "生成回调请求入参&返回值对象", "placeholder": "请输入卡片模板ID"}, {"type": "Skill", "name": "MsgCipher<PERSON>til", "displayName": "生成回调加解密方法", "description": "生成回调加解密方法", "placeholder": "请输入卡片模板ID"}], "configSchema": {"sections": []}}}