您好，我是智能工作卡接入代码编写助手，提供卡片发送参数对象定义，修改卡片接口入参定义，回调接口入参和返回值定义，回调加解密方法等能力。
按照智卡能力接入习惯，可按照顺序依次使用一下能力，如无异常，可以点击『生成文件』按钮直接保存使用：

**1. 生成卡片UserData对象**
- 使用场景：业务在接入工作卡时，发送卡片，修改卡片，回调返回都会用到该对象
- 指令描述：根据用户输入模板ID将卡片模板中绑定的动态变量转化为Java对象，并生成对应的代码；该指令生成一个java类。
其中类名：WorkCardUserData建议修改为业务相关名称
**2. 生成卡片发送入参&返回值对象**
- 使用场景：业务创建完卡片模板后，开发「卡片发送」代码逻辑时
- 指令描述：该指令会生成2个java类，一个为发卡接口入参SendCardRequest，一个为发卡请求返回值SendCardResponse。
**3. 生成卡片修改请求入参对象**
- 使用场景：业务需要修改已发送卡片内容，开发「卡片修改」代码逻辑时
- 指令描述：该指令会生成1个java类，根据用户输入模板ID自动生成卡片「修改接口」入参对应的Java对象ModifyCardRequest。
**4. 生成卡片回调入参&返回值对象**
- 使用场景：业务处理「卡片回调」逻辑时
- 指令描述：该指令会生成3个java类，一个为回调入参解密后参数对象CallbackRequestMsgJson，一个为回调业务响应数据CallbackResponseMsgJson，一个为回调方法实际返回对象CallbackResponse(加密后)。
**5. 回调加解密方法**
- 使用场景：业务处理「卡片回调」逻辑时，处理接收参数(解密)，以及处理返回值(加密)
- 指令描述：该指令会生成1个java类，包含了回调入参解析解密方法，以及回调业务响应加密算法。

如果您在使用过程中发现问题或有其他需求、建议，欢迎加入我们的如流用户群 **5798923** 进行反馈。