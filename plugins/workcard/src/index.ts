import {PluginSetupContext} from '@comate/plugin-host';
import {HelpSkillProvider} from './providers/help.js';
import {buildParamForUserDataProvider} from './providers/buildParamForUserData.js';
import {buildParamForSendCardProvider} from './providers/buildParamForSendCard.js';
import {buildParamForModifyCardProvider} from './providers/buildParamForModifyCard.js';
import {buildParamForCallbackRequestProvider} from './providers/buildParamForCallbackRequest.js';
import {MsgCipherUtilProvider} from './providers/MsgCipherUtil.js';

export function setup({registry}: PluginSetupContext) {
    registry.registerSkillProvider('workcard-help', HelpSkillProvider);
    registry.registerSkillProvider('buildParamForUserData', buildParamForUserDataProvider);
    registry.registerSkillProvider('buildParamForSendCard', buildParamForSendCardProvider);
    registry.registerSkillProvider('buildParamForModifyCard', buildParamForModifyCardProvider);
    registry.registerSkillProvider('buildParamForCallbackRequest', buildParamForCallbackRequestProvider);
    registry.registerSkillProvider('MsgCipherUtil', MsgCipherUtilProvider);
}
