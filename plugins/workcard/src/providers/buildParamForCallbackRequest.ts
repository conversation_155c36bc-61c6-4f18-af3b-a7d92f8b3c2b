import {
    FunctionParameterDefinition,
    SkillProvider,
    TaskProgressChunk,
    TaskProgressChunkStream,
} from '@comate/plugin-host';

import {SEARCH_URL} from '../config/config.js';
interface Args {
    query: string;
}

export class buildParamForCallbackRequestProvider extends SkillProvider<Args> {
    static skillName = 'buildParamForCallbackRequest';

    static displayName = '生成卡片回调请求入参&返回值对象';

    static description = '生成卡片回调请求入参&返回值对象';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        // 用户在输入框输入的问题
        const {query} = this.currentContext;
        const stream = new TaskProgressChunkStream();
        const userDetail = await this.currentUser.requestDetail();
        const realUserName = userDetail && userDetail.name || '未知用户';
        const codePath = this.currentContext.activeFilePath;
        const activeFileLanguage = this.currentContext.activeFileLanguage;
        const repository = await this.retriever.repositoryInfo();

        const raw = JSON.stringify({
            query,
            task_engine_userId: realUserName,
            codePath,
            activeFileLanguage,
            repository,
            source: 'comate',
            cmdType: 'buildParamForCallbackRequest',
        });
        const options = {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: raw,
        };

        try {
            const response = await fetch(SEARCH_URL, options);
            const result = await response.json();
            yield stream.flushReplaceLast(result.content); // markdown字符串
        }
        catch (error) {
            yield stream.flushReplaceLast(`请求出错，请联系负责人或稍后再试。${error as string}`);
        }
    }

    defaultArgumentValue(): Args {
        return {query: '任意代码'};
    }
}
