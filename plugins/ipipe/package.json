{"private": true, "name": "@comate-plugin/ipipe", "version": "0.8.0", "type": "module", "scripts": {"clean": "rm -rf dist", "dev": "cpd dev", "build": "tsc && cpd build", "lint": "eslint --max-warnings=0 src --fix"}, "dependencies": {"simple-git": "^3.22.0"}, "devDependencies": {"@comate/plugin-cli": "^0.9.2", "@comate/plugin-host": "^0.9.2", "typescript": "^5.3.2"}, "comate": {"name": "ipipe", "version": "3.0.0", "icon": "./assets/iPipePlugin.png", "entry": "./dist/index.js", "displayName": "iPipe", "description": "iPipe官方插件，支持获取构建记录、收藏流水线等", "keyword": [], "capabilities": [{"type": "Skill", "name": "ipipe-help", "displayName": "插件介绍", "description": "插件介绍", "placeholder": "无需输入内容，回车获得插件介绍"}, {"type": "Skill", "name": "queryiPipe", "displayName": "查询流水线", "description": "根据输入query模糊搜索查询流水线及其对应的最近运行分支、状态和触发人", "placeholder": "根据输入query模糊搜索查询流水线"}, {"type": "Skill", "name": "getCollectiPipe", "displayName": "获取我收藏的流水线", "description": "获取我收藏的所有流水线或者当前模块中被收藏的流水线", "placeholder": "当前模块/所有模块中被我收藏的流水线"}, {"type": "Skill", "name": "getiPipeBuild", "displayName": "获取&执行流水线构建记录", "description": "获取模块的最近构建记录&执行【待人工执行】的流水线", "placeholder": "获取构建记录&执行流水线"}], "configSchema": {"sections": []}}}