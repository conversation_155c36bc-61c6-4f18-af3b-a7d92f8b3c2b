您好，我是您的comate+之iPipe助理，基于iPipe的OpenAPI给你提供快捷的流水线通道，快速直达你之所想

**查询&执行流水线**
* 根据流水线名模糊查询&执行所有模块的匹配流水线
* query示例：空(当前模块最近运行的流水线)、流水线名关键字

**获取&执行流水线构建记录**
* 获取&执行当前/指定模块的流水线最近构建记录
* query示例：空(当前模块最近运行的流水线)、所有/所有模块、模块：wiki-web、失败、20条

**获取&执行我收藏的流水线**
* 获取&执行当前/所有模块中被我收藏的流水线或者我收藏的流水线
* query示例：空(当前模块被我收藏的流水线)、所有/所有模块、模块：wiki-web

更多介绍请参考：[用户手册](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/t7n0qKWNJW/AtKECXn-wv/hwnb7T70QhhZK2)