// ************ vip 地址
export const getCollectiPipeUrl = 'http://************:8080/comate/pipeline/favorite';
export const getiPipeBuildUrl = 'http://************:8080/comate/pipeline/build';
export const queryiPipeUrl = 'http://************:8080/comate/pipeline/query';
export const buildParamsUrl = 'http://************:8080/comate/pipeline/params';
export const buildTriggerUrl = 'http://************:8080/comate/pipeline/trigger/build';
export const getCollectiPipeV2Url = 'http://************:8080/comate/pipeline_new/favorite';
export const getiPipeBuildV2Url = 'http://************:8080/comate/pipeline_new/build';
export const queryiPipeV2Url = 'http://************:8080/comate/pipeline_new/query';
export const buildParamsV2Url = 'http://************:8080/comate/pipeline_new/params';
export const buildTriggerV2Url = 'http://************:8080/comate/pipeline_new/trigger/build';
