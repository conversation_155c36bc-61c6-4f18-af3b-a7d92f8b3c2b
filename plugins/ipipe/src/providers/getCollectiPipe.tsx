import {
    SkillProvider,
    FunctionParameterDefinition,
    ElementChunkStream,
    TaskProgressChunkStream,
    TaskProgressChunk,
    ProviderInit,
} from '@comate/plugin-host';
import {fetchWithTimeout} from '../common/fetchWithTimeout.js';
import {getCollectiPipeV2Url, buildParamsV2Url, buildTriggerV2Url} from '../config/config.js';
import {initDeployConfig, getDeployConfig} from '../../../tor/src/utils.js';
import {GitUtil} from '../utils/git.js';

interface Params {
    type?: string;
    module?: string;
    pipeline_name?: string;
    pipeline_url?: string;
    user?: string;
    buildId?: number;
}

export class getCollectiPipeProvider extends SkillProvider {
    static skillName = '获取我收藏的流水线';
    static displayName = '获取我收藏的流水线';
    static description = '获取我收藏的所有流水线或者当前模块中被收藏的流水线';

    private readonly cwd: string;
    private readonly git: GitUtil;

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    constructor(init: ProviderInit) {
        super(init);
        this.cwd = init.cwd;
        this.git = new GitUtil(init.cwd);
    }

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new ElementChunkStream();

        try {
            const myHeaders = new Headers();
            myHeaders.append('Content-Type', 'application/json');

            const userDetail = await this.currentUser.requestDetail();
            const realUserName = userDetail && userDetail.name || '未知用户';
            const repositoryInfo = await this.retriever.repositoryInfo();
            const queryInfo = this.currentContext.query;
            const branch = await this.git.getCurrentBranch();

            // 检查repositoryInfo是否具有repositoryUrl属性且不为null
            if (repositoryInfo && 'repositoryUrl' in repositoryInfo && repositoryInfo.repositoryUrl !== null) {
                const url = repositoryInfo.repositoryUrl;
                const pathStart = url.split('://')[1];
                const pathParts = pathStart.split('/');
                const lastThreeParts = pathParts.filter(part => part).slice(-3);
                const curModule = lastThreeParts.join('/');

                const raw = JSON.stringify({
                    query: queryInfo,
                    user: realUserName,
                    currentSpace: curModule,
                    currentBranch: branch
                });

                const options = {
                    method: 'POST',
                    headers: myHeaders,
                    body: raw,
                };
                const response = await fetchWithTimeout(getCollectiPipeV2Url, options);
                const result = await response.json();
                const message = result.data.message;
                const content = result.data.content;
                const code = result.code;

                if (code === 500) {
                    // 所有查询结果均为空
                    yield stream.flushReplaceLast(<p>{message}</p>);
                    return;
                }
                else if (code === 0 || code === 400) {
                    // 当前模块查询成功，或者当前模块未找到，其他模块查询成功
                    yield stream.flush(<p>{message}</p>);
                }
                else {
                    // 其他错误
                    yield stream.flushReplaceLast(<p>请求出错，请联系负责人@qinqin或稍后再试~</p>);
                    return;
                }
                for (let i = 0; i < content.length; i++) {
                    const item = content[i];
                    let branchs_info = item.branches;
                    let pipelineName = item.pipelineName;
                    let pipelineBuildUrl = item.pipelineBuildUrl;
                    let module = item.module;
                    let moduleName = "当前模块"
                    if (code === 400 || module.trim() !== curModule.trim()) {
                        moduleName = module;
                    }

                    yield stream.flush(
                        <flex keepWrap={false} centered gap="sm">
                            <img
                                src="https://rl-wiki-mega-demo-gray.bj.bcebos.com/rl-wiki-gray/rl-wiki-mega-demo/branch.png"
                                alt="分支icon"
                                width={16}
                                height={16}
                            />
                            流水线：<a href={pipelineBuildUrl}>{pipelineName}</a> ({moduleName})
                        </flex>
                    )

                    let levelWord: 'normal' | 'critical' | 'medium' | 'low' | 'high'; // 显式声明类型
                    let colorWord: 'green' | 'red' | 'orange' | 'yellow' | 'gray'; // 显式声明类型
                    yield stream.flush(
                        <p>
                            <p>
                                {branchs_info.map((item: any, index: number) => {
                                    let statusText = null;
                                    switch (item.status) {
                                        case '成功':
                                            levelWord = 'normal';
                                            colorWord = 'green';
                                            break;
                                        case '失败':
                                            levelWord = 'critical';
                                            colorWord = 'red';
                                            break;
                                        case '执行中':
                                            levelWord = 'high';
                                            colorWord = 'orange';
                                            break;
                                        case '待人工执行':
                                            levelWord = 'medium';
                                            colorWord = 'yellow';
                                            break;
                                        case '已跳过':
                                            levelWord = 'low';
                                            colorWord = 'gray';
                                            break;
                                        default:
                                            levelWord = 'low';
                                            colorWord = 'gray';
                                            break;
                                    }
                                    statusText = <alert-tag level={levelWord}>{item.status}</alert-tag>;
                                    const build_params: Params = {
                                        type: 'build',
                                        module: module,
                                        pipeline_url: item.buildUrl,
                                        pipeline_name: pipelineName,
                                        user: realUserName,
                                    };
                                    return (
                                        <p>
                                            <card color={colorWord}>
                                                <flex centered gap="sm">
                                                    {statusText} <a href={item.buildUrl}>{item.branchName}</a>
                                                </flex>
                                                <flex centered gap="sm">
                                                    {(item.jobName.trim().length > 0)
                                                        && (
                                                            <p>
                                                                运行阶段：{item.jobName}
                                                            </p>
                                                    )}
                                                </flex>
                                                <flex centered gap="sm">
                                                    {(item.build_time.trim().length > 0)
                                                        && (
                                                            <p>
                                                                执行时间：{item.build_time}
                                                            </p>
                                                    )}
                                                </flex>
                                                <flex centered gap="sm">
                                                    {(item.triggerUser.trim().length > 0)
                                                        && (
                                                            <p>
                                                                触发人：{item.triggerUser}
                                                            </p>
                                                    )}
                                                </flex>
                                                <flex centered gap="sm">
                                                    {(item.status === '待人工执行' || item.status === '失败')
                                                        && (
                                                            <command-button
                                                                commandName="commitMessage:confirmIssue"
                                                                tooltipText="触发流水线执行"
                                                                data={build_params}
                                                            >
                                                                执行
                                                            </command-button>
                                                    )}
                                                </flex>
                                            </card>
                                        </p>
                                    );
                                })}
                            </p>
                        </p>
                    );
                }
            }
            else {
                // 处理repositoryInfo不包含repositoryUrl属性或者为null的情况
                yield stream.flushReplaceLast(<p>无法获取仓库URL，请联系负责人@qinqin或稍后再试。</p>);
            }
        }
        catch (error) {
            yield stream.flushReplaceLast(<p>请求出错，请联系负责人@qinqin或稍后再试。${error as string}</p>);
        }
    }

    async *handleCommand(commandName: string, data: any): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new ElementChunkStream();

        if (commandName === 'commitMessage:confirmIssue') {
            if (data.type === 'build') {
                const build_params: Params = data;
                // 执行构建
                const raw = JSON.stringify({
                    pipeline_build_url: build_params.pipeline_url,
                    pipeline_name: build_params.pipeline_name,
                    module_name: build_params.module,
                    user: build_params.user,
                });
                const myHeaders = new Headers();
                myHeaders.append('Content-Type', 'application/json');

                const options = {
                    method: 'POST',
                    headers: myHeaders,
                    body: raw,
                };
                const response = await fetchWithTimeout(buildParamsV2Url, options);
                const result = await response.json();

                if (result.code === 0) {
                    // 返回自定义参数，让用户确认和修改
                    // 首先查找本地是否存在配置文件，如若存在，直接将查询到的数据覆盖文件；如若不存在，新建文件后写入查询到的数据
                    try { // 这里可以返回一个ID，便于后续的流水线执行
                        const params = result.data.inParamsMap;
                        build_params.buildId = result.data.stageBuildId;
                        build_params.type = 'yes';
                        const resData = initDeployConfig(this.cwd, 'ipipe', params);
                        if (resData?.type === 'success') {
                            const configPath = resData.path;
                            yield stream.flush(
                                <p>
                                    已为您获取该流水线的的参数，请<strong>检查和确认</strong>配置文件是否需要<strong>
                                        修改
                                    </strong>，文件地址：
                                    <file-link to={configPath} line={1}>
                                        ipipe_params.json
                                    </file-link>{' '}
                                    <br />
                                    修改并保存后点击【确认】后即可开始执行流水线，或点击【取消】来结束此次指令任务
                                </p>
                            );
                        }
                        // 用户点击确认后才开始部署，如果点击取消，则不部署
                        yield stream.flush(
                            <button-group>
                                <command-button
                                    commandName="commitMessage:confirmIssue"
                                    data={build_params}
                                    replyText="确认"
                                    variant="primary"
                                >
                                    确认
                                </command-button>
                                <command-button
                                    commandName="commitMessage:confirmIssue"
                                    data="no"
                                    replyText="取消"
                                >
                                    取消
                                </command-button>
                            </button-group>
                        );
                    }
                    catch (ex) {
                        yield stream.flushReplaceLast(<p>请求出错，请联系负责人@qinqin或稍后再试。${ex as string}</p>);
                        return;
                    }
                }
                else {
                    // 除了自定义参数需要增加交互，其他的场景都是通过后端直接返回message提示用户
                    if (
                        result.data && typeof result.data.message === 'string' && build_params
                        && typeof build_params.pipeline_url === 'string'
                    ) {
                        yield stream.flushReplaceLast(
                            <p>
                                {result.data.message}，可点击<a href={build_params.pipeline_url}>
                                    此链接
                                </a>查看流水线执行详情～
                            </p>
                        );
                    }
                    else {
                        // 处理错误情况，例如显示默认消息或进行错误日志记录
                        yield stream.flushReplaceLast(<p>无法获取消息或流水线URL，请联系负责人@qinqin或稍后再试～</p>);
                    }
                }
            }
            else if (data.type === 'yes') {
                if (!data.user || !data.buildId || !data.pipeline_url) {
                    throw new Error('Invalid data parameters');
                }
                const build_params: Params = data;
                let fileBuffer: Buffer | undefined;
                let request: any;
                const resData = getDeployConfig(this.cwd, 'ipipe');
                if (resData?.type === 'file') {
                    fileBuffer = resData.value;
                    if (fileBuffer) {
                        request = JSON.parse(fileBuffer.toString());
                    }
                }

                const raw = JSON.stringify({
                    user: build_params.user,
                    inParamsMap: request,
                });

                const myHeaders = new Headers();
                myHeaders.append('Content-Type', 'application/json');

                const options = {
                    method: 'POST',
                    headers: myHeaders,
                    body: raw,
                };

                const response = await fetchWithTimeout(
                    buildTriggerV2Url + '?stageBuildId=' + build_params.buildId,
                    options
                );
                const result = await response.json();
                yield stream.flush(
                    <p>返回信息是：{result.data.message}, 流水线URL地址是：{build_params.pipeline_url}</p>
                );
                if (
                    result.code === 200 && typeof result.data.message === 'string' && build_params
                    && typeof build_params.pipeline_url === 'string'
                ) {
                    yield stream.flushReplaceLast(
                        <p>
                            {result.data.message}，可点击<a href={build_params.pipeline_url}>
                                此链接
                            </a>查看流水线执行详情～
                        </p>
                    );
                }
                else {
                    // 处理错误情况，例如显示默认消息或进行错误日志记录
                    yield stream.flushReplaceLast(<p>获取信息异常，请联系负责人@qinqin或稍后再试～</p>);
                }
            }
            else if (data === 'no') {
                yield stream.flushReplaceLast(<p>您已取消此次流水线执行任务～</p>);
            }
        }
    }
}

