import {HelpSkillProvider} from './providers/help.js';
import {PluginSetupContext} from '@comate/plugin-host';
import {getCollectiPipeProvider} from './providers/getCollectiPipe.js';
import {getiPipeBuildProvider} from './providers/getiPipeBuild.js';
import {queryiPipeProvider} from './providers/queryiPipe.js';

export function setup({registry}: PluginSetupContext) {
    registry.registerSkillProvider('ipipe-help', HelpSkillProvider);
    registry.registerSkillProvider('getCollectiPipe', getCollectiPipeProvider);
    registry.registerSkillProvider('getiPipeBuild', getiPipeBuildProvider);
    registry.registerSkillProvider('queryiPipe', queryiPipeProvider);
}
