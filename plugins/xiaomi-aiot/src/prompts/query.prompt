你是一个智能编码助手，精通AIoT开发，熟悉硬件资料，语言风格专业、严谨。
# 指令描述
参考硬件资料，分析用户问题，确定“核心”和“子板”的内容，最后结果以json格式输出。

# 任务要求
1. 结果必须以json格式输出，包含"platform"和"boards"两个字段。
2. "platform"可选值：u1
3. "boards"可选值：E1，E2，E3，E4，S1，S2，S3，S5，S6，S7，S8，S9，S10，S11，C2，C3
4. 输出格式如下：
```json
{
    "platform": "xxx",
    "boards": ["xxx", "xxx"]
}
```

# 输入
用户问题：
{{query}}

# 参考资料
硬件资料：
【E2 风扇执行器子板】介绍
1/4【E2风扇执行器子板】介绍
更新版本记录
版本 时间 修改内容
V1.0 2023-4-1文档创建2/4
子板正面
子板侧面
1.子板定位
E2子板是风扇执行器子板，采用PCA9685驱动芯片作为核心。
2.子板功能3/4
功能框图
E2子板是风扇执行器子板，可实现低、中、高三级调速的电风扇，可用于模
拟风扇、加湿器等类似产品。输入电压5V，通过SPX3819M5-L-3-3/TR稳压芯片
转换为3.3V。
PCA9685芯片驱动风扇。PCA9685芯片地址为0xC8,0xCA,0xCC,0xCE（由拨码
开关配置），默认DIP开关都拨在上方时，地址为0xC8。
3.子板参数
子板名称：风扇
•子板类别：执行器子板
•子板型号：E2
•子板尺寸：107×78×42mm
•主处理器：
驱动芯片PCA9685
•接口说明：
①电源指示灯——1个（RED）
②拨码开关——2位（地址选择开关）
3风扇——1个4/44.使用方法说明
•连接方式。通过pogopin接口，与母板的电源及通信接口相连接，详细接口
对应关系见E2子板原理图。
•拨码开关。该板子有两位拨码开关，默认在OFF状态。拨码开关对应i2c地
址的最后两位。
•堆叠方式。支持平铺堆叠和垂直堆叠两种方式：
方式1
方式2

【U1 GD32F450ZKT6计算与存储子板】介绍
1/4【U1GD32F450ZKT6计算与存储子板】介绍
更新版本记录
版本 时间 修改内容
V1.0 2023-4-8文档创建2/4
子板正面
子板侧面
1.子板定位
U1子板是GD32F450ZKT6计算与存储子板，采用GD32F450ZKT6芯片作为核心。
2.子板功能3/4
功能框图
U1子板是GD32F450ZKT6计算与存储子板，可作为核心控制单片机，控制通
信、传感器、执行器等搭建不同的IoT场景。输入电压5V，经SPX3819M5-L-3-3/TR
稳压芯片转换为3.3V。
可通过GD32F450ZKT6芯片进行存储，并可驱动各个芯片实现相应功能。
0#槽位：切换开关接通，USB主从切换到芯片的USB-H1。
1-5#槽位：切换开关断开，USB主从切换到芯片的USB-D。
3.子板参数
•子板名称：GD32F450ZKT6
•子板类别：计算与存储子板
•子板型号：U1
•子板尺寸：107×77×26.3mm
•主处理器：GD32F450ZKT6
•接口说明：
1电源指示灯——1个（RED）4/42复位按键——1个
3系统运行指示灯——1个
4以太网通信指示灯——2个
4.使用方法说明
•连接方式。向下通过pogopin（24pin）连接母板，进而连接其他子板；向
下提供pogopin（12pin）调试接口；向上提供pogopin（24pin）连接其他子板；
向上提供pogopin（4pin）连接Speaker子板。详细接口对应关系见U1子板原理
图。
•堆叠方式。支持平铺堆叠和垂直堆叠两种方式：
方式1
方式2

【C2 Zigbee通信子板】介绍
1/5【C2ZigBee通信子板】介绍
更新版本记录
版本 时间 修改内容
V1.0 2023-4-8文档创建2/5
子板正面
子板侧面
1.子板定位
C2子板是ZIGBEE无线通信子板，采用亿佰特的E18作为核心，出厂自带
ZigBee标准自组网协议用户也可以进行二次开发。
2.子板功能3/5
功能框图
C2子板是ZIGBEE无线通信子板，应用于低速率下无线通信技术，采用ZIGBEE
技术进行无线互联。该板采用了亿佰特设计生产的E18作为核心，输入电压5V，
经SPX3819M5-L-3-3/TR稳压芯片转换为3.3V。E18是一款小体积2.4GHz频段的
ZigBee转串口无线模块，贴片型，PCB板载天线，发射功率2.5mW，引脚间距1.27mm，
出厂自带自组网固件，到手即用，适用于多种应用场景。E18-MS1-PCB采用美国
德州仪器公司原装进口CC2530射频芯片,芯片内部集成了8051单片机及无线收发
器，出厂自带固件基于ZigBee协议实现的串口数据透传，并适用于ZigBee设计
及2.4GHzIEEE802.15.4协议。E18系列出厂带有ZigBee标准自组网协议用户
也可以进行二次开发。
指令集：HEX格式，通讯协议：参考E18数据手册文档。
3.子板参数
•子板名称：ZigBee通信
•子板类别：通信子板
•子板型号：C2
•子板尺寸：107×77×26.3mm
•主处理器：4/5ZigBee模组E18-MS1-PCB
切换开关RS2266
•接口说明：
1电源指示灯——1个（RED）
2复位按键——1个
3拨码开关——1位
4串口波特率复位按键——1个
5网络通信指示灯——2个
4.使用方法说明
•连接方式。向下通过pogopin（24pin）里面的UART2连接母板，进而连接
计算子板，向上提供pogopin（24pin）供其他子板与计算子板通讯。详细接口对
应关系见C2子板原理图。
•拨码开关。该板子有1位拨码开关，通过拨码开关可以选择工作模式，当拨
码开关向上时，SW=OFF，此时为通信模式，当拨码开关向下时，SW=ON，此时为供
电模式。详细控制请参数demo说明及例程。
•堆叠方式。支持平铺堆叠和垂直堆叠两种方式：
方式1
5/5方式2

【C3 通用WiFi-BLE通信子板】介绍
1/4【C3通用WiFi-BLE通信子板】介绍
更新版本记录
版本 时间 修改内容
V1.0 2023-4-8文档创建2/4
子板正面
子板侧面
1.子板定位
C3子板是通用WiFi-BLE通信子板，采用WIFI模组ESP-WROOM-32D作为核心。
2.子板功能3/4
功能框图
C3子板是通用WiFi-BLE通信子板，板载WIFI模组ESP-WROOM-32D，可连接
无线WIFI网络，可连接蓝牙，通过WIFI网络实现远程控制，通信功能，通过蓝
牙连接手机实现远程控制。C3子板可通过拨码开关选择模式，当拨码开关向上时，
SW=OFF，此时为通信模式；当拨码开关向下时，SW=ON，此时为供电模式。
3.子板参数
•子板名称：通用WiFi-BLE
•子板类别：通信子板
•子板型号：C3
•子板尺寸：107×77×26.3mm
•主处理器：
WIFI模组ESP-WROOM-32D
•接口说明：
1电源指示灯——1个（RED）
2复位按键——1个
3拨码开关——1位4/44串口波特率复位按键——1个
4.使用方法说明
•连接方式。向下通过pogopin（24pin）里面的UART1连接母板，进而连接
计算子板，向上提供pogopin（24pin）供其他子板与计算子板通讯。详细接口对
应关系见C3子板原理图。
•拨码开关。该板子有1位拨码开关，通过拨码开关可以选择工作模式，当拨
码开关向上时，SW=OFF，此时为通信模式，当拨码开关向下时，SW=ON，此时为供
电模式。详细控制请参数demo说明及例程。
•堆叠方式。支持平铺堆叠和垂直堆叠两种方式：
方式1
方式2

【E1 LED+数码管执行器子板】介绍
1/4【E1LED+数码管执行器子板】介绍
更新版本记录
版本 时间 修改内容
V1.0 2023-3-31文档创建
V1.1 2023-3-31格式一致性2/4
子板正面
子板侧面
1.子板定位
E1子板是LED+数码管执行器子板，采用HT16K33和PCA9685驱动芯片作为核
心。
2.子板功能
执行器子板，输入电压5V，通过SPX3819M5-L-3-3/TR稳压芯片转换为3.3V。
参照下面功能框图掌握子板状态：3/4
功能框图
HT1633是一款内存映射和多功能数码管驱动芯片。设备中的最大显示段数为
128个模式（16个段和8个公共区），具有13×3（最大）矩阵键扫描电路。HT1633
的软件配置特点使其适合多种LED应用，包括LED模块和显示子系统。HT1633与
大多数微控制器兼容，并通过双线双向I2C总线进行通信。HT16K33芯片地址为
0xE0，0xE2，0xE4，0xE6（由拨码开关配置），默认DIP开关都拨在上方时，地址
为0xE0。
PCA9685芯片以PWM的方式驱动RGB灯，RGB灯以红绿蓝三色混光而成，可以
设置颜色，亮度等；PCA9685芯片地址为0xC0，0xC2，0xC4，0xC6（由拨码开关
配置），默认DIP开关都拨在上方时，地址为0xC0。
3.子板参数
•子板名称：LED+数码管
•子板类别：执行器子板
•子板型号：E1
•子板尺寸：107×77×26.3mm
•主处理器：
数码管驱动芯片HT16K334/4PWM驱动芯片PCA9685
•接口说明：
1电源指示灯——1个（RED）
2拨码开关——2位（地址选择开关）
3LED数码管——4位1路
4RGBLED——1路
4.使用方法说明
•连接方式。通过pogopin接口，与母板的电源及通信接口相连接，详细接口
对应关系见E1子板原理图。
•拔码开关。该板子有两位拨码开关，默认在OFF状态，拨码开关对应i2c地
址的最后两位。
•堆叠方式。支持平铺堆叠和垂直堆叠两种方式：
方式1
方式2

【S3 麦克风阵列传感器子板】介绍
1/4【S3麦克风阵列传感器子板】介绍
更新版本记录
版本 时间 修改内容
V1.0 2023-4-8文档创建2/4
子板正面
子板侧面
1.子板定位
S3子板是麦克风阵列传感器子板。
2.子板功能3/4
功能框图
S3子板是麦克风阵列传感器子板，包括一个单MIC和两组环阵MIC，可用于
采集声音信号。
3.子板参数
•子板名称：麦克风阵列
•子板类别：传感器子板
•子板型号：S3
•子板尺寸：107×77×26.3mm
•主处理器：
数字麦克风MP34DT05-A
MEMS硅麦克风SLM42Q3AT
切换开关RS2266
•接口说明：
1电源指示灯——1个（RED）
2拨码开关——1位（切换麦克风阵列的线阵和环阵）
4.使用方法说明4/4•连接方式。通过pogopin(24pin)接口，与母板的电源及通信接口相连接，
详细接口对应关系见S3子板原理图。
•拨码开关。该板子有1位拨码开关，默认在OFF状态，可通过拨码开关切换
麦克风阵列的线阵和环阵，当拨码开关向上时；SW=OFF为环阵，当拨码开关向下
时，SW=ON为线阵。详细控制请参数demo说明及例程。
•堆叠方式。支持平铺堆叠和垂直堆叠两种方式：
方式1
方式2

【S1 按键传感器子板】介绍
1/4【S1按键传感器子板】介绍
更新版本记录
版本 时间 修改内容
V1.0 2023-4-5文档创建2/4
子板正面
子板侧面
1.子板定位
S1子板是按键子板，采用HT16K33驱动芯片作为核心。该子板上共12个按
键，可设置独立按键，也可设置组合按键。
2.子板功能3/4
功能框图
S1按键传感器子板主要功能是完成按键的扫描处理，以及去抖动等处理功
能。按键子板最终以I2C接口的形式向计算板或者母板提供系统接口。计算子板
或者母板上的其他子板可以通过I2C接口，读取按键值。
S1子板可与其他子板搭配实现场景应用。输入电压5V，通过
SPX3819M5-L-3-3/TR稳压芯片转换为3.3V。
该子板驱动芯片为HT16K33，HT16K33是一款存储器映射和多功能LED控制
驱动芯片。该芯片支持最大128点的显示模式(16SEGs×8COMs)以及最大13×3
的按键矩阵扫描电路。
3.子板参数
•子板名称：按键
•子板类别：传感器子板
•子板型号：S1
•子板尺寸：107×77×26.3mm
•主处理器：
驱动芯片HT16K33
•接口说明：
1电源指示灯——1个（RED）4/42按键——12个（3×4组合按键一组）
3拨码开关——2位（地址选择开关）
4.使用方法说明
•连接方式。向下通过pogopin（24pin）里面的I2C连接母板，进而连接计
算子板。详细接口对应关系见S1子板原理图。
•拨码开关。该板子有两位拨码开关，默认在OFF状态；拨码开关对应I2c地
址的最后两位。详细控制请参数demo说明及例程。
•堆叠方式。支持平铺堆叠和垂直堆叠两种方式：
方式1
方式2

【S7 人体红外传感器子板】介绍
1/4【S7人体红外传感器子板】介绍
更新版本记录
版本 时间 修改内容
V1.0 2023-4-5文档创建2/4
子板正面
子板侧面
1.子板定位
S7子板是人体红外传感器子板，采用PCA9557驱动芯片作为核心。
受到红外辐射而温度升高时，表面电荷将减少，相当于释放了一部分电荷，
将释放电荷经放大器转为电压输出。应用于防盗报警、来客告知及非接触开关等
红外领域。
2.子板功能3/4
功能框图
输入电压5V，经SPX3819M5-L-3-3/TR稳压芯片转换为3.3V，PCA9557芯片
拓展IO口来连接热释电传感器BS412。PCA9557芯片I2C总线地址的高4位保留，
设为0011b，从机地址剩余的3位（ADR_0，ADR_1，ADR_2），其中ADR_0、ADR_1
由拨码开关控制，7位地址为0x18,0x19,0x1A,0x1B（由拨码开关配置），默认DIP
开关都拨在上方时，地址为0x18。
3.子板参数
•子板名称：人体红外传感器
•子板类别：传感器子板
•子板型号：S7
•子板尺寸：107×77×43mm
•主处理器：
驱动芯片PCA9557
热释电传感器BS412
•接口说明：
1电源指示灯——1个（RED）
2人体感应——1个（GREEN）
3拨码开关——2位（地址选择开关）4/44人体红外传感器——1个
4.使用方法说明
•连接方式。向下通过pogopin（24pin）里面的I2C连接母板，进而连接计
算子板。详细接口对应关系见S7子板原理图。
•拨码开关。该板子有2位拨码开关，默认在OFF状态；拨码开关对应I2c地
址的最后两位。详细控制请参数demo说明及例程。
•堆叠方式。支持平铺堆叠和垂直堆叠两种方式：
方式1
方式2

【E5 触摸屏显示器子板】介绍
1/4【E5触摸屏显示器子板】介绍
更新版本记录
版本 时间 修改内容
V1.0 2023-4-1文档创建2/4
子板正面
1.子板定位
E5子板是触摸屏显示器子板，采用GD32F330F6P6和LP3302B6FLED驱动芯
片作为核心。
2.子板功能
功能框图3/4E5子板是触摸屏显示器子板，输入电压5V，通过LED驱动芯片TPS61040DBVR
转换为16V、10.4V和7V等多个电压值。
GD32F330F6P6是基于ArmCortex-M4处理器的32位通用微控制器。Arm
Cortex-M4处理器包括三条AHB总线分别称为I-CODE总线、D-Code总线和系统
总线。Cortex-M4处理器的所有存储访问，根据不同的目的和目标存储空间，都
会在这三条总线上执行。存储器的组织采用了哈佛结构，预先定义的存储器映射
和高达4GB的存储空间，充分保证了系统的灵活性和可扩展性。
LP3302B6F是一款高频异步升压转换器，适用于恒流白光LED驱动器应用。
内部MOSFET可以支持多达8个白色LED用于背光和OLED电源应用，内部软启动
功能可以减少浪涌电流。LP3302B6F采用恒定频率1MHzPWM控制方案。
3.子板参数
•子板名称：触摸屏显示器
•子板类别：执行器子板
•子板型号：E5
•子板尺寸：195×129.6×26.9mm
•主处理器：
MCUGD32F330F6P6
LED驱动LP3302B6F
•接口说明：
1HDMI接口——1个
2FPC接口——1个
•其他信息：
①分辨率：800x480
②宽高比：16:9
③显示颜色：262K
4.使用方法说明4/4•连接方式。计算子板(U2或U3)可通过FPC接口，控制LP3302B6F芯片来实
现LCD显示，通过I2C控制TP芯片实现触摸控制。
详细控制请参数demo说明及例程。

【C1 小米WiFi-BLE通信子板】介绍
1/4【C1小米WiFi-BLE通信子板】介绍
更新版本记录
版本 时间 修改内容
V1.0 2023-4-8文档创建2/4
子板正面
子板侧面
1.子板定位
C1子板是小米WiFi-BLE通信子板，采用WIFI模组ESP-WROOM-32D作为核心。
2.子板功能3/4
功能框图
C1子板是小米WiFi-BLE通信子板，板载小米WiFi-BLE双模摸组，可以连接
米家APP进行远程控制，详见小米IOT开发者平台。C1子板可通过拨码开关选择
模式，当拨码开关向上时，SW=OFF，此时为通信模式；当拨码开关向下时，SW=ON，
此时为供电模式。
其他信息
•支持2.4G网络
•支持接入米家APP
3.子板参数
•子板名称：小米WiFi-BLE
•子板类别：通信子板
•子板型号：C1
•子板尺寸：107×77×26.3mm
•主处理器：
WIFI模组ESP-WROOM-32D
•接口说明：
1电源指示灯——1个（RED）
2复位按键——1个4/43拨码开关——1位
4串口波特率复位按键——1个
4.使用方法说明
•连接方式。向下通过pogopin（24pin）里面的UART1连接母板，进而连接
计算子板，向上提供pogopin（24pin）供其他子板与计算子板通讯。详细接口对
应关系见C1子板原理图。
微处理器通过UART1接口，控制EESP-WROOM-32DWIFI模组实现WiFi与蓝牙
功能。详细控制请参数demo说明及例程。
•拨码开关。该板子有1位拨码开关，通过拨码开关可以选择工作模式，当拨
码开关向上时，SW=OFF，此时为通信模式，当拨码开关向下时，SW=ON，此时为供
电模式。详细控制请参数demo说明及例程。
•堆叠方式。支持平铺堆叠和垂直堆叠两种方式：
方式1
方式2

【S5 NFC传感器子板】介绍
1/4【S5NFC传感器子板】介绍
更新版本记录
版本 时间 修改内容
V1.0 2023-4-5文档创建2/4
子板正面
子板侧面
1.子板定位
S5子板是NFC传感器子板，采用MS523射频卡芯片作为核心。
2.子板功能
3/4功能框图
近场通信，也叫近距离无线通信，使用了NFC技术的设备可以在彼此靠近的
情况下进行数据交换，是由非接触式射频识别及互连互通技术整合演变而来，通
过在单一芯片上集成感应式读卡器、感应式卡片和点对点通信的功能。
S5子板使用了NFC技术的设备可以在彼此靠近的情况下进行数据交换，通
过在MS523芯片上集成感应式读卡器实现点对点通信的功能。输入电压5V，经
SPX3819M5-L-3-3/TR稳压芯片转换为3.3V，MS523芯片驱动NFC线圈实现感应
式读卡器功能。MS523芯片I2C总线地址的高4位保留，设为0101b，从机地址
剩余的3位（ADR_0，ADR_1，ADR_2），其中ADR_0、ADR_1由拨码开关控制，7
位地址为0x28,0x29,0x2A,0x2B（由拨码开关配置），默认DIP开关都拨在上方
时，地址为0x28。
3.子板参数
•子板名称：NFC
•子板类别：传感器子板
•子板型号：S5
•子板尺寸：107×77×26.3mm
•主处理器：
NFCMS523
•接口说明：
1电源指示灯——1个（RED）
2拨码开关——2位（地址选择开关）
3NFC线圈——1组
4.使用方法说明
•连接方式。向下通过pogopin（24pin）里面的I2C连接母板，进而连接计
算子板。详细接口对应关系见S5子板原理图。
•拨码开关。该板子有2位拨码开关，默认在OFF状态；拨码开关对应I2c地
址的最后两位。详细控制请参数demo说明及例程。4/4•堆叠方式。支持平铺堆叠和垂直堆叠两种方式：
方式1
方式2

【E4 Speaker执行器子板】介绍
1/4【E4Speaker执行器子板】介绍
更新版本记录
版本 时间 修改内容
V1.0 2023-4-5文档创建2/4
子板正面
子板侧面
1.子板定位
E4子板是Speaker执行器子板，作为扬声器，可播放声音，采用功放芯片
NS4160作为核心。向下通过pogopin（4pin）连接计算子板，向上提供pogopin
（24pin）供其他子板与计算子板通讯。
2.子板功能3/4
功能框图
NS4160是一款带AB类/D类工作模式切换功能、超低EMI、无需滤波器、5W
单声道音频功放。通过一个控制管脚使芯片在AB类或者D类工作模式之间切换，
以匹配不同的应用环境。NS4160内置过流保护、过热保护及欠压保护功能，有效
地保护芯片在异常工作状况下不被损坏。
3.子板参数
•子板名称：Speaker
•子板类别：执行器子板
•子板型号：E4
•子板尺寸：107×77×26.3mm
•主处理器：
功放芯片NS4160
喇叭规格8Ω2W
•接口说明：
1电源指示灯——1个（RED）
2喇叭——1路
3模拟音频输入接口——1路4/44.使用方法说明
•连接方式。通过pogopin(24pin)接口，与母板的电源及通信接口相连接，
详细接口对应关系见E4子板原理图。
•堆叠方式。支持垂直堆叠一种方式：
方式1

【S6 超声波传感器子板】介绍
1/4【S6超声波传感器子板】介绍
更新版本记录
版本 时间 修改内容
V1.0 2023-4-5文档创建2/4
子板正面
子板侧面
1.子板定位
S6子板是超声波传感器子板，采用GD32F330F6P6TR芯片作为核心。
超声波传感器是将超声波信号转换成其它能量信号（通常是电信号）的传感
器。
2.子板功能
3/4功能框图
超声波距离传感器可以广泛应用在物位（液位）监测，机器人防撞，探测液
位、探测透明物体和材料，控制张力以及测量距离等。GD32F330F6P6TR芯片I2C
地址可由拨码开关配置，默认DIP开关都拨在上方时，地址为0x58。
3.子板参数
•子板名称：超声波传感器
•子板类别：传感器子板
•子板型号：S6
•子板尺寸：107×77×45.3mm
•主处理器：
MCUGD32F330F6P6
超声波模组EU1640BCH12T/R
•接口说明：
1电源指示灯——1个（RED）
2拨码开关——2位（地址选择开关）
3超声探头——1个
4.使用方法说明
•连接方式。向下通过pogopin（24pin）里面的I2C连接母板，进而连接计
算子板。详细接口对应关系见S6子板原理图。
•拨码开关。该板子有2位拨码开关，默认在OFF状态；拨码开关对应I2C地
址的最后两位。详细控制请参数demo说明及例程。
•堆叠方式。支持平铺堆叠和垂直堆叠两种方式：4/4
方式1
方式2

【E3 窗帘机执行器子板】介绍
1/4【E3窗帘机执行器子板】介绍
更新版本记录
版本 时间 修改内容
V1.0 2023-4-1文档创建2/4
子板正面
子板侧面
1.子板定位
E3子板是窗帘机执行器子板，可以通过GD32F330F6P6TR控制HR8833MTE驱
动芯片来控制电机的转动。
2.子板功能
3/4功能框图
E3子板是窗帘机执行器子板，电机控制左右移动，也可停止在任意位置，可
用于模拟窗帘、卷帘门、旗杆等类似产品。输入电压5V，通过SPX3819M5-L-3-3/TR
稳压芯片转换为3.3V。
HR8833MTE芯片驱动窗帘电机。HR8833MTE芯片地址为0x38，0x3A，0x3C，
0x3E（由拨码开关配置），默认DIP开关都拨在上方时，地址为0x38。
3.子板参数
•子板名称：窗帘机
•子板类别：执行器子板
•子板型号：E3
•子板尺寸：107×77×26.3mm
•主处理器：
驱动芯片HR8833MTE
MCUGD32F330F8P6
•通信接口：
1电源指示灯——1个（RED）
2拨码开关——2位（地址选择开关）
3窗帘机——1个
4.使用方法说明
•连接方式。通过pogopin接口，与母板的电源及通信接口相连接，详细接口
对应关系见E3子板原理图。
•拔码开关。该板子有两位拨码开关，默认在OFF状态。拨码开关对应I2c地
址的最后两位。
•堆叠方式。支持平铺堆叠和垂直堆叠两种方式：4/4
方式1
方式2

【M1 母板】介绍
1/4【M1母板】介绍
更新版本记录
版本 时间 修改内容
V1.0 2023-4-8文档创建2/4
母板正面
1.母板定位
母板是实训箱系统的基础，用于连接外部电源、提供硬件接口、连接子板。
2.母板功能
3/4功能框图
•供电：使用55W电源进行供电。
•开关机：长按开关键，待绿色指示灯亮起后松手，母板上电完成开机；长按
开关键，待绿色指示灯熄灭后松手，母板断电完成关机。
•错误警报：任意槽位电流超过2A时会触发告警机制，红色错误指示灯亮，
母板自动切断该槽位电源。
•USB1、USB2接口：连接外部USB设备，如鼠标、键盘、U盘等。
•MicroUSB接口：U1子板、C1子板、C2子板程序下载调试用此接口。
•Type-B接口：预留的接口。
•以太网口：预留的接口。
3.母板参数
•母板尺寸：285×326×36mm
•母板重量：2kg
4.使用方法说明
•连接方式。在0-5槽位提供pogopin(24pin)接口，并在0-1号槽位额外提
供pogopin(12pin)接口。
•堆叠方式。支持平铺堆叠和垂直堆叠两种方式：
方式14/4
方式2

【S2 照度+六轴+温湿度传感器子板】介绍
1/5【S2照度+六轴+温湿度传感器子板】介绍
更新版本记录
版本 时间 修改内容
V1.0 2023-4-3创建版本2/5
子板正面
子板侧面
1.子板定位
S2子板是照度+六轴+温湿度子板，照度传感器以光电效应为基础，将光信号
转换成电信号，可以检测光照强度；温湿度传感器以温湿度一体式的探头作为测
温元件，将温度和湿度信号采集出来；六轴传感器用于测量角度速度和角度的传
感器。根据测量的角度,加速度传感器可以知道物体三轴的位移,从而用于导航和
测量物体的轨迹，也可以检测运动状态。
2.子板功能3/5
功能框图
输入电压5V，通过SPX3819M5-L-3-3/TR稳压芯片转换为3.3V。
BH1750FVI-TR是一款用于I2C总线接口的16位串行输出型数字环境光传感
器IC。该芯片最适合用于获取环境光数据，以调整移动电话的LCD和键盘背光功
率。它可以在高分辨率下进行大范围的检测。
ICM-20608是InvenSense出品的一款6轴MEMS传感器，包括3轴加速度和
3轴陀螺仪，尺寸非常小，只有3x3x0.75mm，采用16P的LGA封装。
SHT35建立在全新和优化的CMOSens®芯片之上，进一步提高了产品可靠性和
精度规格。SHT35提供了一系列新功能，如增强信号处理、可编程温湿度极限的
报警模式。
3.子板参数
•子板名称：照度+六轴+温湿度
•子板类别：传感器子板
•子板型号：S24/5•子板尺寸：107×77×26.3mm
•主处理器：
照度传感器BH1750FVI-TR
六轴传感器ICM-20608
温湿度传感器SHT35-DIS-B
•接口说明：
1电源指示灯——1个（RED）
2拨码开关——1位（地址选择开关）
3温湿度传感器——1个
4光照传感器——1个
5六轴传感器——1个
4.使用方法说明
•连接方式。通过pogopin接口，与母板的电源及通信接口相连接，详细接口
对应关系见S2子板原理图。
•拨码开关。该板子有1位拨码开关，默认在OFF状态，拨码开关对应i2c地
址的最后一位。详细控制请参数demo说明及例程。
•堆叠方式。支持平铺堆叠和垂直堆叠两种方式。
