import {PluginSetupContext} from '@comate/plugin-host';
// import {HardwareQaSkillProvider} from './providers/hardwareQa.js';
import {ChatProvider} from './providers/chat.js';
import {CodeGenerateSkillProvider} from './providers/codeGenerate.js';
import {HelpFallbackProvider} from './providers/fallback.js';

export function setup({registry}: PluginSetupContext) {
    // registry.registerSkillProvider('hardwareQa', HardwareQaSkillProvider);
    registry.registerSkillProvider('chat', ChatProvider);
    registry.registerSkillProvider('codeGenerate', CodeGenerateSkillProvider);
    registry.registerFallbackProvider('hello', HelpF<PERSON>backProvider, true);
}
