import {marked, Tokens} from 'marked';

export const getMarkdownCodeBlock = (markdown: string) => {
    const tokens = marked.lexer(markdown);
    const codeBlock = tokens.find((token): token is Tokens.Code => token.type === 'code');
    if (codeBlock) {
        return codeBlock.text;
    }
    return '';
};

export const BASE_URL = 'https://comate.baidu.com/api/v2/api';

export const LIB_DOWNLOAD_LOCATION =
    'https://vercel-static.bj.bcebos.com/stash/v1/dc8c989/frontend/92932cd/xiaomi/aiot/Library.zip';

const codeGeneratePromptTemplateUrl =
    'https://vercel-static.bj.bcebos.com/stash/v1/dc8c989/frontend/92932cd/xiaomi/codeGenerate.prompt';

const codeGenerateQueryPromptTemplateUrl =
    'https://vercel-static.bj.bcebos.com/stash/v1/dc8c989/frontend/92932cd/xiaomi/codeGenerateQuery.prompt';

export async function getCodeGeneratePromptTemplate() {
    const result = await fetch(codeGeneratePromptTemplateUrl);
    const codeGeneratePromptTemplate = await result.text();
    return codeGeneratePromptTemplate;
}

export async function getCodeGenerateQueryPromptTemplate() {
    const result = await fetch(codeGenerateQueryPromptTemplateUrl);
    const codeGenerateQueryPromptTemplate = await result.text();
    return codeGenerateQueryPromptTemplate;
}

const codeGenerateDemoPromptTemplateUrl =
    'https://vercel-static.bj.bcebos.com/stash/v1/dc8c989/frontend/92932cd/xiaomi/codeGenerateDemo.prompt';

export async function getCodeGenerateDemoPromptTemplate() {
    const result = await fetch(codeGenerateDemoPromptTemplateUrl);
    const codeGenerateDemoPromptTemplate = await result.text();
    return codeGenerateDemoPromptTemplate;
}

const chatPromptTemplateUrl =
    'https://vercel-static.bj.bcebos.com/stash/v1/dc8c989/frontend/92932cd/xiaomi/chat.prompt';

export async function getChatPromptTemplate() {
    try {
        const response = await fetch(chatPromptTemplateUrl);
        const result = await response.text();
        return result;
    }
    catch (e) {
        console.error(e);
        return '你是小米AIoT助手，擅长回答相关问题';
    }
}
