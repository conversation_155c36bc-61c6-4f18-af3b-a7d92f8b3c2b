import {
    SkillProvider,
    FunctionParameterDefinition,
    TaskProgressChunk,
    StringChunkStream,
    TextModel,
} from '@comate/plugin-host';
import {getChatPromptTemplate} from './utils.js';
import chatWithKnowledgeAndCode from '../prompts/chatWithKnowledgeAndCode.prompt';
import chatWithKnowledgeOnly from '../prompts/chatWithKnowledgeOnly.prompt';
import chatWithCodeOnly from '../prompts/chatWithCodeOnly.prompt';
import chatWithoutKnowledgeAndCode from '../prompts/chatWithoutKnowledgeAndCode.prompt';

export class ChatProvider extends SkillProvider {
    static skillName = 'softwareQa';

    static displayName = '软件相关智能问答';

    static description = '智能解答关于软件相关的相关问题';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const {query, selectedCode} = this.currentContext;
        const stream = new StringChunkStream();

        yield stream.flush('正在思考中，请耐心等候...');

        const knowledge = await this.retriever.knowledgeFromQuery(
            query,
            {
                knowledgeSets: [
                    {uuid: 'f206ee1b-384b-4923-abe7-c28f20297922', type: 'NORMAL'},
                ],
            }
        );

        const hasKnowledge = knowledge.length > 0;
        const hasSelectedCode = selectedCode.trim() !== '';

        let template;

        if (hasKnowledge && hasSelectedCode) {
            template = chatWithKnowledgeAndCode;
        }
        else if (hasKnowledge && !hasSelectedCode) {
            template = chatWithKnowledgeOnly;
        }
        else if (!hasKnowledge && hasSelectedCode) {
            template = chatWithCodeOnly;
        }
        else {
            template = chatWithoutKnowledgeAndCode;
        }

        const chatPromptTemplate = await getChatPromptTemplate();

        const prompt = this.llm.createPrompt(
            chatPromptTemplate + template,
            {
                knowledge: hasKnowledge ? knowledge : '',
                selectedCode: hasSelectedCode ? selectedCode : '',
                query,
            }
        );

        const chunks = this.llm.askForTextStreaming(prompt, {
            model: TextModel.ErnieBot4Turbo128,
            modelOptions: {
                enableMultiturnDialogue: {byPlugin: true},
            },
        });

        yield stream.flushReplaceLast('');

        for await (const chunk of chunks) {
            yield stream.flush(chunk);
        }
    }
}
