import {FallbackProvider, TaskProgressChunk, StringChunkStream} from '@comate/plugin-host';

export class HelpFallbackProvider extends FallbackProvider {
    static description = '介绍插件能力与开发方式';

    async *handleQuery(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new StringChunkStream();
        yield stream.flush('你好，我是小米aiot插件。\n\n');
        yield stream.flush('能看到这些文字，你已经成功地运行了插件。可以输入 / 选择具体功能使用插件。\n\n');
        yield stream.flush('当前支持：代码生成功能 \n\n 即将支持：硬件相关智能问答、软件相关智能问答，敬请期待');
    }
}
