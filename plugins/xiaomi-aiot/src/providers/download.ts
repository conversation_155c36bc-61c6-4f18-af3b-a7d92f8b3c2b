import fs from 'fs';
import {mkdir} from 'fs/promises';
import https from 'https';
import {join} from 'path';

export function download(url: string, destinationFolder: string, filename?: string): Promise<string> {
    return new Promise(async (resolve, reject) => {
        try {
            await mkdir(destinationFolder, {recursive: true});
        }
        catch (e) {
            reject(e);
            return;
        }
        const resolvedFilename = filename || url.split('/').pop() || `download_${Date.now()}`;
        const destination = join(destinationFolder, resolvedFilename);
        const file = fs.createWriteStream(destination);

        const request = https.get(url, response => {
            if (response.statusCode !== 200) {
                reject(new Error('Server responded with status code ' + response.statusCode));
            }
            response.pipe(file);
        });

        file.on('finish', () => {
            file.close();
            resolve(destination);
        });

        file.on('error', err => {
            fs.unlink(destination, () => reject(err));
        });

        request.on('error', err => {
            fs.unlink(destination, () => reject(err));
        });
    });
}
