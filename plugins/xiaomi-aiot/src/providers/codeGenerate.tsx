import {
    SkillProvider,
    FunctionParameterDefinition,
    TaskProgressChunk,
    ElementChunkStream,
    TextModel,
    ProviderInit,
} from '@comate/plugin-host';
import {AcceptWithDependentFiles, CopyFile, formatPrompt} from '@comate/plugin-shared-internals';
// import codeGeneratePromptTemplate from '../prompts/codeGenerate.prompt';
// import codeGenerateDemoPromptTemplate from '../prompts/codeGenerateDemo.prompt';
import codeGenerateQueryPromptTemplate from '../prompts/query.prompt';
import {download} from './download.js';
import {tmpdir} from 'os';
import path from 'path';
import AdamZip from 'adm-zip';
import {BASE_URL, getCodeGenerateDemoPromptTemplate, getCodeGeneratePromptTemplate, getMarkdownCodeBlock, LIB_DOWNLOAD_LOCATION} from './utils.js';
import {writeFile} from 'fs/promises';
import {authorizedFirstPartyFetch} from '@comate/plugin-host';
// import {dependencies, mainDotC} from './const.js';

export const PLATFORM_REGEX = /\b(u1)\b/i;

export const BOARD_REGEX = /\b(e1|e2|e3|e4|s1|s2|s3|s4|s5|s6|s7|s8|s9|s10|s11|c2|c3|u1)\b/i;

export const XIAOMI_TMPDIR = path.join(tmpdir(), 'comate-engine', 'xiaomi-aiot');

const MAX_CHARS = 516096;

interface GetReferenceCodeParams {
    query: string;
    platform: string;
    board: string;
    topK: number;
    queryRewrite: boolean;
    score: number;
}

interface Response<T> {
    code: number;
    msg: string;
    data: T;
}

interface FileContent {
    content: string;
    filepath: string;
    type: 'MI_AIOT_APP' | 'MI_AIOT_BSP' | 'MI_AIOT_BOARD_INFO';
}

export interface GetReferenceCodeResult {
    /** 给模型参考的文件 */
    referenceFiles: FileContent[];
    /** 需要采纳的代码 */
    includedFiles: FileContent[];
}

// 用于缓存历史意图
let inference = {
    platform: '',
    board: ''
};

export class CodeGenerateSkillProvider extends SkillProvider {
    static skillName = 'codeGenerate';

    static displayName = '代码生成';

    static description = '代码生成';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    private readonly cwd: string;

    constructor(init: ProviderInit) {
        super(init);
        this.cwd = init.cwd;
    }

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new ElementChunkStream();
        const {query} = this.currentContext;

        if (query.trim().length <= 0) {
            yield stream.flushReplaceLast(
                <p>输入不能为空，请明确“核心”和“子板”信息以及描述您的需求。</p>
            );
            return;
        }

        let [, platform] = PLATFORM_REGEX.exec(query) ?? [];
        let [, board] = BOARD_REGEX.exec(query.replace(platform, '')) ?? [];

        console.log('Platform: ', platform, 'Board:', board);

        if (!platform || !board) {
            const queryPrompt = this.llm.createPrompt(
                codeGenerateQueryPromptTemplate,
                {
                    query
                }
            );

            console.log('意图识别 queryPrompt', queryPrompt);

            const result = await this.llm.askForText(queryPrompt, {
                model: TextModel.ErnieBot4Turbo128,
            });

            console.log('意图识别 result', result);
            const jsonMatch = result.match(/```json\s*([\s\S]*?)\s*```/);
            if (jsonMatch && jsonMatch[1]) {
                try {
                    const jsonObject = JSON.parse(jsonMatch[1]);
                    platform = jsonObject.platform;
                    board = jsonObject.boards[0];
                } catch (error) {
                    console.error("Invalid JSON", error);
                }
            }

            console.log('jsonMatch', jsonMatch);
        }

        if (!platform || !board) {
            // 这次没有识别出来的情况，尝试获取历史意图
            platform = inference.platform || platform;
            board = inference.board || board;

            console.log('使用历史意图 inference', inference);
            if (!platform || !board) {
                yield stream.flushReplaceLast(
                    <p>缺少平台或子板信息，无法生成代码。</p>
                );
                return;
            }
        } else {
            // 有意图，更新历史意图
            inference.platform = platform;
            inference.board = board;
        }

        console.log(`final Platform: ${platform}, Board: ${board}`);

        const normalizedPlatform = platform.toLowerCase();
        const normalizedBoard = board.toUpperCase();

        yield stream.flush(
            <loading>正在检索示例代码...</loading>
        );

        let references: GetReferenceCodeResult | undefined = undefined;

        try {
            const params: GetReferenceCodeParams = {
                query,
                platform: normalizedPlatform,
                board: normalizedBoard,
                topK: 10,
                queryRewrite: true,
                score: 3,
            };

            console.log('readForMi params', params);
            const res = await authorizedFirstPartyFetch(`${BASE_URL}/aidevops/knowledge/rest/v1/readForMi`, {
                method: 'POST',
                body: JSON.stringify(params),
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            const resObj = await res.json() as Response<GetReferenceCodeResult>;
            if (resObj.code !== 200) {
                throw new Error(resObj.msg);
            }
            references = resObj.data;
        } catch (e) {
            yield stream.flushReplaceLast(
                <p>{(e as Error).message}</p>
            );
        }

        if (!references || references.referenceFiles.length <= 0) {
            yield stream.flushReplaceLast(
                <p>没有找到参考代码，无法生成。</p>
            );
            return;
        }

        yield stream.flushReplaceLast(
            <loading>已获取示例代码，正在按需求生成...</loading>
        );

        const codeGenerateDemoPromptTemplate = await getCodeGenerateDemoPromptTemplate();

        const applicationDemo = references.referenceFiles.filter((item) => item.type === 'MI_AIOT_APP');
        const applicationDemoPrompt = applicationDemo.map((demo) =>
            formatPrompt(codeGenerateDemoPromptTemplate, {filename: demo.filepath, code: demo.content})
        );

        const boardInfoDemo = references.referenceFiles.filter((item) => item.type === 'MI_AIOT_BOARD_INFO');
        const boardInfoDemoPrompt = boardInfoDemo.map((demo) =>
            formatPrompt(codeGenerateDemoPromptTemplate, {filename: demo.filepath, code: demo.content})
        );

        const libraryDemo = references.includedFiles.filter((item) => item.type === 'MI_AIOT_BSP');
        const libraryDemoPrompt = libraryDemo.map((demo) =>
            formatPrompt(codeGenerateDemoPromptTemplate, {filename: demo.filepath, code: demo.content})
        );

        const prompt = await this.getCodeGeneratePrompt({applicationDemoPrompt, libraryDemoPrompt, boardInfoDemoPrompt, query});

        let result = '';
        const chunks = this.llm.askForTextStreaming(prompt, {
            model: TextModel.ErnieBot4Turbo128,
            modelOptions: {
                enableMultiturnDialogue: {byPlugin: true},
            }
        });

        for await (const chunk of chunks) {
            result += chunk;
            yield stream.flushReplaceLast(<markdown>{result}</markdown>);
        }

        // 获取代码块
        const mainDotC = getMarkdownCodeBlock(result);

        if (!mainDotC) {
            return;
        }

        // 获取依赖文件
        const libs = await this.getStandardLibrary();
        const dependentFiles = await this.getDirectlyDependentFiles(references);
        const dependencies: CopyFile[] = [...libs, ...dependentFiles].map(fromPath => {
            const basename = path.basename(fromPath);
            const toPath = path.join(this.cwd, basename);
            return {
                fromPath,
                toPath,
            }
        });

        // 用于依赖文件和生成代码的全部采纳
        const acceptContent: AcceptWithDependentFiles = {
            method: 'acceptWithDependentFiles',
            entry: {
                filePath: path.join(this.cwd, 'main.c'),
                content: mainDotC,
            },
            dependencies,
        };

        const actionData = JSON.stringify(acceptContent);
        yield stream.flush(
            <blockquote>
                <flex centered gap="s">
                    <command-button
                        action="acceptWithDependentFiles"
                        actionData={actionData}
                        tooltipText='写入以上文件到当前目录'
                        variant="primary"
                        // commandName 和 data 类型要求必须要，但实际没用到
                        commandName="xiaomi-aiot.command.noop"
                        data={null}
                    >
                        全部采纳
                    </command-button>
                </flex>
                <p>采纳后，上方生成的代码将写入 main.c，并一同采纳以下内容。</p>
                <p>你也可以分别采纳以下内容：</p>
            </blockquote>
        );

        yield stream.flush(
            <table>
                <thead>
                    <tr>
                        <th>文件/目录名</th>
                        <th><flex horizontalCentered>操作</flex></th>
                    </tr>
                </thead>
                <tbody>
                    {(() => {
                        const rows = [];
                        for (let i = 0; i < dependencies.length; i++) {
                            const item = dependencies[i];
                            const actionData = JSON.stringify({
                                dependencies: [item]
                            });
                            rows.push(
                                <tr>
                                    <td>
                                        <flex horizontalCentered>
                                            {path.basename(item.fromPath)} (依赖项)
                                        </flex>
                                    </td>
                                    <td>
                                        <flex horizontalCentered>
                                            <command-button
                                                variant="text"
                                                action="acceptDependentFiles"
                                                actionData={actionData}
                                                // commandName 和 data 类型要求必须要，但实际没用到
                                                commandName="xiaomi-aiot.command.noop"
                                                data={null}
                                            >
                                                采纳
                                            </command-button>
                                        </flex>
                                    </td>
                                </tr>
                            );
                        }
                        return rows;
                    })()}
                </tbody>
            </table>
        );
    }

    // 获取标准库
    private async getStandardLibrary() {
        if (!this.cwd) {
            return [];
        }
        const destination = await download(LIB_DOWNLOAD_LOCATION, XIAOMI_TMPDIR);
        const zip = new AdamZip(destination);
        zip.extractAllTo(XIAOMI_TMPDIR);
        return [path.join(XIAOMI_TMPDIR, 'Library')];
    }

    private async getDirectlyDependentFiles(references: GetReferenceCodeResult) {
        if (!this.cwd) {
            return [];
        }
        const writeFilesPromises = references.includedFiles.map(async file => {
            const filePath = path.join(XIAOMI_TMPDIR, file.filepath);
            await writeFile(
                filePath,
                file.content
            );
            return filePath;
        });
        return Promise.all(writeFilesPromises);
    }


    // 裁剪prompt长度，防止超过限制
    private async getCodeGeneratePrompt({applicationDemoPrompt, libraryDemoPrompt, boardInfoDemoPrompt, query}: Record<string, any>) {
        const codeGeneratePromptTemplate = await getCodeGeneratePromptTemplate();
        let prompt = formatPrompt(codeGeneratePromptTemplate,
            {
                applicationDemo: applicationDemoPrompt.join('\n'),
                libraryDemo: libraryDemoPrompt.join('\n'),
                boardInfo: boardInfoDemoPrompt.join('\n'),
                query
            }
        );
        if (prompt.length > MAX_CHARS) {
            return this.llm.createPrompt(codeGeneratePromptTemplate, {
                applicationDemo: applicationDemoPrompt.join('\n'),
                libraryDemo: libraryDemoPrompt.join('\n'),
                boardInfo: '',
                query
            });;
        }
        return this.llm.createPrompt(codeGeneratePromptTemplate, {
            applicationDemo: applicationDemoPrompt.join('\n'),
            libraryDemo: libraryDemoPrompt.join('\n'),
            boardInfo: boardInfoDemoPrompt.join('\n'),
            query
        });
    }
}
