{"private": true, "name": "@comate-plugin/xiaomi-aiot", "version": "0.8.0", "type": "module", "scripts": {"clean": "rm -rf dist", "dev": "cpd dev", "build": "tsc && cpd build"}, "devDependencies": {"@comate/plugin-cli": "^0.9.2", "@comate/plugin-host": "^0.9.2", "@types/adm-zip": "^0.5.5", "typescript": "^5.3.2"}, "comate": {"name": "xiaomi-aiot", "version": "1.0.0", "icon": "./assets/icon.png", "entry": "./dist/index.js", "displayName": "小米AIoT盒子", "description": "支持代码生成和硬件相关问答、软件相关问答", "keyword": [], "capabilities": [{"type": "Skill", "name": "chat", "displayName": "智能问答", "description": "智能解答关于小米AIoT盒子相关的相关问题", "placeholder": "输入小米AIoT盒子相关的问题获得答案", "tipConfig": {"tip": "", "tipCodeBlock": {"type": "selected", "showTipWhileEmpty": false}}}, {"type": "Skill", "name": "codeGenerate", "displayName": "代码生成", "description": "代码生成", "placeholder": "输入问题为您生成代码"}, {"type": "Fallback", "name": "hello", "displayName": "插件介绍", "description": "确认插件成功运行并介绍开发方式"}], "configSchema": {"sections": []}}, "dependencies": {"@comate/plugin-shared-internals": "^0.9.2", "adm-zip": "^0.5.12", "marked": "^11.2.0"}}