{"private": true, "name": "@comate-plugin/tor", "version": "0.8.0", "type": "module", "scripts": {"clean": "rm -rf dist", "dev": "cpd dev", "build": "tsc && cpd build", "lint": "eslint --max-warnings=0 src --fix"}, "devDependencies": {"@comate/plugin-cli": "^0.9.2", "@comate/plugin-host": "^0.9.2", "typescript": "^5.3.2"}, "comate": {"name": "tor", "version": "1.0.0", "icon": "./assets/TorPlugin.png", "entry": "./dist/index.js", "displayName": "TOR助理", "description": "找知识库文档、代码变量起名等提效工具包", "keyword": [], "capabilities": [{"type": "Fallback", "name": "fallback", "displayName": "默认能力", "description": "未指定能力时，兜底的处理能力"}, {"type": "Skill", "name": "tor-help", "displayName": "插件介绍", "description": "插件介绍", "placeholder": "无需输入内容，回车获得插件介绍"}, {"type": "Skill", "name": "docSearch", "displayName": "找知识库文档", "description": "根据分享、浏览/编辑、关联会议等行为找知识库文档"}, {"type": "Skill", "name": "codeNameCreate", "displayName": "代码变量起名", "description": "给代码变量或类快速起名，支持驼峰、下划线格式"}, {"type": "Skill", "name": "timestampTool", "displayName": "时间戳工具", "description": "时间戳转换工具", "placeholder": "输入时间戳或格式化时间进行转换"}, {"type": "Skill", "name": "errorLogAnalysis", "displayName": "报错日志分析", "description": "分析编码环节错误日志的原因，并给出解决方案"}, {"type": "Skill", "name": "kuRecentScan", "displayName": "知识库浏览/收藏/星标数据", "description": "召回最近浏览/星标/关注/收藏的知识库文档，快速追溯需求或接口文档"}, {"type": "Skill", "name": "kuEnvDeploy", "displayName": "知识库环境部署", "description": "部署知识库业务模块的开发和测试环境"}, {"type": "Skill", "name": "perfTest", "displayName": "性能测试用例生成", "description": "支持通过复制cURL信息或自然语言描述，快速生成Locust性能测用例'"}, {"type": "Skill", "name": "monitorTest", "displayName": "监控测试用例生成", "description": "支持通过复制cURL信息或自然语言描述，快速生成监控测试用例'"}, {"type": "Skill", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "displayName": "个人常用地址管理", "description": "支持自定义常用地址，实现快速查询"}], "configSchema": {"sections": []}}}