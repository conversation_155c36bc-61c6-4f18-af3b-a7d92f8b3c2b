// ************ vip 地址
export const defaultAbilityUrl = 'http://************:8080/comate/defaultAbility';
export const codeNameCreateUrl = 'http://************:8080/comate/codeNameCreate';
export const errorLogAnalysisUrl = 'http://************:8080/comate/errorLogAnalysis';
export const kuRecentScanUrl = 'http://*************:8000/api/v1/ku/recentData';
export const personalCommonAddressUrl = 'http://************:8080/comate/common/address';

// 搜索文档
export const searchDocApi = 'http://*************:8400/comate_plus/search_doc';

// 知识库环境部署
export const getEnvUrl = 'http://************:8080/comate/kuEnvInfo';
export const deployEnvUrl = 'http://************:8080/api/ku/env_agile';

// 性能测试用例生成
export const perfTestUrl = 'http://************:8080/comate/perfTest';

// 搜索监控用例生成
export const monitorCaseUrl = 'http://*************:8400/comate/monitor/test';

// 时间戳工具
export const timestampUrl = 'http://************:8080/comate/timestampTool';
