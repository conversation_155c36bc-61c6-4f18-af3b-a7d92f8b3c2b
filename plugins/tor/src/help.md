您好，我是**TOR编码助理**，不仅具备一系列**通用能力**，如 **"智能找知识库文档"、"代码变量命名"** 以及 **"报错日志分析"** 等，还能结合业务需求实现定制化的辅助，更多精彩请详阅[用户手册](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/t7n0qKWNJW/AtKECXn-wv/04ZOPpuYluD7c9)，部分能力示例介绍如下：

### 🌟【通用能力展示】
#### 🔍 **找知识库文档**（仅支持「选择指令」）
- **功能亮点**： 支持查找1个月内特定人员分享、编辑、创建、浏览、评论、星标的文档，特定会议里的关联或者投屏的文档，特定主题相关的文档等。
- **示例查询**："找我编辑的文档"、"找[张三]写的关于[comate]的文档"、"找[李四]发给我的文档"
#### 🔍 **代码变量起名**
- **功能亮点**：无论是灵感闪现还是需求明确，我都能瞬间为您的代码变量或类赋予恰如其分的名字，支持驼峰式与下划线命名风格，让代码更加清晰易读。
- **示例查询**："给'{输入的变量中文名}'起个名字"、"给插件度量起个名字"
#### 🔍 **报错日志分析**
- **功能亮点**：面对错综复杂的错误日志，我能够迅速洞察其根源，并提供针对性的解决方案，助您快速排除故障，恢复编码流畅。
- **示例查询**："{你的错误日志片段}"
#### ⏰ **时间戳工具**
- **功能亮点**：获取当前时间戳、将时间戳转换为日期时间、将日期时间转换为时间戳。
- **示例查询**："获取当前时间戳"、"转换1730690427"、"转换2024-11-01 12:00:00"
### 🌈【业务特性能力探索】

#### 📚 **知识库浏览/收藏/星标数据**（仅支持「选择指令」）
- **功能亮点**：我能够根据您的需求，召回最近浏览/收藏/星标的知识库文档，快速追溯需求或接口文档，默认是召回最近浏览。
- **灵活查询**："浏览、关注、星标、收藏"

选择TOR编码助理，让您的研发之路更加顺畅无阻！
