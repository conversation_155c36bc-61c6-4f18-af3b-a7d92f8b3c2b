import {HelpSkillProvider} from './providers/help.js';
import {PluginSetupContext} from '@comate/plugin-host';
import {HelpFallbackProvider} from './providers/fallback.js';
import {CodeNameCreateProvider} from './providers/codeNameCreate.js';
import {ErrorLogAnalysisProvider} from './providers/errorLogAnalysis.js';
import {kuRecentScanProvider} from './providers/kuRecentScan.js';
import {DocSearchProvider} from './providers/docSearch.js';
import {kuEnvDeployProvider} from './providers/kuEnvDeploy.js';
import {TimestampToolProvider} from './providers/timestampTool.js';
import {perfTestProvider} from './providers/perfTest.js';
import {monitorTestProvider} from './providers/monitorTest.js';
import {PersonalCommonAddressProvider} from './providers/personalCommonAddress.js';

export function setup({registry}: PluginSetupContext) {
    registry.registerFallbackProvider('fallback', HelpFallbackProvider, true);
    registry.registerSkillProvider('tor-help', HelpSkillProvider);
    registry.registerSkillProvider('codeNameCreate', CodeNameCreateProvider);
    registry.registerSkillProvider('errorLogAnalysis', ErrorLogAnalysisProvider);
    registry.registerSkillProvider('kuRecentScan', kuRecentScanProvider);
    registry.registerSkillProvider('docSearch', DocSearchProvider);
    registry.registerSkillProvider('timestampTool', TimestampToolProvider);
    registry.registerSkillProvider('kuEnvDeploy', kuEnvDeployProvider);
    registry.registerSkillProvider('perfTest', perfTestProvider);
    registry.registerSkillProvider('monitorTest', monitorTestProvider);
    registry.registerSkillProvider('personalCommonAddress', PersonalCommonAddressProvider);
}
