import fs from 'node:fs';
import path from 'node:path';

export const configFolder = './.comate';
export const torPath = `${configFolder}/ku_env.json`;
export const ipipePath = `${configFolder}/ipipe_params.json`;

function checkFolderExistsSync(folderPath: string) {
    try {
        return fs.existsSync(folderPath) && fs.statSync(folderPath).isDirectory();
    }
    catch (err) {
        return false;
    }
}

function checkFileExistsSync(filePath: string) {
    try {
        return fs.existsSync(filePath) && fs.statSync(filePath).isFile();
    }
    catch (err) {
        return false;
    }
}

function initConfigFile(cwd: string, configJson: any, type: string, mkdir?: boolean) {
    let configPath = '';
    if (type === 'ipipe') {
        configPath = ipipePath;
    }
    else {
        configPath = torPath;
    }
    if (mkdir) {
        fs.mkdirSync(path.resolve(cwd, configFolder));
    }
    fs.writeFileSync(path.resolve(cwd, configPath), JSON.stringify(configJson, null, 2));
}

export function initDeployConfig(cwd: string, type: string, configJson: any) {
    const configPath = type === 'ipipe' ? ipipePath : torPath;
    const absolutePath = path.resolve(cwd, configPath);
    try {
        const isFileExisted = checkFileExistsSync(absolutePath);
        if (isFileExisted) {
            // 如果配置文件存在，刷新下文件内容
            const jsonData = JSON.stringify(configJson, null, 2);
            fs.writeFileSync(absolutePath, jsonData);

            return {type: 'success', path: configPath};
        }
        else {
            const isFolderExisted = checkFolderExistsSync(path.resolve(cwd, configFolder));
            if (!isFolderExisted) {
                initConfigFile(cwd, configJson, type, true);
                // const fileBuffer = fs.readFileSync(path.resolve(cwd, configPath));
                return {type: 'success', path: configPath};
            }

            const isFileExisted = checkFileExistsSync(path.resolve(cwd, configPath));
            if (!isFileExisted) {
                initConfigFile(cwd, configJson, type);
                // const fileBuffer = fs.readFileSync(path.resolve(cwd, configPath));
                return {type: 'success', path: configPath};
            }
        }
    }
    catch (ex: any) {
        throw new Error(`获取配置文件失败 ${ex instanceof Error ? ex.message : ex}`);
    }
    return {type: 'error', path: configPath};
}

export function getDeployConfig(cwd: string, type?: string) {
    let configPath = '';
    if (type === 'ipipe') {
        configPath = ipipePath;
    }
    else {
        configPath = torPath;
    }
    try {
        const fileBuffer = fs.readFileSync(path.resolve(cwd, configPath));
        return {type: 'file', value: fileBuffer};
    }
    catch (ex: any) {
        const Message = '获取最新配置文件异常！';
        return {type: 'message', content: Message};
    }
}

/**
 * 将代码块格式化为Markdown语法中的代码块
 *
 * @param codeLanguage 代码的语言类型，如javascript, typescript等
 * @param code 要格式化的代码内容
 * @returns 返回格式化后的Markdown代码块字符串
 */
export const echoCodeBlock = (codeLanguage: string, code: string) => {
    return `\n\`\`\`${codeLanguage}\n${code}\n\`\`\`\n\n`;
};
