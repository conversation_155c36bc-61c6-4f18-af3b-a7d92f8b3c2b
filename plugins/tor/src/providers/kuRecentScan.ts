import {
    SkillProvider,
    FunctionParameterDefinition,
    TaskProgressChunkStream,
    TaskProgressChunk,
} from '@comate/plugin-host';
import {fetchWithTimeout} from '../common/fetchWithTimeout.js';
import {kuRecentScanUrl} from '../config/config.js';

export class kuRecentScanProvider extends SkillProvider {
    static skillName = '知识库浏览/收藏/星标数据';
    static displayName = '知识库浏览/收藏/星标数据';
    static description = '召回最近浏览/星标/关注/收藏的知识库文档，快速追溯需求或接口文档';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new TaskProgressChunkStream();

        try {
            const userDetail = await this.currentUser.requestDetail();
            const realUserName = userDetail && userDetail.name || '未知用户';

            const raw = JSON.stringify({
                query: this.currentContext.query,
                name: realUserName,
            });

            const options = {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: raw,
            };
            const response = await fetchWithTimeout(kuRecentScanUrl, options);
            const result = await response.json();
            yield stream.flushReplaceLast(result.data.content); // markdown字符串
        }
        catch (error) {
            yield stream.flushReplaceLast(`请求出错，请联系负责人@qinqin或稍后再试。${error as string}`);
        }
    }
}
