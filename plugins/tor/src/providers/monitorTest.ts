import {
    SkillProvider,
    FunctionParameterDefinition,
    TaskProgressChunkStream,
    TaskProgressChunk,
} from '@comate/plugin-host';
import {fetchWithTimeout} from '../common/fetchWithTimeout.js';
import {monitorCaseUrl} from '../config/config.js';

export class monitorTestProvider extends SkillProvider {
    static skillName = '监控测试用例生成';
    static displayName = '监控测试用例生成';
    static description = '支持通过复制cURL信息或自然语言描述，快速生成监控测试用例';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new TaskProgressChunkStream();

        try {
            const myHeaders = new Headers();
            myHeaders.append('Content-Type', 'application/json');

            const userDetail = await this.currentUser.requestDetail();
            const realUserName = userDetail && userDetail.name || '未知用户';

            const raw = JSON.stringify({
                query: this.currentContext.query,
                user: realUserName,
            });

            const options = {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: raw,
            };
            const response = await fetchWithTimeout(monitorCaseUrl, options);
            const result = await response.json();
            yield stream.flushReplaceLast(result.data.result); // markdown字符串
        }
        catch (error) {
            yield stream.flushReplaceLast(`请求出错，请联系负责人@wangshunzhe或稍后再试。${error as string}`);
        }
    }
}
