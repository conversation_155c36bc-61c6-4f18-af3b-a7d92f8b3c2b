import {
    SkillProvider,
    FunctionParameterDefinition,
    TaskProgressChunkStream,
    TaskProgressChunk,
} from '@comate/plugin-host';
import {fetchWithTimeout} from '../common/fetchWithTimeout.js';
import {errorLogAnalysisUrl} from '../config/config.js';

export class ErrorLogAnalysisProvider extends SkillProvider {
    static skillName = '报错日志分析';
    static displayName = '报错日志分析';
    static description = '分析编码环节错误日志的原因，并给出解决方案';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new TaskProgressChunkStream();

        try {
            const myHeaders = new Headers();
            myHeaders.append('Content-Type', 'application/json');

            const userDetail = await this.currentUser.requestDetail();
            const realUserName = userDetail && userDetail.name || '未知用户';

            const raw = JSON.stringify({
                query: this.currentContext.query,
                user: realUserName,
            });

            const options = {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: raw,
            };
            const response = await fetchWithTimeout(errorLogAnalysisUrl, options);
            const result = await response.json();
            yield stream.flushReplaceLast(result.data.content); // markdown字符串
        }
        catch (error) {
            yield stream.flushReplaceLast(`请求出错，请联系负责人@rongsong或稍后再试。${error as string}`);
        }
    }
}
