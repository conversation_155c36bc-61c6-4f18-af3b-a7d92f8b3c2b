import {
    SkillProvider,
    FunctionParameterDefinition,
    TaskProgressChunkStream,
    TaskProgressChunk,
} from '@comate/plugin-host';
import {fetchWithTimeout} from '../common/fetchWithTimeout.js';
import {perfTestUrl} from '../config/config.js';

export class perfTestProvider extends SkillProvider {
    static skillName = '性能测试用例生成';
    static displayName = '性能测试用例生成';
    static description = '支持通过复制cURL信息或自然语言描述，快速生成Locust性能测用例';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new TaskProgressChunkStream();

        try {
            const myHeaders = new Headers();
            myHeaders.append('Content-Type', 'application/json');

            const userDetail = await this.currentUser.requestDetail();
            const realUserName = userDetail && userDetail.name || '未知用户';

            const raw = JSON.stringify({
                query: this.currentContext.query,
                user: realUserName,
            });

            const options = {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: raw,
            };
            const response = await fetchWithTimeout(perfTestUrl, options);
            const result = await response.json();
            yield stream.flushReplaceLast(result.data.content); // markdown字符串
        }
        catch (error) {
            yield stream.flushReplaceLast(`请求出错，请联系负责人@rongsong或稍后再试。${error as string}`);
        }
    }
}
