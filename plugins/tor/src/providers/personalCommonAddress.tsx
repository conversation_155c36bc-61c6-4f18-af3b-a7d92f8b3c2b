import {
    SkillProvider,
    FunctionParameterDefinition,
    TaskProgressChunk,
    ElementChunkStream,
} from '@comate/plugin-host';
import {fetchWithTimeout} from '../common/fetchWithTimeout.js';
import {personalCommonAddressUrl} from '../config/config.js';


export class PersonalCommonAddressProvider extends SkillProvider {
    static skillName = '个人常用地址管理';
    static displayName = '个人常用地址管理';
    static description = '支持自定义常用地址，实现快速查询';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new ElementChunkStream();

        try {
            const myHeaders = new Headers();
            myHeaders.append('Content-Type', 'application/json');
            const userDetail = await this.currentUser.requestDetail();
            const realUserName = userDetail && userDetail.name || '未知用户';
            const raw = JSON.stringify({
                query: this.currentContext.query,
                user: realUserName,
            });
            const options = {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: raw,
            };
            const response = await fetchWithTimeout(personalCommonAddressUrl, options);
            const result = await response.json();
            const aisuda = result.data.aisuda;
            const address_list = result.data.content;
            const length = address_list.length;
            if (length === 0){
                yield stream.flushReplaceLast(
                    <p>
                        <strong>你还没有设置常用地址哦~</strong><br/>
                    </p>
                );
            }else{
                yield stream.flush(<p><strong>你的常用地址如下：</strong></p>);
                for (let i = 1; i < length + 1; i++) {
                    const url = address_list[i-1].address;
                    const name = address_list[i-1].name;
                    yield stream.flush(
                        <blockquote>
                        <flex centered gap="sm">
                            {i}.<a href={url}>{name}</a>
                        </flex>
                        </blockquote>
                );}
            }
            yield stream.flush(
                <flex flexEndCentered>
                    <a href={aisuda}>↗去配置更多地址</a>
                </flex>
            );
        }
        catch (ex) {
            yield stream.flushReplaceLast(
                <p>请求出错，请联系负责人wangwei160或稍后再试。</p>
            );
        }
    }
}
