import {
    SkillProvider,
    FunctionParameterDefinition,
    ElementChunkStream,
    TaskProgressChunkStream,
    TaskProgressChunk,
    ProviderInit,
} from '@comate/plugin-host';
import {fetchWithTimeout} from '../common/fetchWithTimeout.js';
import {getEnvUrl} from '../config/config.js';
import {deployEnvUrl} from '../config/config.js';
import {initDeployConfig, getDeployConfig} from '../utils.js';

export class kuEnvDeployProvider extends SkillProvider {
    static skillName = '知识库环境部署';
    static displayName = '知识库环境部署';
    static description = '部署知识库业务模块的开发和测试环境';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    private readonly cwd: string;
    private request: string | undefined;

    constructor(init: ProviderInit) {
        super(init);
        this.cwd = init.cwd;
    }

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new ElementChunkStream();

        try {
            const myHeaders = new Headers();
            myHeaders.append('Content-Type', 'application/json');

            const userDetail = await this.currentUser.requestDetail();
            const realUserName = userDetail && userDetail.name || '未知用户';
            const queryInfo = this.currentContext.query;

            const raw = JSON.stringify({
                query: queryInfo,
                user: realUserName,
            });

            const options = {
                method: 'POST',
                headers: myHeaders,
                body: raw,
            };
            const response = await fetchWithTimeout(getEnvUrl, options);
            const result = await response.json();
            const code = result.code;
            const message = result.msg;
            if (code != 0) {
                yield stream.flushReplaceLast(<p>{message}</p>);
            }
            else {
                const envInfo = result.data.env_info;
                const env = envInfo.env;
                const module = envInfo.module;
                // 首先查找本地是否存在配置文件，如若存在，直接将查询到的数据覆盖文件；如若不存在，新建文件后写入查询到的数据
                try {
                    const data = initDeployConfig(this.cwd, 'tor', envInfo);
                    if (data?.type === 'success') {
                        const configPath = data.path;
                        console.log('configPath: ', configPath);
                        yield stream.flush(
                            <p>
                                已为你[更新]<strong>环境【rd/qa{env}】</strong>和<strong>
                                    模块【{module}】
                                </strong>的配置文件 <br />
                                请检查和确认配置文件是否需要修改，文件地址：
                                <file-link to={configPath} line={1}>
                                    ku_env.json
                                </file-link>{' '}
                                <br />
                                修改并保存后点击【确认】后即可开始部署流程，或点击【取消】来结束此次指令任务
                            </p>
                        );
                    }
                    // 用户点击确认后才开始部署，如果点击取消，则不部署
                    yield stream.flush(
                        <button-group>
                            <command-button
                                commandName="commitMessage:confirmIssue"
                                data="yes"
                                replyText="确认"
                                variant="primary"
                            >
                                确认
                            </command-button>
                            <command-button
                                commandName="commitMessage:confirmIssue"
                                data="no"
                                replyText="取消"
                            >
                                取消
                            </command-button>
                        </button-group>
                    );
                }
                catch (ex) {
                    yield stream.flushReplaceLast(<p>请求出错，请联系负责人@qinqin或稍后再试。${ex as string}</p>);
                    return;
                }
            }
        }
        catch (error) {
            yield stream.flushReplaceLast(<p>请求出错，请联系负责人@qinqin或稍后再试。${error as string}</p>);
        }
    }

    // 承接点击事件
    async *handleCommand(commandName: string, data: any): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new ElementChunkStream();

        if (commandName === 'commitMessage:confirmIssue') {
            if (data === 'yes') {
                // 部署环境
                let fileBuffer;
                let request;
                const data = getDeployConfig(this.cwd, 'tor');
                if (data?.type === 'file') {
                    fileBuffer = data.value;
                    request = fileBuffer?.toString();
                }
                const myHeaders = new Headers();
                myHeaders.append('Content-Type', 'application/json');

                const options = {
                    method: 'POST',
                    headers: myHeaders,
                    body: request,
                };
                const response = await fetchWithTimeout(deployEnvUrl, options);
                const result = await response.json();
                console.log('response: ', result.data.agile_url);
                if (result.code === '200') {
                    yield stream.flush(
                        <p>
                            <a href={result.data.agile_url}>已成功触发流水线环境部署，可点击查看～</a>
                        </p>
                    );
                }
                else {
                    yield stream.flushReplaceLast(<p>部署出错，请联系负责人@qinqin或稍后再试。{result.message}</p>);
                }
            }
            else {
                yield stream.flushReplaceLast(<p>您已取消此次环境部署～</p>);
            }
        }
    }
}
