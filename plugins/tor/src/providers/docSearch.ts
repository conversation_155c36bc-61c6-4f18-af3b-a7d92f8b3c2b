import {
    SkillProvider,
    FunctionParameterDefinition,
    TaskProgressChunkStream,
    TaskProgressChunk,
} from '@comate/plugin-host';
import {
    searchDocApi,
} from '../config/config.js';

export class DocSearchProvider extends SkillProvider {
    static skillName = '找知识库文档';
    static displayName = '找知识库文档';
    static description = '根据分享、浏览/编辑、关联会议等行为找知识库文档';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const userDetail = await this.currentUser.requestDetail();
        const userName = userDetail && userDetail.name || '未知用户';
        const query = this.currentContext.query;
        const stream = new TaskProgressChunkStream();

        if (!query) {
            yield stream.flushReplaceLast(
                '找文档功能支持查找1个月内特定人员分享、编辑、创建、浏览、评论、星标的文档，'
                    + '特定会议里的关联或者投屏的文档，特定主题相关的文档等。  \n'
                    + '示例："找我编辑的文档"、"找[张三]写的关于[comate]的文档"、"找[李四]发给我的文档"'
            );
            return;
        }

        const apiConfig = {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user: userName,
                query: query,
            }),
        };

        try {
            const response = await fetch(searchDocApi, apiConfig);
            const result = await response.json();
            this.logger.info(result);
            if (!result || !result.data || !result.data.content) {
                if (result.message) {
                    throw new Error(result.message);
                }
                else {
                    throw new Error('result.data is null or result.data.content is empty');
                }
            }
            yield stream.flushReplaceLast(result.data.content);
        }
        catch (error) {
            yield stream.flushReplaceLast(`请求出错，请联系负责人@baizhihao 或稍后再试。${error as string}`);
        }
    }
}
