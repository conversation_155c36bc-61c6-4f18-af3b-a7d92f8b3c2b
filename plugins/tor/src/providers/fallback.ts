import {FallbackProvider, TaskProgressChunk, TaskProgressChunkStream} from '@comate/plugin-host';
import {fetchWithTimeout} from '../common/fetchWithTimeout.js';
import {defaultAbilityUrl} from '../config/config.js';

export class HelpFallbackProvider extends FallbackProvider {
    static description = '默认能力';

    async *handleQuery(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new TaskProgressChunkStream();

        try {
            const myHeaders = new Headers();
            myHeaders.append('Content-Type', 'application/json');

            const userDetail = await this.currentUser.requestDetail();
            const realUserName = userDetail && userDetail.name || '未知用户';

            const raw = JSON.stringify({
                query: this.currentContext.query,
                user: realUserName,
            });

            const options = {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: raw,
            };
            const response = await fetchWithTimeout(defaultAbilityUrl, options);
            const result = await response.json();
            yield stream.flushReplaceLast(result.data.content); // markdown字符串
        }
        catch (error) {
            yield stream.flushReplaceLast(`请求出错，请联系负责人@rongsong或稍后再试。${error as string}`);
        }
    }
}
