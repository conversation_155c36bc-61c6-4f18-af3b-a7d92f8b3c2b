import {
    SkillProvider,
    FunctionParameterDefinition,
    TaskProgressChunkStream,
    TaskProgressChunk,
} from '@comate/plugin-host';
import {fetchWithTimeout} from '../common/fetchWithTimeout.js';
import {codeNameCreateUrl} from '../config/config.js';

export class CodeNameCreateProvider extends SkillProvider {
    static skillName = '代码变量起名';
    static displayName = '代码变量起名';
    static description = '给代码变量或类快速起名，支持驼峰、下划线等格式';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new TaskProgressChunkStream();

        try {
            const myHeaders = new Headers();
            myHeaders.append('Content-Type', 'application/json');

            const userDetail = await this.currentUser.requestDetail();
            const realUserName = userDetail && userDetail.name || '未知用户';

            const raw = JSON.stringify({
                query: this.currentContext.query,
                user: realUserName,
            });

            const options = {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: raw,
            };
            const response = await fetchWithTimeout(codeNameCreateUrl, options);
            const result = await response.json();
            yield stream.flushReplaceLast(result.data.content); // markdown字符串
        }
        catch (error) {
            yield stream.flushReplaceLast(`请求出错，请联系负责人@rongsong或稍后再试。${error as string}`);
        }
    }
}
