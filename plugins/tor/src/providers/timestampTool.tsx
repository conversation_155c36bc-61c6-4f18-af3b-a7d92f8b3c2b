import {
    SkillProvider,
    FunctionParameterDefinition,
    TaskProgressChunk,
    StringChunkStream,
} from "@comate/plugin-host";
import { echoCodeBlock } from "../utils.js";
import { timestampUrl } from "../config/config.js";

/**
 * 获取当前时间戳的 Markdown 格式字符串
 *
 * @returns 包含当前秒级和毫秒级时间戳的 Markdown 格式字符串
 */
function getCurrentTimeStampMd() {
    const currentTime = Date.now();
    const currentTimeMs = Math.floor(currentTime / 1000);
    const messageMd = `当前时间戳\n\n`
        + `秒(s)时间戳：${echoCodeBlock("markdown", currentTimeMs.toString())}\n\n`
        + `毫秒(ms)时间戳：${echoCodeBlock("markdown", currentTime.toString())}`;
    return messageMd;
}

export class TimestampToolProvider extends SkillProvider {
    static skillName = "时间戳工具";
    static displayName = "时间戳工具";
    static description = "获取和转换时间戳";

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const userDetail = await this.currentUser.requestDetail();
        const userName = userDetail && userDetail.name || '未知用户';
        const query = this.currentContext.query;
        const stream = new StringChunkStream();
        // 如果没有输入参数，则返回当前时间戳
        if (!query) {
            const message = getCurrentTimeStampMd();
            yield stream.flush(message);
            return;
        }
        const apiConfig = {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user: userName,
                query: query,
            }),
        };
        try {
            const response = await fetch(timestampUrl, apiConfig);
            const result = await response.json();
            this.logger.info(result);
            if (!result || !result.data || !result.data.function) {
                if (result.message) {
                    throw new Error(result.message);
                }
                else {
                    throw new Error('result.data is null or result.data.function is empty');
                }
            }
            const intention = result.data.function;
            if (intention == 'get_current_timestamp') {
                const message = getCurrentTimeStampMd();
                yield stream.flush(message);
            }
            else {
                const answer = result.data.answer;
                yield stream.flush(answer);
            }
        }
        catch (error) {
            yield stream.flush(`请求出错，请联系负责人\`@baizhihao\` 或稍后再试。\n${error as string}`);
        }
    }

}
