import {
    FunctionParameterDefinition,
    SkillProvider,
    TaskProgressChunk,
    StringChunkStream,
} from '@comate/plugin-host';
import WebSocket from 'ws';

export class NpmPackageSyncSkillProvider extends SkillProvider {
    static skillName = 'npm-package-sync';
    static displayName = 'NPM包同步';
    static description = 'NPM包同步';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new StringChunkStream();
        const packageName = this.currentContext.query;

        try {
            for await (const message of this.syncPackage(packageName)) {
                yield stream.flush(message);
            }
        }
        catch (error) {
            yield stream.flush(`[E] Error: ${error}\n`);
        }
    }

    private syncPackage(packageName: string): AsyncIterableIterator<string> {
        let resolver: ((value: IteratorResult<string, void>) => void) | null = null;
        let isFinished = false;

        const iterator: AsyncIterableIterator<string> = {
            async next() {
                if (isFinished) {
                    return {value: undefined, done: true};
                }
                return new Promise(resolve => {
                    resolver = resolve;
                });
            },
            [Symbol.asyncIterator]() {
                return this;
            },
        };

        const ws = new WebSocket('wss://npm-cdn-sync-service.now.baidu-int.com/ws');

        ws.on('open', () => {
            ws.send(JSON.stringify({event: 'syncPackage', data: {packageName}}));
        });

        ws.on('message', (log: string) => {
            const message = JSON.parse(log).message;
            let formattedMessage = '';

            if (
                message.startsWith('Start sync')
                || message.startsWith('sync')
                || message.startsWith('logUrl')
                || message.startsWith('Sync')
            ) {
                formattedMessage = `[I] ${message}\n`;
            }
            else if (message.includes('同步完成，退出码 0')) {
                formattedMessage = `[S] ${message}\n`;
            }

            if (formattedMessage && resolver) {
                resolver({value: formattedMessage, done: false});
            }

            if (message.includes('同步完成，退出码 0')) {
                isFinished = true;
                if (resolver) {
                    resolver({value: undefined, done: true});
                }
                ws.close();
            }
        });

        ws.on('error', error => {
            isFinished = true;
            if (resolver) {
                resolver({value: `[E] WebSocket error: ${error.message}\n`, done: false});
                resolver({value: undefined, done: true});
            }
        });

        return iterator;
    }
}
