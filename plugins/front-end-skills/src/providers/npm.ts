import {
    FunctionParameterDefinition,
    SkillProvider,
    TaskProgressChunk,
    StringChunkStream,
    TextModel,
} from '@comate/plugin-host';

const npmPromptTemplate = `
你是一位专业的NPM服务问题解决专家。根据以下知识库回答用户的问题：

{{knowledge}}

在回答时，请遵循以下指导：
- 直接回答问题，不需要使用"当然"、"好的"等开场白。
- 如果问题涉及具体的错误信息，请先指出可能的原因，然后提供相应的解决方案。
- 如果问题需要更多信息才能准确诊断，请询问用户以获取必要的细节。
- 回答应简洁明了，仅包含解决问题所需的关键信息。
用户的问题是：

{{query}}

根据以上信息提供专业、准确的回答。
`;

export class NpmSkillProvider extends SkillProvider {
    static skillName = 'npm';
    static displayName = 'npm镜像FAQ';
    static description = 'NPM服务问题解决助手，快速诊断和解决用户遇到的各种NPM相关问题';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const {query} = this.currentContext;
        const stream = new StringChunkStream();

        try {
            if (!query) {
                yield stream.flushReplaceLast('请用自然语言描述你的问题');
                return;
            }

            const knowledge = await this.retriever.knowledgeFromQuery(
                query,
                {
                    knowledgeSets: [
                        {uuid: '36ca1a79-c369-424d-8e43-d49916e993a4', type: 'NORMAL'},
                    ],
                }
            );

            const prompt = this.llm.createPrompt(
                npmPromptTemplate,
                {knowledge, query}
            );

            const chunks = this.llm.askForTextStreaming(prompt, {model: TextModel.Default});

            yield stream.flush('\n\n');
            for await (const chunk of chunks) {
                yield stream.flush(chunk);
            }
        }
        catch (error) {
            yield stream.flushReplaceLast('出现错误，请稍后再试');
        }
    }
}
