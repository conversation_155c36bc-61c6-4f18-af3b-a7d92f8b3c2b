import {
    FunctionParameterDefinition,
    Skill<PERSON>rovider,
    TaskProgressChunk,
    StringChunkStream,
} from '@comate/plugin-host';
import WebSocket from 'ws';

export class NpmCdnSyncSkillProvider extends SkillProvider {
    static skillName = 'npm-cdn-sync';
    static displayName = 'npm包CDN同步';
    static description = 'npm包CDN同步';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new StringChunkStream();
        const packageWithVersion = this.currentContext.query;

        try {
            for await (const message of this.syncCDN(packageWithVersion)) {
                yield stream.flush(message);
            }
        }
        catch (error) {
            yield stream.flush(`[E] Error: ${error}\n`);
        }
    }

    private syncCDN(packageWithVersion: string): AsyncIterableIterator<string> {
        const messageQueue: string[] = [];
        let resolver: ((value: IteratorResult<string>) => void) | null = null;
        let isFinished = false;

        const iterator: AsyncIterableIterator<string> = {
            async next(): Promise<IteratorResult<string>> {
                if (messageQueue.length > 0) {
                    return {value: messageQueue.shift()!, done: false};
                }
                if (isFinished) {
                    return {value: undefined, done: true};
                }
                return new Promise(resolve => {
                    resolver = resolve;
                });
            },
            [Symbol.asyncIterator]() {
                return this;
            },
        };

        const [packageName, version] = packageWithVersion.split('@');
        const ws = new WebSocket('wss://npm-cdn-sync-service.now.baidu-int.com/ws');

        ws.on('open', () => {
            ws.send(JSON.stringify({event: 'syncCDN', data: {packageName, version}}));
        });

        ws.on('message', (log: string) => {
            const message = JSON.parse(log).message;
            let formattedMessage = '';

            if (
                message.startsWith('开始同步') || message.startsWith('从上游下载') || message.includes('同步原始压缩包')
            ) {
                formattedMessage = `[I] ${message}\n\n`;
            }
            else if (message.includes('下载成功') || message.includes('同步完成')) {
                formattedMessage = `[S] ${message}\n\n`;
            }
            else if (message.startsWith('完成同步')) {
                formattedMessage = `[D] ${message}\n\n`;
            }
            else {
                formattedMessage = `${message}\n`;
            }

            messageQueue.push(formattedMessage);
            if (resolver) {
                resolver({value: messageQueue.shift()!, done: false});
                resolver = null;
            }

            if (message.includes('同步完成')) {
                isFinished = true;
                ws.close();
            }
        });

        ws.on('close', () => {
            isFinished = true;
            if (resolver) {
                if (messageQueue.length > 0) {
                    resolver({value: messageQueue.shift()!, done: false});
                }
                else {
                    resolver({value: undefined, done: true});
                }
                resolver = null;
            }
        });

        ws.on('error', error => {
            messageQueue.push(`[E] WebSocket error: ${error.message}\n`);
            isFinished = true;
            if (resolver) {
                resolver({value: messageQueue.shift()!, done: false});
                resolver = null;
            }
        });

        return iterator;
    }
}
