import {
    FunctionParameterDefinition,
    SkillProvider,
    TaskProgressChunk,
    TaskProgressChunkStream,
} from '@comate/plugin-host';
import jsyaml from 'js-yaml';
import {echoCodeBlock} from '../util/index.js';
import defaultPromptTemplate from '../prompts/nodeUpgrade.prompt';

interface Profile {
    profile: null;
    name: string;
    mode: string;
    build: Build;
}
export interface Build {
    command: string;
}

interface BasicYaml {
    Profiles: Profile[];
}

export class CiYamlNodeUpgradeProvider extends SkillProvider {
    static skillName = 'ci.yml-node-upgrade';

    static displayName = 'ci.yml nodejs升级';

    static description = '根据当前ci.yml以及command，自动升级编译环境所需要node版本';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    private readonly promptTemplate = defaultPromptTemplate;

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const {query} = this.currentContext;
        const stream = new TaskProgressChunkStream();
        try {
            let oldYaml = '';
            const fileSystem = await this.requestWorkspaceFileSystem();

            if (fileSystem) {
                oldYaml = await fileSystem.readFile('ci.yml', 'utf-8');
                yield stream.flush(`读取到ci.yml内容：${echoCodeBlock('yaml', oldYaml)}`);
            }
            else {
                this.logger.warn('No file system');
                yield stream.flush('未读取到当前ci.yml内容，将直接根据模板为您生成参考');
            }

            yield stream.flush('根据您的需求，开始为您生成新的ci.yml配置...\n\n');
            const prompt = this.llm.createPrompt(this.promptTemplate, {query, oldYaml});

            const chunks = this.llm.askForTextStreaming(prompt);

            for await (const chunk of chunks) {
                yield stream.flush(chunk);
            }

            if (fileSystem && oldYaml) {
                try {
                    yield stream.flush('读取build sh脚本...\n\n');
                    const yamlObj = jsyaml.load(oldYaml) as BasicYaml;
                    const commands = yamlObj.Profiles.map(profile => profile.build?.command);
                    // eslint-disable-next-line max-depth
                    for (const command of commands) {
                        // (?=\s|$)是一个正向前瞻断言，它会查找后面跟着的是一个空白字符或者字符串末尾的位置
                        // 但不会消耗任何字符（即不会成为匹配结果的一部分）。
                        // 这样我们就可以确保捕获的是完整的文件路径，且不会包括后面的参数或空白字符
                        const match = /(?:sh|bash)\s+(.*?)(?=\s|$)/.exec(command);
                        // eslint-disable-next-line max-depth
                        if (match && match[1]) {
                            const shFile = match[1];
                            yield stream.flush('\n正在优化`' + shFile + '`文件\n\n');
                            const shFileData = await fileSystem.readFile(shFile, 'utf-8');

                            const lines = shFileData.split('\n');
                            const newlines = lines.filter(line => !line.includes('$NODEJS_'));

                            yield stream.flush(echoCodeBlock('bash', newlines.join('\n')));
                        }
                    }
                }
                catch (e) {
                    yield stream.flush('解析yaml失败，请检查ci.yml配置是否正确');
                }
            }
        }
        catch (error: any) {
            this.logger.warn(error.message);
        }
    }
}
