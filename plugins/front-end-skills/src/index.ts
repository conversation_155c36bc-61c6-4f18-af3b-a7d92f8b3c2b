import {HelpSkillProvider} from './providers/help.js';
import {PluginSetupContext} from '@comate/plugin-host';
import {CiYamlNodeUpgradeProvider} from './providers/nodeUpgrade.js';
import {CustomFallbackProvider} from './providers/fallback.js';
import {NpmSkillProvider} from './providers/npm.js';
import {NpmPackageSyncSkillProvider} from './providers/npmPackageSync.js';
import {NpmCdnSyncSkillProvider} from './providers/npmCdnSync.js';

export function setup({registry}: PluginSetupContext) {
    registry.registerSkillProvider('front-end-skills-help', HelpSkillProvider);
    registry.registerSkillProvider('ci.yml-node-upgrade', CiYamlNodeUpgradeProvider);
    registry.registerSkillProvider('npm', NpmSkillProvider);
    registry.registerSkillProvider('npm-package-sync', NpmPackageSyncSkillProvider);
    registry.registerSkillProvider('npm-cdn-sync', NpmCdnSyncSkillProvider);
    registry.registerFallbackProvider('fallback', CustomFallbackProvider);
}
