{"private": true, "name": "@comate-plugin/front-end-skills", "version": "0.9.2", "type": "module", "scripts": {"clean": "rm -rf dist", "dev": "cpd dev", "build": "tsc && cpd build", "lint": "eslint --max-warnings=0 src"}, "dependencies": {"js-yaml": "^4.1.0", "ws": "^8.16.0"}, "devDependencies": {"@comate/plugin-cli": "^0.9.2", "@comate/plugin-host": "^0.9.2", "@types/js-yaml": "^4.0.9", "@types/ws": "^8.5.10", "eslint": "^8.54.0", "typescript": "^5.3.3"}, "comate": {"name": "front-end-skills", "version": "1.0.0", "icon": "./assets/icon.svg", "entry": "./dist/index.js", "displayName": "大前端工具集", "description": "围绕前端开发的一系列辅助技能", "keyword": ["ci.yml-node-upgrade"], "capabilities": [{"type": "Skill", "name": "front-end-skills-help", "displayName": "插件介绍", "description": "插件介绍", "placeholder": "无需输入内容，回车获得插件介绍"}, {"type": "Skill", "name": "ci.yml-node-upgrade", "displayName": "ci.yml升级编译node版本", "description": "根据当前ci.yml以及command，自动升级编译环境所需要node版本"}, {"type": "Skill", "name": "npm", "displayName": "npm镜像FAQ", "description": "NPM服务问题解决助手，快速诊断和解决用户遇到的各种NPM相关问题"}, {"type": "Skill", "name": "npm-package-sync", "displayName": "npm包版本同步", "description": "输入包名进行npm包版本同步", "placeholder": "请输入\"包名\"，如yarn"}, {"type": "Skill", "name": "npm-cdn-sync", "displayName": "npm包CDN同步", "description": "输入包名@版本进行npm包cdn同步", "placeholder": "请输入\"包名@版本\"，如yarn@1.22.22"}, {"type": "Fallback", "name": "fallback", "displayName": "兜底", "description": "兜底"}], "configSchema": {"sections": []}}}