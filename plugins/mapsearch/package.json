{"private": true, "name": "@comate-plugin/mapsearch", "version": "0.8.0", "type": "module", "scripts": {"clean": "rm -rf dist", "dev": "cpd dev", "build": "tsc && cpd build"}, "dependencies": {}, "devDependencies": {"@comate/plugin-cli": "^0.9.2", "@comate/plugin-host": "^0.9.2", "typescript": "^5.3.2"}, "comate": {"name": "mapsearch", "version": "1.0.0", "icon": "./assets/mapsearchicon.png", "entry": "./dist/index.js", "displayName": "地图检索中台", "description": "一个综合性的服务平台，它集成了地图检索相关的各种规范、组件库、需求协同流程以及开发标准。", "keyword": ["地图检索中台", "mapsearch"], "capabilities": [{"type": "Skill", "name": "mapsearch-help", "displayName": "插件介绍", "description": "插件介绍", "placeholder": "无需输入内容，回车获得插件介绍"}, {"type": "Skill", "name": "chat", "displayName": "智能问答", "description": "智能解答关于地图检索中台的相关问题"}, {"type": "Fallback", "name": "fallback", "displayName": "智能问答", "description": "智能解答关于地图检索中台的相关问题"}], "configSchema": {"sections": []}}}