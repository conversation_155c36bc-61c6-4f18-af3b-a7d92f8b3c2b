import {PluginSetupContext} from '@comate/plugin-host';
import {HelpSkillProvider} from './providers/help.js';
import {ChatSkillProvider} from './providers/chat.js';
import {CustomFallbackProvider} from './providers/fallback.js';

export function setup({registry}: PluginSetupContext) {
    registry.registerSkillProvider('mapsearch-help', HelpSkillProvider);
    registry.registerSkillProvider('chat', ChatSkillProvider);
    registry.registerFallbackProvider('fallback', CustomFallbackProvider, true);
}
