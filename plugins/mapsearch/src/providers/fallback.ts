import {
    FallbackProvider,
    FunctionParameterDefinition,
    TaskProgressChunk,
    TextModel,
    StringChunkStream,
} from '@comate/plugin-host';

export class CustomFallbackProvider extends FallbackProvider {
    static skillName = 'chat';

    static displayName = '地图检索中台智能问答';

    static description = '智能解答关于地图检索中台的相关问题';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *handleQuery(): AsyncIterableIterator<TaskProgressChunk> {
        const {query} = this.currentContext;
        const stream = new StringChunkStream();

        yield stream.flush('正在思考中，请耐心等候...');

        try {
            const knowledge = await this.retriever.knowledgeFromQuery(
                query,
                {
                    knowledgeSets: [
                        {uuid: '7d1342d8-7f32-4368-a41b-a74c4b5842c5', type: 'NORMAL'},
                    ],
                }
            );

            const prompt = this.llm.createPrompt(
                knowledge.length
                    ? `你是百度地图检索官方助手，请根据以下背景知识：
                        {{knowledge}}
                        回答问题：{{query}}
                        `
                    : '请回答问题：{{query}}',
                {knowledge, query}
            );

            const chunks = this.llm.askForTextStreaming(prompt, {model: TextModel.Default});

            yield stream.flushReplaceLast('');

            for await (const chunk of chunks) {
                yield stream.flush(chunk);
            }
        }
        catch (error) {
            yield stream.flushReplaceLast('出现错误，请稍后再试');
        }
    }
}
