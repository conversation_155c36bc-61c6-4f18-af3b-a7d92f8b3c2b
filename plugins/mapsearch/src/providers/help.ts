import {
    FunctionParameterDefinition,
    SkillProvider,
    StringChunkStream,
    TaskProgressChunk,
} from '@comate/plugin-host';
import {readFileSync} from 'node:fs';
import {fileURLToPath} from 'node:url';
import {dirname} from 'node:path';

export class HelpSkillProvider extends SkillProvider {
    static skillName = 'mapsearch-help';
    static displayName: '插件介绍';
    static description = '插件介绍';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new StringChunkStream();
        const filename = fileURLToPath(import.meta.url);
        const content = readFileSync(`${dirname(filename)}/help.md`, 'utf8');

        yield stream.flush(content);
    }
}
