import {BackgroundServiceProvider, ProviderInit} from '@comate/plugin-host';

import Watcher from 'watcher';

import {getErrorMessage, getErrorStack} from '../utils/error.js';
import {SASTFileUtils} from '../sast/bundle.js';
import {
    ResultType,
    ConnectionOptions,
} from '../utils/http.js';
import {BASE_URL, SYNC_SETTING_INTERVAL} from '../utils/constants.js';
import {Base, Platform, sleep} from '../utils/types.js';
import {ReportData, reportError} from '../utils/report.js';
import {isPathFile} from '../utils/file.js';
import {initScanner, ScannerUtils} from '../sast/scanner.js';
import {settings} from '../sast/settings.js';
import {ComateContext} from '../sast/context.js';

interface AutoScanSettings {
    autoScan: boolean;
    syncInterval: number;
}

export class ScanBackgroundServiceProvider extends BackgroundServiceProvider {
    static readonly scanTimeout = 5 * 60 * 1000; // 扫描超时时间，单位 ms
    static readonly resultCheckInterval = 2000; // 获取扫描结果的间隔，单位 ms

    private readonly base = new Base();
    private readonly cwd: string;
    private readonly baseOptions: ConnectionOptions; // option 会反复用到，作为全局变量，每次请求注意修改 requestID
    private readonly reportBase: ReportData;
    private fileUtils!: SASTFileUtils;
    private readonly scanSettings: AutoScanSettings = {
        autoScan: false,
        syncInterval: SYNC_SETTING_INTERVAL,
    };
    private appName: string = '';
    private scanner!: ScannerUtils;

    constructor(init: ProviderInit) {
        super(init);
        this.disableLogging('fs');
        this.cwd = init.cwd;
        this.reportBase = {
            userID: this.currentUser.id,
            userName: '',
            skill: ScanBackgroundServiceProvider.name,
            traceID: this.base.traceID,
        };
        this.baseOptions = {
            baseURL: BASE_URL,
            userID: this.currentUser.id,
            requestID: this.base.traceID,
        };
    }

    async startBackgroundService(): Promise<void> {
        try {
            if (!await this.isInner()) {
                this.logger.info('startBackgroundService', {message: 'not inner, end'});
                return;
            }
            await this.run();
        }
        catch (error) {
            this.logger.error(
                'startBackgroundService',
                this.base.logContent({
                    error: error,
                    message: 'run',
                    stack: getErrorStack(error),
                })
            );
            this.reportBase.traceID = this.base.traceID;
            this.reportBase.message = 'backgroud task run error';
            this.reportBase.error = getErrorMessage(error);
            this.reportBase.stack = getErrorStack(error);
            await reportError(this.logger, this.reportBase);
        }
    }

    private async isInner(): Promise<boolean> {
        try {
            const host = await this.retriever.hostInfo();
            this.logger.info('isInner', {message: `platform: ${host.platform}`});
            return host.platform === Platform.inner;
        }
        catch (error) {
            this.logger.error('isInner', {
                message: 'get platform error',
                error: getErrorMessage(error),
                stack: getErrorStack(error),
            });
            // 默认是外部版本
            return false;
        }
    }

    private async run() {
        const fs = await this.requestWorkspaceFileSystem();
        if (!fs) {
            // 无文件权限，不能继续扫描
            return;
        }

        const userDetail = await this.currentUser.requestDetail();
        const userName = userDetail ? userDetail.name : '';

        const host = await this.retriever.hostInfo();
        this.appName = host.appName;

        this.baseOptions.extraHeaders = {'Comate-Username': encodeURIComponent(userName)};
        this.reportBase.userName = userName;
        const repository = await this.retriever.repositoryInfo();

        const context = new ComateContext(
            true,
            this.currentUser.id,
            userName,
            this.cwd,
            fs,
            this.logger,
            repository,
            this.appName
        );
        this.scanner = await initScanner(context);

        // eslint-disable-next-line no-constant-condition
        while (true) {
            // 获取扫描配置
            await this.syncSettings(); // 初始化配置
            if (this.scanSettings.autoScan) {
                break;
            }
            else {
                this.logger.info('run', {message: `no scan: ${userName}`});
                await sleep(this.scanSettings.syncInterval);
            }
        }
        this.logger.info('run', {message: 'scan'});

        this.fileUtils = new SASTFileUtils(
            fs,
            this.cwd,
            this.logger
        );

        const watcher = new Watcher(
            this.cwd,
            {
                // 更多选项，参见 https://www.npmjs.com/package/watcher#options
                debounce: 300, // 忽略间隔内的相同事件
                pollingInterval: 1000 * 30, // 监听的路径不存在时，轮询的间隔，除非 cwd 被删了，否则应该用不到
                recursive: true,
                persistent: true,
                ignore: (file: string) => this.ignore(file),
                ignoreInitial: true, // 为 false 时，初始化会触发监听文件 add 事件，每个目标文件都会发起一次扫描
            }
        );

        this.logger.info('run', this.base.logContent({message: 'start watch'}));

        // 第一次扫描之前就启动 watcher 可能会出现部分文件重复扫描，但绝不会漏
        watcher
            .on('error', error => {
                this.logger.error(
                    'run',
                    this.base.logContent({
                        error: error,
                        message: 'watcher error',
                        stack: getErrorStack(error),
                    })
                );
            })
            .on('add', path => this.updateTargetFile(path))
            .on('change', path => this.updateTargetFile(path))
            .on('unlink', path => this.removeTargetFile(path));

        // 首次扫描
        await this.scanner.fullScan(this.scanner.upScanType());

        // eslint-disable-next-line no-constant-condition
        while (true) {
            // 这里不做别的操作，因为如果停了 watcher，想要重启得重新 new 一个，还是留着，只是忽略全部变更事件
            await Promise.all<unknown>([this.syncSettings(), sleep(this.scanSettings.syncInterval)]);
        }
    }

    private ignore(p: string): boolean {
        try {
            if (!this.scanSettings.autoScan) {
                // 自动扫描关闭，忽略全部文件
                return true;
            }

            if (!p.startsWith(this.cwd)) {
                // https://github.com/fabiospampinato/watcher/issues/14
                return true;
            }

            if (SASTFileUtils.isIgnoredPath(p)) {
                return true;
            }

            return false;
        }
        catch (error) {
            this.logger.error(
                'ignore',
                this.base.logContent({
                    error: getErrorMessage(error),
                    message: 'ignore error',
                    file: p,
                    stack: getErrorStack(error),
                })
            );
        }
        return true;
    }

    private async updateTargetFile(absolutePath: string) {
        try {
            const isFile = await isPathFile(absolutePath);
            if (!isFile) {
                return;
            }

            await this.scanner.addChangedFiles([absolutePath]);
        }
        catch (error) {
            this.logger.error(
                'updateTargetFile',
                this.base.logContent({
                    error: error,
                    message: 'update file error',
                    file: absolutePath,
                    stack: getErrorStack(error),
                })
            );
        }
    }

    private async removeTargetFile(absolutePath: string) {
        try {
            // 删除文件记录
            await this.scanner.removeFile(absolutePath);
        }
        catch (error) {
            this.logger.error(
                'removeTargetFile',
                this.base.logContent({
                    error: error,
                    message: 'remove file error',
                    file: absolutePath,
                    stack: getErrorStack(error),
                })
            );
        }
    }

    private async syncSettings() {
        const settingResp = await settings(this.baseOptions);
        if (settingResp.type === ResultType.error) {
            // 获取失败就结束
            this.logger.error(
                'syncSettings',
                this.base.logContent({
                    message: 'get settings error',
                    error: settingResp.error.text,
                    api: settingResp.error.apiName,
                    stack: settingResp.error.detail ?? '',
                })
            );
            return;
        }

        const res = settingResp.value;

        try {
            this.scanSettings.autoScan = res.scanConfiguration.autoScanEnabled;
            this.scanSettings.syncInterval = res.systemConfiguration.refreshConfInterval * 1000;

            await this.scanner.syncConfig(res);

            this.logger.info(
                'syncSettings',
                this.base.logContent({
                    message: 'sync settings',
                    data: this.scanSettings,
                })
            );
        }
        catch (error) {
            this.logger.error(
                'syncSettings',
                this.base.logContent({
                    message: 'process settings error',
                    error: getErrorMessage(error),
                    stack: getErrorStack(error),
                })
            );
        }
    }
}
