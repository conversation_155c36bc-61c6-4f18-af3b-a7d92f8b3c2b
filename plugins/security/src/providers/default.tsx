import {FallbackProvider, TaskProgressChunk, ElementChunkStream, ProviderInit} from '@comate/plugin-host';
import {getErrorStack, getErrorMessage} from '../utils/error.js';
import {Platform} from '../utils/types.js';

export class ScanFallbackProvider extends FallbackProvider {
    static description = '使用说明';
    private inner: boolean = false;

    constructor(init: ProviderInit) {
        super(init);
        this.disableLogging('fs');
    }

    private async isInner(): Promise<boolean> {
        try {
            const host = await this.retriever.hostInfo();
            this.logger.info('isInner', {message: `platform: ${host.platform}`});
            return host.platform === Platform.inner;
        }
        catch (error) {
            this.logger.error('isInner', {
                message: 'get platform error',
                error: getErrorMessage(error),
                stack: getErrorStack(error),
            });
            // 默认是外部版本
            return false;
        }
    }

    async *handleQuery(): AsyncIterableIterator<TaskProgressChunk> {
        this.inner = await this.isInner();
        const stream = new ElementChunkStream();
        try {
            yield stream.flushReplaceLast(
                <p>
                    安全助手使用示例：<br />
                    输入框中输入“/代码安全检测”<br />
                    或指定目录，如“/代码安全检测 dir”<br />
                    点击发送或回车即可发起扫描
                </p>
            );
        }
        catch (error) {
            this.logger.error(
                'handleQuery',
                {message: 'sast execute', error: error, stack: getErrorStack(error)}
            );
        }
        if (this.inner) {
            yield stream.flush(
                <a href="https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/idGHtE2EDf/uIO7T0dV7K/gbbYDTA66GuEuR">
                    如有疑问，联系我们
                </a>
            );
        }
    }
}
