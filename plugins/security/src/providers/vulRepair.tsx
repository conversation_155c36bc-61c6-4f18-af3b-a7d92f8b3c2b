import {
    TaskProgress<PERSON>hunk,
    <PERSON>ll<PERSON><PERSON>ider,
    ProviderInit,
    FunctionParameterDefinition,
    ElementChunkStream,
    FileSystem,
    CommandHandler,
} from '@comate/plugin-host';

import {
    getErrorMessage,
    getErrorStack,
} from '../utils/error.js';
import {LogRecord, Platform, sleep} from '../utils/types.js';
import {settings} from '../sast/settings.js';
import {BundleUtils, initScanner, RepairData, ScannerUtils} from '../sast/scanner.js';
import * as http from '../utils/http.js';
import {ComateContext, getComateContext, initComateContext} from '../sast/context.js';
import {helpChunk} from '../sast/message.js';
import {ScanItem, SASTFileUtils} from '../sast/bundle.js';
import {reportError} from '../utils/report.js';
import {Repair} from '../sast/repair.js';
import {SAST} from '../sast/analysis.js';
import {readFile, size} from '../utils/file.js';

interface InputData {
    vulHash: string;
    filePath: string;
    scanType: http.ScanType;
}

enum ScanStatus {
    continue = 0,
    end = 1,
}

interface ResultEnd {
    status: ScanStatus.end;
    msg: string;
}

interface ResultContinue {
    status: ScanStatus.continue;
    repairData: RepairData;
}

type ScanResult = ResultEnd | ResultContinue;

function validInputData(data: InputData) {
    if (typeof data.vulHash !== 'string' || data.vulHash.length === 0) {
        throw new Error('invalid data: vulHash type error or empty');
    }
    if (typeof data.filePath !== 'string' || data.filePath.length === 0) {
        throw new Error('invalid data: filePath type error or empty');
    }
    if (typeof data.scanType !== 'number' || !Object.values(http.ScanType).includes(data.scanType)) {
        throw new Error('invalid data: scanType type or value error');
    }
}

const MSG_VUL_NOT_FOUND =
    '未能检测出漏洞，可能该文件中相关漏洞已经被修复，请您确认文件时间及版本，或者您可尝试点击「查看全部漏洞」扫描其他文件是否存在安全问题。';
const MSG_REPAIR_NOT_OPEN = '当前自动修复未开启，请您通过下方联系方式联系我们。';
const MSG_LARGE_FILE = '该漏洞文件过大，暂不支持修复，您可尝试点击「查看全部漏洞」扫描其他文件是否存在安全问题。';
const MSG_READ_FILE_ERROR =
    '该漏洞文件无法被正常读取，请您确认文件状态，或者您可尝试点击「查看全部漏洞」扫描其他文件是否存在安全问题。';
const MSG_UNSUPPORTED_FILE = '暂不支持该文件的修复。';

// 避免每次触发都重新初始化 context，这里留个缓存
let context: ComateContext;
let scanner: ScannerUtils;

export class RepairSkillProvider extends SkillProvider implements CommandHandler {
    static skillName = 'vulRepair';

    static description = '漏洞修复';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    private readonly cwd: string;
    private readonly skill: string;
    private traceID: string;
    private inner = false;
    private repair = 0;
    private fs!: FileSystem;

    constructor(init: ProviderInit) {
        super(init);
        this.disableLogging('fs');
        this.cwd = init.cwd;
        this.skill = RepairSkillProvider.name;
        this.traceID = crypto.randomUUID();
    }

    async *handleCommand(commandName: string, data: any): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new ElementChunkStream();
        if (!await this.initContext()) {
            yield stream.flushReplaceLast(<p>检测失败。</p>);
        }

        if (commandName === 'codeScan:scan') {
            const traceID = crypto.randomUUID();
            try {
                scanner = scanner ?? await initScanner(context);
                this.logger.info('handleCommand', this.logContent({message: 'scan command', data: data}));
                const sast = new SAST(
                    context,
                    scanner,
                    traceID,
                    stream,
                    this.skill
                );
                yield* sast.execute(data);
            }
            catch (error) {
                this.logger.error('handleCommand', {
                    traceID: traceID,
                    message: 'scan',
                    error: getErrorMessage(error),
                    stack: getErrorStack(error),
                });
                yield stream.flushReplaceLast(<p>检测失败。</p>);
            }

            yield stream.flush(helpChunk(context.isInner));
        }
        else if (commandName === 'codeScan:repair') {
            const traceID = crypto.randomUUID();
            try {
                this.logger.info('handleCommand', {message: 'repair command', data: data});
                const repair = new Repair(
                    traceID,
                    stream,
                    this.skill,
                    context
                );
                yield* repair.run(data, http.RepairTrigger.button);
            }
            catch (error) {
                this.logger.error('handleCommand', {
                    traceID: traceID,
                    message: 'repair',
                    error: getErrorMessage(error),
                    stack: getErrorStack(error),
                });
                yield stream.flushReplaceLast(<p>修复失败。</p>);
            }
            yield stream.flush(helpChunk(context.isInner));
        }
        else if (commandName === 'codeScan:repair-hint') {
            yield stream.flush(
                <p>该漏洞修复需要专用模型支持，该版本暂未部署专用模型，请您联系 Comate 相关人员咨询。</p>
            );
        }
    }

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        this.inner = await this.isInner();
        const stream = new ElementChunkStream();
        this.logger.info('execute', this.logContent({message: `input data: ${this.currentContext.data}`}));

        try {
            yield* this.exec(stream);
        }
        catch (error) {
            this.logger.error(
                'execute',
                this.logContent({message: 'repair execute', error: getErrorMessage(error), stack: getErrorStack(error)})
            );
            yield stream.flushReplaceLast(<p>修复失败。</p>);
        }

        // 不管成功失败，都加这个按钮
        yield stream.flush(
            <p>
                <command-button
                    commandName="codeScan:scan"
                    data="."
                    replyText="为我的代码库扫描可能的安全漏洞"
                    variant="primary"
                >
                    查看全部漏洞
                </command-button>
            </p>
        );

        yield stream.flush(helpChunk(this.inner));
    }

    private async *exec(stream: ElementChunkStream): AsyncIterableIterator<TaskProgressChunk> {
        // 解析输入数据
        let data = this.parseInput();
        if (!data) {
            yield stream.flushReplaceLast(<p>参数错误。</p>);
            return;
        }

        const fs = await this.requestWorkspaceFileSystem();
        if (!fs) {
            // 无文件权限，不能继续扫描
            yield stream.flushReplaceLast(<p>无文件权限，无法进行漏洞修复。</p>);
            return;
        }
        this.fs = fs;

        try {
            const stat = await this.fs.stat(data.filePath);
            if (stat.type !== 'file') {
                // 不是文件
                this.logger.info('exec', this.logContent({message: 'not a file'}));
                yield stream.flushReplaceLast(<p>{MSG_UNSUPPORTED_FILE}</p>);
                return;
            }
        }
        catch (error) {
            this.logger.error(
                'exec',
                this.logContent({
                    message: 'get file states error',
                    error: getErrorMessage(error),
                    stack: getErrorStack(error),
                })
            );
            yield stream.flushReplaceLast(<p>{MSG_READ_FILE_ERROR}</p>);
            return;
        }

        // 初始化配置
        if (!await this.init()) {
            yield stream.flushReplaceLast(<p>修复失败，请再次尝试。</p>);
            return;
        }

        if (this.repair === 0) {
            yield stream.flushReplaceLast(<p>{MSG_REPAIR_NOT_OPEN}</p>);
            return;
        }

        // 检查是否目标文件
        if (SASTFileUtils.isIgnoredPath(data.filePath)) {
            this.logger.info('exec', this.logContent({message: 'ignore path'}));
            yield stream.flushReplaceLast(<p>{MSG_UNSUPPORTED_FILE}</p>);
            return;
        }
        const targetType = scanner.isTargetFile(data.filePath);
        if (targetType === 0 || (targetType & data.scanType) === 0) {
            // 不是目标文件，或不是指定扫描类型的目标文件
            this.logger.info('exec', this.logContent({message: 'not target file'}));
            yield stream.flushReplaceLast(<p>{MSG_UNSUPPORTED_FILE}</p>);
            return;
        }

        // 读取文件
        let fileHash = '';
        try {
            const [hash, content] = await readFile(fs, data.filePath);
            if (content.length === 0) {
                // 文件为空
                this.logger.info('exec', this.logContent({message: 'empty file'}));
                yield stream.flushReplaceLast(<p>{MSG_UNSUPPORTED_FILE}</p>);
                return;
            }
            if (SASTFileUtils.isFilteredFile(size(content))) {
                // 文件过大
                yield stream.flushReplaceLast(<p>{MSG_LARGE_FILE}</p>);
                return;
            }
            fileHash = hash;
        }
        catch (error) {
            this.logger.error(
                'readFile',
                this.logContent({
                    error: getErrorMessage(error),
                    message: 'get file content error',
                    file: data.filePath,
                    stack: getErrorStack(error),
                })
            );
            yield stream.flushReplaceLast(<p>{MSG_READ_FILE_ERROR}</p>);
            return;
        }

        const targetInfo: ScanItem = {hash: fileHash, type: targetType};
        // 扫描
        const scanRes = await this.scan(data, targetInfo);
        if (scanRes.status === ScanStatus.end) {
            yield stream.flushReplaceLast(<p>{scanRes.msg}</p>);
            return;
        }

        // 修复
        const repair = new Repair(
            this.traceID,
            stream,
            this.skill,
            context
        );
        yield* repair.run(scanRes.repairData, http.RepairTrigger.link);
    }

    private async scan(data: InputData, targetInfo: ScanItem): Promise<ScanResult> {
        // 这里用输入的 scanType，不是文件真正的 type
        const target = new Map<string, ScanItem>([[data.filePath, {type: data.scanType, hash: targetInfo.hash}]]);

        // 从缓存获取扫描结果
        this.logger.info('scan', this.logContent({message: 'get scan result from cache'}));

        const cacheResult = await scanner.getTargetFileCache(target);
        if (cacheResult.needScan.size > 0) {
            // 没找到缓存，需要发起扫描
            this.logger.info('scan', this.logContent({message: 'need scan'}));

            return await this.getRepairDataFromRemote(data, targetInfo, cacheResult.needScan);
        }
        else {
            // 解析缓存
            const cache = cacheResult.result.get(data.filePath);
            if (!cache) {
                // 没有漏洞
                this.logger.info('scan', this.logContent({message: 'vul empty'}));
                return {status: ScanStatus.end, msg: MSG_VUL_NOT_FOUND};
            }
            // 查找目标漏洞
            for (const v of cache.result) {
                if (v.hash === data.vulHash) {
                    if (v.repair) {
                        return {status: ScanStatus.continue, repairData: v.repair};
                    }

                    return {status: ScanStatus.end, msg: MSG_REPAIR_NOT_OPEN};
                }
            }
            this.logger.info('scan', this.logContent({message: 'vulHash not found'}));
            return {status: ScanStatus.end, msg: MSG_VUL_NOT_FOUND};
        }
    }

    private async getRepairDataFromRemote(
        data: InputData,
        targetInfo: ScanItem,
        needScan: Map<string, ScanItem>
    ): Promise<ScanResult> {
        // 上传文件
        // 更新 bundle 需要用文件完整的信息，而不是需要扫描的信息
        const allTargetFiles = await scanner.getWholeBundle(
            this.traceID,
            new Map<string, ScanItem>([[data.filePath, targetInfo]])
        );
        const bundleUtils = new BundleUtils(allTargetFiles);

        const baseOptions = context.connectOptions(this.traceID);

        const fileUtils = new SASTFileUtils(this.fs, this.cwd, this.logger);
        const bundleResponse = await fileUtils.uploadFiles(baseOptions, bundleUtils.getFileHash(), this.traceID);
        if (bundleResponse.type === http.ResultType.error) {
            this.logger.error(
                'exec',
                this.logContent({
                    message: 'uploadFiles',
                    error: bundleResponse.error.text,
                    api: bundleResponse.error.apiName,
                    stack: bundleResponse.error.detail ?? '',
                })
            );
            if (bundleResponse.error.code === http.ResultErrorCode.networkErr) {
                return {status: ScanStatus.end, msg: '修复失败，无法连接服务端。'};
            }
            return {status: ScanStatus.end, msg: '修复失败。'};
        }

        // 扫描
        const analysisOptions: http.GetAnalysisOptions = {
            ...baseOptions,
            bundleHash: bundleResponse.value,
            trigger: http.Trigger.link,
            scanType: data.scanType,
            initiator: context.ideName,
            gitInfo: context.gitInfo(),
            limitToFiles: [data.filePath],
        };

        const startTime = Date.now();
        do {
            const analysisResponse = await http.getAnalysis(analysisOptions);
            if (analysisResponse.type === http.ResultType.error) {
                this.logger.error(
                    'scan',
                    this.logContent({
                        message: 'getAnalysis',
                        error: analysisResponse.error.text,
                        api: analysisResponse.error.apiName,
                        stack: analysisResponse.error.detail ?? '',
                    })
                );
                if (analysisResponse.error.code === http.ResultErrorCode.networkErr) {
                    // 网络问题
                    this.logger.info(
                        'scan',
                        this.logContent({
                            message: 'getAnalysis: network error, retry',
                            api: analysisResponse.error.apiName,
                        })
                    );
                    await sleep(ScannerUtils.resultCheckInterval);
                    continue;
                }
                return {status: ScanStatus.end, msg: '修复失败。'};
            }
            if (analysisResponse.value.status === http.AnalysisStatus.completed) {
                try {
                    const scannedFiles = bundleUtils.getScannedFiles(needScan, data.scanType);
                    const res = scanner.formatResult(
                        this.traceID,
                        scannedFiles,
                        bundleUtils.getFileHash(),
                        analysisResponse.value.data.runs
                    );
                    scanner.cacheResult(analysisOptions.scanType, res);
                    const vuls = res.get(data.filePath)?.result;
                    if (!vuls) {
                        this.logger.info('getRepairDataFromRemote', this.logContent({message: 'vul empty'}));
                        return {status: ScanStatus.end, msg: MSG_VUL_NOT_FOUND};
                    }
                    for (const v of vuls) {
                        if (v.hash === data.vulHash) {
                            if (v.repair) {
                                return {status: ScanStatus.continue, repairData: v.repair};
                            }
                            return {status: ScanStatus.end, msg: MSG_REPAIR_NOT_OPEN};
                        }
                    }
                    this.logger.info('getRepairDataFromRemote', this.logContent({message: 'vulHash not found'}));
                    return {status: ScanStatus.end, msg: MSG_VUL_NOT_FOUND};
                }
                catch (error) {
                    this.logger.error(
                        'scan',
                        this.logContent({
                            message: 'formatResult',
                            error: getErrorMessage(error),
                            stack: getErrorStack(error),
                        })
                    );
                    const opt = context.reportOptions(this.traceID, this.skill, 'format result error', error);
                    await reportError(this.logger, opt);
                    return {status: ScanStatus.end, msg: '修复失败。'};
                }
            }

            await sleep(ScannerUtils.resultCheckInterval);
        }
        while (ScannerUtils.scanTimeout > Date.now() - startTime);

        return {status: ScanStatus.end, msg: '修复失败，请再次尝试。'};
    }

    private parseInput(): InputData | undefined {
        // 解析输入数据
        try {
            const data = JSON.parse(this.currentContext.data) as InputData;
            validInputData(data);
            return data;
        }
        catch (error) {
            this.logger.error(
                'execute',
                this.logContent({message: 'parse data', error: getErrorMessage(error), stack: getErrorStack(error)})
            );
        }
    }

    private async isInner(): Promise<boolean> {
        try {
            const host = await this.retriever.hostInfo();
            this.logger.info('isInner', this.logContent({message: `platform: ${host.platform}`}));
            return host.platform === Platform.inner;
        }
        catch (error) {
            this.logger.error(
                'isInner',
                this.logContent({
                    message: 'get platform error',
                    error: getErrorMessage(error),
                    stack: getErrorStack(error),
                })
            );
            // 默认是外部版本
            return false;
        }
    }

    private logContent(content: LogRecord<string, any>): LogRecord<string, any> {
        return {
            traceID: this.traceID,
            // time: new Date().toString(),
            ...content,
        };
    }

    private async initContext(): Promise<boolean> {
        context = context ?? getComateContext();
        if (!context) {
            try {
                if (!this.fs) {
                    const fs = await this.requestWorkspaceFileSystem();
                    if (!fs) {
                        this.logger.info('initContext', this.logContent({message: 'request fs error'}));
                        return false;
                    }
                    this.fs = fs;
                }
                if (!this.inner) {
                    this.inner = await this.isInner();
                }

                const userDetail = await this.currentUser.requestDetail();
                const userName = userDetail ? userDetail.name : '';

                const host = await this.retriever.hostInfo();
                const repository = await this.retriever.repositoryInfo();

                // 全局变量
                context = initComateContext(
                    this.inner,
                    this.currentUser.id,
                    userName,
                    this.cwd,
                    this.fs,
                    this.logger,
                    repository,
                    host.appName
                );
            }
            catch (error) {
                this.logger.info(
                    'initContext',
                    this.logContent({
                        message: 'init context error',
                        error: getErrorMessage(error),
                        detail: getErrorStack(error),
                    })
                );
                return false;
            }
        }
        return true;
    }

    private async init(): Promise<boolean> {
        if (!await this.initContext()) {
            return false;
        }

        const settingResp = await settings(context.connectOptions(this.traceID));
        if (settingResp.type === http.ResultType.error) {
            // 获取失败就结束
            this.logger.error(
                'initSettings',
                this.logContent({
                    message: 'get settings error',
                    error: settingResp.error.text,
                    api: settingResp.error.apiName,
                    stack: settingResp.error.detail ?? '',
                })
            );
            return false;
        }

        try {
            scanner = scanner ?? await initScanner(context);

            await scanner.syncConfig(settingResp.value);

            if (settingResp.value.scanConfiguration.sca.autofixedEnabled) {
                this.repair |= http.ScanType.sca;
            }

            if (settingResp.value.scanConfiguration.sast.autofixedEnabled) {
                this.repair |= http.ScanType.sast;
            }

            this.logger.info('init', {message: 'get settings', repair: this.repair});
        }
        catch (error) {
            this.logger.error(
                'init',
                this.logContent({
                    message: 'process settings error',
                    error: getErrorMessage(error),
                    stack: getErrorStack(error),
                })
            );
            return false;
        }
        return true;
    }
}
