import {
    TaskProgress<PERSON>hunk,
    <PERSON>ll<PERSON>rov<PERSON>,
    ProviderInit,
    ElementChunkStream,
    CommandHandler,
    FunctionParameterDefinition,
    StringChunkStream,
} from '@comate/plugin-host';

import crypto from 'node:crypto';
import path from 'node:path';

import {isPathDirectory} from '../utils/file.js';
import {SAST} from '../sast/analysis.js';
import {getErrorStack, getErrorMessage} from '../utils/error.js';
import {Platform} from '../utils/types.js';
import {Repair} from '../sast/repair.js';
import {initScanner, RepairData} from '../sast/scanner.js';
import {ComateContext, initComateContext, getComateContext} from '../sast/context.js';
import {RepairTrigger} from '../utils/http.js';
import * as msgs from '../sast/message.js';

// 避免每次触发都重新初始化 context，这里留个缓存
let context: ComateContext;

export class CodeScanSkillProvider extends SkillProvider implements CommandHandler {
    static skillName = 'codeScan';

    static description = '代码安全检测';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    private readonly cwd: string;
    private readonly skill: string;
    private readonly traceID: string;
    private inner: boolean = false;
    private help: boolean = true;

    constructor(init: ProviderInit) {
        super(init);
        this.disableLogging('fs');
        this.cwd = init.cwd;
        this.skill = CodeScanSkillProvider.name;
        this.traceID = crypto.randomUUID();
    }

    private async isInner(): Promise<boolean> {
        try {
            const host = await this.retriever.hostInfo();
            this.logger.info('isInner', {message: `platform: ${host.platform}`});
            return host.platform === Platform.inner;
        }
        catch (error) {
            this.logger.error('isInner', {
                message: 'get platform error',
                error: getErrorMessage(error),
                stack: getErrorStack(error),
            });
            // 默认是外部版本
            return false;
        }
    }

    async *handleCommand(commandName: string, data: RepairData): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new ElementChunkStream();
        if (!context) {
            // 不可能，可以修复一定是先扫描了，扫描一定会初始化 context
            yield stream.flushReplaceLast(<p>修复失败。</p>);
        }

        if (commandName === 'codeScan:repair') {
            try {
                this.logger.info('handleCommand', {message: 'repair command', data: data});
                const repair = new Repair(
                    data.traceID,
                    stream,
                    CodeScanSkillProvider.name,
                    context
                );
                yield* repair.run(data, RepairTrigger.button);
            }
            catch (error) {
                this.logger.error('handleCommand', {
                    message: 'repair',
                    error: getErrorMessage(error),
                    stack: getErrorStack(error),
                });
                yield stream.flushReplaceLast(<p>修复失败。</p>);
            }
            yield stream.flush(msgs.helpChunk(context.isInner));
        }
        else if (commandName === 'codeScan:repair-hint') {
            yield stream.flush(
                <p>该漏洞修复需要专用模型支持，该版本暂未部署专用模型，请您联系 Comate 相关人员咨询。</p>
            );
        }
    }

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        this.inner = await this.isInner();

        const stream = new ElementChunkStream();

        if (!this.inner) {
            const authorized = await this.requestCodeSecurity();
            if (!authorized) {
                yield stream.flushReplaceLast(
                    <p>
                        <p>
                            因您所在的企业未开启此功能，暂时无法使用 /代码安全。请联系企业管理员，前往 Baidu Comate
                            控制台-配置中心 授权开启。
                        </p>
                        <markdown>
                            代码安全能力支持为你扫描 Java 第三方开源依赖漏洞、SQLi（SQL
                            注入）、SSRF（服务器端请求伪造）、RCE（远程命令执行）等高危安全漏洞，
                            并可基于大模型能力为你生成修复后的代码，如需了解更多代码安全能力，请查询 [Comate
                            使用手册](https://comate.baidu.com/zh/readme)。
                        </markdown>
                    </p>
                );
                return;
            }
        }

        try {
            yield* this.exec(stream);
        }
        catch (error) {
            this.logger.error(
                'execute',
                {message: 'sast execute', error: getErrorMessage(error), stack: getErrorStack(error)}
            );
            yield stream.flushReplaceLast(<p>检测失败。</p>);
        }
        if (this.help) {
            yield stream.flush(msgs.helpChunk(this.inner));
        }
    }

    private async *exec(stream: ElementChunkStream): AsyncIterableIterator<TaskProgressChunk> {
        // 集中获取授权
        const fs = await this.requestWorkspaceFileSystem();
        if (!fs) {
            yield stream.flushReplaceLast(<p>检测失败，无文件权限。</p>);
            return;
        }

        let dir = '.'; // 可能是个目录，也可能是个文件
        const queryDir = this.currentContext.query.trim();
        if (queryDir.length > 0) {
            dir = queryDir;
            try {
                if (!path.isAbsolute(dir)) {
                    // 转成绝对路径，避免有 ./ 之类的
                    dir = path.resolve(this.cwd, dir);
                }

                // 最终需要转成相对路径，否则 fs 会出错
                dir = path.relative(this.cwd, dir);

                this.logger.info('execute', {message: `user input path: ${dir}`});

                const stat = await fs.stat(dir);
                if (stat.type != 'file' && stat.type != 'directory') {
                    // 不是目录也不是文件
                    const stringStream = new StringChunkStream();
                    yield stringStream.flush(msgs.INSTRUCTION_MD);
                    yield stringStream.flush('\n\n' + msgs.helpMd(this.inner));
                    this.help = false;

                    return;
                }
            }
            catch (error) {
                this.logger.info(
                    'execute',
                    {message: 'user input dir error', error: getErrorMessage(error), stack: getErrorStack(error)}
                );
                const stringStream = new StringChunkStream();
                yield stringStream.flush(msgs.INSTRUCTION_MD);

                let first = true;
                for await (let ans of this.askLLM(queryDir)) {
                    if (first) {
                        yield stringStream.flush('\n\n如果以上内容无法解决您的问题，可参考以下回答：\n\n');
                        first = false;
                    }
                    yield stringStream.flush(ans);
                }

                yield stringStream.flush('\n\n' + msgs.helpMd(this.inner));
                this.help = false;

                return;
            }
        }

        context = context ?? getComateContext();
        if (!context) {
            const userDetail = await this.currentUser.requestDetail();
            const userName = userDetail ? userDetail.name : '';
            const repository = await this.retriever.repositoryInfo();
            const host = await this.retriever.hostInfo();

            context = initComateContext(
                this.inner,
                this.currentUser.id,
                userName,
                this.cwd,
                fs,
                this.logger,
                repository,
                host.appName
            );
        }
        const scanner = await initScanner(context);

        const sast = new SAST(
            context,
            scanner,
            this.traceID,
            stream,
            this.skill
        );
        yield* sast.execute(dir);
    }

    private async *askLLM(input: string): AsyncIterable<string> {
        try {
            const prompt = this.llm.createPrompt(
                `回答问题“${input}”，字数不超过50`
            );
            yield* this.llm.askForTextStreaming(prompt);
        }
        catch (error) {
            this.logger.info('askLLM', {
                message: 'ask llm error',
                error: getErrorMessage(error),
                stack: getErrorStack(error),
            });
        }
        return;
    }
}
