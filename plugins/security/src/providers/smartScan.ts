import {
    TaskProgressChunk,
    SkillProvider,
    ProviderInit,
    FileSystem,
    FunctionParameterDefinition,
    StringChunkStream,
} from '@comate/plugin-host';
import {
    Card,
    FileFlaw,
    Flaw,
    Link,
    FlawDetail,
    SummaryTableProps,
    FlawTree,
    LoadingText,
    RepairStatusTag,
    RepairableStatusTag,
    FlawTag,
} from '../utils/comate.js';

import crypto from 'node:crypto';
import path from 'node:path';

import {readFile} from '../utils/file.js';
import {VulCount} from '../sast/analysis.js';
import * as errors from '../utils/error.js';
import {Platform, LogRecord, sleep, startsWithLetter} from '../utils/types.js';
import {Repair} from '../sast/repair.js';
import {initScanner, getScanner, ScannerUtils, FileResult, VulInfo, BundleUtils, mergeResult} from '../sast/scanner.js';
import {initComateContext, getComateContext, ComateContext} from '../sast/context.js';
import * as http from '../utils/http.js';
import * as bundle from '../sast/bundle.js';
import * as msgs from '../sast/message.js';
import {settings} from '../sast/settings.js';
import {reportError} from '../utils/report.js';
import {CheckFileStatus, CheckFileResult} from '../sast/repair.js';

const repairDescription = new Map<string, string>([
    ['SQL 注入漏洞', '对于 SQL 注入漏洞，我将尝试通过预编译的方法处理参数，避免注入'],
    ['RCE（远程代码执行）漏洞', '对于 RCE 漏洞，我将编写一段过滤代码对用户输入进行检查'],
    [
        'Apache Log4j2 远程代码执行漏洞',
        '对于 Apache Log4j2 远程代码执行漏洞，我将尝试对低版本组件进行升级，指定一个安全版本',
    ],
    ['Fastjson 任意代码执行漏洞', '对于 Fastjson 任意代码执行漏洞，我将尝试对低版本组件进行升级，指定一个安全版本'],
    ['XXE（XML外部实体注入）漏洞', '对于 XXE（XML外部实体注入）漏洞，我将尝试禁用外部实体的解析，避免注入'],
]);

const flawTagMap = new Map<string, FlawTag>([
    [http.RuleLevel.critical, FlawTag.critical],
    [http.RuleLevel.high, FlawTag.high],
    [http.RuleLevel.medium, FlawTag.medium],
    [http.RuleLevel.low, FlawTag.low],
]);

const repairStatusTagMap = new Map<http.RepairStatus, RepairStatusTag>([
    [http.RepairStatus.running, RepairStatusTag.repairing],
    [http.RepairStatus.verifying, RepairStatusTag.verifying],
    [http.RepairStatus.finished, RepairStatusTag.success],
    [http.RepairStatus.failed, RepairStatusTag.fail],
]);

const streamOutputInterval = 500;

// 避免每次触发都重新初始化 context，这里留个缓存
let context: ComateContext = getComateContext();
let scanner: ScannerUtils = getScanner();

enum Status {
    continue = 0,
    end = 1,
}

type Result<T> = ResultContinue<T> | ResultEnd;

interface ResultContinue<T> {
    status: Status.continue;
    data: T;
}

interface ResultEnd {
    status: Status.end;
}

interface ScanSettings {
    repair: number;
}

interface RepairFile {
    hash: string;
    vuls: RepairVul[];
    card: FileFlaw;
}

interface RepairVul {
    type: http.ScanType;
    ruleID: string;
    detail: VulInfo;
    card: Flaw;
    hash: string;
}

interface RepairResult {
    hash: string;
    language: string;
    status: http.RepairStatus;
    repairedContent: string;
}

function replaceLast() {
    let first = true;
    return function (stream: StringChunkStream, msg: string): TaskProgressChunk {
        if (first) {
            first = false;
            return stream.flushReplaceLast(msg);
        }
        else {
            return stream.flush(msg);
        }
    };
}

class ResultTool {
    card: Card;
    targetFileSize = 0;
    scanResult = new Map<string, FileResult>();
    vulCount = new VulCount();
    cards = new Map<string, FileFlaw>();
    repairableFiles = new Map<string, RepairFile>();
    repairType: number;
    repairableVulCount = 0;
    repairedVulCount = new VulCount();

    constructor(repairType: number) {
        this.repairType = repairType;
        this.card = {
            loading: true,
            type: 'scan',
            children: [],
            bottomTip: '',
        };
    }

    *getScanFileCards(): Generator<string> {
        const fileCards: FileFlaw[] = [];
        this.card.children = fileCards;
        this.card.bottomTip = '正在扫描目标文件';
        for (const [f, scanRes] of this.scanResult) {
            const vulLines: Flaw[] = [];

            for (const v of scanRes.result) {
                this.vulCount.incr(v.vul.ruleLevel);

                const vulDetail: FlawDetail = {
                    title: v.vul.ruleName,
                    id: v.vul.cveID,
                    flawType: flawTagMap.get(v.vul.ruleLevel) ?? FlawTag.none,
                    importPath: v.vul.importPath,
                    description: v.vul.ruleDescription,
                    advice: v.vul.ruleSuggestion,
                };

                if (v.vul.reference.length > 0) {
                    const references: Link[] = [];
                    for (const ref of v.vul.reference) {
                        references.push({title: ref.name, url: ref.url});
                    }
                    vulDetail.references = references;
                }

                vulLines.push({
                    title: v.vul.ruleName,
                    start: v.vul.line,
                    end: v.vul.line,
                    flawType: flawTagMap.get(v.vul.ruleLevel) ?? FlawTag.none,
                    fixStatus: RepairableStatusTag.none,
                    details: vulDetail,
                });
            }

            const fileCard: FileFlaw = {
                fixStatus: RepairStatusTag.none,
                children: vulLines,
                fileName: path.basename(f),
                filePath: f,
                description: '',
                content: '',
            };

            fileCards.push(fileCard);
            this.cards.set(f, fileCard);

            // 要求一个个输出
            yield `\`\`\`secubot\n${JSON.stringify(this.card)}\n\`\`\``;
        }

        this.card.bottomTip = `已完成漏洞扫描，共扫描到 ${this.scanResult.size} 个文件的 ${this.vulCount.sum} 个漏洞`;
        yield `\`\`\`secubot\n${JSON.stringify(this.card)}\n\`\`\``;
    }

    stopLoading(): string {
        this.card.loading = false;
        return `\`\`\`secubot\n${JSON.stringify(this.card)}\n\`\`\``;
    }

    processRepairableFile() {
        this.repairableVulCount = 0;
        for (const [f, res] of this.scanResult) {
            const vuls: RepairVul[] = [];
            const fileCard = this.cards.get(f);
            if (!fileCard) {
                // 不可能
                continue;
            }
            for (let i = 0; i < res.result.length; i++) {
                const vul = res.result[i];
                if ((this.repairType & vul.type) === 0) {
                    // 没开修复
                    continue;
                }
                if (vul.repair) {
                    // 可修复
                    vuls.push({
                        type: vul.type,
                        ruleID: vul.repair.file.vulList[0].ruleID,
                        detail: vul.vul,
                        card: fileCard.children[i],
                        hash: vul.hash,
                    });
                    this.repairableVulCount++;
                }
            }
            if (vuls.length > 0) {
                this.repairableFiles.set(f, {hash: res.hash, vuls: vuls, card: fileCard});
            }
        }
    }

    summary(fix: boolean): [string, string] {
        const table: SummaryTableProps = {
            type: 'summaryTable',
            sum: this.vulCount.getCounts(),
            fix: fix ? this.repairedVulCount.getCounts() : [],
        };

        for (const f of this.repairableFiles.values()) {
            f.vuls.forEach(v => {
                v.card.fixStatus = RepairableStatusTag.true; // 树结构需要显示可修复标记
            });
        }
        const fileTree: FlawTree = {
            type: 'flawTree',
            children: [...this.cards.values()],
        };

        return [
            `\`\`\`secubot\n${JSON.stringify(table)}\n\`\`\``,
            `\`\`\`secubot\n${JSON.stringify(fileTree)}\n\`\`\``,
        ];
    }
}

export class SmartCodeScanSkillProvider extends SkillProvider {
    static skillName = 'smartCodeScan';

    static description = '代码安全检测';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    private readonly cwd: string;
    private readonly skill: string;
    private readonly traceID: string;
    private inner: boolean = false;
    private readonly scanSettings: ScanSettings = {
        repair: 0,
    };
    private readonly stream: StringChunkStream;
    private readonly chatID: string;
    private status = errors.Code.succ;
    private errMsg = '';
    private readonly chatVuls: http.ChatFileVul[] = [];

    constructor(init: ProviderInit) {
        super(init);
        this.disableLogging('fs');
        this.cwd = init.cwd;
        this.skill = SmartCodeScanSkillProvider.name;
        this.traceID = crypto.randomUUID();
        this.stream = new StringChunkStream();
        this.chatID = this.currentContext.data.messageId ?? '';
    }

    private async isInner(): Promise<boolean> {
        try {
            const host = await this.retriever.hostInfo();
            this.logger.info('isInner', this.logContent({message: `platform: ${host.platform}`}));
            return host.platform === Platform.inner;
        }
        catch (error) {
            this.logger.error(
                'isInner',
                this.logContent({
                    message: 'get platform error',
                    error: errors.getErrorMessage(error),
                    stack: errors.getErrorStack(error),
                })
            );
            // 默认是外部版本
            return false;
        }
    }

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        this.inner = await this.isInner();
        try {
            yield* this.exec();
        }
        catch (error) {
            const logContent = {
                message: 'sast execute',
                error: errors.getErrorMessage(error),
                stack: errors.getErrorStack(error),
            };
            this.logger.error(
                'execute',
                logContent
            );
            this.status = errors.Code.errOther;
            this.errMsg = logContent.error;
            yield this.stream.flush(this.formateErrorElement('任务执行失败，出现未知错误'));
        }
        try {
            await this.postChatResult(this.status, this.errMsg, this.chatVuls);
        }
        catch (error) {
            this.logger.error(
                'execute',
                {
                    message: 'post chat',
                    error: errors.getErrorMessage(error),
                    stack: errors.getErrorStack(error),
                }
            );
        }
    }

    async *exec(): AsyncIterableIterator<TaskProgressChunk> {
        // 获取授权
        const fs = await this.requestWorkspaceFileSystem();
        if (!fs) {
            this.status = errors.Code.errPermission;
            this.errMsg = 'get fs error';
            this.logger.info('exec', this.logContent({message: `${this.errMsg}: ${fs}`}));
            yield this.stream.flush(
                this.formateErrorElement('由于缺少文件权限，无法执行任务，请授予文件系统访问权限后重试')
            );
            return;
        }

        // 检查输入
        const gen = this.checkInput(fs);
        let res = await gen.next();
        while (!res.done) {
            yield this.stream.flush(res.value);
            res = await gen.next();
        }
        if (res.value.status === Status.end) {
            return;
        }
        const scanPath = res.value.data;

        // 获取扫描配置
        const status = await this.init(fs);
        if (!status) {
            yield this.stream.flush(this.formateErrorElement('任务启动失败，出现了配置错误'));
            return;
        }

        if (this.scanSettings.repair) {
            yield this.stream.flush(
                '好的，已收到您的任务，接下来我将通过「分析代码库基本信息」「扫描安全漏洞」「修复并验证安全漏洞」\
                3 个步骤为您执行代码安全任务。\n\n'
            );
        }
        else {
            yield this.stream.flush(
                '好的，已收到您的任务，接下来我将通过「分析代码库基本信息」「扫描安全漏洞」2 个步骤为您执行代码安全任务。\n\n'
            );
        }

        const loading: LoadingText = {
            type: 'loadingText',
            content: '正在为您分析当前代码库的基本信息',
        };
        yield this.stream.flush(`\`\`\`secubot\n${JSON.stringify(loading)}\n\`\`\``); // 这句需要被覆盖
        let replaceTool = replaceLast();

        const resultTool = new ResultTool(this.scanSettings.repair);
        let targetResult = null;
        let bundleUtils = null;
        try {
            const gen = this.analysis(resultTool, scanPath);
            let res = await gen.next();
            while (!res.done) {
                yield replaceTool(this.stream, res.value);
                res = await gen.next();
            }
            if (res.value.status === Status.end) {
                return;
            }
            [targetResult, bundleUtils] = res.value.data;
        }
        catch (error) {
            const logContent = {
                message: 'analysis error',
                error: errors.getErrorMessage(error),
                stack: errors.getErrorStack(error),
            };
            this.logger.error(
                'exec',
                this.logContent(logContent)
            );
            yield replaceTool(this.stream, this.formateErrorElement('分析失败，出现未知错误'));
            this.status = errors.Code.errBundle;
            this.errMsg = `${logContent.message}: ${logContent.error}`;
            return;
        }

        // 调用大模型分析代码文件
        // yield this.stream.flushReplaceLast(
        //     '我已完成当前代码库的分析，这是一个 Java 代码库，具有 xx 个代码文件。\
        //     它是基于 SpringBoot 框架编写 Web 应用程序，\
        //     使用 MyBatis-Plus 实现数据库交互，同时依赖了 Fastjson、Log4j2 等重要的第三方组件。\n\n'
        // );

        yield this.stream.flush(
            '接下来我将基于分析的代码库基本信息为您扫描安全漏洞，漏洞可能分布在不同的文件，这需要一定的时间。\
            扫描到漏洞后，您可以点击名称查看对应的代码位置和详细说明。\n\n'
        );

        loading.content = '正在为您扫描代码库的安全漏洞';
        yield this.stream.flush(`\`\`\`secubot\n${JSON.stringify(loading)}\n\`\`\``); // 需要被覆盖
        replaceTool = replaceLast();

        let partialScan = '';
        try {
            const gen = this.scan(resultTool, targetResult, bundleUtils);
            let res = await gen.next();
            while (!res.done) {
                yield replaceTool(this.stream, res.value);
                res = await gen.next();
            }
            if (res.value.status === Status.end) {
                return;
            }

            if (
                targetResult.status === bundle.GetTargetFileStatus.countExceeded
                || targetResult.status === bundle.GetTargetFileStatus.timeout
            ) {
                partialScan = `因文件数过多未全部扫描，更大范围及多种自定义扫描能力即将上线（${
                    msgs.helpMd(this.inner)
                }）。`;
            }
            if (resultTool.scanResult.size === 0) {
                // 每次 flush 都是新的一行，所以这里要先拼起来再输出
                yield replaceTool(
                    this.stream,
                    `${partialScan}暂时没有发现任何安全问题，您可以随时唤起我重新进行安全检测。\n\n`
                );
                return;
            }

            // 输出扫描结果，要求先输出一次 loading 状态的
            const genResult = resultTool.getScanFileCards();
            let first = true;
            for (const text of genResult) {
                if (first) {
                    yield replaceTool(this.stream, text);
                    first = false;
                }
                else {
                    await sleep(streamOutputInterval); // 要求慢点输出
                    yield this.stream.flushReplaceLast(text);
                }
            }

            this.generateChatData(resultTool.scanResult);

            await sleep(500); // 要求慢点输出
            yield this.stream.flushReplaceLast(resultTool.stopLoading());
        }
        catch (error) {
            this.logger.error(
                'exec',
                this.logContent({
                    message: 'scan error',
                    error: errors.getErrorMessage(error),
                    stack: errors.getErrorStack(error),
                })
            );
            yield replaceTool(this.stream, this.formateErrorElement('扫描失败，出现未知错误'));
            return;
        }

        if (this.scanSettings.repair === 0) {
            // 没有修复，结束
            yield this.stream.flush(
                partialScan
                    + `最后我们做个总结，本次任务共检测了 ${resultTool.targetFileSize} 个文件，`
                    + `在 ${resultTool.scanResult.size} 个文件中发现 ${resultTool.vulCount.sum} 个漏洞，`
                    + '接下来您可以在下面的图表中查看归纳信息。'
            );
            // 要求每个组件分开 flush
            const [table, tree] = resultTool.summary(false);
            yield this.stream.flush(table);
            yield this.stream.flush(tree);
            return;
        }

        resultTool.processRepairableFile();
        if (resultTool.repairableVulCount === 0) {
            // 没有可修复的漏洞
            yield this.stream.flush(
                partialScan
                    + `最后我们做个总结，本次任务共检测了 ${resultTool.targetFileSize} 个文件，`
                    + `在 ${resultTool.scanResult.size} 个文件中发现 ${resultTool.vulCount.sum} 个漏洞，`
                    + '暂不支持这些漏洞的自动修复，您可以点击以下归纳信息中的文件，查看漏洞详情和修复建议，尝试手动修复。'
            );
            const [table, tree] = resultTool.summary(false);
            yield this.stream.flush(table);
            yield this.stream.flush(tree);
            return;
        }

        yield this.stream.flush(
            partialScan
                + `接下来我将对 ${resultTool.repairableFiles.size} 个`
                + `文件中的 ${resultTool.repairableVulCount} 个漏洞尝试自动修复。\n\n`
        );

        // 修复
        try {
            const gen = this.repair(resultTool);
            let res = await gen.next();
            while (!res.done) {
                yield res.value;
                res = await gen.next();
            }
            if (res.value.status === Status.end) {
                return;
            }
        }
        catch (error) {
            this.logger.error(
                'exec',
                this.logContent({
                    message: 'repair error',
                    error: errors.getErrorMessage(error),
                    stack: errors.getErrorStack(error),
                })
            );
            yield this.stream.flush(this.formateErrorElement('修复失败，出现未知错误'));
        }

        yield this.stream.flush(
            `以上是我为您扫描和修复的全部漏洞，最后我们做个总结，本次任务共检测了 ${resultTool.targetFileSize} 个文件，`
                + `在 ${resultTool.scanResult.size} 个文件中发现 ${resultTool.vulCount.sum} 个漏洞，`
                + `我们对其中的 ${resultTool.repairableVulCount} 个漏洞尝试做了自动修复。`
                + '接下来您可以在下面的图表中查看归纳信息，并通过采纳按钮来使用我给出的修复结果，\
                或者也可以参考漏洞详情和修复建议尝试手动修复。'
        );

        const [table, tree] = resultTool.summary(true);
        yield this.stream.flush(table);
        yield this.stream.flush(tree);
    }

    private async *checkInput(fs: FileSystem): AsyncGenerator<string, Result<string>> {
        let dir = '.'; // 可能是个目录，也可能是个文件
        const queryDir = this.currentContext.query.trim();
        if (queryDir.length > 0) {
            dir = queryDir;
            try {
                if (!path.isAbsolute(dir)) {
                    // 转成绝对路径，避免有 ./ 之类的
                    dir = path.resolve(this.cwd, dir);
                }

                // 最终需要转成相对路径，否则 fs 会出错
                dir = path.relative(this.cwd, dir);

                this.logger.info('checkInput', this.logContent({message: `user input path: ${dir}`}));

                const stat = await fs.stat(dir);
                if (stat.type !== 'file' && stat.type !== 'directory') {
                    const logContent = {message: 'not a file or directory'};
                    this.logger.info('checkInput', this.logContent(logContent));
                    // 不是目录也不是文件
                    yield msgs.INSTRUCTION_MD;
                    yield '\n\n' + msgs.helpMd(this.inner);
                    this.status = errors.Code.errUserInput;
                    this.errMsg = logContent.message;
                    return {status: Status.end};
                }
            }
            catch (error) {
                const logContent = {
                    message: 'parse user input error',
                    error: errors.getErrorMessage(error),
                    stack: errors.getErrorStack(error),
                };
                this.logger.info(
                    'checkInput',
                    this.logContent(logContent)
                );
                yield msgs.INSTRUCTION_MD;
                yield '\n\n' + msgs.helpMd(this.inner);

                this.status = errors.Code.errUserInput;
                this.errMsg = `${logContent.message}: ${logContent.error}`;
                return {status: Status.end};
            }
        }
        return {status: Status.continue, data: dir};
    }

    private async *askLLM(input: string): AsyncIterable<string> {
        try {
            const prompt = this.llm.createPrompt(
                `回答问题“${input}”，字数不超过50`
            );
            yield* this.llm.askForTextStreaming(prompt);
        }
        catch (error) {
            this.logger.info('askLLM', {
                message: 'ask llm error',
                error: errors.getErrorMessage(error),
                stack: errors.getErrorStack(error),
            });
        }
    }

    private async *analysis(
        resultTool: ResultTool,
        scanPath: string
    ): AsyncGenerator<string, Result<[bundle.GetTargetFileResult, BundleUtils]>> {
        const promises = [
            scanner.getTargetFiles(scanPath, scanner.upScanType(), this.traceID, true),
            sleep(streamOutputInterval),
        ];
        const res = await Promise.all(promises);

        const [targetResult, bundleUtils] = res[0] as [bundle.GetTargetFileResult, BundleUtils];
        if (!targetResult.files || targetResult.files.size === 0) {
            yield this.formateErrorElement('我已完成当前代码库的分析，暂不支持该类型代码的扫描');
            return {status: Status.end};
        }
        resultTool.targetFileSize = targetResult.files.size;
        const mana = new Set<string>();
        let isJava = false;
        for (const f of targetResult.files.keys()) {
            const baseName = path.basename(f);
            if (baseName === 'pom.xml') {
                mana.add('Maven');
            }
            else if (baseName === 'gradle.properties') {
                mana.add('Gradle');
            }
            else if (baseName.endsWith('.java')) {
                isJava = true;
            }
        }

        const text: string[] = ['我已完成当前代码库的分析'];
        if (isJava) {
            text.push('这是一个 Java 代码库');
        }
        if (mana.size > 0) {
            text.push(`它使用 ${[...mana.keys()].join('、')} 管理第三方组件`);
        }
        text.push(`存在 ${targetResult.files.size} 个需要扫描的文件。\n\n`);
        yield text.join('，');
        return {status: Status.continue, data: [targetResult, bundleUtils]};
    }

    private async *scan(
        resultTool: ResultTool,
        targetResult: bundle.GetTargetFileResult,
        bundleUtils: BundleUtils
    ): AsyncGenerator<string, Result<null>> {
        this.logger.info('scan', this.logContent({message: `targetFiles.size=${targetResult.files.size}`}));
        const cacheResult = await scanner.getTargetFileCache(targetResult.files);

        if (cacheResult.needScan.size === 0) {
            resultTool.scanResult = cacheResult.result;
            return {status: Status.continue, data: null};
        }

        // 有需要扫描的文件
        let scanType = 0;
        cacheResult.needScan.forEach(v => {
            scanType |= v.type;
        });

        this.logger.info(
            'scan',
            this.logContent({message: `need scan: size=${cacheResult.needScan.size}, type=${scanType}`})
        );

        const baseOptions = context.connectOptions(this.traceID, this.chatID);
        const fileUtils = new bundle.SASTFileUtils(context.fs, context.cwd, this.logger);
        const bundleResponse = await fileUtils.uploadFiles(baseOptions, bundleUtils.getFileHash(), this.traceID);
        if (bundleResponse.type === http.ResultType.error) {
            this.logger.error(
                'scan',
                this.logContent({
                    message: 'uploadFiles',
                    error: bundleResponse.error.text,
                    api: bundleResponse.error.apiName,
                    stack: bundleResponse.error.detail ?? '',
                })
            );
            this.status = errors.Code.errBundle;
            this.errMsg = bundleResponse.error.text;
            if (bundleResponse.error.code === http.ResultErrorCode.networkErr) {
                yield this.formateErrorElement('扫描失败，出现了网络错误');
                this.errMsg = `network error: ${bundleResponse.error.text}`;
            }
            else {
                yield this.formateErrorElement('扫描失败，出现了未知错误');
            }
            return {status: Status.end};
        }

        // 扫描
        const analysisOptions: http.GetAnalysisOptions = {
            ...baseOptions,
            bundleHash: bundleResponse.value,
            trigger: http.Trigger.manual,
            scanType: scanType,
            initiator: context.ideName,
            gitInfo: context.gitInfo(),
            limitToFiles: bundleUtils.getLimitToFiles(cacheResult.needScan) ?? undefined,
        };

        const startTime = Date.now();
        do {
            const analysisResponse = await http.getAnalysis(analysisOptions);
            if (analysisResponse.type === http.ResultType.error) {
                this.logger.error(
                    'scan',
                    this.logContent({
                        message: 'getAnalysis',
                        error: analysisResponse.error.text,
                        api: analysisResponse.error.apiName,
                        stack: analysisResponse.error.detail ?? '',
                    })
                );
                if (analysisResponse.error.code === http.ResultErrorCode.networkErr) {
                    // 网络问题
                    this.logger.info(
                        'scan',
                        this.logContent({
                            message: 'getAnalysis: network error, retry',
                            api: analysisResponse.error.apiName,
                        })
                    );
                    await sleep(ScannerUtils.resultCheckInterval);
                    continue;
                }
                yield this.formateErrorElement('扫描失败，出现了未知错误');
                this.status = errors.Code.errScanReq;
                this.errMsg = analysisResponse.error.text;
                return {status: Status.end};
            }
            if (analysisResponse.value.status === http.AnalysisStatus.completed) {
                try {
                    const res = scanner.formatResult(
                        this.traceID,
                        bundleUtils.getScannedFiles(cacheResult.needScan, scanType),
                        bundleUtils.getFileHash(),
                        analysisResponse.value.data.runs
                    );
                    scanner.cacheResult(scanType, res);
                    // 合并结果
                    const merge = mergeResult(res, cacheResult);
                    resultTool.scanResult = merge;

                    return {status: Status.continue, data: null};
                }
                catch (error) {
                    const logContent = {
                        message: 'formatResult',
                        error: errors.getErrorMessage(error),
                        stack: errors.getErrorStack(error),
                    };
                    this.logger.error(
                        'scan',
                        this.logContent(logContent)
                    );

                    yield this.formateErrorElement('扫描失败，出现了未知错误');
                    this.status = errors.Code.errScanRes;
                    this.errMsg = logContent.error;
                }
                return {status: Status.end};
            }

            await sleep(ScannerUtils.resultCheckInterval);
        }
        while (ScannerUtils.scanTimeout > Date.now() - startTime);

        yield this.formateErrorElement('扫描失败，超时');
        this.status = errors.Code.errScanTimeout;
        return {status: Status.end};
    }

    private async *repair(resultTool: ResultTool): AsyncGenerator<TaskProgressChunk, Result<null>> {
        const repairRes = new Map<string, RepairResult>();
        let index = 1;
        const fileCards: FileFlaw[] = [];
        const card: Card = {
            type: 'repair',
            loading: true,
            children: fileCards,
            bottomTip: '',
        };
        let repairableVulCount = resultTool.repairableVulCount;
        let repairableFileCount = resultTool.repairableFiles.size;
        let succfileCount = 0;
        let lastFlushTime = Date.now();
        for (const [f, res] of resultTool.repairableFiles) {
            const repairMsg: string[] = [`第 ${index} 个文件是 ${f}`];
            let type = 0;
            const vuls: http.RepairVulItem[] = [];
            const vulRepairDes = new Map<string, string>();
            const repairVulCount = new VulCount();

            const vulFileCard: FileFlaw = {...res.card};
            const vulCard: Flaw[] = [];
            vulFileCard.children = vulCard;

            for (const vul of res.vuls) {
                type |= vul.type;
                vuls.push({ruleID: vul.ruleID, line: vul.detail.line, hash: vul.hash});
                vulCard.push(vul.card);
                const des = repairDescription.get(vul.detail.ruleName);
                if (des && vulRepairDes.size < 2) {
                    // 只保留两个
                    let ruleName = vul.detail.ruleName;
                    if (startsWithLetter(vul.detail.ruleName)) {
                        // 英文字母开头的加个前置空格
                        ruleName = ' ' + ruleName;
                    }
                    vulRepairDes.set(ruleName, des);
                }
                repairVulCount.incr(vul.detail.ruleLevel);
            }
            if (vulRepairDes.size > 0) {
                repairMsg.push(
                    `，它存在${[...vulRepairDes.keys()].join('和')}，您可以点击漏洞名称查看漏洞详情。${
                        [...vulRepairDes.values()].join('，')
                    }，最终的修复代码我将帮您进行二次验证，以确保安全有效，文件如下：`
                );
            }
            else {
                repairMsg.push(
                    '，您可以点击漏洞名称查看漏洞详情。最终的修复代码我将帮您进行二次验证，以确保安全有效，文件如下：'
                );
            }

            vulFileCard.description = repairMsg.join('');
            vulFileCard.fixStatus = RepairStatusTag.repairing;
            fileCards.push(vulFileCard);

            card.bottomTip = `剩余 ${repairableFileCount} 个文件中的 ${repairableVulCount} 个漏洞待修复。`;
            if (index === 1) {
                yield this.stream.flush(`\`\`\`secubot\n${JSON.stringify(card)}\n\`\`\``);
            }
            else {
                yield this.stream.flushReplaceLast(`\`\`\`secubot\n${JSON.stringify(card)}\n\`\`\``);
            }

            // yield 文件和漏洞 running
            const gen = this.repairFile({name: f, hash: res.hash, vulList: vuls}, type);
            let fileRes = await gen.next();
            while (!fileRes.done) {
                // 更新修复状态
                vulFileCard.fixStatus = repairStatusTagMap.get(fileRes.value) ?? RepairStatusTag.none;
                yield this.stream.flushReplaceLast(`\`\`\`secubot\n${JSON.stringify(card)}\n\`\`\``);

                fileRes = await gen.next();
            }

            if (fileRes.value.status === Status.end) {
                vulFileCard.fixStatus = RepairStatusTag.fail;
            }
            else {
                // 修复成功
                repairRes.set(f, fileRes.value.data);
                vulFileCard.fixStatus = RepairStatusTag.success;
                vulFileCard.content = fileRes.value.data.repairedContent;
                res.card.content = fileRes.value.data.repairedContent;
                succfileCount++;
                resultTool.repairedVulCount.addCount(repairVulCount);
            }
            repairableVulCount -= res.vuls.length;
            repairableFileCount--;
            card.bottomTip = `剩余 ${repairableFileCount} 个文件中的 ${repairableVulCount} 个漏洞待修复。`;

            const leftTime = streamOutputInterval - (Date.now() - lastFlushTime);
            if (leftTime > 0) {
                // 慢点输出
                await sleep(leftTime);
            }
            yield this.stream.flushReplaceLast(`\`\`\`secubot\n${JSON.stringify(card)}\n\`\`\``);
            lastFlushTime = Date.now();

            index++;
        }

        card.bottomTip = `已完成修复并验证安全漏洞，共修复 ${succfileCount} 个`
            + `文件中的 ${resultTool.repairedVulCount.sum} 个漏洞`;
        card.loading = false;
        await sleep(streamOutputInterval);
        yield this.stream.flushReplaceLast(`\`\`\`secubot\n${JSON.stringify(card)}\n\`\`\``);

        return {status: Status.continue, data: null};
    }

    private async *repairFile(
        data: http.RepairItem,
        type: number
    ): AsyncGenerator<http.RepairStatus, Result<RepairResult>> {
        const repairTraceID = crypto.randomUUID();
        const baseOptions = context.connectOptions(repairTraceID, this.chatID);
        const requestOptions: http.RepairOptions = {
            ...baseOptions,
            files: [data],
            type: type,
            trigger: http.RepairTrigger.smart,
        };

        let preStatus = http.RepairStatus.running;
        const startTime = Date.now();
        do {
            const repairRes = await http.repairFile(requestOptions);

            if (repairRes.type === http.ResultType.error) {
                this.logger.error(
                    'repairFile',
                    this.logContent({
                        repairTraceID: repairTraceID,
                        error: repairRes.error.text,
                        api: repairRes.error.apiName,
                        stack: repairRes.error.detail ?? '',
                    })
                );
                return {status: Status.end};
            }

            if (repairRes.value.status === http.RepairStatus.missingFile) {
                this.logger.info('repairFile', this.logContent({repairTraceID: repairTraceID, message: 'miss file'}));

                const fileRes: CheckFileResult = await this.checkFile(data.name, data.hash);
                switch (fileRes.status) {
                    case CheckFileStatus.changed:
                        this.logger.info(
                            'repairFile',
                            this.logContent({repairTraceID: repairTraceID, message: 'file not exists'})
                        );
                        return {status: Status.end};
                    case CheckFileStatus.error:
                        return {status: Status.end};
                    case CheckFileStatus.ok: {
                        const files: http.BundleFiles = {};
                        files[data.name] = {
                            hash: data.hash,
                            content: fileRes.content,
                        };
                        const uploadRes = await http.uploadFile({...baseOptions, files: files});
                        if (uploadRes.type === http.ResultType.error) {
                            this.logger.error(
                                'repairFile',
                                this.logContent({
                                    repairTraceID: repairTraceID,
                                    error: uploadRes.error.text,
                                    api: uploadRes.error.apiName,
                                    stack: uploadRes.error.detail ?? '',
                                })
                            );
                            if (uploadRes.error.code === http.ResultErrorCode.networkErr) {
                                this.logger.info(
                                    'repairFile',
                                    this.logContent({repairTraceID: repairTraceID, message: 'network error'})
                                );
                            }
                            return {status: Status.end};
                        }
                        break;
                    }
                    default:
                        this.logger.info(
                            'repairFile',
                            this.logContent({repairTraceID: repairTraceID, message: 'check file error'})
                        );
                        return {status: Status.end};
                }
            }
            else {
                this.logger.info('repairFile', {repairTraceID: repairTraceID, data: repairRes.value.data.files});
                const fileRes = this.checkRepairResult(repairRes.value.data.files);
                if (!fileRes || fileRes.hash !== data.hash) {
                    this.logger.info(
                        'repairFile',
                        this.logContent({
                            repairTraceID: repairTraceID,
                            message: `check result error, file=${fileRes?.hash}`,
                        })
                    );

                    const opt = context.reportOptions(
                        this.traceID,
                        this.skill,
                        `repair error: result format error: file=${data.name}, hash=${data.hash}`,
                        ''
                    );
                    await reportError(this.logger, opt);
                    return {status: Status.end};
                }
                if (fileRes.status === http.RepairStatus.finished) {
                    return {status: Status.continue, data: fileRes};
                }
                // 执行中，更新状态
                if (preStatus !== fileRes.status) {
                    yield fileRes.status;
                    preStatus = fileRes.status;
                }
            }
            await sleep(Repair.checkInterval);
        }
        while (Repair.timeout > Date.now() - startTime);
        this.logger.info(
            'repairFile',
            this.logContent({repairTraceID: repairTraceID, message: 'timeout'})
        );
        return {status: Status.end};
    }

    private checkRepairResult(res: http.RepairFile[]): RepairResult | null {
        try {
            const repairRes = res[0];
            if (typeof repairRes.hash !== 'string' || repairRes.hash === '') {
                throw new Error(`file hash error: ${repairRes.hash}`);
            }

            if (typeof repairRes.repairedContent !== 'string') {
                throw new Error(`content error: ${repairRes.repairedContent}`);
            }
            if (typeof repairRes.status !== 'number') {
                throw new Error(`status error: ${repairRes.status}`);
            }
            if (repairRes.status === http.RepairStatus.finished && repairRes.repairedContent === '') {
                throw new Error(`content empty: ${repairRes.repairedContent}`);
            }
            return {
                hash: repairRes.hash,
                language: repairRes.language ? repairRes.language.toLowerCase() : '',
                status: repairRes.status,
                repairedContent: repairRes.repairedContent,
            };
        }
        catch (error) {
            this.logger.error(
                'checkRepairResult',
                this.logContent({
                    message: 'repair result error',
                    error: errors.getErrorMessage(error),
                    stack: errors.getErrorStack(error),
                })
            );
        }
        return null;
    }

    private async checkFile(name: string, hash: string): Promise<CheckFileResult> {
        try {
            const [currentHash, content] = await readFile(context.fs, name);
            if (currentHash !== hash) {
                return {status: CheckFileStatus.changed, content: ''};
            }

            return {status: CheckFileStatus.ok, content: content};
        }
        catch (error) {
            this.logger.error(
                'checkFile',
                this.logContent(
                    {
                        error: errors.getErrorMessage(error),
                        message: 'get file content error',
                        file: name,
                        stack: errors.getErrorStack(error),
                    }
                )
            );
            return {status: CheckFileStatus.error, content: ''};
        }
    }

    private formateErrorElement(msg: string): string {
        return `${msg}，您可以通过下面的联系方式向我的开发者反馈：[联系我们](${msgs.helpLink(this.inner)})。\n\n`;
    }

    private logContent(content: LogRecord<string, any>): LogRecord<string, any> {
        return {
            traceID: this.traceID,
            // time: new Date().toString(),
            ...content,
        };
    }

    private async init(fs: FileSystem): Promise<boolean> {
        // 初始化 context
        context = context ?? getComateContext();
        if (!context) {
            const userDetail = await this.currentUser.requestDetail();
            const userName = userDetail ? userDetail.name : '';
            const repository = await this.retriever.repositoryInfo();
            const host = await this.retriever.hostInfo();

            context = initComateContext(
                this.inner,
                this.currentUser.id,
                userName,
                this.cwd,
                fs,
                this.logger,
                repository,
                host.appName
            );
        }

        scanner = scanner ?? await initScanner(context);

        const baseOptions = context.connectOptions(this.traceID, this.chatID);

        const settingResp = await settings(baseOptions);
        if (settingResp.type === http.ResultType.error) {
            // 获取失败就结束
            this.logger.error(
                'init',
                this.logContent({
                    message: 'get settings error',
                    error: settingResp.error.text,
                    api: settingResp.error.apiName,
                    stack: settingResp.error.detail ?? '',
                })
            );
            this.status = errors.Code.errSetting;
            this.errMsg = settingResp.error.text;
            return false;
        }

        const res = settingResp.value;
        const s = this.scanSettings;

        try {
            await scanner.syncConfig(res);

            if (res.scanConfiguration.sca.autofixedEnabled) {
                s.repair |= http.ScanType.sca;
            }

            if (res.scanConfiguration.sast.autofixedEnabled) {
                s.repair |= http.ScanType.sast;
            }

            this.logger.info('init', this.logContent({message: 'get settings', data: this.scanSettings}));
        }
        catch (error) {
            const logContent = {
                message: 'parse settings error',
                error: errors.getErrorMessage(error),
                stack: errors.getErrorStack(error),
            };
            this.logger.error(
                'init',
                this.logContent(logContent)
            );
            this.status = errors.Code.errSetting;
            this.errMsg = `${logContent.message}: ${logContent.error}`;
            return false;
        }
        return true;
    }

    private generateChatData(data: Map<string, FileResult>) {
        for (const [file, v] of data) {
            const vuls: http.ChatVul[] = [];
            for (const vul of v.result) {
                vuls.push({
                    vulHash: vul.hash,
                });
            }
            const fileVuls: http.ChatFileVul = {
                fileHash: v.hash,
                fileName: file,
                vuls: vuls,
            };
            this.chatVuls.push(fileVuls);
        }
    }

    private async postChatResult(status: errors.Code, errMsg: string, data: any = []) {
        this.logger.info('postChatResult', this.logContent({message: `chatID=${this.chatID}`}));
        if (this.chatID === '') {
            // 没有 chatID 就不推了
            return;
        }

        let msg = errors.errMsg.get(status) ?? 'unknown';
        if (msg !== '') {
            msg += `: ${errMsg}`;
        }

        const baseOptions = context.connectOptions(this.traceID, this.chatID);
        const requestOptions: http.NewChatOptions = {
            ...baseOptions,
            type: http.ChatType.agentMain,
            gitInfo: context.gitInfoFirst(),
            ide: context.ideName,
            query: this.currentContext.query,
            status: status,
            errMsg: msg,
            vuls: data,
        };

        const res = await http.newChat(requestOptions);
        if (res.type === http.ResultType.error) {
            this.logger.error(
                'postChatResult',
                this.logContent({
                    message: 'post chat result error',
                    error: res.error.text,
                    api: res.error.apiName,
                    stack: res.error.detail ?? '',
                })
            );
        }
    }
}
