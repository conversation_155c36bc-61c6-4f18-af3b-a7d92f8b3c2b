import path from 'node:path';

import {
    SkillProvider,
    FunctionParameterDefinition,
    ElementChunkStream,
    TaskProgressChunk,
    DiagnosticScanHandler,
    ProviderInit,
    DiagnosticScanTaskProgressChunk,
} from '@comate/plugin-host';
import {DiagnosticScanChangedFiles, DiagnosticScanTypes} from '@comate/plugin-shared-internals';

import {Repair} from '../sast/repair.js';
import {ComateContext, initComateContext, getComateContext} from '../sast/context.js';
import {Platform} from '../utils/types.js';
import {getErrorMessage, getErrorStack} from '../utils/error.js';
import {initScanner, ScannerUtils, serverityMap} from '../sast/scanner.js';
import {helpChunk} from '../sast/message.js';
import {SASTFileUtils} from '../sast/bundle.js';
import {settings} from '../sast/settings.js';
import * as http from '../utils/http.js';

interface ScanSettings {
    autoScan: boolean;
}

// 每次调 scanDiagnostics 都会重新初始化 DiagnosticScanProvider，所以做个缓存，避免每次都重新初始化
let context: ComateContext;
let scanner: ScannerUtils;

export class DiagnosticScanProvider extends SkillProvider implements DiagnosticScanHandler {
    static skillName = 'diagnosticScan';
    static displayName = '诊断扫描';
    static description = '提供诊断扫描能力';
    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    private readonly scanSettings: ScanSettings = {
        autoScan: false,
    };
    private readonly cwd: string;

    constructor(init: ProviderInit) {
        super(init);
        this.disableLogging('fs');
        this.cwd = init.cwd;
    }

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new ElementChunkStream();
        if (!context) {
            // 不可能，可以修复一定是先扫描了，扫描一定会初始化 context
            yield stream.flushReplaceLast(<p>修复失败。</p>);
        }

        const data = this.currentContext.data;
        this.logger.info('execute', {message: 'diagnostic repair', data: data});
        try {
            const repair = new Repair(
                data.traceID,
                stream,
                DiagnosticScanProvider.name,
                context
            );
            yield* repair.run(data, http.RepairTrigger.wave);
        }
        catch (error) {
            this.logger.error('execute', {
                message: 'repair',
                error: getErrorMessage(error),
                stack: getErrorStack(error),
            });
            yield stream.flushReplaceLast(<p>修复失败。</p>);
        }
        yield stream.flush(helpChunk(context.isInner));
    }

    async *scanDiagnostics(
        scanType: DiagnosticScanTypes,
        changedFiles: DiagnosticScanChangedFiles
    ): AsyncIterableIterator<DiagnosticScanTaskProgressChunk> {
        try {
            await this.init();
            if (!context || !scanner) {
                return;
            }

            if (scanType === 'init') {
                this.logger.info('scanDiagnostics', {message: 'get request: init'});
                if (this.scanSettings.autoScan) {
                    await scanner.waitForScan();
                }
                return;
            }
            if (scanType === 'open') {
                this.logger.info('scanDiagnostics', {
                    message: 'get request: open',
                    files: this.currentContext.activeFilePath,
                });
                yield* this.diagnose([this.currentContext.activeFilePath], false);
                return;
            }
            if (scanType === 'change') {
                // 绝对路径
                const {modified, added, deleted} = changedFiles;
                this.logger.info('scanDiagnostics', {
                    message: 'get request: change',
                    modifiedFiles: modified,
                    addedFiles: added,
                    deletedFiles: deleted,
                });
                if (modified.length > 0 || added.length > 0) {
                    yield* this.diagnose([...added, ...modified], true);
                }
                if (deleted.length > 0) {
                    yield* this.removeTargetFile(deleted);
                }
            }
        }
        catch (error) {
            this.logger.error('scanDiagnostics', {
                message: 'get diagnosis error',
                error: getErrorMessage(error),
                stack: getErrorStack(error),
            });
        }
    }

    private async *diagnose(
        filePathList: readonly string[],
        change: boolean
    ): AsyncIterableIterator<DiagnosticScanTaskProgressChunk> {
        const files: string[] = [];
        for (const f of filePathList) {
            if (this.ignore(f)) {
                // 不会扫描的文件，忽略
                continue;
            }
            const relativePath = path.relative(this.cwd, f);
            files.push(relativePath);
        }

        if (files.length === 0) {
            return;
        }
        yield* this.getCache(files);

        if (change && this.scanSettings.autoScan) {
            // 文件发生变更，并且自动扫描开启
            await scanner.addChangedFiles(files);
        }

        // else if (needScan.size > 0) {
        //     // 如果文件没有变更，但是缓存没有结果，就当做变更文件，走一遍流程
        //     await scanner.addChangedFiles([...needScan])
        // }
        // else {
        //     // 除了文件变更，可能存在配置变更需要扫描的情况
        //     await scanner.waitForScan()
        // }
    }

    private async *getCache(files: string[]): AsyncIterableIterator<DiagnosticScanTaskProgressChunk> {
        const [res, needScan] = await scanner.getFileCache(files);

        for (const [f, r] of res) {
            const diagnosis: any[] = [];
            for (const v of r) {
                if (v.wave !== true) {
                    continue;
                }
                if (v.vul.line === 0) {
                    continue;
                }
                if (v.vul.ruleLevel !== 'ERROR' && v.vul.ruleLevel !== 'WARNING') {
                    continue;
                }

                const msg = [
                    `漏洞名称：[${serverityMap.get(v.vul.ruleLevel)}]${
                        v.vul.isNecessary ? '[必修]' : ''
                    } ${v.vul.ruleName}`,
                ];
                if (v.vul.cveID !== '') {
                    msg.push(`漏洞编号：${v.vul.cveID}`);
                }
                msg.push(`漏洞描述：${v.vul.ruleDescription}`);
                msg.push(`修复建议：${v.vul.ruleSuggestion}`);
                if (v.vul.importPath !== '') {
                    msg.push(`引入路径：${v.vul.importPath}`);
                }
                const htmlMsg = [...msg];
                if (v.vul.reference.length > 0) {
                    msg.push(`参考文档：${v.vul.reference[0].url}`);
                    htmlMsg.push(`参考文档：<a href="${v.vul.reference[0].url}">${v.vul.reference[0].name}</a>`);
                }
                const code = v.vul.sourceCode.trimStart();
                const startCol = v.vul.sourceCode.length - code.length;
                diagnosis.push({
                    textMessage: msg.join('\n'),
                    htmlMessage: htmlMsg.join('<br>'),
                    source: '百度安全',
                    severity: v.vul.ruleLevel,
                    range: {
                        startLine: v.vul.line,
                        startCharacter: startCol + 1,
                        endLine: v.vul.line,
                        endCharacter: v.vul.sourceCode.length + 1,
                    }, // 从 1 开始
                    repairData: {
                        pluginName: 'security',
                        capability: 'diagnosticScan',
                        query: '发起修复',
                        data: v.repair,
                    },
                    contextCode: [],
                    sourceCode: code,
                    code: context.isInner
                        ? {
                            value: '使用指南',
                            target:
                                'https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/idGHtE2EDf/uIO7T0dV7K/xPO7NHNX7sBdpY',
                        }
                        : undefined,
                });
            }

            this.logger.info('getCache', {message: `return diagnosis: size=${diagnosis.length}`, file: f});
            yield {
                filename: path.resolve(this.cwd, f), // 绝对路径
                diagnostics: diagnosis,
            };
        }
    }

    private ignore(p: string): boolean {
        try {
            if (!p.startsWith(this.cwd)) {
                return true;
            }

            if (SASTFileUtils.isIgnoredPath(p)) {
                return true;
            }

            return false;
        }
        catch (error) {
            this.logger.error(
                'ignore',
                {
                    error: getErrorMessage(error),
                    message: 'ignore error',
                    file: p,
                    stack: getErrorStack(error),
                }
            );
        }
        return true;
    }

    private async *removeTargetFile(absolutePath: string[]): AsyncIterableIterator<DiagnosticScanTaskProgressChunk> {
        try {
            // 删除文件记录
            for (const f of absolutePath) {
                if (this.ignore(f)) {
                    continue;
                }
                yield {
                    filename: f, // 绝对路径
                    diagnostics: [],
                };
                await scanner.removeFile(f);
            }
        }
        catch (error) {
            this.logger.error(
                'removeTargetFile',
                {
                    error: getErrorMessage(error),
                    message: 'remove file error',
                    file: absolutePath,
                    stack: getErrorStack(error),
                }
            );
        }
    }

    private async isInner(): Promise<boolean> {
        try {
            const host = await this.retriever.hostInfo();
            this.logger.info('isInner', {message: `platform: ${host.platform}`});
            return host.platform === Platform.inner;
        }
        catch (error) {
            this.logger.error('isInner', {
                message: 'get platform error',
                error: getErrorMessage(error),
                stack: getErrorStack(error),
            });
            // 默认是外部版本
            return false;
        }
    }

    private async init() {
        context = context ?? getComateContext();
        if (!context) {
            const fs = await this.requestWorkspaceFileSystem();
            if (!fs) {
                // 无文件权限，不能继续扫描
                return;
            }

            const userDetail = await this.currentUser.requestDetail();
            const userName = userDetail ? userDetail.name : '';

            const host = await this.retriever.hostInfo();
            const repository = await this.retriever.repositoryInfo();

            // 全局变量
            context = initComateContext(
                await this.isInner(),
                this.currentUser.id,
                userName,
                this.cwd,
                fs,
                this.logger,
                repository,
                host.appName
            );
        }

        const traceID = crypto.randomUUID();
        const settingResp = await settings(context.connectOptions(traceID));
        if (settingResp.type === http.ResultType.error) {
            // 获取失败就结束
            this.logger.error(
                'init',
                {
                    traceID: traceID,
                    message: 'get settings error',
                    error: settingResp.error.text,
                    api: settingResp.error.apiName,
                    stack: settingResp.error.detail ?? '',
                }
            );
            return;
        }

        try {
            this.scanSettings.autoScan = settingResp.value.scanConfiguration.autoScanEnabled;

            let tmp;
            if (!scanner) {
                tmp = await initScanner(context);
            }
            else {
                tmp = scanner;
            }
            await tmp.syncConfig(settingResp.value);
            scanner = tmp; // 首次同步配置成功，scanner 才有值

            this.logger.info('init', {message: 'get settings', data: this.scanSettings});
        }
        catch (error) {
            this.logger.error(
                'init',
                {
                    message: 'process settings error',
                    error: getErrorMessage(error),
                    stack: getErrorStack(error),
                }
            );
        }
    }
}
