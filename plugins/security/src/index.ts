import {PluginSetupContext} from '@comate/plugin-host';
import {CodeScanSkillProvider} from './providers/scan.js';
import {DiagnosticScanProvider} from './providers/diagnosticScan.js';
import {RepairSkillProvider} from './providers/vulRepair.js';
import {SmartCodeScanSkillProvider} from './providers/smartScan.js';

export function setup({registry}: PluginSetupContext) {
    // registry.registerFallbackProvider('default', ScanFallbackProvider);
    registry.registerSkillProvider('codeScan', CodeScanSkillProvider);
    registry.registerSkillProvider('secubot', SmartCodeScanSkillProvider); // 智能体
    registry.registerSkillProvider('vulRepair', RepairSkillProvider);
    registry.registerSkillProvider('diagnosticScan', DiagnosticScanProvider);
}
