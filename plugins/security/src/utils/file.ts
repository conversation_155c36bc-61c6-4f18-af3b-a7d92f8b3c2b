import fs from 'node:fs/promises';
import crypto from 'crypto';

import {FileSystem} from '@comate/plugin-host';

// 判断是否为文件，需要传入绝对路径
export async function isPathFile(path: string) {
    const stat = await fs.stat(path);
    return stat.isFile();
}

// 判断是否为目录，需要传入绝对路径
export async function isPathDirectory(path: string) {
    const stat = await fs.stat(path);
    return stat.isDirectory();
}

export async function readFile(fs: FileSystem, path: string): Promise<[string, string]> {
    const buf = await fs.readFile(path);
    if (buf.length === 0) {
        // 文件内容为空
        return ['', ''];
    }

    const content = buf.toString('utf8');
    // 计算文件哈希
    const hash = crypto.createHash('sha256').update(content).digest('hex');

    return [hash, content];
}

export function size(content: string): number {
    const size = new Blob([content]).size;
    return size;
}
