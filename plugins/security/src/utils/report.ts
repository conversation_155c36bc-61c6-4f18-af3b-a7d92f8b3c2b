import {report, ResultType} from './http.js';
import {getErrorMessage, getErrorStack} from './error.js';
import {BASE_URL} from './constants.js';

export interface ReportData extends Data {
    userID: string;
    userName: string;
}

export interface Data {
    traceID: string;
    skill: string;
    message?: string;
    error?: string;
    stack?: string;
}

export async function reportError(logger: any, data: ReportData) {
    try {
        const options = {
            baseURL: BASE_URL,
            userID: data.userID,
            requestID: data.traceID,
            extraHeaders: {'Comate-Username': encodeURIComponent(data.userName)},
            data: {...data as Data},
        };
        const res = await report(options);
        if (res.type === ResultType.error) {
            // 获取失败就结束
            logger.error(
                'reportError',
                {
                    traceID: options.requestID,
                    message: 'report error',
                    data: data,
                    error: res.error.text,
                    api: res.error.apiName,
                    stack: res.error.detail ?? '',
                }
            );
        }
    }
    catch (error) {
        logger.error('reportError', {
            traceID: data.traceID,
            message: '',
            data: data,
            error: getErrorMessage(error),
            stack: getErrorStack(error),
        });
    }
}
