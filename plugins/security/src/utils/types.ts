import crypto from 'node:crypto';

export const sleep = (duration: number) => new Promise(resolve => setTimeout(resolve, duration));

export type LogRecord<K extends keyof any, T> = {
    [P in K]: T;
};

export class Base {
    private _traceID: string = crypto.randomUUID();

    constructor(traceID?: string) {
        if (traceID) {
            this._traceID = traceID;
        }
    }

    get traceID(): string {
        return this._traceID;
    }
    set traceID(requestID: string) {
        this._traceID = requestID;
    }

    logContent(content: LogRecord<string, any>): LogRecord<string, any> {
        return {
            traceID: this._traceID,
            // time: new Date().toString(),
            ...content,
        };
    }
}

export type FilterFunction = (path: string) => boolean;
export const defaultPathFilter: FilterFunction = () => false;

export enum Platform {
    inner = 'baidu-int',
    saas = 'baidu-saas',
}

export const eqSet = (a: Set<any>, b: Set<any>) =>
    a.size === b.size
    && [...a].every(x => b.has(x));

export function startsWithLetter(str: string) {
    // 判断是否英文字母开头
    return /^[A-Za-z]/.test(str);
}
