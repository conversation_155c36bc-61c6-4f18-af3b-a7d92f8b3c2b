export const HTTP_TIMEOUT = 60 * 1000; // http 请求超时时间
export const HTTP_TIMEOUT_UPLOAD_FILE = 5 * 60 * 1000; // http 请求超时时间
export const SYNC_SETTING_INTERVAL = 10 * 60 * 1000; // 同步配置的默认间隔，10m
export const BASE_URL = process.env.ENVIRONMENT === 'test'
    ? 'https://comate-sec-test.baidu-int.com/api'
    : 'https://comate-sec.baidu-int.com/api';
export const BASE_URL_SAAS = 'https://comate-sec.baidu.com/api';
export const ERR_TIMEOUT = new Error('timeout');
export const HTTP_RETRY = 3000;
export const HTTP_RETRY_COUNT = 3;
export const CLIENT_VERSION = '2.6.4'; // 插件版本

export function baseURL(inner: boolean): string {
    if (inner) {
        return BASE_URL;
    }
    return BASE_URL_SAAS;
}
