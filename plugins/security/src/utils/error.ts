export enum Code {
    succ = 0,
    errOther = 1,
    errPermission = 0x1000,
    errUserInput = 0x1001,
    errSetting = 0x1002,
    errBundle = 0x2000,
    errScanReq = 0x3000,
    errScanRes = 0x3001,
    errScanTimeout = 0x3002,
}

export const errMsg = new Map<Code, string>([
    [Code.succ, ''],
    [Code.errOther, 'other'],
    [Code.errPermission, 'permission'],
    [Code.errUserInput, 'user input'],
    [Code.errSetting, 'get settings'],
    [Code.errBundle, 'bundle'],
    [Code.errScanReq, 'scan: request error'],
    [Code.errScanRes, 'scan: parse result error'],
    [Code.errScanTimeout, 'scan: timeout'],
]);

export function getErrorMessage(error: unknown) {
    if (error instanceof Error) {
        return error.message;
    }
    return String(error);
}

export function getErrorStack(error: unknown): string {
    if (error instanceof Error) {
        return error.stack ?? '';
    }
    return '';
}
