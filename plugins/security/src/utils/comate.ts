export enum RepairStatusTag {
    none = 'none',
    repairing = 'repairing',
    verifying = 'verifying',
    verifyFail = 'verifyFailed',
    success = 'success',
    fail = 'fail',
}

export enum RepairableStatusTag {
    none = 'none',
    true = 'true',
    false = 'false',
}

export enum FlawTag {
    none = 'none',
    critical = 'C',
    high = 'H',
    medium = 'M',
    low = 'L',
}

// 4个组件
export interface Card {
    loading: boolean; // card中的任务进行状态 false表示结束
    type: 'scan' | 'repair'; // 表明是哪个状态
    children: FileFlaw[]; // 有哪些文件
    bottomTip: string;
}
export interface FlawTree {
    type: string; // flawTree
    children: FileFlaw[];
}
export interface LoadingText {
    type: string; // loadingText
    content: string;
}
// 不是通用表格 仅用于该总结表格
export interface SummaryTableProps {
    type: string; // 'summaryTable'
    sum: number[];
    fix: number[];
}

export interface FileFlaw {
    fixStatus: RepairStatusTag;
    children: Flaw[]; // 漏洞的信息
    fileName: string;
    filePath: string; // 相对路径
    description: string; // 文字描述。
    content: string; // 修复后代码全文
}
// 具体漏洞
export interface Flaw {
    title: string;
    start: number;
    end: number;
    flawType: FlawTag;
    fixStatus: RepairableStatusTag; // 是否可修复 none什么都不展示 true表示可修复，显示可修复字样
    details: FlawDetail; // 漏洞详情 markdown
}
// details的栏目写死
export interface FlawDetail {
    title: string;
    id?: string;
    flawType?: FlawTag;
    importPath?: string;
    description?: string;
    advice?: string;
    references?: Link[]; // 存疑 效果里除了链接还有描述文字 是否能拿到？
}
export interface Link {
    url: string;
    title: string;
}
