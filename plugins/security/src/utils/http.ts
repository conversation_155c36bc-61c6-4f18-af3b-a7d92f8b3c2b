import {fetch as comateFetch} from '@comate/plugin-host';

import {gzip} from 'pako';

import {getErrorMessage, getErrorStack, Code} from './error.js';
import {HTTP_RETRY, HTTP_TIMEOUT, HTTP_RETRY_COUNT, CLIENT_VERSION, HTTP_TIMEOUT_UPLOAD_FILE} from './constants.js';
import {sleep} from './types.js';

export enum ResultType {
    succ = 'success',
    error = 'error,',
    cache = 'cache', // 什么都没做
}

export enum ResultErrorCode {
    other = 0,
    timeout = 1,
    networkErr = 2,
}

interface ResultSuccess<T> {
    type: ResultType.succ;
    value: T;
}

export interface ResultError {
    type: ResultType.error;
    error: {
        code: ResultErrorCode;
        text: string;
        apiName: string;
        detail?: string;
    };
}

export type Result<T> = ResultSuccess<T> | ResultError;

// 触发方式
export enum Trigger {
    manual = 'manual', // 手动触发
    auto = 'auto', // 自动触发
    link = 'link', // 链接触发
}

// 请求服务端的基本参数
export interface ConnectionOptions {
    baseURL: string; // 服务端地址
    userID: string; // comate 用户唯一 id
    requestID: string; // 客户端生成的请求 id，每次扫描生成一个
    chatID?: string; // 对话 uuid
    extraHeaders?: {[key: string]: string};
}

// 请求服务端的基本 header 参数
function commonHttpHeaders(options: ConnectionOptions) {
    return {
        'Content-Type': 'application/json',
        'Content-Encoding': 'gzip',
        'Comate-User-Id': options.userID,
        'SAST-Request-ID': options.requestID,
        'SAST-Client-Version': CLIENT_VERSION,
        'SAST-Chat-ID': options.chatID ?? '',
        ...options.extraHeaders,
    };
}

export async function fetchWithTimeout(
    url: string,
    options: RequestInit,
    timeout: number = HTTP_TIMEOUT
): Promise<Response> {
    for (let i = 0; i < HTTP_RETRY_COUNT; i++) {
        try {
            return comateFetch(url, {signal: AbortSignal.timeout(timeout), ...options});
        }
        catch (error) {
            if (i >= 2) {
                throw error;
            }
            await sleep(HTTP_RETRY);
        }
    }
    throw new Error('fetch error');
}

// 扫描类型
export enum ScanType {
    sca = 1,
    sast = 1 << 1,
}

export enum DriverType {
    sast = 'sast',
    sca = 'sca',
}

export const scanTypeMap = new Map<ScanType, DriverType>([
    [ScanType.sast, DriverType.sast],
    [ScanType.sca, DriverType.sca],
]);

export enum RuleLevel {
    low = 'NONE',
    medium = 'NOTE',
    high = 'WARNING',
    critical = 'ERROR',
}

// 发起检测任务的参数
export interface AnalysisContext {
    userID: string;
    userName?: string;
    initiator?: string;
    trigger: Trigger;
    workspaceName?: string;
    workspacePath?: string;
    gitInfo?: GitInfo[];
}

export interface GitInfo {
    path?: string;
    gitURL?: string;
    gitBranch?: string;
    gitCommitID?: string;
}

export interface GetAnalysisOptions extends ConnectionOptions, AnalysisContext {
    bundleHash: string;
    scanType: number;
    limitToFiles?: string[];
}

interface AnalysisResult {
    status: AnalysisStatus; // 任务状态
    progress: number; // 任务进度
    data: {
        version: string; // 结果格式对应的版本
        runs: ScanResult[]; // 扫描结果
    };
}

// 扫描结果
export interface ScanResult {
    tool: {
        driver: Driver;
        rules: RuleInfo[];
    };
    result: VulResult[];
}

export interface Driver {
    name: DriverType;
    version: string;
}

export interface RuleInfo {
    id: string;
    name: string;
    defaultConfiguration: {
        level: RuleLevel;
    };
    help: {
        markdown: string;
        text: string;
    };
    suggestion: string;
    description: string;
    properties?: {
        repairVersion: {
            min: string;
            max: string;
        };
        cveID: string;
        reference: VulReference[];
    };
}

export interface VulReference {
    name: string;
    url: string;
}

export interface VulResult {
    ruleID: string;
    message: {text: string};
    locations: [{
        physicalLocation: {
            artifactLocation: {
                uri: string;
                region: {
                    startLine: number;
                    endLine: number;
                };
            };
        };
    }];
    properties: {
        isAutoFixable: boolean;
        isNecessary: boolean;
        hasFixSkill: boolean;
        isWaved: boolean;
        importPath: string;
        files: SCACompInfo[];
        sourceCode: string;
        hash: string;
    };
}

export interface SCACompInfo {
    compName?: string; // 组件名
    compVersion?: string; // 组件版本
    path?: string; // 文件路径
}

export enum AnalysisStatus {
    running = 1,
    completed = 2,
    failed = 3,
}

function validateAnalysisResult(input: AnalysisResult): AnalysisResult {
    const res = input;
    // 校验需要的字段
    if (typeof input.progress !== 'number') {
        throw new Error('analysis result error: progress type error');
    }
    if (input.progress < 0) {
        throw new Error(`analysis result error: progress value error: ${input.progress}`);
    }
    return res;
}

// 创建扫描任务 or 查询扫描结果
export async function getAnalysis(options: GetAnalysisOptions): Promise<Result<AnalysisResult>> {
    const data = {
        key: {
            type: 'file',
            hash: options.bundleHash,
            limitToFiles: options.limitToFiles,
        },
        scan: options.scanType,
        analysisContext: {
            initiator: options.initiator,
            trigger: options.trigger,
            workspaceName: options.workspaceName,
            workspacePath: options.workspacePath,
            gitInfo: options.gitInfo,
        },
    };

    try {
        const response = await fetchWithTimeout(`${options.baseURL}/v2/analysis`, {
            method: 'POST',
            headers: commonHttpHeaders(options),
            body: gzip(JSON.stringify(data)),
        });

        if (!response.ok) {
            return {
                type: ResultType.error,
                error: {
                    code: ResultErrorCode.other,
                    text: `request error: status=${response.status}`,
                    apiName: 'getAnalysis',
                },
            };
        }

        try {
            const body = await response.json();
            const result = body as AnalysisResult;
            if (result.status !== AnalysisStatus.running && result.status !== AnalysisStatus.completed) {
                return {
                    type: ResultType.error,
                    error: {
                        code: ResultErrorCode.other,
                        text: `remote return error: status=${result.status}`,
                        apiName: 'getAnalysis',
                    },
                };
            }
            validateAnalysisResult(result);
            return {type: ResultType.succ, value: result};
        }
        catch (error) {
            return {
                type: ResultType.error,
                error: {
                    code: ResultErrorCode.other,
                    text: getErrorMessage(error),
                    apiName: 'getAnalysis',
                    detail: getErrorStack(error),
                },
            };
        }
    }
    catch (error) {
        return {
            type: ResultType.error,
            error: {
                code: ResultErrorCode.networkErr,
                text: getErrorMessage(error),
                apiName: 'getAnalysis',
                detail: getErrorStack(error),
            },
        };
    }
}

export interface File {
    hash: string;
    content: string;
}

export interface BundleFiles {
    [filePath: string]: string | File;
}

export interface CreateBundleOptions extends ConnectionOptions {
    files: BundleFiles;
}

export interface BundleResult {
    status: GeneralStatus;
    message: string;
    data: RemoteBundle;
}

export interface RemoteBundle {
    bundleHash: string;
    missingFiles: string[];
}

enum GeneralStatus {
    succ = 0,
    failed = 1,
}

function validateBundleResultData(input: BundleResult): BundleResult {
    const res = input;
    if (typeof input.data.bundleHash !== 'string') {
        throw new Error('bundle result error: no data.bundleHash or type error');
    }
    if (input.data.missingFiles) {
        res.data.missingFiles.forEach(value => {
            if (typeof value !== 'string') {
                throw new Error('bundle result error: data.missingFiles item type error');
            }
        });
    }
    else {
        res.data.missingFiles = [];
    }
    return res;
}

// 上传文件 hash
export async function createBundle(options: CreateBundleOptions): Promise<Result<RemoteBundle>> {
    try {
        const response = await fetchWithTimeout(`${options.baseURL}/v1/bundle`, {
            method: 'POST',
            headers: commonHttpHeaders(options),
            body: gzip(JSON.stringify(options.files)),
        }, HTTP_TIMEOUT_UPLOAD_FILE);

        if (!response.ok) {
            return {
                type: ResultType.error,
                error: {
                    code: ResultErrorCode.other,
                    text: `request error: status=${response.status}`,
                    apiName: 'createBundle',
                },
            };
        }

        try {
            let result = (await response.json()) as BundleResult;
            if (result.status !== GeneralStatus.succ) {
                return {
                    type: ResultType.error,
                    error: {
                        code: ResultErrorCode.other,
                        text: `remote return error: message=${result.message}`,
                        apiName: 'createBundle',
                    },
                };
            }
            result = validateBundleResultData(result);
            return {type: ResultType.succ, value: result.data};
        }
        catch (error) {
            return {
                type: ResultType.error,
                error: {
                    code: ResultErrorCode.other,
                    text: getErrorMessage(error),
                    apiName: 'createBundle',
                    detail: getErrorStack(error),
                },
            };
        }
    }
    catch (error) {
        return {
            type: ResultType.error,
            error: {
                code: ResultErrorCode.networkErr,
                text: getErrorMessage(error),
                apiName: 'createBundle',
                detail: getErrorStack(error),
            },
        };
    }
}

interface ExtendBundleOptions extends ConnectionOptions {
    bundleHash: string;
    files: BundleFiles;
}

// 上传缺失文件
export async function extendBundle(options: ExtendBundleOptions): Promise<Result<RemoteBundle>> {
    try {
        const response = await fetchWithTimeout(`${options.baseURL}/v1/bundle/${options.bundleHash}`, {
            method: 'PUT',
            headers: commonHttpHeaders(options),
            body: gzip(JSON.stringify({files: options.files})),
        }, HTTP_TIMEOUT_UPLOAD_FILE);

        if (!response.ok) {
            return {
                type: ResultType.error,
                error: {
                    code: ResultErrorCode.other,
                    text: `request error: status=${response.status}`,
                    apiName: 'extendBundle',
                },
            };
        }

        try {
            const result = (await response.json()) as BundleResult;
            if (result.status !== GeneralStatus.succ) {
                return {
                    type: ResultType.error,
                    error: {
                        code: ResultErrorCode.other,
                        text: `remote return error: message=${result.message}`,
                        apiName: 'extendBundle',
                    },
                };
            }

            return {type: ResultType.succ, value: result.data};
        }
        catch (error) {
            return {
                type: ResultType.error,
                error: {
                    code: ResultErrorCode.other,
                    text: getErrorMessage(error),
                    apiName: 'extendBundle',
                    detail: getErrorStack(error),
                },
            };
        }
    }
    catch (error) {
        return {
            type: ResultType.error,
            error: {
                code: ResultErrorCode.networkErr,
                text: getErrorMessage(error),
                apiName: 'extendBundle',
                detail: getErrorStack(error),
            },
        };
    }
}

export interface SettingResult {
    status: GeneralStatus;
    message: string;
    data: Settings;
}

export interface Settings {
    scanConfiguration: ScanConfig;
    systemConfiguration: GlobalSettings;
}

export interface ScanConfig {
    // [index: string]: any;
    sca: ScanSettings;
    sast: ScanSettings;
    autoScanEnabled: boolean;
}

export interface ScanSettings {
    enabled: boolean;
    autofixedEnabled: boolean;
    supportedLanguages: string[];
}

interface GlobalSettings {
    limitFiles: number;
    manualLimitFiles: number;
    refreshConfInterval: number; // 单位 s
    collectionScanFileTimeout: number; // 单位 s
}

function validateSettings(input: Settings) {
    if (input.scanConfiguration.sca.supportedLanguages.length === 0) {
        throw new Error('setting result error: scanConfiguration.sca.supportedLanguages empty');
    }
    input.scanConfiguration.sca.supportedLanguages.forEach(value => {
        if (typeof value !== 'string') {
            throw new Error('setting result error: scanConfiguration.sca.supportedLanguages item type error');
        }
    });

    if (input.scanConfiguration.sast.supportedLanguages.length === 0) {
        throw new Error('setting result error: scanConfiguration.sast.supportedLanguages empty');
    }
    input.scanConfiguration.sast.supportedLanguages.forEach(value => {
        if (typeof value !== 'string') {
            throw new Error('setting result error:scanConfiguration.sast.supportedLanguages item type error');
        }
    });
    if (typeof input.scanConfiguration.autoScanEnabled !== 'boolean') {
        throw new Error('setting result error: scanConfiguration.autoScanEnabled type error');
    }
    if (typeof input.systemConfiguration.limitFiles !== 'number') {
        throw new Error('setting result error: systemConfiguration.limitFiles type error');
    }
    if (typeof input.systemConfiguration.refreshConfInterval !== 'number') {
        throw new Error('setting result error: systemConfiguration.refreshConfInterval type error');
    }
    if (typeof input.systemConfiguration.collectionScanFileTimeout !== 'number') {
        throw new Error('setting result error: systemConfiguration.collectionScanFileTimeout type error');
    }
}

// 获取用户配置
export async function userSettings(options: ConnectionOptions): Promise<Result<Settings>> {
    try {
        const response = await fetchWithTimeout(`${options.baseURL}/v2/analysis/settings`, {
            method: 'GET',
            headers: commonHttpHeaders(options),
        });

        if (!response.ok) {
            return {
                type: ResultType.error,
                error: {
                    code: ResultErrorCode.other,
                    text: `request error: status=${response.status}`,
                    apiName: 'userSettings',
                },
            };
        }

        try {
            const result = (await response.json()) as SettingResult;
            if (result.status !== GeneralStatus.succ) {
                return {
                    type: ResultType.error,
                    error: {
                        code: ResultErrorCode.other,
                        text: `remote return error: message=${result.message}`,
                        apiName: 'userSettings',
                    },
                };
            }
            validateSettings(result.data);
            return {type: ResultType.succ, value: result.data};
        }
        catch (error) {
            return {
                type: ResultType.error,
                error: {
                    code: ResultErrorCode.other,
                    text: getErrorMessage(error),
                    apiName: 'userSettings',
                    detail: getErrorStack(error),
                },
            };
        }
    }
    catch (error) {
        return {
            type: ResultType.error,
            error: {
                code: ResultErrorCode.networkErr,
                text: getErrorMessage(error),
                apiName: 'userSettings',
                detail: getErrorStack(error),
            },
        };
    }
}

interface ReportOptions extends ConnectionOptions {
    data: {[key: string]: string | number};
}

interface ReportResult {
    status: GeneralStatus;
    message: string;
}

export async function report(options: ReportOptions): Promise<Result<boolean>> {
    try {
        const response = await fetchWithTimeout(`${options.baseURL}/v1/analysis/log`, {
            method: 'POST',
            headers: commonHttpHeaders(options),
            body: gzip(JSON.stringify(options.data)),
        });
        if (!response.ok) {
            return {
                type: ResultType.error,
                error: {
                    code: ResultErrorCode.other,
                    text: `request error: status=${response.status}`,
                    apiName: 'report',
                },
            };
        }
        const result = (await response.json()) as ReportResult;
        if (result.status !== GeneralStatus.succ) {
            return {
                type: ResultType.error,
                error: {
                    code: ResultErrorCode.other,
                    text: `remote return error: message=${result.message}`,
                    apiName: 'report',
                },
            };
        }
        return {type: ResultType.succ, value: true};
    }
    catch (error) {
        return {
            type: ResultType.error,
            error: {
                code: ResultErrorCode.other,
                text: getErrorMessage(error),
                apiName: 'report',
                detail: getErrorStack(error),
            },
        };
    }
}

export enum RepairStatus {
    running = 0,
    failed = 1,
    missingFile = 2,
    finished = 3,
    verifying = 4,
}

// 自动修复触发方式
export enum RepairTrigger {
    button = 1, // 自动修复按钮
    wave = 2, // 波浪线
    link = 3, // 链接触发
    smart = 4, // 智能体触发
}

const succStatus = new Set([
    RepairStatus.running,
    RepairStatus.verifying,
    RepairStatus.missingFile,
    RepairStatus.finished,
]);

export interface RepairVulItem {
    ruleID: string;
    line: number;
    hash: string;
}

export interface RepairItem {
    name: string;
    hash: string;
    vulList: RepairVulItem[];
}

export interface RepairOptions extends ConnectionOptions {
    files: RepairItem[];
    type: ScanType;
    trigger: RepairTrigger;
}

export interface RepairResult {
    status: RepairStatus;
    message: string;
    data: {
        taskID: string;
        progress: number;
        missingFiles: [];
        files: RepairFile[];
    };
}

export interface RepairFile {
    name: string;
    hash: string;
    language: string;
    status: number;
    repairResult: Array<{ruleID: string, codeBlock: RepairCode}>;
    repairedContent: string;
}

export interface RepairCode {
    from: {
        startLine?: number;
        endLine?: number;
        code: string;
    };
    to: {
        code: string;
    };
}

async function repairBase(options: RepairOptions, path: string): Promise<Result<RepairResult>> {
    try {
        const response = await fetchWithTimeout(`${options.baseURL}${path}`, {
            method: 'POST',
            headers: commonHttpHeaders(options),
            body: gzip(JSON.stringify({
                files: options.files,
                type: options.type,
                trigger: options.trigger,
            })),
        });
        if (!response.ok) {
            return {
                type: ResultType.error,
                error: {
                    code: ResultErrorCode.other,
                    text: `request error: status=${response.status}`,
                    apiName: path,
                },
            };
        }
        try {
            const result = (await response.json()) as RepairResult;
            if (!succStatus.has(result.status)) {
                return {
                    type: ResultType.error,
                    error: {
                        code: ResultErrorCode.other,
                        text: `remote return error: message=${result.message} status=${result.status}`,
                        apiName: path,
                    },
                };
            }
            return {type: ResultType.succ, value: result};
        }
        catch (error) {
            return {
                type: ResultType.error,
                error: {
                    code: ResultErrorCode.other,
                    text: getErrorMessage(error),
                    apiName: path,
                    detail: getErrorStack(error),
                },
            };
        }
    }
    catch (error) {
        return {
            type: ResultType.error,
            error: {
                code: ResultErrorCode.networkErr,
                text: getErrorMessage(error),
                apiName: path,
                detail: getErrorStack(error),
            },
        };
    }
}

export async function repair(options: RepairOptions): Promise<Result<RepairResult>> {
    return repairBase(options, '/v2/repair');
}

export async function repairFile(options: RepairOptions): Promise<Result<RepairResult>> {
    return repairBase(options, '/v1/repair_file');
}

export interface UploadFileOptions extends ConnectionOptions {
    files: BundleFiles;
}

export interface UploadFileResult {
    status: GeneralStatus;
    message: string;
}

export async function uploadFile(options: UploadFileOptions): Promise<Result<UploadFileResult>> {
    try {
        const response = await fetchWithTimeout(`${options.baseURL}/v1/upload`, {
            method: 'PUT',
            headers: commonHttpHeaders(options),
            body: gzip(JSON.stringify({files: options.files})),
        });

        if (!response.ok) {
            return {
                type: ResultType.error,
                error: {
                    code: ResultErrorCode.other,
                    text: `request error: status=${response.status}`,
                    apiName: 'uploadFile',
                },
            };
        }

        try {
            const result = (await response.json()) as UploadFileResult;
            if (result.status !== GeneralStatus.succ) {
                return {
                    type: ResultType.error,
                    error: {
                        code: ResultErrorCode.other,
                        text: `remote return error: message=${result.message}`,
                        apiName: 'uploadFile',
                    },
                };
            }
            return {type: ResultType.succ, value: result};
        }
        catch (error) {
            return {
                type: ResultType.error,
                error: {
                    code: ResultErrorCode.other,
                    text: getErrorMessage(error),
                    apiName: 'uploadFile',
                    detail: getErrorStack(error),
                },
            };
        }
    }
    catch (error) {
        return {
            type: ResultType.error,
            error: {
                code: ResultErrorCode.networkErr,
                text: getErrorMessage(error),
                apiName: 'uploadFile',
                detail: getErrorStack(error),
            },
        };
    }
}

export enum ChatType {
    agentMain = 1, // 智能体主流程
}

export interface NewChatOptions extends ConnectionOptions {
    type: number;
    gitInfo: GitInfo;
    ide: string;
    query: string;
    status: Code;
    errMsg: string;
    vuls: ChatFileVul[];
}

export interface ChatFileVul {
    fileName: string;
    fileHash: string;
    vuls: ChatVul[];
}

export interface ChatVul {
    vulHash: string;
}

export interface NewChatResult {
    status: GeneralStatus;
    message: string;
}

export async function newChat(options: NewChatOptions): Promise<Result<NewChatResult>> {
    try {
        const response = await fetchWithTimeout(`${options.baseURL}/v1/chats`, {
            method: 'POST',
            headers: commonHttpHeaders(options),
            body: gzip(JSON.stringify({
                type: options.type,
                gitInfo: {url: options.gitInfo.gitURL, branch: options.gitInfo.gitBranch},
                ide: options.ide,
                query: options.query,
                status: options.status,
                errMessage: options.errMsg,
                vuls: options.vuls,
            })),
        });

        if (!response.ok) {
            return {
                type: ResultType.error,
                error: {
                    code: ResultErrorCode.other,
                    text: `request error: status=${response.status}`,
                    apiName: 'newChat',
                },
            };
        }

        try {
            const result = (await response.json()) as NewChatResult;
            if (result.status !== GeneralStatus.succ) {
                return {
                    type: ResultType.error,
                    error: {
                        code: ResultErrorCode.other,
                        text: `remote return error: message=${result.message}`,
                        apiName: 'newChat',
                    },
                };
            }
            return {type: ResultType.succ, value: result};
        }
        catch (error) {
            return {
                type: ResultType.error,
                error: {
                    code: ResultErrorCode.other,
                    text: getErrorMessage(error),
                    apiName: 'newChat',
                    detail: getErrorStack(error),
                },
            };
        }
    }
    catch (error) {
        return {
            type: ResultType.error,
            error: {
                code: ResultErrorCode.networkErr,
                text: getErrorMessage(error),
                apiName: 'newChat',
                detail: getErrorStack(error),
            },
        };
    }
}
