import fastGlob from 'fast-glob';
import fsPromises from 'node:fs/promises';
import Ignore from 'ignore';
import slash from 'slash';
import path from 'node:path';

const GITIGNORE_FILES_PATTERN = '**/.gitignore';
const GITIGNORE_SEARCH_DEPTH = 10;

const defaultIgnoredDirectories = [
    '**/node_modules',
    '**/flow-typed',
    '**/coverage',
    '**/.git',
    '**/venv',
];

const ignoreFilesGlobOptions = {
    absolute: true,
    dot: true,
};

interface File {
    filePath: string;
    content: string;
}

export interface GitignoreOptions {
    readonly cwd: string;
}

const applyBaseToPattern = (pattern: string, base: string) => {
    return pattern.startsWith('!')
        ? '!' + path.posix.join(base, pattern.slice(1))
        : path.posix.join(base, pattern);
};

const parseIgnoreFile = (file: File, cwd: string) => {
    const base = slash(path.relative(cwd, path.dirname(file.filePath)));

    return file
        .content
        .split(/\r?\n/)
        .filter((line: string) => line && !line.startsWith('#'))
        .map((pattern: string) => applyBaseToPattern(pattern, base));
};

const toRelativePath = (fileOrDirectory: string, cwd: string) => {
    cwd = slash(cwd);
    if (path.isAbsolute(fileOrDirectory)) {
        if (slash(fileOrDirectory).startsWith(cwd)) {
            return path.relative(cwd, fileOrDirectory);
        }

        throw new Error(`Path ${fileOrDirectory} is not in cwd ${cwd}`);
    }

    return fileOrDirectory;
};

const getIsIgnoredPredicate = (files: File[], cwd: string) => {
    const patterns = files.flatMap(file => parseIgnoreFile(file, cwd));
    const ignores = Ignore.default().add(patterns);

    return (fileOrDirectory: string) => {
        fileOrDirectory = toRelativePath(fileOrDirectory, cwd);
        return fileOrDirectory ? ignores.ignores(slash(fileOrDirectory)) : false;
    };
};

export const isGitIgnored = async (options: GitignoreOptions) => {
    const paths = await fastGlob(GITIGNORE_FILES_PATTERN, {
        cwd: options.cwd,
        suppressErrors: true,
        deep: GITIGNORE_SEARCH_DEPTH,
        ignore: defaultIgnoredDirectories,
        ...ignoreFilesGlobOptions,
    });

    const files = await Promise.all(
        paths.map(async filePath => ({
            filePath,
            content: await fsPromises.readFile(filePath, 'utf8'),
        }))
    );

    return getIsIgnoredPredicate(files, options.cwd);
};
