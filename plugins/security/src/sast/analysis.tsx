import {
    TaskProgressChunk,
    ElementChunkStream,
} from '@comate/plugin-host';

import * as http from '../utils/http.js';
import {sleep, Base} from '../utils/types.js';
import {SASTFileUtils, GetTargetFileStatus} from './bundle.js';
import {settings} from './settings.js';
import {getErrorMessage, getErrorStack} from '../utils/error.js';
import {reportError} from '../utils/report.js';
import {ScannerUtils, FileResult, CacheResult, serverityMap, BundleUtils, mergeResult} from './scanner.js';
import {ComateContext} from './context.js';
import * as msgs from './message.js';

interface ScanSettings {
    repair: number;
}

export class VulCount {
    critical: number = 0;
    high: number = 0;
    medium: number = 0;
    low: number = 0;
    sum: number = 0;

    incr(severity: http.RuleLevel) {
        switch (severity) {
            case http.RuleLevel.critical:
                this.critical++;
                break;
            case http.RuleLevel.high:
                this.high++;
                break;
            case http.RuleLevel.medium:
                this.medium++;
                break;
            case http.RuleLevel.low:
                this.low++;
                break;
            default:
                return;
        }
        this.sum++;
    }

    addCount(cnt: VulCount) {
        this.critical += cnt.critical;
        this.high += cnt.high;
        this.medium += cnt.medium;
        this.low += cnt.low;
        this.sum += cnt.sum;
    }

    getCounts(): number[] {
        return [this.critical, this.high, this.medium, this.low];
    }
}

// 静态代码扫描
export class SAST extends Base {
    static readonly serverityTagMap = new Map<string, any>([
        [http.RuleLevel.critical, 'critical'],
        [http.RuleLevel.high, 'high'],
        [http.RuleLevel.medium, 'medium'],
        [http.RuleLevel.low, 'low'],
    ]);

    private readonly logger: any;
    private readonly stream: ElementChunkStream;
    private readonly fileUtils: SASTFileUtils;
    private scanSettings: ScanSettings = {
        repair: 0,
    };
    private readonly scanner: ScannerUtils;
    private readonly context: ComateContext;
    private targetStatus!: GetTargetFileStatus;
    private readonly skill: string;

    constructor(
        context: ComateContext,
        scanner: ScannerUtils,
        traceID: string,
        stream: ElementChunkStream,
        skill: string
    ) {
        super(traceID);
        this.context = context;
        this.logger = context.logger.for(this.constructor.name);
        this.stream = stream;
        this.skill = skill;
        this.scanner = scanner;
        this.fileUtils = new SASTFileUtils(context.fs, context.cwd, this.logger);
    }

    // 格式化结果为 markdown
    formatResult(data: Map<string, FileResult>) {
        let prefix = '';
        if (
            this.targetStatus === GetTargetFileStatus.countExceeded
            || this.targetStatus === GetTargetFileStatus.timeout
        ) {
            prefix = msgs.PARTIAL_SCAN;
        }
        if (data.size === 0) {
            // 无漏洞
            this.stream.replaceLast(
                <p>
                    {prefix}
                    <p>检测完成，暂未发现漏洞。</p>
                </p>
            );
            return;
        }
        const vulCount = new VulCount();
        const output = new Array();
        for (const [f, res] of data) {
            // 同一个文件的得放一起，不然修复按钮会返回失败，不清楚为什么
            const fileVuls = new Array(
                <p>
                    <file-link showIcon to={f} line={1}>{f}</file-link>
                </p>
            );

            for (const r of res.result) {
                let button: any;
                if ((this.scanSettings.repair & r.type) > 0) {
                    if (r.repair) {
                        button = (
                            <p>
                                <command-button
                                    commandName="codeScan:repair"
                                    data={r.repair}
                                    replyText="发起修复"
                                    variant="primary"
                                >
                                    发起修复
                                </command-button>
                            </p>
                        );
                    }
                    else if (r.repairHint) {
                        button = (
                            <p>
                                <command-button
                                    commandName="codeScan:repair-hint"
                                    data=""
                                    replyText="发起修复"
                                    variant="primary"
                                >
                                    发起修复
                                </command-button>
                            </p>
                        );
                    }
                }

                let lineLink: any;
                if (r.vul.line > 0) {
                    lineLink = <file-link to={f} line={r.vul.line}>Line{r.vul.line}</file-link>;
                }
                let msg = `* 漏洞描述：${r.vul.ruleDescription}\n* 修复建议：${r.vul.ruleSuggestion}`;
                if (r.vul.importPath !== '') {
                    msg = `* 引入路径：${r.vul.importPath}\n${msg}`;
                }

                fileVuls.push(
                    <p>
                        <p>
                            <alert-tag level={SAST.serverityTagMap.get(r.vul.ruleLevel)}>
                                {serverityMap.get(r.vul.ruleLevel) ?? ''}
                            </alert-tag>
                            {lineLink}
                            {r.vul.isNecessary ? '[必修] ' : ''}
                            {r.vul.ruleName}
                        </p>
                        <blockquote>
                            <markdown>{msg}</markdown>
                        </blockquote>
                        {button}
                    </p>
                );

                vulCount.incr(r.vul.ruleLevel);
            }
            output.push(fileVuls);
        }

        if (vulCount.sum === 0) {
            this.stream.replaceLast(
                <p>
                    {prefix}
                    <p>检测完成，暂未发现漏洞。</p>
                </p>
            );
            return;
        }

        this.stream.replaceLast(
            <p>
                {prefix}
                <p>检测完成，漏洞如下：</p>
            </p>
        );
        this.stream.append(<p>漏洞统计：</p>);
        this.stream.append(
            <table>
                <thead>
                    <tr>
                        <th>
                            <flex horizontalCentered>严重</flex>
                        </th>
                        <th>
                            <flex horizontalCentered>高危</flex>
                        </th>
                        <th>
                            <flex horizontalCentered>中危</flex>
                        </th>
                        <th>
                            <flex horizontalCentered>低危</flex>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <flex horizontalCentered>{vulCount.critical}</flex>
                        </td>
                        <td>
                            <flex horizontalCentered>{vulCount.high}</flex>
                        </td>
                        <td>
                            <flex horizontalCentered>{vulCount.medium}</flex>
                        </td>
                        <td>
                            <flex horizontalCentered>{vulCount.low}</flex>
                        </td>
                    </tr>
                </tbody>
            </table>
        );
        for (let v of output) {
            this.stream.append(<p>{v}</p>);
        }
    }

    async *execute(scanPath: string): AsyncIterableIterator<TaskProgressChunk> {
        try {
            this.logger.info(
                'execute',
                this.logContent({message: `start: path=${scanPath}`})
            );

            yield* this.exec(scanPath);
        }
        catch (error) {
            const opt = this.context.reportOptions(this.traceID, this.skill, 'execute error', error);
            await reportError(this.logger, opt);
            throw error;
        }
    }

    private async *exec(scanPath: string): AsyncIterableIterator<TaskProgressChunk> {
        yield this.stream.flushReplaceLast(
            <loading>准备中...</loading>
        );

        const baseOptions = this.context.connectOptions(this.traceID);

        // 获取扫描配置
        const status = await this.initSettings(baseOptions);
        if (status === http.ResultErrorCode.networkErr) {
            yield this.stream.flushReplaceLast(<p>检测失败，无法连接服务端。</p>);
            return;
        }

        const [targetResult, bundleUtils] = await this.scanner.getTargetFiles(
            scanPath,
            this.scanner.upScanType(),
            this.traceID,
            true
        );
        if (!targetResult.files || targetResult.files.size === 0) {
            let prefix;
            if (targetResult.status === GetTargetFileStatus.timeout) {
                prefix = msgs.PARTIAL_SCAN;
            }
            yield this.stream.flushReplaceLast(
                <p>
                    {prefix}
                    <p>检测完成，暂未发现漏洞。</p>
                </p>
            );
            return;
        }
        this.targetStatus = targetResult.status;
        this.logger.info(
            'exec',
            this.logContent({message: `targetFiles.size=${targetResult.files.size}, bundle.size=${bundleUtils.size}`})
        );

        const cacheResult = await this.scanner.getTargetFileCache(targetResult.files);
        if (cacheResult.needScan.size > 0) {
            // 有需要扫描的文件
            let scanType = 0;
            cacheResult.needScan.forEach((v, _) => {
                scanType |= v.type;
            });

            this.logger.info(
                'exec',
                this.logContent({
                    message: `need scan: size=${cacheResult.needScan.size}, type=${scanType}`,
                })
            );

            const bundleResponse = await this.fileUtils.uploadFiles(
                baseOptions,
                bundleUtils.getFileHash(),
                this.traceID
            );
            if (bundleResponse.type === http.ResultType.error) {
                this.logger.error(
                    'exec',
                    this.logContent({
                        message: 'uploadFiles',
                        error: bundleResponse.error.text,
                        api: bundleResponse.error.apiName,
                        stack: bundleResponse.error.detail ?? '',
                    })
                );
                if (bundleResponse.error.code === http.ResultErrorCode.networkErr) {
                    yield this.stream.flushReplaceLast(<p>检测失败，无法连接服务端。</p>);
                }
                else {
                    yield this.stream.flushReplaceLast(<p>检测失败。</p>);
                }
                return;
            }

            // 扫描
            const analysisOptions: http.GetAnalysisOptions = {
                ...baseOptions,
                bundleHash: bundleResponse.value,
                trigger: http.Trigger.manual,
                scanType: scanType,
                initiator: this.context.ideName,
                gitInfo: this.context.gitInfo(),
                limitToFiles: bundleUtils.getLimitToFiles(cacheResult.needScan) ?? undefined,
            };

            yield* this.scan(analysisOptions, cacheResult, bundleUtils);
        }
        else {
            this.formatResult(cacheResult.result);
            yield this.stream.flush();
        }
    }

    private async *scan(
        analysisOptions: http.GetAnalysisOptions,
        cacheResult: CacheResult,
        bundleUtils: BundleUtils
    ): AsyncIterableIterator<TaskProgressChunk> {
        const startTime = Date.now();
        let preProgress = -1; // 记录上一次的进度，用作校验，避免进度倒退
        do {
            const analysisResponse = await http.getAnalysis(analysisOptions);
            if (analysisResponse.type === http.ResultType.error) {
                this.logger.error(
                    'scan',
                    this.logContent({
                        message: 'getAnalysis',
                        error: analysisResponse.error.text,
                        api: analysisResponse.error.apiName,
                        stack: analysisResponse.error.detail ?? '',
                    })
                );
                if (analysisResponse.error.code === http.ResultErrorCode.networkErr) {
                    // 网络问题
                    this.logger.info(
                        'scan',
                        this.logContent({
                            message: 'getAnalysis: network error, retry',
                            api: analysisResponse.error.apiName,
                        })
                    );
                    await sleep(ScannerUtils.resultCheckInterval);
                    continue;
                }
                yield this.stream.flushReplaceLast(<p>检测失败。</p>);
                return;
            }
            if (analysisResponse.value.status === http.AnalysisStatus.completed) {
                try {
                    const res = this.scanner.formatResult(
                        this.traceID,
                        bundleUtils.getScannedFiles(cacheResult.needScan, analysisOptions.scanType),
                        bundleUtils.getFileHash(),
                        analysisResponse.value.data.runs
                    );
                    this.scanner.cacheResult(analysisOptions.scanType, res);
                    const merge = mergeResult(res, cacheResult);
                    this.formatResult(merge);
                    yield this.stream.flush();
                }
                catch (error) {
                    this.logger.error(
                        'scan',
                        this.logContent({
                            message: 'formatResult',
                            error: getErrorMessage(error),
                            stack: getErrorStack(error),
                        })
                    );
                    const opt = this.context.reportOptions(this.traceID, this.skill, 'format result error', error);
                    await reportError(this.logger, opt);
                    yield this.stream.flushReplaceLast(<p>检测失败。</p>);
                }
                return;
            }

            const progress = analysisResponse.value.progress;
            if (progress >= preProgress) {
                // 进度有增加才更新对话框内容
                let msg = '';
                const scanTime = (Date.now() - startTime) / 1000;
                if (scanTime < 1) {
                    msg = '检测中：0%...';
                }
                else if (scanTime <= 10) {
                    msg = `检测中：${progress}%...`;
                }
                else if (scanTime <= 60) {
                    msg = `检测中：${progress}%...检测即将完成`;
                }
                else if (scanTime <= 120) {
                    let left = ScannerUtils.scanTimeout / 1000 - scanTime;
                    msg = `检测中：${progress}%...喝杯水休息一下吧，检测预计剩余${this.leftTimeMsg(left)}`;
                }
                else if (scanTime > 120) {
                    let left = ScannerUtils.scanTimeout / 1000 - scanTime;
                    msg = `检测中：${progress}%...要检测的内容很多呀，检测预计剩余${this.leftTimeMsg(left)}`;
                }

                if (msg.length > 0) {
                    yield this.stream.flushReplaceLast(<loading>{msg}</loading>);
                }
                preProgress = progress;
            }

            await sleep(ScannerUtils.resultCheckInterval);
        }
        while (ScannerUtils.scanTimeout > Date.now() - startTime);

        yield this.stream.flushReplaceLast(<p>检测失败，请再次尝试。</p>);
    }

    private leftTimeMsg(left: number): string {
        if (left >= 60) {
            return ` ${Math.floor(left / 60)} 分钟`;
        }
        else {
            return ` ${Math.floor(left > 1 ? left : 1)} 秒`;
        }
    }

    private async initSettings(options: http.ConnectionOptions): Promise<http.ResultErrorCode | undefined> {
        const settingResp = await settings(options);
        if (settingResp.type === http.ResultType.error) {
            // 获取失败就结束
            this.logger.error(
                'initSettings',
                this.logContent({
                    message: 'get settings error',
                    error: settingResp.error.text,
                    api: settingResp.error.apiName,
                    stack: settingResp.error.detail ?? '',
                })
            );
            return settingResp.error.code;
        }

        const res = settingResp.value;
        const s = this.scanSettings;

        try {
            await this.scanner.syncConfig(res);

            if (res.scanConfiguration.sca.autofixedEnabled) {
                s.repair |= http.ScanType.sca;
            }

            if (res.scanConfiguration.sast.autofixedEnabled) {
                s.repair |= http.ScanType.sast;
            }

            this.logger.info('initSettings', {message: 'get settings', data: this.scanSettings});
        }
        catch (error) {
            this.logger.error(
                'initSettings',
                this.logContent({
                    message: 'process settings error',
                    error: getErrorMessage(error),
                    stack: getErrorStack(error),
                })
            );
            return http.ResultErrorCode.other;
        }
        return;
    }
}
