import {
    FileSystem,
    RepositoryInfo,
} from '@comate/plugin-host';

import {ConnectionOptions, GitInfo} from '../utils/http.js';
import {baseURL} from '../utils/constants.js';
import {getErrorMessage, getErrorStack} from '../utils/error.js';
import {ReportData} from '../utils/report.js';

let context: any = null;

export class ComateContext {
    readonly isInner: boolean;
    readonly userID: string;
    readonly userName: string;
    readonly cwd: string;
    readonly fs: FileSystem;
    readonly logger: any;
    readonly repoInfo: RepositoryInfo;
    readonly ideName: string;
    private _gitInfo!: GitInfo[];

    constructor(
        isInner: boolean,
        userID: string,
        userName: string,
        cwd: string,
        fs: FileSystem,
        logger: any,
        repoInfo: RepositoryInfo,
        ideName: string
    ) {
        this.isInner = isInner;
        this.userID = userID;
        this.userName = userName;
        this.cwd = cwd;
        this.fs = fs;
        this.logger = logger;
        this.repoInfo = repoInfo;
        this.ideName = ideName;
    }

    connectOptions(traceID: string, chatID: string = ''): ConnectionOptions {
        return {
            baseURL: baseURL(this.isInner),
            userID: this.userID,
            requestID: traceID,
            chatID: chatID,
            extraHeaders: {'Comate-Username': encodeURIComponent(this.userName)},
        };
    }

    reportOptions(traceID: string, skill: string, msg: string, err: any): ReportData {
        return {
            userID: this.userID,
            userName: this.userName,
            skill: skill,
            traceID: traceID,
            message: msg,
            error: getErrorMessage(err),
            stack: getErrorStack(err),
        };
    }

    gitInfo(): GitInfo[] {
        if (!this._gitInfo) {
            const res = new Array<GitInfo>();
            if (this.repoInfo.versionControl === 'git') {
                res.push({
                    gitURL: this.repoInfo.repositoryUrl ?? '',
                    gitBranch: this.repoInfo.branch ?? '',
                });
            }
            this._gitInfo = res;
        }
        return this._gitInfo;
    }

    gitInfoFirst(): GitInfo {
        const gitArr = this.gitInfo();
        if (gitArr.length > 0) {
            return gitArr[0];
        }
        return {gitURL: '', gitBranch: ''};
    }
}

export function initComateContext(
    isInner: boolean,
    userID: string,
    userName: string,
    cwd: string,
    fs: FileSystem,
    logger: any,
    repoInfo: RepositoryInfo,
    ideName: string
): ComateContext {
    if (!context) {
        context = new ComateContext(
            isInner,
            userID,
            userName,
            cwd,
            fs,
            logger,
            repoInfo,
            ideName
        );
    }
    return context;
}

export function getComateContext() {
    return context;
}
