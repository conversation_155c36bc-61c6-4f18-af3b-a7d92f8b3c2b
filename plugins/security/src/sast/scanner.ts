import {FileSystem} from '@comate/plugin-host';
import {Mutex} from 'async-mutex';
import path from 'node:path';

import * as http from '../utils/http.js';
import {eqSet, defaultPathFilter, FilterFunction, sleep, LogRecord} from '../utils/types.js';
import {ComateContext} from './context.js';
import {SASTFileUtils, GetTargetFileStatus, GetTargetFileResult, ScanItem} from './bundle.js';
import {getErrorMessage, getErrorStack} from '../utils/error.js';

let scanner: any = null;
const scannerMutext = new Mutex();

export const serverityMap = new Map<string, string>([
    [http.RuleLevel.critical, '严重'],
    [http.RuleLevel.high, '高危'],
    [http.RuleLevel.medium, '中危'],
    [http.RuleLevel.low, '低危'],
]);

export interface CacheResult {
    result: Map<string, FileResult>;
    needScan: Map<string, ScanItem>;
}

export interface FileResult {
    scanType: number;
    hash: string;
    scanTime: number;
    result: VulResult[];
}

export interface VulResult {
    type: http.ScanType;
    wave: boolean;
    vul: VulInfo;
    repair: RepairData | null;
    repairHint: boolean;
    hash: string;
}

export interface VulInfo {
    ruleLevel: http.RuleLevel;
    ruleName: string;
    ruleDescription: string;
    ruleSuggestion: string;
    line: number;
    cveID: string;
    importPath: string;
    sourceCode: string;
    isNecessary: boolean;
    reference: http.VulReference[];
}

export interface RepairData {
    traceID: string;
    vulName: string;
    file: http.RepairItem;
    type: http.ScanType;
}

interface Rule {
    id: string;
    name: string;
    messageMd: string;
    messageTx: string;
    suggestion: string;
    description: string;
    level: http.RuleLevel;
    type: http.ScanType;
    cveID: string;
    refers: http.VulReference[];
}

interface ScanConfig {
    autoScan: boolean;
    autoFileLimit: number;
    manualFileLimit: number;
    targetSearchTimeout: number;
}

export class BundleUtils {
    private readonly bundle: Map<string, ScanItem>;
    private fileHash!: Map<string, string>;

    constructor(bundle: Map<string, ScanItem>) {
        this.bundle = bundle;
    }

    getFileHash(): Map<string, string> {
        if (!this.fileHash) {
            const target = new Map<string, string>();
            this.bundle.forEach((v, k) => {
                target.set(k, v.hash);
            });
            this.fileHash = target;
        }
        return this.fileHash;
    }

    get size(): number {
        return this.bundle.size;
    }

    getLimitToFiles(needScan: Map<string, ScanItem>): null | string[] {
        // needScan 是 bundle 的子集
        if (needScan.size === this.bundle.size) {
            // 扫描整个 bundle
            return null;
        }

        const limitFiles: string[] = [];
        needScan.forEach((_, k) => {
            limitFiles.push(k);
        });
        return limitFiles;
    }

    getScannedFiles(needScan: Map<string, ScanItem>, scanType: number): Map<string, ScanItem> {
        // 因为多种类型扫描共用一个 bundle，如果多个扫描类型有共同的目标文件，最终扫描的范围会大于等于 needScan，找出最终被扫描的内容
        const scanned = new Map(needScan);
        scanned.forEach((v, k) => {
            // 根据 scanType 更新实际扫描的类型
            v.type = (this.bundle.get(k)?.type ?? v.type) & scanType;
        });
        if ((scanType & http.ScanType.sca) > 0) {
            // sca 每次都是扫整个 bundle
            this.bundle.forEach((v, k) => {
                if ((v.type & http.ScanType.sca) > 0) {
                    const item = scanned.get(k);
                    if (item) {
                        item.type |= http.ScanType.sca;
                    }
                    else {
                        scanned.set(k, {
                            type: http.ScanType.sca,
                            hash: v.hash,
                        });
                    }
                }
            });
        }
        return scanned;
    }
}

class Scanner {
    static readonly resultCacheTTL = 24 * 3600 * 1000;
    private readonly _type: http.ScanType;
    private readonly name: http.DriverType;
    private readonly logger: any;
    private readonly context: ComateContext;

    private _up: boolean = false;
    private _target: Set<string> = new Set();

    private readonly changeMutex = new Mutex();
    private readonly _changedFiles = new Map<string, string>();

    private readonly scannedFiles = new Map<string, FileResult>(); // 目标文件数量有限，这个正常情况不会太大

    private readonly syncConfigMutex = new Mutex();

    isTargetFile = defaultPathFilter;

    constructor(context: ComateContext, type: http.ScanType) {
        this._type = type;
        this.name = http.scanTypeMap.get(type)!;
        this.logger = context.logger.for(this.constructor.name);
        this.context = context;
    }

    async init(conf: http.Settings) {
        await this.syncConfig(conf);
    }

    get scanType(): http.ScanType {
        return this._type;
    }

    get up(): boolean {
        return this._up;
    }

    // 设置目标文件，返回需要扫描的目标文件
    getNeedScan(files: Map<string, ScanItem>): Map<string, string> {
        const needScan = new Map<string, string>();
        for (const [f, v] of files) {
            if ((v.type & this.scanType) > 0) {
                if (this.getFileResultCache(f, v.hash) === null) {
                    needScan.set(f, v.hash);
                }
            }
        }
        this.logger.info(
            'getNeedScan',
            this.logContent({
                message: `needScan=${needScan.size}`,
            })
        );
        return needScan;
    }

    getFileResultCache(relativePath: string, hash: string, check: boolean = true): FileResult | null {
        const res = this.scannedFiles.get(relativePath);
        if (res) {
            if (!check || !hash) {
                return res;
            }
            if (res.hash === hash && Date.now() - res.scanTime < Scanner.resultCacheTTL) {
                return res;
            }
            this.logger.info(
                'getFileResultCache',
                this.logContent({
                    message: 'has cache, but cannot reuse',
                    path: relativePath,
                })
            );
        }
        return null;
    }

    cacheResult(result: Map<string, FileResult>) {
        for (const [f, r] of result) {
            if ((r.scanType & this.scanType) > 0) {
                const vuls = new Array<VulResult>();
                for (const v of r.result) {
                    if (v.type === this._type) {
                        this.logger.info('cacheResult', this.logContent({message: `get vul: file=${f}`, vul: v}));
                        vuls.push(v);
                    }
                }
                this.logger.info('cacheResult', this.logContent({message: `file=${f}, vulCount=${vuls.length}`}));
                this.scannedFiles.set(f, {hash: r.hash, scanType: this.scanType, scanTime: r.scanTime, result: vuls});
            }
        }
    }

    // 返回是否需要发起扫描
    async addFile(relativePath: string, hash: string): Promise<boolean> {
        const release = await this.changeMutex.acquire();
        try {
            // 这里再判断一次，避免判断和添加目标文件之间，目标文件规则变更，变更规则需要加这个锁，所以加他就对了
            if (!this._up) {
                return false;
            }
            if (!this.isTargetFile(relativePath)) {
                return false;
            }
            if (!this.getFileResultCache(relativePath, hash)) {
                this.logger.info('addFile', this.logContent({message: 'add file', file: relativePath}));
                this._changedFiles.set(relativePath, hash);
                return true;
            }
        }
        catch (error) {
            this.logger.error(
                'addFile',
                this.logContent({
                    message: 'add file error',
                    file: relativePath,
                    error: getErrorMessage(error),
                    stack: getErrorStack(error),
                })
            );
        }
        finally {
            release();
        }
        return false;
    }

    async removeFile(relativePath: string) {
        const release = await this.changeMutex.acquire();
        if (this._changedFiles.delete(relativePath)) {
            this.logger.info('removeFile', this.logContent({message: 'remove changed file', file: relativePath}));
        }
        release();

        this.scannedFiles.delete(relativePath);
    }

    async getChangedFile(): Promise<Map<string, string>> {
        const release = await this.changeMutex.acquire();
        const res = new Map(this._changedFiles);
        this._changedFiles.clear(); // 清空
        release();
        return res;
    }

    async clearChangedFile() {
        // 清空变更文件集合，优先级高
        const changedRelease = await this.changeMutex.acquire(10);
        this._changedFiles.clear();
        changedRelease();
    }

    // 配置变更的回调，返回是否需要重扫
    async syncConfig(conf: http.Settings): Promise<boolean> {
        const newConf = conf.scanConfiguration[this.name];

        const release = await this.syncConfigMutex.acquire();
        try {
            const target = new Set<string>(newConf.supportedLanguages);
            if (eqSet(target, this._target) && newConf.enabled === this._up) {
                // this.logger.info('syncConfig', this.logContent({message: 'no change'}));
                return false;
            }

            this._target = target;
            this._up = newConf.enabled;
            this.isTargetFile = this.targetFunc();
            this.logger.info(
                'syncConfig',
                this.logContent({
                    message: 'scanner changed',
                    data: newConf,
                })
            );

            return this._up;
        }
        finally {
            release();
        }
    }

    targetFunc() {
        // 根据 up 判断，返回不同的过滤函数，不需要像 isTargetFile 每个文件都判断 up
        if (!this._up) {
            return defaultPathFilter;
        }

        // 固定的目标
        const target = new Set(this._target);
        return (absolutePath: string): boolean => {
            const extension = path.extname(absolutePath);
            if (target.has(extension)) {
                return true;
            }
            return false;
        };
    }

    private logContent(content: LogRecord<string, any>): LogRecord<string, any> {
        return {
            scanner: this.name,
            ...content,
        };
    }
}

export class ScannerUtils {
    static readonly scanTimeout = 5 * 60 * 1000; // 扫描超时时间，单位 ms
    static readonly resultCheckInterval = 2000; // 获取扫描结果的间隔，单位 ms

    private readonly logger: any;
    private readonly scanners: Map<http.ScanType, Scanner>;
    private readonly cwd: string;
    private readonly fs: FileSystem;
    private readonly fileUtils: SASTFileUtils;
    private readonly context: ComateContext;
    private configChangedType: number = 0;

    private readonly configMutext = new Mutex();
    private readonly scanMutex = new Mutex();
    private readonly waitMutex = new Mutex();

    private bundle = new Map<string, ScanItem>();
    private hasGetBundle = false; // 是否获取过全量目标文件
    private readonly bundleMutex = new Mutex();

    private config: ScanConfig = {
        autoScan: false,
        autoFileLimit: 0,
        manualFileLimit: 0,
        targetSearchTimeout: 5 * 60 * 1000,
    };

    constructor(context: ComateContext, types: http.ScanType[]) {
        this.context = context;
        this.logger = context.logger.for(this.constructor.name);
        this.cwd = context.cwd;
        this.fs = context.fs;
        this.scanners = new Map();
        for (const t of types) {
            this.scanners.set(t, new Scanner(context, t));
        }
        this.fileUtils = new SASTFileUtils(this.fs, this.cwd, this.logger);
    }

    getScanners(): IterableIterator<Scanner> {
        return this.scanners.values();
    }

    upScanType(): number {
        let type = 0;
        for (const [t, s] of this.scanners) {
            if (s.up) {
                type |= t;
            }
        }
        return type;
    }

    // 获取指定文件的扫描结果，波浪线调用
    async getFileCache(relativePaths: string[]): Promise<[Map<string, VulResult[]>, Set<string>]> {
        const vulResult = new Map<string, VulResult[]>();
        const scanFiles = new Set<string>(); // 需要扫描的文件

        for (const p of relativePaths) {
            const targetType = this.isTargetFile(p);
            if (targetType === 0) {
                this.logger.info('getFileCache', {message: 'not target', file: p});
                continue;
            }

            const vul: VulResult[] = [];
            for (const [t, s] of this.scanners) {
                if ((targetType & t) > 0) {
                    const res = await s.getFileResultCache(p, '', false);
                    if (!res) {
                        // 没有缓存
                        this.logger.info('getFileCache', {message: 'no cache', file: p, scanner: t});
                        scanFiles.add(p);
                        continue;
                    }
                    this.logger.info('getFileCache', {message: 'find cache', file: p, scanner: t});
                    vul.push(...res.result);
                }
            }

            vulResult.set(p, vul);
        }
        return [vulResult, scanFiles];
    }

    // 获取目标文件的扫描结果，手动扫描指定文件
    async getTargetFileCache(targets: Map<string, ScanItem>): Promise<CacheResult> {
        const vulResult = new Map<string, FileResult>();
        const scanFiles = new Map<string, ScanItem>(); // 需要扫描的文件

        for (const [p, v] of targets) {
            const vul = new Array<VulResult>();
            let needScanType = 0;
            let vulType = 0;
            for (const [t, s] of this.scanners) {
                if ((v.type & t) > 0) {
                    const res = await s.getFileResultCache(p, v.hash, true);
                    if (!res) {
                        // 没有缓存
                        needScanType |= t;
                        continue;
                    }
                    this.logger.info('getTargetFileCache', {message: 'got cache', file: p, scanner: t});
                    vulType |= t;
                    vul.push(...res.result);
                }
            }
            if (needScanType > 0) {
                this.logger.info('getTargetFileCache', {message: 'need scan', file: p, scanType: needScanType});
                scanFiles.set(p, {type: needScanType, hash: v.hash});
            }
            if (vul.length > 0) {
                vulResult.set(p, {hash: v.hash, scanType: vulType, scanTime: Date.now(), result: vul});
            }
        }
        return {result: vulResult, needScan: scanFiles};
    }

    // 获取指定目录下的目标文件
    async getTargetFiles(
        relativePath: string,
        scanType: number,
        traceID: string,
        manual: boolean = false
    ): Promise<[GetTargetFileResult, BundleUtils]> {
        const stat = await this.fs.stat(relativePath);
        if (stat.type === 'file') {
            const targetFiles = new Map<string, ScanItem>();
            let bundleFiles = new Map<string, ScanItem>();
            const fileType = this.isTargetFile(relativePath);
            const targetType = fileType & scanType;
            if (targetType > 0) {
                // 需要扫
                const fileInfo = await this.fileUtils.readFile(relativePath);
                if (fileInfo) {
                    if (!SASTFileUtils.isFilteredFile(fileInfo.size)) {
                        targetFiles.set(relativePath, {type: targetType, hash: fileInfo.hash});
                        // 有 target 才获取 bundle，这里需要用 fileType，表示文件类型，而不是要扫描的类型
                        bundleFiles = await this.getWholeBundle(
                            traceID,
                            new Map<string, ScanItem>([[relativePath, {type: fileType, hash: fileInfo.hash}]])
                        );
                    }
                }
            }
            return [
                {status: GetTargetFileStatus.normal, files: targetFiles},
                new BundleUtils(bundleFiles),
            ];
        }
        else {
            const fileLimit = manual ? this.config.manualFileLimit : this.config.autoFileLimit;
            // getTargetFiles 非并发安全，每次都新建一个
            const fileUtils = new SASTFileUtils(this.fs, this.cwd, this.logger);

            // 获取所有类型的目标文件，后面才能更新 bundle
            const allTargetType = this.upScanType();
            const res = await fileUtils.getTargetFiles(
                relativePath,
                this.getTargetFilter(allTargetType),
                fileLimit,
                this.config.targetSearchTimeout,
                traceID
            );

            // 过滤需要扫描的范围
            let scanFiles = res.files;
            if (res.files.size > 0 && allTargetType !== scanType) {
                // 过滤出 scanType 的文件，只有自动扫描才会出现扫描类型和全部类型不等的情况
                this.logger.info('getTargetFiles', {
                    traceID: traceID,
                    message: `filter target: allType=${allTargetType}, scanType=${scanType}`,
                });
                const files = new Map<string, ScanItem>();
                res.files.forEach((v, k) => {
                    const targetType = v.type & scanType;
                    if (targetType > 0) {
                        // 实际需要扫描的内容
                        files.set(k, {hash: v.hash, type: targetType});
                    }
                });
                scanFiles = files;
            }

            let bundleFiles = new Map<string, ScanItem>();
            if (res.status !== GetTargetFileStatus.failed && relativePath === '.') {
                // 覆盖全量目标文件
                this.setWholeBundle(traceID, res.files);
                if (scanFiles.size > 0) {
                    bundleFiles = new Map(res.files);
                }
            }
            else if (scanFiles.size > 0) {
                bundleFiles = await this.getWholeBundle(traceID, res.files);
            }

            // 等创建完 bundle 再覆盖成需要扫描的内容
            res.files = scanFiles;

            return [res, new BundleUtils(bundleFiles)];
        }
    }

    async getWholeBundle(traceID: string, update: Map<string, ScanItem> | null): Promise<Map<string, ScanItem>> {
        if (!this.hasGetBundle) {
            const release = await this.bundleMutex.acquire();
            if (!this.hasGetBundle) {
                const fileUtils = new SASTFileUtils(this.fs, this.cwd, this.logger);
                const res = await fileUtils.getTargetFiles(
                    '.',
                    this.getTargetFilter(this.upScanType()),
                    this.config.manualFileLimit,
                    this.config.targetSearchTimeout,
                    traceID
                );
                if (res.files) {
                    this.bundle = res.files;
                }
                if (res.status !== GetTargetFileStatus.failed) {
                    this.hasGetBundle = true;
                    this.logger.info('getWholeBundle', {
                        traceID: traceID,
                        message: `init bundle: size=${this.bundle.size}`,
                    });
                }
            }
            release();
        }

        const b = new Map(this.bundle);
        if (update) {
            update.forEach((v, k) => {
                b.set(k, v);
                this.bundle.set(k, v);
            });
        }
        return b;
    }

    setWholeBundle(traceID: string, bundle: Map<string, ScanItem>) {
        this.bundle = bundle;
        this.hasGetBundle = true;
        this.logger.info('setWholeBundle', {traceID: traceID, message: `update bundle: size=${this.bundle.size}`});
    }

    async getChangedFiles(): Promise<Map<string, ScanItem>> {
        const targetFiles = new Map<string, ScanItem>();
        for (const [t, s] of this.scanners) {
            const files = await s.getChangedFile();
            files.forEach((v, k) => {
                if (!targetFiles.has(k)) {
                    targetFiles.set(k, {type: 0, hash: v});
                }
                const item = targetFiles.get(k);
                item!.type |= t;
            });
        }
        return targetFiles;
    }

    isTargetFile(absolutePath: string): number {
        let isTarget = 0;
        for (const [scanType, scanner] of this.scanners) {
            if (scanner.isTargetFile(absolutePath) === true) {
                isTarget |= scanType;
            }
        }
        return isTarget;
    }

    getTargetFilter(scanType: number = http.ScanType.sast | http.ScanType.sca): Map<http.ScanType, FilterFunction> {
        // 如果 scanner.isTargetFile 发生变更，之前调用该方法获取到的 FilterFunction 也会受影响
        const filters = new Map<http.ScanType, FilterFunction>();
        for (const [t, s] of this.scanners) {
            if ((scanType & t) > 0) {
                filters.set(t, s.targetFunc());
            }
        }
        return filters;
    }

    async addChangedFiles(relativePaths: string[]) {
        for (const p of relativePaths) {
            const type = this.isTargetFile(p);

            const res = await this.fileUtils.readFile(p);
            if (!res || SASTFileUtils.isFilteredFile(res.size)) {
                this.logger.info('addChangedFiles', {message: 'hash empty or large file', file: p});
                return;
            }

            for (const [scanType, scanner] of this.scanners) {
                if ((type & scanType) > 0) {
                    await scanner.addFile(p, res.hash);
                }
            }
        }

        // 发起扫描
        await this.waitForScan();
    }

    async removeFile(absolutePath: string) {
        for (const scanner of this.scanners.values()) {
            if (scanner.isTargetFile(absolutePath)) {
                // 先做个判断，尽量减少加锁操作
                const relativePath = path.relative(this.cwd, absolutePath);
                await scanner.removeFile(relativePath);
                this.bundle.delete(relativePath);
            }
        }
    }

    // 返回是否需要重扫
    private setConfig(conf: http.Settings): boolean {
        const newConfig: ScanConfig = {
            autoScan: conf.scanConfiguration.autoScanEnabled,
            autoFileLimit: conf.systemConfiguration.limitFiles,
            manualFileLimit: conf.systemConfiguration.manualLimitFiles,
            targetSearchTimeout: conf.systemConfiguration.collectionScanFileTimeout * 1000,
        };
        const old = this.config;
        this.config = newConfig;
        return this.config.autoScan !== old.autoScan && this.config.autoScan;
    }

    async syncConfig(conf: http.Settings) {
        let scanType = 0;
        for (const [t, s] of this.scanners) {
            if (await s.syncConfig(conf)) {
                scanType |= t;
            }
        }

        if (this.setConfig(conf)) {
            scanType = this.upScanType();
        }

        this.logger.info('syncConfig', {message: 'sync scanner utils config', data: this.config});

        if (scanType > 0 && this.config.autoScan) {
            const release = await this.configMutext.acquire();
            this.configChangedType |= scanType;
            this.logger.info('syncConfig', {message: `need fullscan: ${this.configChangedType}`});
            release();
        }
    }

    async getConfigChangeType(): Promise<number> {
        let res = 0;
        if (this.configChangedType > 0) {
            const release = await this.configMutext.acquire();
            if (this.configChangedType > 0) {
                if (this.config.autoScan) {
                    res = this.configChangedType;
                }
                this.configChangedType = 0;
                this.logger.info('getConfigChangeType', {message: 'reset'});
            }
            release();
        }
        return res;
    }

    // 全面查找目标文件，自动扫描、配置变更使用
    async fullScan(type: number) {
        this.logger.info(
            'fullScan',
            {message: `start: type=${type}`}
        );

        const traceID = crypto.randomUUID();
        const [targetResult, bundleUtils] = await this.getTargetFiles('.', type, traceID);

        if (targetResult.status === GetTargetFileStatus.failed) {
            // 查找失败
            this.logger.info('fullScan', {traceID: traceID, message: 'get target file failed'});
            return;
        }

        const needScan = new Map<string, ScanItem>();
        for (const [t, s] of this.scanners) {
            if ((t & type) > 0) {
                const res = s.getNeedScan(targetResult.files);
                res.forEach((v, k) => {
                    if (!needScan.has(k)) {
                        needScan.set(k, {type: 0, hash: v});
                    }
                    const item = needScan.get(k);
                    item!.type |= t;
                });
            }
        }

        try {
            await this.scan(traceID, needScan, bundleUtils, http.Trigger.auto);
        }
        catch (error) {
            this.logger.error(
                'fullScan',
                {
                    traceID: traceID,
                    error: getErrorMessage(error),
                    message: 'scan error',
                    stack: getErrorStack(error),
                }
            );
        }
    }

    async waitForScan() {
        if (this.scanMutex.isLocked()) {
            this.logger.info('waitForScan', {message: 'scanMutex locked'});
            if (this.waitMutex.isLocked()) {
                // 已经有任务在等待了，直接返回
                this.logger.info('waitForScan', {message: 'waitMutex locked'});
                return;
            }
            const waitRelease = await this.waitMutex.acquire();
            this.logger.info('waitForScan', {message: 'waiting'});
            const scanRelease = await this.scanMutex.acquire();
            waitRelease();
            try {
                const fullScanType = await this.getConfigChangeType();
                const files = await this.getChangedFiles(); // 全量扫描也需要清空 change 文件
                if (fullScanType > 0) {
                    await this.fullScan(fullScanType);
                }
                else {
                    // 非全量扫描
                    const traceID = crypto.randomUUID();
                    const bundleFiles = await this.getWholeBundle(traceID, files);
                    await this.scan(traceID, files, new BundleUtils(bundleFiles), http.Trigger.auto);
                }
            }
            catch (error) {
                this.logger.error(
                    'waitForScan',
                    {
                        error: error,
                        message: 'scan error',
                        stack: getErrorStack(error),
                    }
                );
            }
            finally {
                scanRelease();
            }
        }

        const scanRelease = await this.scanMutex.acquire();
        try {
            const fullScanType = await this.getConfigChangeType();
            const files = await this.getChangedFiles();
            if (fullScanType > 0) {
                await this.fullScan(fullScanType);
            }
            else {
                // 非全量扫描
                const traceID = crypto.randomUUID();
                const bundleFiles = await this.getWholeBundle(traceID, files);
                await this.scan(traceID, files, new BundleUtils(bundleFiles), http.Trigger.auto);
            }
        }
        catch (error) {
            this.logger.error(
                'waitForScan',
                {
                    error: error,
                    message: 'scan error',
                    stack: getErrorStack(error),
                }
            );
        }
        finally {
            scanRelease();
        }
    }

    async scan(traceID: string, needScan: Map<string, ScanItem>, bundleInfo: BundleUtils, trigger: http.Trigger) {
        if (needScan.size === 0) {
            this.logger.info('scan', {message: 'no target'});
            return;
        }

        let scanType = 0;
        needScan.forEach(v => {
            scanType |= v.type;
        });

        this.logger.info(
            'scan',
            {
                traceID: traceID,
                message: `scanType=${scanType}, needScan.size=${needScan.size}`,
            }
        );
        const baseOptions = this.context.connectOptions(traceID);
        const bundleResponse = await this.fileUtils.uploadFiles(baseOptions, bundleInfo.getFileHash(), traceID);
        if (bundleResponse.type === http.ResultType.error) {
            this.logger.error(
                'scan',
                {
                    traceID: traceID,
                    message: 'uploadFiles',
                    error: bundleResponse.error.text,
                    api: bundleResponse.error.apiName,
                    stack: bundleResponse.error.detail ?? '',
                }
            );
            return;
        }

        const analysisOptions: http.GetAnalysisOptions = {
            ...baseOptions,
            bundleHash: bundleResponse.value,
            gitInfo: this.context.gitInfo(),
            trigger: trigger,
            initiator: this.context.ideName,
            scanType: scanType,
            limitToFiles: bundleInfo.getLimitToFiles(needScan) ?? undefined,
        };

        const startTime = Date.now();
        do {
            const analysisResponse = await http.getAnalysis(analysisOptions);
            if (analysisResponse.type === http.ResultType.error) {
                this.logger.error(
                    'scan',
                    {
                        traceID: traceID,
                        message: 'getAnalysis',
                        error: analysisResponse.error.text,
                        api: analysisResponse.error.apiName,
                        stack: analysisResponse.error.detail ?? '',
                    }
                );
                if (analysisResponse.error.code === http.ResultErrorCode.networkErr) {
                    // 网络问题，重试固定次数
                    this.logger.info(
                        'scan',
                        {
                            traceID: traceID,
                            message: 'getAnalysis: network error, retry',
                            api: analysisResponse.error.apiName,
                        }
                    );
                    await sleep(ScannerUtils.resultCheckInterval);
                    continue;
                }
                return;
            }
            if (analysisResponse.value.status === http.AnalysisStatus.completed) {
                this.logger.info('scan', {traceID: traceID, message: 'completed'});
                // 格式化结果
                const scannedFiles = bundleInfo.getScannedFiles(needScan, scanType);
                const res = this.formatResult(
                    traceID,
                    scannedFiles,
                    bundleInfo.getFileHash(),
                    analysisResponse.value.data.runs
                );
                // 缓存结果
                this.cacheResult(scanType, res);
                return;
            }
            await sleep(ScannerUtils.resultCheckInterval);
        }
        while (ScannerUtils.scanTimeout > Date.now() - startTime);

        this.logger.info('scan', {traceID: traceID, message: 'timeout'});
    }

    formatResult(
        traceID: string,
        scannedFiles: Map<string, ScanItem>,
        bundleFileHash: Map<string, string>,
        data: http.ScanResult[]
    ): Map<string, FileResult> {
        const res = new Map<string, FileResult>();
        const now = Date.now();
        for (const [f, v] of scannedFiles) {
            res.set(f, {hash: v.hash, scanType: v.type, scanTime: now, result: new Array<VulResult>()});
        }
        if (data.length === 0) {
            return res;
        }

        for (const item of data) {
            if (!this.validDriver(item.tool.driver)) {
                this.logger.info('formatResult', {traceID: traceID, message: `invalid driver: ${item.tool.driver}`});
                continue;
            }
            const rules = this.formatRule(item.tool.driver.name, item.tool.rules);

            for (const vul of item.result) {
                const r = rules.get(vul.ruleID);
                if (!r) {
                    this.logger.info('formatResult', {traceID: traceID, message: `invalid ruleID: ${vul.ruleID}`});
                    continue;
                }
                const formatRes = this.formatVul(traceID, r, vul, bundleFileHash);
                for (const [path, value] of formatRes) {
                    const v = res.get(path);
                    if (!v) {
                        this.logger.info('formatResult', {
                            traceID: traceID,
                            message: `vul result not in scanned set: path=${path}, type=${value.type}`,
                        });
                        // res.set(path, {
                        //     hash: bundleFileHash.get(path)!,
                        //     scanType: value.type,
                        //     scanTime: now,
                        //     result: new Array<VulResult>(value),
                        // });
                    }
                    else if ((v.scanType & value.type) === 0) {
                        this.logger.info('formatResult', {
                            traceID: traceID,
                            message: `vul type not in scanned set: path=${path}, type=${value.type}`,
                        });
                    }
                    else {
                        v.result.push(value);
                    }
                }
            }
        }
        return res;
    }

    private formatRule(driver: http.DriverType, rules: http.RuleInfo[]): Map<string, Rule> {
        const res = new Map<string, Rule>();
        for (const rule of rules) {
            if (!this.validRule(rule)) {
                continue;
            }

            res.set(rule.id, {
                id: rule.id,
                name: rule.name,
                messageMd: rule.help.markdown,
                messageTx: rule.help.text,
                suggestion: rule.suggestion,
                description: rule.description,
                level: rule.defaultConfiguration.level,
                type: driver === http.DriverType.sca ? http.ScanType.sca : http.ScanType.sast,
                cveID: rule.properties?.cveID ?? '',
                refers: rule.properties?.reference ?? [],
            });
        }
        return res;
    }

    private validDriver(driver: http.Driver): boolean {
        return Object.values(http.DriverType)?.includes(driver.name);
    }

    private validRule(rule: http.RuleInfo): boolean {
        try {
            if (typeof rule.id !== 'string' || rule.id === '') {
                this.logger.info('validRule', {message: 'rule.id type error or empty'});
                return false;
            }
            if (typeof rule.name !== 'string' || rule.name === '') {
                this.logger.info('validRule', {message: 'rule.name type error or empty'});
                return false;
            }
            if (typeof rule.help.markdown !== 'string' || rule.help.markdown === '') {
                this.logger.info('validRule', {message: 'rule.help.markdown type error or empty'});
                return false;
            }
            if (typeof rule.help.text !== 'string' || rule.help.text === '') {
                this.logger.info('validRule', {message: 'rule.help.text type error or empty'});
                return false;
            }
            if (typeof rule.suggestion !== 'string' || rule.suggestion === '') {
                this.logger.info('validRule', {message: 'rule.help.markdown type error or empty'});
                return false;
            }
            if (typeof rule.description !== 'string' || rule.description === '') {
                this.logger.info('validRule', {message: 'rule.help.text type error or empty'});
                return false;
            }
            if (!Object.values(http.RuleLevel)?.includes(rule.defaultConfiguration.level)) {
                this.logger.info('validRule', {message: 'rule.defaultConfiguration.level value error'});
                return false;
            }
            if (rule.properties) {
                if (rule.properties.cveID && typeof rule.properties.cveID !== 'string') {
                    this.logger.info('validRule', {message: 'rule.properties.cveID type error'});
                    return false;
                }
                if (rule.properties.reference) {
                    for (const r of rule.properties.reference) {
                        if (typeof r.name !== 'string' || typeof r.url !== 'string') {
                            this.logger.info('validRule', {message: 'rule.properties.reference type error'});
                            return false;
                        }
                    }
                }
            }
        }
        catch (error) {
            this.logger.info('validRule', {message: 'rule error', error: getErrorMessage(error)});
            return false;
        }
        return true;
    }

    private *formatVul(
        traceID: string,
        rule: Rule,
        vul: http.VulResult,
        bundle: Map<string, string>
    ): Generator<[string, VulResult]> {
        for (const loc of vul.locations) {
            try {
                const startLine = loc.physicalLocation.artifactLocation.region.startLine;
                if (typeof startLine !== 'number' || startLine < 0) {
                    // 行号从 1 开始，0 的是间接依赖，不展示行号即可
                    continue;
                }

                if (!vul.properties.hash || vul.properties.hash === '') {
                    // 没有漏洞 hash，忽略
                    continue;
                }

                const hash = bundle.get(loc.physicalLocation.artifactLocation.uri);
                if (!hash) {
                    // 正常不会出现这种情况，如果查不到 hash，跳过
                    continue;
                }

                let repairData = null;
                let repairHint = false;
                if (vul.properties.isAutoFixable && startLine > 0) {
                    if (vul.properties.hasFixSkill === false) {
                        repairHint = true;
                    }
                    else {
                        const isNecessary = vul.properties.isNecessary ? '[必修]' : '';
                        repairData = {
                            traceID: crypto.randomUUID(), // 每个修复按钮固定 traceID
                            vulName: `[${serverityMap.get(rule.level)}]${isNecessary} ${rule.name}`,
                            file: {
                                name: loc.physicalLocation.artifactLocation.uri,
                                hash: hash,
                                vulList: [{ruleID: rule.id, line: startLine, hash: vul.properties.hash}],
                            },
                            type: rule.type,
                        };
                    }
                }

                const vulData: VulInfo = {
                    ruleLevel: rule.level,
                    ruleName: rule.name,
                    ruleDescription: rule.description,
                    ruleSuggestion: rule.suggestion,
                    line: startLine,
                    cveID: rule.cveID ?? '',
                    importPath: vul.properties.importPath ?? '',
                    sourceCode: vul.properties.sourceCode ?? '',
                    isNecessary: vul.properties.isNecessary ?? false,
                    reference: rule.refers,
                };

                yield [
                    loc.physicalLocation.artifactLocation.uri,
                    {
                        type: rule.type,
                        wave: vul.properties.isWaved ?? false,
                        vul: vulData,
                        repair: repairData,
                        repairHint: repairHint,
                        hash: vul.properties.hash,
                    },
                ];
            }
            catch (error) {
                this.logger.error('formatVul', {
                    traceID: traceID,
                    message: 'format vul error',
                    error: getErrorMessage(error),
                    stack: getErrorStack(error),
                });
            }
        }
    }

    cacheResult(type: number, result: Map<string, FileResult>) {
        for (const [t, s] of this.scanners) {
            if ((type & t) > 0) {
                s.cacheResult(result);
            }
        }
    }
}

export function mergeResult(scanResult: Map<string, FileResult>, cacheResult: CacheResult): Map<string, FileResult> {
    // scanResult 是完整的扫描结果，包括无漏洞文件
    scanResult.forEach((v, p) => {
        const cache = cacheResult.result.get(p);
        if (cache) {
            cache.result.forEach(c => {
                if ((v.scanType & c.type) === 0) {
                    // 将共同的文件，不重合的类型合入扫描结果
                    v.result.push(c);
                }
            });
        }
    });
    cacheResult.result.forEach((v, p) => {
        if (!scanResult.has(p)) {
            // 将扫描结果没有的文件合入扫描结果
            scanResult.set(p, v);
        }
    });
    // 过滤未指定扫描的文件，和没有漏洞的文件
    const mergeResult = new Map<string, FileResult>();
    scanResult.forEach((v, p) => {
        if (v.result.length > 0 && (cacheResult.result.has(p) || cacheResult.needScan.has(p))) {
            mergeResult.set(p, v);
        }
    });
    return mergeResult;
}

export async function initScanner(context: ComateContext): Promise<ScannerUtils> {
    if (!scanner) {
        const release = await scannerMutext.acquire();
        if (!scanner) {
            scanner = new ScannerUtils(context, [http.ScanType.sast, http.ScanType.sca]);
        }
        release();
    }
    return scanner;
}

export function getScanner() {
    return scanner;
}
