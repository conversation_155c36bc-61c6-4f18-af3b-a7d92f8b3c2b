import {Mutex} from 'async-mutex';
import {
    Settings,
    userSettings,
    ResultType,
    ConnectionOptions,
    Result,
    ResultErrorCode,
} from '../utils/http.js';
import {getErrorMessage, getErrorStack} from '../utils/error.js';
import {SYNC_SETTING_INTERVAL} from '../utils/constants.js';

const MAX_SYNC_INTERVAL = 6 * 3600; // 配置同步间隔限制 6h
const mutex = new Mutex();
let syncInterval = SYNC_SETTING_INTERVAL; // 同步配置的间隔，默认 10 分钟
let s: any = null;
let lastSyncTime = 0;

export async function settings(options: ConnectionOptions): Promise<Result<Settings>> {
    if (!s || Date.now() - lastSyncTime > syncInterval) {
        const release = await mutex.acquire();
        try {
            if (!s || Date.now() - lastSyncTime > syncInterval) {
                const settingResp = await userSettings(options);
                if (settingResp.type === ResultType.error) {
                    // 获取失败就结束
                    return settingResp;
                }
                if (settingResp.value.systemConfiguration.refreshConfInterval < MAX_SYNC_INTERVAL) {
                    // 根据服务端返回的配置，设置配置同步间隔
                    syncInterval = settingResp.value.systemConfiguration.refreshConfInterval * 1000;
                }
                else if (s) {
                    // 如果 s 不为空，采用上一次的配置
                    settingResp.value.systemConfiguration.refreshConfInterval = s
                        .systemConfiguration
                        .refreshConfInterval;
                }
                else {
                    // 采用默认配置
                    settingResp.value.systemConfiguration.refreshConfInterval = MAX_SYNC_INTERVAL;
                }

                // 手动扫描文件限制指定 1w
                settingResp.value.systemConfiguration.manualLimitFiles = 10000;

                s = settingResp.value;
                lastSyncTime = Date.now();
                return settingResp;
            }
        }
        catch (error) {
            if (!s) {
                return {
                    type: ResultType.error,
                    error: {
                        code: ResultErrorCode.other,
                        text: getErrorMessage(error),
                        apiName: 'userSettings',
                        detail: getErrorStack(error),
                    },
                };
            }
        }
        finally {
            release();
        }
    }
    return {type: ResultType.succ, value: s};
}
