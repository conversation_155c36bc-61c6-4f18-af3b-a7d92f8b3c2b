import {
    TaskProgressChunk,
    FileSystem,
    ElementChunkStream,
} from '@comate/plugin-host';

import {readFile} from '../utils/file.js';
import {
    RepairItem,
    RepairOptions,
    repair,
    ResultType,
    RepairStatus,
    uploadFile,
    BundleFiles,
    RepairFile,
    ResultErrorCode,
    RepairTrigger,
} from '../utils/http.js';
import {RepairData} from '../sast/scanner.js';
import {getErrorStack, getErrorMessage} from '../utils/error.js';
import {sleep, Base} from '../utils/types.js';
import {reportError} from '../utils/report.js';
import {ComateContext} from './context.js';

export interface CheckFileResult {
    status: CheckFileStatus;
    content: string;
}

export enum CheckFileStatus {
    ok = 0,
    error = 1,
    changed = 2,
}

// 静态代码扫描
export class Repair extends Base {
    static readonly timeout = 60 * 1000; // 生成修复结果的超时时间，单位 ms，虽然用户可以随时终止插件，但还是设个，避免永无止境的等待
    static readonly checkInterval = 2000; // 获取先修复结果的间隔，单位 ms

    private readonly userID: string;
    private readonly userName: string;
    private readonly logger: any;
    private readonly stream: ElementChunkStream;
    private readonly fs: any;
    private readonly inner: boolean;
    private readonly callSkill: string;
    private readonly context: ComateContext;

    constructor(traceID: string, stream: ElementChunkStream, skill: string, context: ComateContext) {
        super(traceID);
        this.userID = context.userID;
        this.userName = context.userName;
        this.logger = context.logger.for(this.constructor.name);
        this.stream = stream;
        this.fs = context.fs;
        this.callSkill = skill;
        this.inner = context.isInner;
        this.context = context;
    }

    async *run(data: RepairData, trigger: RepairTrigger) {
        try {
            yield* this.repair(data, trigger);
        }
        catch (error) {
            const opt = this.context.reportOptions(this.traceID, this.callSkill, 'repair error', error);
            await reportError(this.logger, opt);
            throw error;
        }
    }

    private async *repair(data: RepairData, trigger: RepairTrigger): AsyncIterableIterator<TaskProgressChunk> {
        const baseOptions = this.context.connectOptions(data.traceID);
        const requestOptions: RepairOptions = {
            ...baseOptions,
            files: [data.file],
            type: data.type,
            trigger: trigger,
        };

        yield this.stream.flushReplaceLast(
            <loading>开始生成修复代码...</loading>
        );

        const startTime = Date.now();
        do {
            const repairRes = await repair(requestOptions);

            if (repairRes.type === ResultType.error) {
                this.logger.error(
                    'handleCommand',
                    this.logContent({
                        error: repairRes.error.text,
                        api: repairRes.error.apiName,
                        stack: repairRes.error.detail ?? '',
                    })
                );
                if (repairRes.error.code === ResultErrorCode.networkErr) {
                    yield this.stream.flushReplaceLast(<p>修复失败，无法连接服务端。</p>);
                    return;
                }
                yield this.stream.flushReplaceLast(<p>修复失败。</p>);
                return;
            }

            if (repairRes.value.status === RepairStatus.missingFile) {
                this.logger.info('repair', this.logContent({message: 'miss file'}));
                if (!this.fs) {
                    yield this.stream.flushReplaceLast(<p>修复失败，无文件权限。</p>);
                    return;
                }

                const fileRes: CheckFileResult = await this.checkFile(this.fs, data.file);
                switch (fileRes.status) {
                    case CheckFileStatus.changed:
                        yield this.stream.flushReplaceLast(<p>原漏洞文件已不存在，无法修复。</p>);
                        return;
                    case CheckFileStatus.error:
                        yield this.stream.flushReplaceLast(<p>修复失败。</p>);
                        return;
                    case CheckFileStatus.ok:
                        const files: BundleFiles = {};
                        files[data.file.name] = {
                            hash: data.file.hash,
                            content: fileRes.content,
                        };
                        const uploadRes = await uploadFile({...baseOptions, files: files});
                        if (uploadRes.type === ResultType.error) {
                            this.logger.error(
                                'handleCommand',
                                this.logContent({
                                    error: uploadRes.error.text,
                                    api: uploadRes.error.apiName,
                                    stack: uploadRes.error.detail ?? '',
                                })
                            );
                            if (uploadRes.error.code === ResultErrorCode.networkErr) {
                                yield this.stream.flushReplaceLast(<p>修复失败，无法连接服务端。</p>);
                                return;
                            }
                            yield this.stream.flushReplaceLast(<p>修复失败。</p>);
                            return;
                        }
                        break;
                    default:
                        yield this.stream.flushReplaceLast(<p>修复失败。</p>);
                        return;
                }
            }
            else if (repairRes.value.status === RepairStatus.finished) {
                const code = this.repairResult(repairRes.value.data.files);
                if (!code || code.file != data.file.name) {
                    yield this.stream.flushReplaceLast(<p>修复失败。</p>);

                    this.logger.info(
                        'repair',
                        this.logContent({
                            repairTraceID: this.traceID,
                            message: 'check result error',
                            data: code,
                        })
                    );

                    const opt = this.context.reportOptions(
                        this.traceID,
                        this.callSkill,
                        `repair error: result format error: file=${data.file.name}, hash=${data.file.hash}`,
                        ''
                    );
                    await reportError(this.logger, opt);
                    return;
                }

                yield this.stream.flushReplaceLast(
                    <p>
                        漏洞名称：{data.vulName}
                        <br />
                        漏洞位置：<file-link to={data.file.name} line={data.file.vulList[0].line}>
                            {data.file.name} 文件第 {data.file.vulList[0].line} 行
                        </file-link>
                        <br />
                        修复结果：
                    </p>
                );

                yield this.stream.flush(
                    <code-block
                        language={code.language}
                        actions={['replaceToFile', 'copy']}
                        replaceToFileData={{
                            filePath: code.file,
                            from: code.from,
                            to: code.to,
                        }}
                        closed={false}
                    >
                        {code.to}
                    </code-block>
                );
                return;
            }

            let process = Math.floor((Date.now() - startTime) / Repair.timeout * 100);
            if (process >= 100) {
                process = 99;
            }
            const msg = `修复代码生成中：${process}%...`;
            yield this.stream.flushReplaceLast(
                <loading>{msg}</loading>
            );
            await sleep(Repair.checkInterval);
        }
        while (Repair.timeout > Date.now() - startTime);

        yield this.stream.flushReplaceLast(<p>修复失败，请再次尝试。</p>);
    }

    private repairResult(res: RepairFile[]): any {
        try {
            if (typeof res[0].name !== 'string' || res[0].name == '') {
                throw new Error(`file name error: ${res[0].name}`);
            }
            const repairRes = res[0].repairResult[0];
            if (typeof repairRes.codeBlock.from.code != 'string' || repairRes.codeBlock.from.code == '') {
                throw new Error(`from code error: ${repairRes.codeBlock.from.code}`);
            }
            if (typeof repairRes.codeBlock.to.code != 'string' || repairRes.codeBlock.to.code == '') {
                throw new Error(`to code error: ${repairRes.codeBlock.to.code}`);
            }
            return {
                method: 'replaceFileContent',
                file: res[0].name,
                from: repairRes.codeBlock.from.code,
                to: repairRes.codeBlock.to.code,
                language: res[0].language ? res[0].language.toLowerCase() : '',
            };
        }
        catch (error) {
            this.logger.error(
                'repairResult',
                this.logContent({
                    message: 'repair result error',
                    error: getErrorMessage(error),
                    stack: getErrorStack(error),
                })
            );
        }
    }

    private async checkFile(fs: FileSystem, fileItem: RepairItem): Promise<CheckFileResult> {
        try {
            const [hash, content] = await readFile(fs, fileItem.name);
            if (hash !== fileItem.hash) {
                return {status: CheckFileStatus.changed, content: ''};
            }

            return {status: CheckFileStatus.ok, content: content};
        }
        catch (error) {
            this.logger.error(
                'checkFile',
                this.logContent(
                    {
                        error: getErrorMessage(error),
                        message: 'get file content error',
                        file: fileItem.name,
                        stack: getErrorStack(error),
                    }
                )
            );
            return {status: CheckFileStatus.error, content: ''};
        }
    }
}
