import path from 'node:path';

import {
    FileSystem,
    FileEntry,
} from '@comate/plugin-host';

import {
    createBundle,
    extendBundle,
    File,
    ConnectionOptions,
    Result,
    ResultType,
    ScanType,
} from '../utils/http.js';
import {Base, FilterFunction} from '../utils/types.js';
import {getErrorMessage, getErrorStack} from '../utils/error.js';
import {readFile, size} from '../utils/file.js';
import {ERR_TIMEOUT} from '../utils/constants.js';

export interface ScanItem {
    type: number;
    hash: string;
}

// 获取目标文件的状态
export enum GetTargetFileStatus {
    normal = 0, // 正常
    failed = 1, // 失败
    countExceeded = 2, // 超出数量限制，目前只有这种情况会终止文件查找
    timeout = 3, // 超时
}

// 获取目标文件的返回值
export interface GetTargetFileResult {
    status: GetTargetFileStatus;
    files: Map<string, ScanItem>;
}

export interface FileInfo extends File {
    size: number;
}

type UploadResult<T> = Result<T> | {type: ResultType.cache, value: string};

// 静态代码扫描，文件处理工具类
export class SASTFileUtils extends Base {
    static readonly excludeDirSet = new Set([
        '.git',
        'node_modules',
        'venv',
        '.history',
        '.idea',
        '.vscode',
        '.DS_Store',
    ]);
    static readonly maxDepth = 20; // 文件遍历层数限制
    static readonly maxLength = 1024 * 1024; // 一次上传的文件的字符数限制
    static readonly maxFileSize = 500 * 1024; // 单个文件大小限制

    private readonly fs: FileSystem;
    private readonly cwd: string;
    private readonly logger: any;
    private targetFilter!: Map<ScanType, FilterFunction>;
    private maxFileCount!: number; // 目标文件数量限制，避免扫描时间太长
    private walkTimeout!: boolean;
    private targetFileResult!: GetTargetFileResult;

    constructor(fs: FileSystem, cwd: string, logger: any) {
        super();
        this.fs = fs;
        this.cwd = cwd;
        this.logger = logger.for(this.constructor.name);
    }

    async uploadFiles(
        options: ConnectionOptions,
        targetFiles: Map<string, string>,
        traceID: string
    ): Promise<UploadResult<string>> {
        this.traceID = traceID;
        this.logger.info('uploadFiles', this.logContent({message: `createBundle: size=${targetFiles.size}`}));
        // 上传文件哈希
        const createBundleResponse = await createBundle({
            ...options,
            files: Object.fromEntries(targetFiles),
        });
        if (createBundleResponse.type === ResultType.error) {
            return createBundleResponse;
        }

        const bundleHash = createBundleResponse.value.bundleHash;
        this.logger.info(
            'uploadFiles',
            this.logContent({
                message:
                    `bundleHash=${bundleHash}, missingFiles.length=${createBundleResponse.value.missingFiles.length}`,
            })
        );

        if (createBundleResponse.value.missingFiles.length > 0) {
            // 上传缺失文件
            let length = 0; // 累计文件内容长度
            let count = 0; // 已处理文件数
            const fileCount = createBundleResponse.value.missingFiles.length;
            const bundleFiles = new Map<string, File>();
            for (const f of createBundleResponse.value.missingFiles) {
                count++;
                const hash = targetFiles.get(f);
                if (hash) {
                    // 文件哈希和内容是分开获取的，期间文件内容如果发生变化，存在哈希与文件不对应的问题
                    const file = await this.readFile(f);
                    let content = ''; // 文件可能已经删除了，这里为了正常扫描，传递一个空白内容

                    if (file) {
                        content = file.content;
                        length += file.size;
                        if (hash !== file.hash) {
                            this.logger.info(
                                'uploadFiles',
                                this.logContent({
                                    message: `target file changed: file=${f}, old=${hash}, new=${file.hash}`,
                                })
                            );
                        }
                    }
                    else {
                        this.logger.info(
                            'uploadFiles',
                            this.logContent({message: `target file deleted: file=${f}`})
                        );
                    }

                    bundleFiles.set(f, {hash: hash, content: content});
                }

                // 分批发送
                if (length >= SASTFileUtils.maxLength || count === fileCount) {
                    this.logger.info('uploadFiles', this.logContent({message: `upload batch: fileCount=${count}`}));
                    const extBundleResponse = await extendBundle({
                        ...options,
                        bundleHash: bundleHash,
                        files: Object.fromEntries(bundleFiles),
                    });
                    if (extBundleResponse.type === ResultType.error) {
                        return extBundleResponse;
                    }
                    // 重置
                    length = 0;
                    bundleFiles.clear();
                }
            }
            return {type: ResultType.succ, value: bundleHash};
        }
        return {type: ResultType.cache, value: bundleHash};
    }

    // 获取目标文件，searchRoot 为相对工作空间的路径
    async getTargetFiles(
        searchRoot: string,
        filters: Map<ScanType, FilterFunction>,
        fileLimit: number,
        timeout: number = 0,
        traceID: string
    ): Promise<GetTargetFileResult> {
        this.traceID = traceID;
        this.logger.info(
            'getTargetFiles',
            this.logContent({
                message:
                    `start walk: scanDir=${searchRoot}, fileLimit=${fileLimit}, timeout=${timeout}, traceID=${traceID}`,
            })
        );
        this.walkTimeout = false;
        this.targetFilter = filters;
        this.maxFileCount = fileLimit;
        this.targetFileResult = {status: GetTargetFileStatus.normal, files: new Map()};
        try {
            const promises = [this.walk(searchRoot)];
            if (timeout > 0) {
                const timeoutPromise = new Promise<never>(
                    (_, reject) =>
                        setTimeout(() => {
                            reject(ERR_TIMEOUT);
                        }, timeout)
                );
                promises.push(timeoutPromise);
            }
            await Promise.race<void>(promises);
        }
        catch (error) {
            this.logger.info(
                'getTargetFiles',
                this.logContent({
                    message: 'get target file error',
                    error: getErrorMessage(error),
                    stack: getErrorStack(error),
                })
            );
            if (error === ERR_TIMEOUT) {
                this.walkTimeout = true;
                this.targetFileResult.status = GetTargetFileStatus.timeout;
            }
            else {
                this.targetFileResult.status = GetTargetFileStatus.failed;
            }
        }
        this.logger.info(
            'getTargetFiles',
            this.logContent({
                message: `end: status=${this.targetFileResult.status}, size=${this.targetFileResult.files.size}`,
            })
        );
        return this.targetFileResult;
    }

    isIgnored(absolutePath: string): boolean {
        // 只判断最后的目录，不管上级目录
        const dirName = path.basename(absolutePath);
        return SASTFileUtils.excludeDirSet.has(dirName);
    }

    static isIgnoredPath(absolutePath: string): boolean {
        const splits = absolutePath.split(path.sep);
        for (const s of splits) {
            if (SASTFileUtils.excludeDirSet.has(s)) {
                return true;
            }
        }
        return false;
    }

    static isFilteredFile(size: number): boolean {
        // 文件太大不扫
        return size > SASTFileUtils.maxFileSize;
    }

    async readFile(path: string): Promise<FileInfo | false> {
        try {
            // path 必须是相对工作空间的目录，否则 readFile 会报无权限
            const [hash, content] = await readFile(this.fs, path);
            if (content.length === 0) {
                // 文件为空，跳过
                return false;
            }
            const s = size(content);

            return {hash: hash, content: content, size: s};
        }
        catch (error) {
            this.logger.error(
                'readFile',
                this.logContent({
                    error: getErrorMessage(error),
                    message: 'get file content error',
                    file: path,
                    stack: getErrorStack(error),
                })
            );
            return false;
        }
    }

    // dir 为是相对工作空间的目录
    private async walk(dir: string, depth: number = 0) {
        if (this.walkTimeout) {
            this.logger.info('walk', this.logContent({message: 'timeout'}));
            return;
        }
        if (depth > SASTFileUtils.maxDepth) {
            // 限制递归层数，记录是什么目录这么多层，后面可以上传服务端，调整白名单
            this.logger.info('walk', this.logContent({message: 'dir depth exceed limit', dir: dir}));
            return;
        }

        let files: FileEntry[] = [];
        try {
            files = await this.fs.readDirectory(dir);
        }
        catch (error) {
            // 一般是没权限，忽略
            this.logger.error(
                'walk',
                this.logContent({
                    error: getErrorMessage(error),
                    message: 'read directory error',
                    dir: dir,
                    stack: getErrorStack(error),
                })
            );
            return;
        }

        if (files.length === 0) {
            // 目录为空，没有文件
            return;
        }

        // 查找目标文件，获取文件内容和哈希
        for (const item of files) {
            if (this.walkTimeout) {
                this.logger.info('walk', this.logContent({message: 'timeout'}));
                return;
            }
            try {
                switch (item.type) {
                    case 'file': {
                        const fullPath = path.join(dir, item.name); // 空间内的相对路径
                        const targetType = this.isTarget(fullPath);
                        if (targetType === 0) {
                            break;
                        }
                        const fileItem = await this.readFile(fullPath);
                        if (!fileItem) {
                            break;
                        }
                        if (SASTFileUtils.isFilteredFile(fileItem.size)) {
                            // 跳过大文件
                            this.logger.info(
                                'walk',
                                this.logContent({
                                    message: `large file, skip: file=${fullPath}, size=${fileItem.size}`,
                                })
                            );
                            break;
                        }
                        this.targetFileResult.files.set(fullPath, {type: targetType, hash: fileItem.hash});
                        if (this.targetFileResult.files.size >= this.maxFileCount) {
                            // 超过目标文件数量限制，返回
                            this.targetFileResult.status = GetTargetFileStatus.countExceeded;
                            this.logger.info('walk', this.logContent({message: 'file count exceeded'}));
                            return;
                        }
                        break;
                    }
                    case 'directory': {
                        const relativePath = path.join(dir, item.name); // 工作空间内的相对路径
                        if (!this.isIgnored(relativePath)) {
                            await this.walk(relativePath, depth + 1);
                            if (
                                this.targetFileResult.status === GetTargetFileStatus.countExceeded
                                || this.targetFileResult.status === GetTargetFileStatus.timeout
                            ) {
                                // 文件数量超出限制或超时直接结束
                                return;
                            }
                        }
                        break;
                    }
                }
            }
            catch (error) {
                this.logger.error(
                    'walk',
                    this.logContent({
                        error: getErrorMessage(error),
                        message: 'process file or directory error',
                        file: item,
                        path: dir,
                        stack: getErrorStack(error),
                    })
                );
            }
        }
    }

    private isTarget(p: string): number {
        let type = 0;
        for (const [t, f] of this.targetFilter) {
            if (f(p)) {
                type |= t;
            }
        }
        return type;
    }
}
