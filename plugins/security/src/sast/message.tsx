export const PARTIAL_SCAN = <p>因文件数过多未全部检测，指定目录后重新检测可获得更精准的结果。</p>;

export const INSTRUCTION_HEAD = '您的输入有误，代码安全能够帮您检测代码中的安全漏洞，以下是"代码安全"的使用方式：';
export const INSTRUCTION_CONTENT =
    '* 扫描整个项目：输入`/代码安全`指令后，回车或点击发送，可针对当前项目发起安全扫描。\n\n* 扫描单个目录或文件：输入`/代码安全 {目录名称或文件名称}`指令后（如：`/代码安全 src/controller`)，回车或点击发送，可针对当前项目特定目录发起安全扫描。';
export const INSTRUCTION = (
    <p>
        {INSTRUCTION_HEAD}
        <markdown>
            {INSTRUCTION_CONTENT}
        </markdown>
    </p>
);

export const INSTRUCTION_MD = `${INSTRUCTION_HEAD}

${INSTRUCTION_CONTENT}`;

const helpLinkSaas = 'https://comate.baidu.com/?loadFeedback=true';
const helpLinkInner = 'https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/idGHtE2EDf/uIO7T0dV7K/gbbYDTA66GuEuR';
const helpMsg = '如有疑问，联系我们';

export function helpLink(inner: boolean): string {
    if (inner) {
        return helpLinkInner;
    }
    return helpLinkSaas;
}

export function helpMd(inner: boolean): string {
    if (inner) {
        return `[${helpMsg}](${helpLinkInner})`;
    }
    return `[${helpMsg}](${helpLinkSaas})`;
}

export function helpChunk(inner: boolean) {
    let link = helpLinkSaas;
    if (inner) {
        link = helpLinkInner;
    }
    return (
        <p>
            <a href={link}>
                {helpMsg}
            </a>
        </p>
    );
}
