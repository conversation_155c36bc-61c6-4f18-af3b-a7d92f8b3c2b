import {FileSystemMocker, LoggerMocker} from './mock.js';
import {describe, expect, test} from 'vitest';
import {ElementChunkStream, NoneRepositoryInfo} from '@comate/plugin-host';
import {SAST, VulCount} from '../sast/analysis.js';
import {ScanType, RuleLevel, VulReference} from '../utils/http.js';
import {FileResult, initScanner} from '../sast/scanner.js';
import {ComateContext} from '../sast/context.js';

describe('formatResult', () => {
    test('格式化结果', async () => {
        const fs = new FileSystemMocker();
        const repoInfo: NoneRepositoryInfo = {
            versionControl: 'none',
        };
        const logger = new LoggerMocker();
        const stream = new ElementChunkStream();
        const context = new ComateContext(
            true,
            'userID',
            'userName',
            'cwd',
            fs,
            logger,
            repoInfo,
            'appName'
        );
        const scanner = await initScanner(context);

        const sast = new SAST(
            context,
            scanner,
            'requestID',
            stream,
            'test'
        );
        const data = new Map<string, FileResult>(
            [[
                'src/main.java',
                {
                    hash: 'abc',
                    scanType: ScanType.sast,
                    scanTime: Date.now(),
                    result: [{
                        type: ScanType.sast,
                        wave: true,
                        vul: {
                            ruleLevel: RuleLevel.critical,
                            ruleName: 'Sql 注入漏洞',
                            ruleDescription: '漏洞描述',
                            ruleSuggestion: '修复建议',
                            line: 12,
                            cveID: '',
                            importPath: '',
                            sourceCode: '',
                            isNecessary: true,
                            reference: new Array<VulReference>(),
                        },
                        repair: null,
                        repairHint: false,
                        hash: '',
                    }],
                },
            ], [
                'pom.xml',
                {
                    hash: 'cde',
                    scanType: ScanType.sca,
                    scanTime: Date.now(),
                    result: [{
                        type: ScanType.sca,
                        wave: true,
                        vul: {
                            ruleLevel: RuleLevel.critical,
                            ruleName: 'Log4j 远程代码执行漏洞',
                            ruleDescription: '漏洞描述',
                            ruleSuggestion: '修复建议',
                            line: 12,
                            cveID: 'cve_xxxx',
                            importPath: 'a->b->c',
                            sourceCode: '',
                            isNecessary: false,
                            reference: new Array<VulReference>(),
                        },
                        repair: {
                            traceID: '',
                            vulName: 'Log4j 远程代码执行漏洞',
                            file: {
                                name: 'pom.xml',
                                hash: 'cde',
                                vulList: [{ruleID: 'codescan_java_log4j-0_CVE-2021-44228', line: 12, hash: ''}],
                            },
                            type: ScanType.sca,
                        },
                        repairHint: false,
                        hash: '',
                    }],
                },
            ]]
        );
        const result = sast.formatResult(data);
        expect(result).toEqual(
            `检测完成，漏洞如下

漏洞统计：<br>


漏洞文件：[src/main.java](src/main.java)

【严重】Sql 注入漏洞[[行：12](./src/main.java:12)]
* 漏洞描述
* 修复建议

漏洞文件：[pom.xml](pom.xml)

【严重】org.apache.logging.log4j:log4j-core:2.1.0 - Log4j 远程代码执行漏洞[[行：12](./pom.xml:12)]
* 漏洞描述
* 修复建议
`
        );
    });
});

describe('VulCount', () => {
    test('incr cirtical', () => {
        const count = new VulCount();
        count.incr(RuleLevel.critical);
        expect(count.critical).toEqual(1);
    });
    test('incr high', () => {
        const count = new VulCount();
        count.incr(RuleLevel.high);
        expect(count.high).toEqual(1);
    });
    test('incr medium', () => {
        const count = new VulCount();
        count.incr(RuleLevel.medium);
        expect(count.medium).toEqual(1);
    });
    test('incr low', () => {
        const count = new VulCount();
        count.incr(RuleLevel.low);
        expect(count.low).toEqual(1);
    });
});
