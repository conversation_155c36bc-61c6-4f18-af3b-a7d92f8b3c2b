/* eslint-disable */
import {FileSystem, FileEntry, FileStat} from '@comate/plugin-host';

export class FileSystemMocker implements FileSystem {
    readFile(filePath: string): Promise<Buffer>;
    readFile(filePath: string, encoding: BufferEncoding): Promise<string>;
    readFile(filePath: string, encoding?: BufferEncoding): Promise<Buffer> | Promise<string> {
        return Promise.resolve(Buffer.from(filePath, 'utf-8'));
    }

    async readDirectory(directoryPath: string): Promise<FileEntry[]> {
        const files: FileEntry[] = [
            {name: '', type: 'file'},
        ];
        return Promise.resolve(files);
    }

    writeFile(filePath: string, content: string | Buffer): Promise<void> {
        return Promise.resolve();
    }

    async stat(filePath: string): Promise<FileStat> {
        const now = Date.now();
        return {type: 'file', size: 1, atimeMs: now, mtimeMs: now, ctimeMs: now};
    }

    disableLogging(): void {}
}

export class LoggerMocker {
    for(source: string): LoggerMocker {
        return this;
    }

    verbose(actionOrContent: string, detail?: Record<string, any>): void {}

    info(actionOrContent: string, detail?: Record<string, any>): void {}

    warn(actionOrContent: string, detail?: Record<string, any>): void {}

    error(actionOrContent: string, detail?: Record<string, any>): void {}
}

export class MarkdownRenderMocker {
    table<T>(data: T[], options: any) {
        return '';
    }
}
