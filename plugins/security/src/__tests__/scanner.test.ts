import {FileSystemMocker, LoggerMocker} from './mock.js';
import {describe, expect, test} from 'vitest';
import {NoneRepositoryInfo} from '@comate/plugin-host';
import {ScanResult, ScanType, Settings} from '../utils/http.js';
import {ComateContext} from '../sast/context.js';
import {initScanner, BundleUtils} from '../sast/scanner.js';
import {ScanItem} from '../sast/bundle.js';

describe('formatResult', async () => {
    const fs = new FileSystemMocker();
    const repoInfo: NoneRepositoryInfo = {
        versionControl: 'none',
    };
    const logger = new LoggerMocker();

    const context = new ComateContext(
        true,
        '',
        '',
        '',
        fs,
        logger,
        repoInfo,
        ''
    );
    const scanner = await initScanner(context);

    test('格式化结果', () => {
        const targetFiles = new Map<string, ScanItem>([['src/main.java', {type: ScanType.sast, hash: 'abc'}], [
            'pom.xml',
            {type: 3, hash: 'cde'},
        ]]);
        const data = [{
            tool: {
                driver: {
                    name: 'sast',
                    version: 'v1.2',
                },
                rules: [{
                    id: 'codescan_java_mybatis-java_sqli',
                    name: 'Sql 注入漏洞',
                    defaultConfiguration: {
                        level: 'ERROR',
                    },
                    help: {
                        markdown: '* 漏洞描述\n* 修复建议',
                        text: '* 漏洞描述\n* 修复建议',
                    },
                }],
            },
            result: [{
                ruleID: 'codescan_java_mybatis-java_sqli',
                message: {text: 'xxxxxx'},
                locations: [{
                    physicalLocation: {
                        artifactLocation: {
                            uri: 'src/main.java',
                            region: {
                                startLine: 12,
                                endLine: 12,
                            },
                        },
                    },
                }],
                properties: {
                    isAutofixable: false,
                    extra: {},
                },
            }],
        }, {
            tool: {
                driver: {
                    name: 'sca',
                    version: 'v1.2',
                },
                rules: [{
                    id: 'codescan_java_log4j-0_CVE-2021-44228',
                    name: 'Log4j 远程代码执行漏洞',
                    defaultConfiguration: {
                        level: 'ERROR',
                    },
                    help: {
                        markdown: '* 漏洞描述\n* 修复建议',
                        text: '* 漏洞描述\n* 修复建议',
                    },
                    properties: {
                        cveID: 'cve_xxxx',
                        repairVersion: {
                            min: '1.2.83',
                            max: '2.0.45',
                        },
                        reference: [
                            {
                                name: '文档1',
                                url: 'http://xxx',
                            },
                            {
                                name: '文档2',
                                url: 'http://yyy',
                            },
                        ],
                    },
                }],
            },
            result: [{
                ruleID: 'codescan_java_log4j-0_CVE-2021-44228',
                message: {
                    text: 'xxxxxx',
                },
                locations: [{
                    physicalLocation: {
                        artifactLocation: {
                            uri: 'pom.xml',
                            region: {
                                startLine: 12,
                                endLine: 12,
                            },
                        },
                    },
                }],
                properties: {
                    isAutoFixable: true,
                    importPath: 'a->b->c',
                    isWaved: true,
                    files: [{
                        compName: 'org.apache.logging.log4j:log4j-core',
                        compVersion: '2.1.0',
                        path: 'pom.xml',
                    }],
                },
            }],
        }] as ScanResult[];
        const b = new BundleUtils(targetFiles);
        const result = scanner.formatResult('', targetFiles, b.getFileHash(), data);

        for (const f of targetFiles.keys()) {
            expect(result.has(f)).toBeTruthy();
            // const res = result.get(f);

            // console.log(res?.result[0]);
        }
    });

    test('isTargetFile', async () => {
        const s = {
            scanConfiguration: {
                sca: {
                    enabled: true,
                    autofixedEnabled: true,
                    supportedLanguages: ['.xml', '.gradle', '.properties'],
                },
                sast: {
                    enabled: true,
                    autofixedEnabled: true,
                    supportedLanguages: ['.java', '.xml'],
                },
            },
            systemConfiguration: {
                limitFiles: 10,
                refreshConfInterval: 60,
                collectionScanFileTimeout: 60,
            },
        } as Settings;

        await scanner.syncConfig(s);
        const data = [
            {
                path: 'xx/xx/pom.xml',
                result: 3,
            },
            {
                path: 'xx/xx/abc.yaml',
                result: 0,
            },
            {
                path: 'xx/xx/.gradl',
                result: 0,
            },
            {
                path: 'xx/xx/abc.gradle',
                result: 1,
            },
            {
                path: 'abc/ddd/gradle.properties',
                result: 1,
            },
            {
                path: 'gradle-wrapper.properties',
                result: 1,
            },
            {
                path: 'test.java',
                result: 2,
            },
        ];
        for (const item of data) {
            const res = scanner.isTargetFile(item.path);
            expect(res).toEqual(item.result);
        }
    });
});
