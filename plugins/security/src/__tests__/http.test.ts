import {describe, expect, test, vi, afterEach} from 'vitest';

import * as comate from '@comate/plugin-host';

import * as http from '../utils/http.js';
import {getErrorMessage} from '../utils/error.js';
import {fail} from 'assert';

describe('getAnalysis', () => {
    afterEach(() => {
        vi.clearAllMocks();
    });

    const option = {
        bundleHash: '',
        baseURL: '',
        userID: '',
        requestID: '',
        chatID: '',
        trigger: http.Trigger.auto,
        scanType: http.ScanType.sca,
    };

    test('请求成功，执行中', async () => {
        const mockResponse = {
            status: http.AnalysisStatus.running,
            progress: 10,
            data: {},
        };
        // 不能 mock 同一个文件的，即不能 mock fetchWithTimeout，所以这里 mock comate.fetch
        const fetchMock = vi
            .spyOn(comate, 'fetch')
            .mockImplementation(() => {
                return Promise.resolve({
                    ok: true,
                    status: 200,
                    json: async () => mockResponse,
                } as Response);
            });

        const res = await http.getAnalysis(option);

        expect(fetchMock).toHaveBeenCalled();
        expect(res.type).toEqual(http.ResultType.succ);
        if (res.type === http.ResultType.succ) {
            expect(res.value.status).toEqual(mockResponse.status);
            expect(res.value.progress).toEqual(mockResponse.progress);
        }
    });
    test('请求成功，任务完成', async () => {
        const mockResponse = {
            status: http.AnalysisStatus.completed,
            progress: 0,
            data: {},
        };
        const fetchMock = vi
            .spyOn(comate, 'fetch')
            .mockImplementation(() =>
                Promise.resolve({
                    ok: true,
                    status: 200,
                    json: async () => mockResponse,
                } as Response)
            );

        const res = await http.getAnalysis(option);

        expect(fetchMock).toHaveBeenCalled();
        expect(res.type).toEqual(http.ResultType.succ);
        if (res.type === http.ResultType.succ) {
            expect(res.value.status).toBe(mockResponse.status);
        }
    });
    test('请求成功，任务失败', async () => {
        const mockResponse = {
            status: http.AnalysisStatus.failed,
            progress: 0,
            data: {},
        };
        const fetchMock = vi
            .spyOn(comate, 'fetch')
            .mockImplementation(() =>
                Promise.resolve({
                    ok: true,
                    status: 200,
                    json: async () => mockResponse,
                } as Response)
            );

        const res = await http.getAnalysis(option);

        expect(fetchMock).toHaveBeenCalled();
        expect(res.type).toEqual(http.ResultType.error);
    });
    test('请求成功，参数错误', async () => {
        const testCase = [{
            name: 'progress 缺失',
            mockResponse: {
                status: http.AnalysisStatus.running,
                data: {},
            },
        }, {
            name: 'progress 类型错误',
            mockResponse: {
                status: http.AnalysisStatus.running,
                process: 'abc',
                data: {},
            },
        }, {
            name: 'progress 数值错误',
            mockResponse: {
                status: http.AnalysisStatus.running,
                process: -1,
                data: {},
            },
        }];

        for (const c of testCase) {
            const fetchMock = vi
                .spyOn(comate, 'fetch')
                .mockImplementation(() =>
                    Promise.resolve({
                        ok: true,
                        status: 200,
                        json: async () => c.mockResponse,
                    } as Response)
                );

            const res = await http.getAnalysis(option);

            expect(fetchMock).toHaveBeenCalled();
            expect(res.type).toEqual(http.ResultType.error);
        }
    });
    test('请求失败', async () => {
        const fetchMock = vi
            .spyOn(comate, 'fetch')
            .mockImplementation(() =>
                Promise.resolve({
                    ok: false,
                    status: 400,
                } as Response)
            );

        const res = await http.getAnalysis(option);

        expect(fetchMock).toHaveBeenCalled();
        expect(res.type).toEqual(http.ResultType.error);
    });
});

describe('createBundle', () => {
    const option = {
        baseURL: '',
        userID: '',
        requestID: '',
        chatID: '',
        files: {},
        trigger: http.Trigger.auto,
    };

    test('请求成功，返回成功', async () => {
        const mockResponse: http.BundleResult = {
            status: 0,
            message: '',
            data: {
                bundleHash: 'bundle hash',
                missingFiles: [],
            },
        };
        const fetchMock = vi
            .spyOn(comate, 'fetch')
            .mockImplementation(() =>
                Promise.resolve({
                    ok: true,
                    status: 200,
                    json: async () => mockResponse,
                } as Response)
            );

        const res = await http.createBundle(option);

        expect(fetchMock).toHaveBeenCalled();
        expect(res.type).toEqual(http.ResultType.succ);
        if (res.type === http.ResultType.succ) {
            expect(res.value.bundleHash).toEqual(mockResponse.data.bundleHash);
        }
    });
    test('请求成功，返回失败', async () => {
        const mockResponse: http.BundleResult = {
            status: 1,
            message: '',
            data: {
                bundleHash: 'bundle hash',
                missingFiles: [],
            },
        };
        const fetchMock = vi
            .spyOn(comate, 'fetch')
            .mockImplementation(() =>
                Promise.resolve({
                    ok: true,
                    status: 200,
                    json: async () => mockResponse,
                } as Response)
            );

        const res = await http.createBundle(option);

        expect(fetchMock).toHaveBeenCalled();
        expect(res.type).toEqual(http.ResultType.error);
    });
    test('请求失败', async () => {
        const fetchMock = vi
            .spyOn(comate, 'fetch')
            .mockImplementation(() =>
                Promise.resolve({
                    ok: false,
                    status: 400,
                } as Response)
            );

        const res = await http.createBundle(option);

        expect(fetchMock).toHaveBeenCalled();
        expect(res.type).toEqual(http.ResultType.error);
    });
    test('请求成功，接口返回格式不正确', async () => {
        const testCase = [
            {
                name: 'status 缺失',
                mockResponse: {
                    message: '',
                    data: {},
                },
            },
            {
                name: 'data 缺失',
                mockResponse: {
                    status: 1,
                    message: '',
                },
            },
            {
                name: 'bundleHash 缺失',
                mockResponse: {
                    status: 1,
                    message: '',
                    data: {
                        missingFiles: [],
                    },
                },
            },
            {
                name: 'bundleHash 类型不正确',
                mockResponse: {
                    status: 1,
                    message: '',
                    data: {
                        bundleHash: 1,
                    },
                },
            },
            {
                name: '文件列表类型不正确',
                mockResponse: {
                    status: 1,
                    message: '',
                    data: {
                        bundleHash: 'bundle hash',
                        missingFiles: '',
                    },
                },
            },
            {
                name: '文件列表类型不正确',
                mockResponse: {
                    status: 1,
                    message: '',
                    data: {
                        bundleHash: 'bundle hash',
                        missingFiles: ['missing file', 1],
                    },
                },
            },
        ];

        for (const c of testCase) {
            const fetchMock = vi
                .spyOn(comate, 'fetch')
                .mockImplementation(() =>
                    Promise.resolve({
                        ok: true,
                        status: 200,
                        json: async () => c.mockResponse,
                    } as Response)
                );

            const res = await http.createBundle(option);

            expect(fetchMock).toHaveBeenCalled();
            expect(res.type).toEqual(http.ResultType.error);
        }
    });
});

describe('extendBundle', () => {
    const option = {
        bundleHash: '',
        baseURL: '',
        userID: '',
        requestID: '',
        chatID: '',
        files: {},
        trigger: http.Trigger.auto,
    };

    test('请求成功，返回成功', async () => {
        const mockResponse: http.BundleResult = {
            status: 0,
            message: '',
            data: {
                bundleHash: 'bundle hash',
                missingFiles: [],
            },
        };
        const fetchMock = vi
            .spyOn(comate, 'fetch')
            .mockImplementation(() =>
                Promise.resolve({
                    ok: true,
                    status: 200,
                    json: async () => mockResponse,
                } as Response)
            );

        const res = await http.extendBundle(option);

        expect(fetchMock).toHaveBeenCalled();
        expect(res.type).toEqual(http.ResultType.succ);
        if (res.type === http.ResultType.succ) {
            expect(res.value.bundleHash).toEqual(mockResponse.data.bundleHash);
        }
    });
    test('请求成功，返回失败', async () => {
        const mockResponse: http.BundleResult = {
            status: 1,
            message: '',
            data: {
                bundleHash: 'bundle hash',
                missingFiles: [],
            },
        };
        const fetchMock = vi
            .spyOn(comate, 'fetch')
            .mockImplementation(() =>
                Promise.resolve({
                    ok: true,
                    status: 200,
                    json: async () => mockResponse,
                } as Response)
            );

        const res = await http.extendBundle(option);

        expect(fetchMock).toHaveBeenCalled();
        expect(res.type).toEqual(http.ResultType.error);
    });
    test('请求失败', async () => {
        const fetchMock = vi
            .spyOn(comate, 'fetch')
            .mockImplementation(() =>
                Promise.resolve({
                    ok: false,
                    status: 400,
                } as Response)
            );

        const res = await http.extendBundle(option);

        expect(fetchMock).toHaveBeenCalled();
        expect(res.type).toEqual(http.ResultType.error);
    });
});

describe('settings', () => {
    const option = {
        bundleHash: '',
        baseURL: '',
        userID: '',
        requestID: '',
        chatID: '',
        trigger: http.Trigger.auto,
    };

    const responseBase: http.Settings = {
        scanConfiguration: {
            sca: {
                enabled: true,
                autofixedEnabled: true,
                supportedLanguages: ['.xml'],
            },
            sast: {
                enabled: true,
                autofixedEnabled: true,
                supportedLanguages: ['.java'],
            },
            autoScanEnabled: false,
        },
        systemConfiguration: {
            limitFiles: 1,
            manualLimitFiles: 1,
            refreshConfInterval: 1,
            collectionScanFileTimeout: 1,
        },
    };

    test('请求成功，参数正确', async () => {
        const mockResponse: http.SettingResult = {
            status: 0,
            message: '',
            data: responseBase,
        };
        const fetchMock = vi
            .spyOn(comate, 'fetch')
            .mockImplementation(() =>
                Promise.resolve({
                    ok: true,
                    status: 200,
                    json: async () => mockResponse,
                } as Response)
            );

        const res = await http.userSettings(option);

        expect(fetchMock).toHaveBeenCalled();
        expect(res.type).toEqual(http.ResultType.succ);
    });
    test('请求成功，参数格式不正确', async () => {
        const testCase = [
            {
                name: 'sca.supportedLanguages 为空',
                mockResponse: {
                    status: 0,
                    message: '',
                    data: {
                        scanConfiguration: {
                            sca: {
                                enabled: false,
                                autofixEnabled: true,
                                supportedLanguages: [],
                            },
                            sast: {
                                enabled: false,
                                autofixEnabled: true,
                                supportedLanguages: ['.java'],
                            },
                            autoScanEnabled: false,
                        },
                        systemConfiguration: {
                            limitFiles: 1,
                            refreshConfInterval: 1,
                            collectionScanFileTimeout: 1,
                        },
                    },
                },
            },
            {
                name: 'sast.supportedLanguages 为空',
                mockResponse: {
                    status: 0,
                    message: '',
                    data: {
                        scanConfiguration: {
                            sca: {
                                enabled: false,
                                autofixEnabled: true,
                                supportedLanguages: ['.xml'],
                            },
                            sast: {
                                enabled: false,
                                autofixEnabled: true,
                                supportedLanguages: [],
                            },
                            autoScanEnabled: false,
                        },
                        systemConfiguration: {
                            limitFiles: 1,
                            refreshConfInterval: 1,
                            collectionScanFileTimeout: 1,
                        },
                    },
                },
            },
            {
                name: '缺少 sast',
                mockResponse: {
                    status: 0,
                    message: '',
                    data: {
                        scanConfiguration: {
                            sca: {
                                enabled: false,
                                autofixEnabled: true,
                                supportedLanguages: ['.xml'],
                            },
                            autoScanEnabled: false,
                        },
                        systemConfiguration: {
                            limitFiles: 1,
                            refreshConfInterval: 1,
                            collectionScanFileTimeout: 1,
                        },
                    },
                },
            },
            {
                name: '缺少 sca',
                mockResponse: {
                    status: 0,
                    message: '',
                    data: {
                        scanConfiguration: {
                            sast: {
                                enabled: false,
                                autofixEnabled: true,
                                supportedLanguages: ['.go'],
                            },
                            autoScanEnabled: false,
                        },
                        systemConfiguration: {
                            limitFiles: 1,
                            refreshConfInterval: 1,
                            collectionScanFileTimeout: 1,
                        },
                    },
                },
            },
            {
                name: '缺少 limitFiles',
                mockResponse: {
                    status: 0,
                    message: '',
                    data: {
                        scanConfiguration: {
                            sca: {
                                enabled: false,
                                autofixEnabled: true,
                                supportedLanguages: ['.xml'],
                            },
                            sast: {
                                autofixEnabled: true,
                                supportedLanguages: [''],
                            },
                            autoScanEnabled: false,
                        },
                        systemConfiguration: {
                            refreshConfInterval: 1,
                            collectionScanFileTimeout: 1,
                        },
                    },
                },
            },
            {
                name: 'limitFiles 类型不正确',
                mockResponse: {
                    status: 0,
                    message: '',
                    data: {
                        scanConfiguration: {
                            sca: {
                                enabled: false,
                                autofixEnabled: true,
                                supportedLanguages: ['.xml'],
                            },
                            sast: {
                                enabled: false,
                                autofixEnabled: true,
                                supportedLanguages: [''],
                            },
                            autoScanEnabled: false,
                        },
                        systemConfiguration: {
                            limitFiles: 'abc',
                            refreshConfInterval: 1,
                            collectionScanFileTimeout: 1,
                        },
                    },
                },
            },
            {
                name: '缺少 autoScanEnabled',
                mockResponse: {
                    status: 0,
                    message: '',
                    data: {
                        scanConfiguration: {
                            sca: {
                                enabled: false,
                                autofixEnabled: true,
                                supportedLanguages: ['.xml'],
                            },
                            sast: {
                                enabled: false,
                                autofixEnabled: true,
                                supportedLanguages: [''],
                            },
                        },
                        systemConfiguration: {
                            limitFiles: 1,
                            refreshConfInterval: 1,
                            collectionScanFileTimeout: 1,
                        },
                    },
                },
            },
            {
                name: 'autoScanEnabled 类型错误',
                mockResponse: {
                    status: 0,
                    message: '',
                    data: {
                        scanConfiguration: {
                            sca: {
                                enabled: false,
                                autofixEnabled: true,
                                supportedLanguages: ['.xml'],
                            },
                            sast: {
                                enabled: false,
                                autofixEnabled: true,
                                supportedLanguages: [''],
                            },
                            autoScanEnabled: 1,
                        },
                        systemConfiguration: {
                            limitFiles: 1,
                            refreshConfInterval: 1,
                            collectionScanFileTimeout: 1,
                        },
                    },
                },
            },
            {
                name: 'refreshConfInterval 类型错误',
                mockResponse: {
                    status: 0,
                    message: '',
                    data: {
                        scanConfiguration: {
                            sca: {
                                enabled: false,
                                autofixEnabled: true,
                                supportedLanguages: ['.xml'],
                            },
                            sast: {
                                enabled: false,
                                autofixEnabled: true,
                                supportedLanguages: [''],
                            },
                            autoScanEnabled: 1,
                        },
                        systemConfiguration: {
                            limitFiles: 1,
                            refreshConfInterval: 'abc',
                            collectionScanFileTimeout: 1,
                        },
                    },
                },
            },
            {
                name: '缺少 refreshConfInterval',
                mockResponse: {
                    status: 0,
                    message: '',
                    data: {
                        scanConfiguration: {
                            sca: {
                                enabled: false,
                                autofixEnabled: true,
                                supportedLanguages: ['.xml'],
                            },
                            sast: {
                                enabled: false,
                                autofixEnabled: true,
                                supportedLanguages: [''],
                            },
                            autoScanEnabled: 1,
                        },
                        systemConfiguration: {
                            limitFiles: 1,
                            collectionScanFileTimeout: 1,
                        },
                    },
                },
            },
            {
                name: 'collectionScanFileTimeout 类型错误',
                mockResponse: {
                    status: 0,
                    message: '',
                    data: {
                        scanConfiguration: {
                            sca: {
                                enabled: false,
                                autofixEnabled: true,
                                supportedLanguages: ['.xml'],
                            },
                            sast: {
                                enabled: false,
                                autofixEnabled: true,
                                supportedLanguages: [''],
                            },
                            autoScanEnabled: 1,
                        },
                        systemConfiguration: {
                            limitFiles: 1,
                            refreshConfInterval: 1,
                            collectionScanFileTimeout: true,
                        },
                    },
                },
            },
            {
                name: '缺少 collectionScanFileTimeout',
                mockResponse: {
                    status: 0,
                    message: '',
                    data: {
                        scanConfiguration: {
                            sca: {
                                enabled: false,
                                autofixEnabled: true,
                                supportedLanguages: ['.xml'],
                            },
                            sast: {
                                enabled: false,
                                autofixEnabled: true,
                                supportedLanguages: [''],
                            },
                            autoScanEnabled: 1,
                        },
                        systemConfiguration: {
                            limitFiles: 1,
                            refreshConfInterval: 1,
                        },
                    },
                },
            },
        ];

        for (const c of testCase) {
            const fetchMock = vi
                .spyOn(comate, 'fetch')
                .mockImplementation(() =>
                    Promise.resolve({
                        ok: true,
                        status: 200,
                        json: async () => c,
                    } as Response)
                );

            const res = await http.userSettings(option);

            expect(fetchMock).toHaveBeenCalled();
            expect(res.type).toEqual(http.ResultType.error);
        }
    });
    test('请求成功，返回失败', async () => {
        const mockResponse: http.SettingResult = {
            status: 1,
            message: '',
            data: responseBase,
        };
        const fetchMock = vi
            .spyOn(comate, 'fetch')
            .mockImplementation(() =>
                Promise.resolve({
                    ok: true,
                    status: 200,
                    json: async () => mockResponse,
                } as Response)
            );

        const res = await http.userSettings(option);

        expect(fetchMock).toHaveBeenCalled();
        expect(res.type).toEqual(http.ResultType.error);
    });
    test('请求失败', async () => {
        const fetchMock = vi
            .spyOn(comate, 'fetch')
            .mockImplementation(() =>
                Promise.resolve({
                    ok: false,
                    status: 400,
                } as Response)
            );

        const res = await http.userSettings(option);

        expect(fetchMock).toHaveBeenCalled();
        expect(res.type).toEqual(http.ResultType.error);
    });
});

describe('report', () => {
    afterEach(() => {
        vi.clearAllMocks();
    });

    const option = {
        bundleHash: '',
        baseURL: '',
        userID: '',
        requestID: '',
        chatID: '',
        data: {},
    };

    test('请求成功', async () => {
        const mockResponse = {
            status: 0,
            message: '',
        };
        const fetchMock = vi
            .spyOn(comate, 'fetch')
            .mockImplementation(() =>
                Promise.resolve({
                    ok: true,
                    status: 200,
                    json: async () => mockResponse,
                } as Response)
            );

        const res = await http.report(option);

        expect(fetchMock).toHaveBeenCalled();
        expect(res.type).toEqual(http.ResultType.succ);
    });
    test('请求成功，上报失败', async () => {
        const mockResponse = {
            status: 1,
            message: '',
        };
        const fetchMock = vi
            .spyOn(comate, 'fetch')
            .mockImplementation(() =>
                Promise.resolve({
                    ok: true,
                    status: 200,
                    json: async () => mockResponse,
                } as Response)
            );

        const res = await http.report(option);

        expect(fetchMock).toHaveBeenCalled();
        expect(res.type).toEqual(http.ResultType.error);
    });
    test('请求失败', async () => {
        const fetchMock = vi
            .spyOn(comate, 'fetch')
            .mockImplementation(() =>
                Promise.resolve({
                    ok: false,
                    status: 400,
                } as Response)
            );

        const res = await http.report(option);

        expect(fetchMock).toHaveBeenCalled();
        expect(res.type).toEqual(http.ResultType.error);
    });
});

describe('uploadFile', () => {
    afterEach(() => {
        vi.clearAllMocks();
    });

    const option = {
        baseURL: '',
        userID: '',
        requestID: '',
        chatID: '',
        files: {},
    };

    test('请求成功', async () => {
        const mockResponse = {
            status: 0,
            message: '',
        };
        const fetchMock = vi
            .spyOn(comate, 'fetch')
            .mockImplementation(() =>
                Promise.resolve({
                    ok: true,
                    status: 200,
                    json: async () => mockResponse,
                } as Response)
            );

        const res = await http.uploadFile(option);

        expect(fetchMock).toHaveBeenCalled();
        expect(res.type).toEqual(http.ResultType.succ);
    });
    test('请求成功，修复失败', async () => {
        const mockResponse = {
            status: 1,
            message: '',
        };
        const fetchMock = vi
            .spyOn(comate, 'fetch')
            .mockImplementation(() =>
                Promise.resolve({
                    ok: true,
                    status: 200,
                    json: async () => mockResponse,
                } as Response)
            );

        const res = await http.uploadFile(option);

        expect(fetchMock).toHaveBeenCalled();
        expect(res.type).toEqual(http.ResultType.error);
    });
    test('请求失败', async () => {
        const fetchMock = vi
            .spyOn(comate, 'fetch')
            .mockImplementation(() =>
                Promise.resolve({
                    ok: false,
                    status: 400,
                } as Response)
            );

        const res = await http.uploadFile(option);

        expect(fetchMock).toHaveBeenCalled();
        expect(res.type).toEqual(http.ResultType.error);
    });
});

describe('newChat', () => {
    afterEach(() => {
        vi.clearAllMocks();
    });

    const option = {
        baseURL: '',
        userID: '',
        requestID: '',
        chatID: '',
        type: 1,
        ide: '',
        query: '',
        gitInfo: {gitURL: '', gitBranch: ''},
        status: 0,
        errMsg: '',
        vuls: [],
    };

    test('请求成功', async () => {
        const mockResponse = {
            status: 0,
            message: '',
        };
        const fetchMock = vi
            .spyOn(comate, 'fetch')
            .mockImplementation(() =>
                Promise.resolve({
                    ok: true,
                    status: 200,
                    json: async () => mockResponse,
                } as Response)
            );

        const res = await http.newChat(option);

        expect(fetchMock).toHaveBeenCalled();
        expect(res.type).toEqual(http.ResultType.succ);
    });
    test('请求成功，上传对话失败', async () => {
        const mockResponse = {
            status: 1,
            message: '',
        };
        const fetchMock = vi
            .spyOn(comate, 'fetch')
            .mockImplementation(() =>
                Promise.resolve({
                    ok: true,
                    status: 200,
                    json: async () => mockResponse,
                } as Response)
            );

        const res = await http.newChat(option);

        expect(fetchMock).toHaveBeenCalled();
        expect(res.type).toEqual(http.ResultType.error);
    });
    test('请求失败', async () => {
        const fetchMock = vi
            .spyOn(comate, 'fetch')
            .mockImplementation(() =>
                Promise.resolve({
                    ok: false,
                    status: 400,
                } as Response)
            );

        const res = await http.newChat(option);

        expect(fetchMock).toHaveBeenCalled();
        expect(res.type).toEqual(http.ResultType.error);
    });
});

describe('fetchWithTimeout', () => {
    afterEach(() => {
        vi.clearAllMocks();
    });

    test('请求异常一次', async () => {
        const fetchMock = vi
            .spyOn(comate, 'fetch')
            .mockImplementationOnce(() => {
                throw new Error('error');
            })
            .mockImplementationOnce(() =>
                Promise.resolve({
                    ok: true,
                    status: 400,
                } as Response)
            );

        await http.fetchWithTimeout('', {}, 1);

        expect(fetchMock).toHaveBeenCalledTimes(2);
    });

    test('请求异常', async () => {
        const fetchMock = vi
            .spyOn(comate, 'fetch')
            .mockImplementation(() => {
                throw new Error('error');
            });
        try {
            await http.fetchWithTimeout('', {}, 1);
            fail();
        }
        catch (e) {
            expect(getErrorMessage(e)).toEqual('error');
        }

        expect(fetchMock).toHaveBeenCalledTimes(3);
    }, 10000);
});
