{"private": true, "name": "@comate-plugin/security", "version": "0.9.2", "type": "module", "scripts": {"clean": "rm -rf dist", "dev": "cpd dev", "build": "npm run clean && tsc && cpd build", "lint": "eslint src", "test": "vitest", "format": "dprint fmt '**/*.{ts,tsx,json}'"}, "devDependencies": {"@comate/plugin-cli": "^0.9.2", "@comate/plugin-host": "^0.9.2", "@comate/plugin-shared-internals": "^0.9.2", "@types/pako": "^2.0.3", "vitest": "^1.2.2"}, "comate": {"name": "security", "version": "2.6.4", "icon": "./assets/comate.png", "entry": "./dist/index.js", "displayName": "Comate", "description": "代码安全检测、扫描", "keyword": ["安全", "扫描", "sca", "sast", "供应链", "代码漏洞"], "capabilities": [{"type": "Skill", "name": "codeScan", "displayName": "代码安全", "description": "代码安全", "displayTag": "beta", "defaultUserMessage": "为我的代码库扫描可能的安全漏洞", "placeholder": "回车或点击发送发起检测"}, {"type": "<PERSON><PERSON><PERSON>", "name": "secubot", "displayName": "代码安全", "description": "代码安全", "defaultUserMessage": "为我的代码库扫描、修复可能的安全漏洞", "placeholder": "回车或点击发送发起检测"}, {"type": "Fallback", "name": "vulRepair", "displayName": "漏洞修复", "description": "漏洞修复"}, {"type": "DiagnosticScan", "name": "diagnosticScan", "displayName": "漏洞修复", "description": "代码安全"}], "configSchema": {"sections": []}}, "dependencies": {"@comate/plugin-host": "^0.9.2", "async-mutex": "^0.5.0", "fast-glob": "^3.3.2", "ignore": "^5.3.1", "pako": "^2.1.0", "slash": "^5.1.0", "watcher": "^2.3.0"}}