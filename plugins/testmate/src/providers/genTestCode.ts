/* eslint-disable complexity, max-statements */
import {
    SkillProvider,
    FunctionParameterDefinition,
    TaskProgressChunkStream,
    TaskProgressChunk,
    ProviderInit,
} from '@comate/plugin-host';
import {generateTestCode, download, unzipFile} from '../service/genTestCode.js';
import fs from 'fs';
import os from 'os';
import path from 'path';
import {spawnSync} from 'child_process';
import {chmod} from 'fs/promises';

export class GenTestCodeSkillProvider extends SkillProvider {
    static skillName = 'generateTestCode';
    static description = '可用于根据用户需求描述智能生成测试代码';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    private readonly cwd: string;

    constructor(init: ProviderInit) {
        super(init);
        this.cwd = init.cwd;
    }

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new TaskProgressChunkStream();
        try {
            const userDetail = await this.currentUser.requestDetail();

            if (!userDetail) {
                yield stream.flushReplaceLast('请先完成授权，插件需在用户授权后才能使用。');
                return;
            }

            yield stream.flush('\n\n正在生成测试代码...');

            let pythonOutput = null;
            const generateLanguage = await this.config.getAsString('generateLanguage', 'auto');
            const repository = await this.retriever.repositoryInfo();
            const {versionControl} = repository;
            const {query, activeFileName, activeFileContent, activeFileLanguage, selectedCode} = this.currentContext;
            const isGitVersionControl = versionControl === 'git';
            const destinationFolder = process.cwd();
            const platform = os.platform();

            const platformConfig = {
                darwin: {
                    pythonEnvUrl: 'https://isoland-resource.bj.bcebos.com/testmate/testmate_python_mac.zip',
                    pythonEnvPath: path.join(destinationFolder, 'testmate_python_mac'),
                    pythonEnvBinPath: path.join(destinationFolder, 'testmate_python_mac', 'bin', 'python3.10'),
                    pythonScriptPath: path.join(
                        destinationFolder,
                        'testmate_python_mac',
                        'python-scripts',
                        'getCaseFromGo.py'
                    ),
                },
                win32: {
                    pythonEnvUrl: 'https://isoland-resource.bj.bcebos.com/testmate/testmate_python_win.zip',
                    pythonEnvPath: path.join(destinationFolder, 'testmate_python_win'),
                    pythonEnvBinPath: path.join(destinationFolder, 'testmate_python_win', 'python.exe'),
                    pythonScriptPath: path.join(
                        destinationFolder,
                        'testmate_python_win',
                        'python-scripts',
                        'getCaseFromGo.py'
                    ),
                },
                linux: {
                    pythonEnvUrl: 'https://isoland-resource.bj.bcebos.com/testmate/testmate_python_linux.zip',
                    pythonEnvPath: path.join(destinationFolder, 'testmate_python_linux'),
                    pythonEnvBinPath: path.join(destinationFolder, 'testmate_python_linux', 'bin', 'python3.10'),
                    pythonScriptPath: path.join(
                        destinationFolder,
                        'testmate_python_linux',
                        'python-scripts',
                        'getCaseFromGo.py'
                    ),
                },
            };
            if (!platformConfig.hasOwnProperty(platform)) {
                yield stream.flushReplaceLast(`不支持当前系统：${platform}`);
                this.logger.info(`不支持当前系统：${platform}`);
                return;
            }
            const config = platformConfig[platform as keyof typeof platformConfig];
            const {pythonEnvUrl, pythonEnvPath, pythonEnvBinPath, pythonScriptPath} = config;

            if (activeFileLanguage === 'go') {
                // 下载python环境，并解压至插件目录
                if (!fs.existsSync(pythonEnvBinPath) || !fs.existsSync(pythonScriptPath)) {
                    yield stream.flush('\n\n首次使用研发代码生成测试用例时，将会有几分钟等待时间，请稍候...');
                    const zipFilePath = await download(pythonEnvUrl, destinationFolder);
                    await unzipFile(zipFilePath, `${destinationFolder}`);
                }
                await Promise.all([
                    chmod(pythonEnvBinPath, 0o760),
                    chmod(pythonScriptPath, 0o760),
                ]);

                const pythonParams = {
                    chatMsg: query,
                    repoInfo: {
                        pythonPath: pythonEnvPath,
                        projectPath: this.cwd,
                        fileName: activeFileName,
                        fileContent: activeFileContent,
                        selectedCode: selectedCode,
                        language: activeFileLanguage,
                    },
                };
                const jsonParams = JSON.stringify(pythonParams);
                const pythonArgs = ['--params', jsonParams];
                const pythonProcess = spawnSync(pythonEnvBinPath, [pythonScriptPath, ...pythonArgs]);
                pythonOutput = pythonProcess.stdout.toString('utf-8').trim();
                this.logger.info(`\n\npythonParams： ${jsonParams}`);
                const exitCode = pythonProcess.status;
                const pythonError = pythonProcess.stderr.toString();
                if (exitCode !== 0) {
                    this.logger.info(`\n\n代码库解析失败:${pythonError}。请联系工作人员`);
                }
            }

            const apiParams = {
                chatMsg: query,
                repoInfo: {
                    repoName: isGitVersionControl ? repository.repositoryUrl : '',
                    branch: isGitVersionControl ? repository.branch : '',
                    currentFile: {
                        fileName: activeFileName,
                        content: activeFileContent,
                        controllerFunc: selectedCode,
                    },
                    context: pythonOutput,
                },
                username: userDetail.name,
                pluginConfig: {
                    language: generateLanguage,
                    frame: '', // 暂时拿不到，临时传空字符串
                },
            };
            const jsonParams1 = JSON.stringify(apiParams);
            this.logger.info(`apiParams: ${jsonParams1}`);
            const res = await generateTestCode(this, apiParams);
            const {code, message, data} = res as any;
            if (code !== 200) {
                this.logger.info(
                    `Plugin Info: 生成测试代码失败：${message}，请求参数：${JSON.stringify(apiParams)}`
                );
                yield stream.flushReplaceLast(`生成测试代码失败：${message}\n\n`);
                return;
            }

            const {type, detail} = data;
            if (!detail || detail.length === 0) {
                yield stream.flushReplaceLast('\n\n未生成测试代码，请检查输入是否正确。');
                return;
            }

            yield stream.flushReplaceLast('\n\n已生成测试代码');
            if (type === 'code') {
                for (const item of detail) {
                    const {content, language} = item;
                    yield stream.flush(
                        '\n\n' + [
                            '```' + language,
                            content,
                            '```',
                        ]
                            .join('\n')
                    );
                }
            }
            else if (type === 'markdown') {
                for (const item of detail) {
                    const {content} = item;
                    yield stream.flush('\n\n' + content);
                }
            }
        }
        catch (error) {
            this.logger.error(`Plugin Error: ${error as string}`);
            // eslint-disable-next-line max-len
            yield stream.flushReplaceLast(
                `\n\n请求出错，请联系负责人******************、**********************或稍后再试。${error as string}`
            );
        }
    }
}
