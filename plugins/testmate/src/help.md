您好，我是自动化用例编写助手，提供自动化用例生成，自动化用例改写和扩展，以及根据自然语言生成用例功能。

一\.调起TestMate插件能力

    方法1：直接调用testmate的测试能力

          在输入中输入："/生成测试代码"

    方法2：依次调用TestMate和测试能力

         （1）\.输入@TestMate，回车

        （2）输入生成测试代码，回车



二\. 生成用例或代码

    场景1：根据鼠标选中代码和自然语言描述生成测试用例

        使用鼠标划选部分代码，并输入对选中代码的修改指令，生成新的用例，

        样例1：生成异常用例

            （1）生成随机条数异常用例：

            （2）生成指定条数的异常用例：生成4条异常用例

            （3）指定参数异常的用例，例如：生成user\_name异常的测试用例

            更多的异常用例场景，欢迎大家体验

         样例2：修改选中用例的输入或校验

            描述：当user\_id为null时，校验code不等于0



    场景2：根据自然语言描述生成测试代码

          学习代码库中代码片段与描述，结合本次的自然语言描述，生成新的代码或用例。



    场景3：根据输入IAPI的接口文档链接生成测试用例

         首先打开IAPI，复制接口文档的链接，再把复制的链接粘贴到插件的输入框中，点击生成



    场景4：输入用例描述\+IAPI链接

          首先在IAPI平台复制接口文档链接，然后在输入框中粘贴该链接，并输入对生成用例的要求

三\. 采纳用例或代码

    1\.鼠标点击右侧代码文件中的某个位置

    2\.点击采纳按钮

    3\.生成的用例或代码即粘贴到光标位置

