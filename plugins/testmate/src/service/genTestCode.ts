import {request} from './request.js';
import {SERVER_HOST} from '../config/config.js';
import fs from 'fs';
import * as path from 'path';
import AdmZip from 'adm-zip';
import axios, {AxiosResponse} from 'axios';

interface GenerateTestCodeParams {
    chatMsg: string;
    repoInfo: {
        repoName: string | null;
        branch?: string | null;
        currentFile?: {
            fileName?: string;
            content?: string;
            controllerFunc?: string;
        };
    };
    username: string;
    pluginConfig?: {
        language?: string;
        frame?: string;
    };
}

export const generateTestCode = (_this: any, params: GenerateTestCodeParams) => {
    return request(
        _this,
        'post',
        `${SERVER_HOST}/api/v1/generateTestCode`,
        {
            body: JSON.stringify({
                ...params,
            }),
            headers: {
                'Content-Type': 'application/json',
            },
        }
    );
};

export const makeDirectorySync = (path: string) => {
    if (!fs.existsSync(path)) {
        fs.mkdirSync(path, {recursive: true});
    }
};

export async function download(url: string, destinationFolder: string, filename?: string): Promise<string> {
    try {
        makeDirectorySync(destinationFolder); // 确保这个函数是定义过的，或者使用现有的库函数，如fs-extra的ensureDirSync

        const resolvedFilename = filename || path.basename(url.split('?')[0]) || `download_${Date.now()}`;
        const destination = path.join(destinationFolder, resolvedFilename);

        const response: AxiosResponse = await axios({
            method: 'get',
            url: url,
            responseType: 'stream',
        });

        if (response.status !== 200) {
            throw new Error(`Download server responded with status code ${response.status}`);
        }

        const writer = fs.createWriteStream(destination);
        response.data.pipe(writer);
        await new Promise((resolve, reject) => {
            writer.on('finish', resolve);
            writer.on('error', reject);
        });

        return destination;
    }
    catch (err) {
        // 如果文件已经创建，但下载失败，则删除它
        const resolvedFilename = filename || path.basename(url.split('?')[0]) || `download_${Date.now()}`;
        const destination = path.join(destinationFolder, resolvedFilename);

        if (fs.existsSync(destination)) {
            fs.unlinkSync(destination);
        }
        throw err;
    }
}

export function unzipFile(zipFilePath: string, destinationFolder: string): Promise<void> {
    return new Promise<void>((resolve, reject) => {
        try {
            const zip = new AdmZip(zipFilePath);
            zip.extractAllTo(destinationFolder, true);
            // 删除压缩包
            fs.unlinkSync(zipFilePath);
            resolve();
        }
        catch (err) {
            reject(err);
        }
    });
}
