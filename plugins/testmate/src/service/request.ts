export function request(_this: any, method: string, url: string, options: any = {}, timeout: number = 600000) {
    return new Promise((resolve, reject) => {
        fetch(`${url}`, {
            method,
            ...options,
            signal: AbortSignal.timeout(timeout),
        })
            .then(async response => {
                if (response.ok) {
                    const ret = await response.json();
                    resolve(ret);
                }
                else {
                    const errorMessage = '请求超时，请稍后再试。';
                    _this.logger.error(
                        `Plugin Error: ${errorMessage}请求参数：${JSON.stringify({method, url, ...options})}`
                    );
                    reject(new Error(errorMessage));
                }
            })
            .catch(() => {
                const errorMessage = '请求超时，请稍后再试。';
                _this.logger.error(
                    `Plugin Error: ${errorMessage}请求参数：${JSON.stringify({method, url, ...options})}`
                );
                return reject(errorMessage);
            });
    });
}
