{"name": "@comate-plugin/testmate", "version": "1.0.0", "type": "module", "scripts": {"clean": "rm -rf dist", "dev": "cpd dev", "build": "tsc && cpd build", "lint": "eslint --max-warnings=0 src --fix"}, "dependencies": {"@types/axios": "^0.14.0", "adm-zip": "^0.5.12", "axios": "^1.6.7"}, "devDependencies": {"@comate/plugin-cli": "^0.9.2", "@comate/plugin-host": "^0.9.2", "@types/adm-zip": "^0.5.5"}, "comate": {"name": "testmate", "version": "1.0.0", "icon": "./assets/icon.svg", "entry": "./dist/index.js", "displayName": "TestMate", "description": "智能测试助理", "keyword": ["TestMate", "Test"], "author": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jianglingzhi", "email": "<EMAIL>"}], "capabilities": [{"type": "Skill", "name": "testmate-help", "displayName": "插件介绍", "description": "插件介绍", "placeholder": "无需输入内容，回车获得插件介绍"}, {"type": "Skill", "name": "genTestCode", "displayName": "生成测试代码", "description": "根据 iAPI 接口文档协作链接或需求描述智能生成测试代码", "placeholder": "请输入中文描述或 iAPI 接口链接"}], "configSchema": {"enabled": true, "sections": [{"title": "插件配置", "properties": {"generateLanguage": {"type": "string", "enum": ["auto", "python", "java", "go"], "title": "生成测试函数语言", "description": "生成测试函数的语言", "default": "auto"}}}]}}}