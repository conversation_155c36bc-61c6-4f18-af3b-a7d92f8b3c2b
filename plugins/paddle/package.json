{"private": true, "name": "@comate-plugin/paddle", "version": "0.8.0", "type": "module", "scripts": {"clean": "rm -rf dist", "dev": "cpd dev", "build": "tsc && cpd build"}, "dependencies": {}, "devDependencies": {"@comate/plugin-cli": "^0.9.2", "@comate/plugin-host": "^0.9.2", "@comate/plugin-shared-internals": "^0.9.2", "typescript": "^5.3.2"}, "comate": {"name": "paddle", "version": "1.0.0", "icon": "./assets/icon.png", "entry": "./dist/index.js", "displayName": "飞桨", "description": "PaddlePaddle深度学习编程助手，支持智能问答、代码编写", "keyword": [], "files": ["dist", "assets"], "capabilities": [{"type": "Skill", "name": "paddle-help", "displayName": "插件介绍", "description": "插件介绍", "placeholder": "无需输入内容，回车获得插件介绍"}, {"type": "Skill", "name": "code-generate", "displayName": "代码生成", "description": "深度学习与飞桨框架的智能编程助手", "placeholder": "请描述你的飞桨代码需求", "tipConfig": {"tip": "你可参考如下代码来编程", "tipCodeBlock": {"type": "selected", "showTipWhileEmpty": false}}}, {"type": "Skill", "name": "code-convert", "displayName": "代码转换", "description": "将其他深度学习框架（例如Pytorch、Tensorflow、Keras等）的代码转换为飞桨的代码", "placeholder": "默认当前文件、或#选择路径、或输入相对路径、或框选代码", "querySelector": {"language": ["python"], "activeFileContent": ["import torch", "from torch", "torch."]}}, {"type": "Skill", "name": "code-explain", "displayName": "代码解释", "description": "解释深度学习与飞桨框架的代码", "placeholder": "框选代码后回车或点击发送", "tipConfig": {"tip": "请解释如下代码", "tipCodeBlock": {"type": "selected", "showTipWhileEmpty": false}}}, {"type": "Skill", "name": "chat", "displayName": "", "description": "智能解答深度学习与飞桨框架的问题", "querySelector": {"language": ["python", "cpp", "c", "go"], "activeFileContent": ["import paddle", "from paddle", "paddle_inference_api.h", "paddle/fluid/inference/goapi", "paddle.", "paddle_infer::"]}, "tipConfig": {"tip": "", "tipCodeBlock": {"type": "selected", "showTipWhileEmpty": false}}}, {"type": "Fallback", "name": "fallback", "description": "智能解答深度学习与飞桨框架的问题", "tipConfig": {"tip": "", "tipCodeBlock": {"type": "selected", "showTipWhileEmpty": false}}}], "configSchema": {"sections": []}}}