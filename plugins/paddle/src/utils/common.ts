import {Logger} from '@comate/plugin-host';
import fs from 'fs';
import path from 'path';

export function copyDir(src: string, dest: string) {
    // 创建目标目录
    if (!fs.existsSync(dest)) {
        fs.mkdirSync(dest, {recursive: true});
    }

    // 读取源目录中的文件/目录列表
    let entries = fs.readdirSync(src, {withFileTypes: true});

    // 遍历文件/目录列表
    for (let entry of entries) {
        let srcPath = path.join(src, entry.name);
        let destPath = path.join(dest, entry.name);

        // 如果是目录，则递归拷贝
        if (entry.isDirectory()) {
            copyDir(srcPath, destPath);
        }
        else {
            // 如果是文件，则直接拷贝
            fs.copyFileSync(srcPath, destPath);
        }
    }
}

export function rmDir(dirPath: string, logger: Logger) {
    try{
        if (!fs.existsSync(dirPath)) {
            return;
        }

        let entries = fs.readdirSync(dirPath, { withFileTypes: true });
        if (entries.length === 0) {
            // 目录为空，直接删除
            fs.rmdirSync(dirPath);
            logger.info(`目录 ${dirPath} 删除成功`)
            return;
        }
        for (let entry of entries) {
            let fullPath = path.join(dirPath, entry.name);
            if (entry.isDirectory()) {
                rmDir(fullPath, logger);
            } else {
                fs.unlinkSync(fullPath);
            }
        }
        logger.info(`目录 ${dirPath} 删除成功`)
    } catch {
        logger.error(`目录 ${dirPath} 删除失败`)
    }
    fs.rmdirSync(dirPath);
}
export function getAllFilesPath(dirPath: string, logger: Logger): string[] {
    let fileNames: string[] = [];
    const files = fs.readdirSync(dirPath);
    for (const file of files) {
        const fullPath = path.join(dirPath, file);
        const stats = fs.statSync(fullPath);
        if (stats.isDirectory()) {
            // 如果是目录，则递归调用
            fileNames = fileNames.concat(getAllFilesPath(fullPath, logger));
        }
        else {
            // 如果是文件，只添加文件名到列表中
            fileNames.push(fullPath);
        }
    }
    return fileNames;
}

export function readFiles(filePaths: string[], baseDir: string, logger: Logger): string[] {
    return filePaths.map(file => {
        try {
            // 同步读取文件内容
            const res = fs.readFileSync(file, 'utf8');
            return `\`\`\`\n${res}\`\`\``;
        }
        catch (error) {
            logger.error(`Error reading file ${file}: ${error}`);
            return ''; // 或者你可以返回一个特定的错误消息或null
        }
    });
}

export function readFile(filePath: string, logger: Logger): string {
    try {
        // 同步读取文件内容
        const res = fs.readFileSync(filePath, 'utf8');
        return res;
    }
    catch (error) {
        logger.error(`Error reading file ${filePath}: ${error}`);
        return ''; // 或者你可以返回一个特定的错误消息或null
    }
}

export function ifOverSize(files: string[], baseDir: string, logger: Logger): boolean {
    const limit = 5 * 1024 * 1024; // 5MB in bytes
    let totalSize = 0;
    for (const file of files) {
        const filePath = file;
        try {
            const stats = fs.statSync(filePath);
            if (stats.isFile()) {
                totalSize += stats.size;
                if (totalSize > limit) {
                    return true;
                }
            }
            else {
                logger.error(`${filePath} is not a file, skipping.`);
            }
        }
        catch (error) {
            logger.error(`Error statting file ${filePath}: ${error}`);
        }
    }
    return false;
}
