export function getLanguageFromFilePath(filePath: string): string {
    const extension = filePath.split('.').pop();
    switch (extension) {
        case 'bat':
            return 'bat';
        case 'c':
            return 'c';
        case 'cc':
            return 'cpp';
        case 'cu':
            return 'cuda-cpp';
        case 'go':
            return 'go';
        case 'js':
            return 'javascript';
        case 'ts':
            return 'typescript';
        case 'py':
            return 'python';
        case 'sh':
            return 'shellscript';
        case 'yaml':
            return 'yaml';
        // 可以继续添加其他语言扩展名的情况
        default:
            return ''; // 如果不知道扩展名对应的语言，返回空字符串
    }
}

export function trimKnowledgeCode(list: string[], maxCount = 10, maxLength = 20000) {
    let curLength = 0;
    const output: string[] = [];
    for (const item of list) {
        if (output.length >= maxCount || curLength + item.length > maxLength) {
            break;
        }
        output.push(item);
        curLength += item.length;
    }
    return output;
}

// 近似估计Ernie的token数，1Token≈2汉字≈4字符
export function estimateTokens(
    promptTemplate: string = '',
    activeFileLanguage: string = '',
    activeFilePath: string = '',
    trim_knowledge: string[] = [''],
    trim_code: string[] = [''],
    query: string = '',
    selectedCode: string = ''
) {
    let estimateTokens = 0;
    estimateTokens += promptTemplate.length / 2;
    estimateTokens += activeFileLanguage.length / 4;
    estimateTokens += activeFilePath.length / 4;
    for (const item of trim_knowledge) {
        estimateTokens += item.length / 2.2;
    }
    for (const item of trim_code) {
        estimateTokens += item.length / 4;
    }
    estimateTokens += query.length / 2;
    estimateTokens += selectedCode.length / 4;
    estimateTokens += 200; // 留出buffer余量
    return estimateTokens;
}
