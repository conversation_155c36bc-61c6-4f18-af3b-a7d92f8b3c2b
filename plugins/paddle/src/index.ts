import {HelpSkillProvider} from './providers/help.js';
import {PluginSetupContext} from '@comate/plugin-host';
import {ChatSkillProvider} from './providers/chat.js';
import {CodeGenerateSkillProvider} from './providers/codeGenerate.js';
import {CodeConvertSkillProvider} from './providers/codeConvert.js';
import {CodeExplainSkillProvider} from './providers/codeExplain.js';
import {CustomFallbackProvider} from './providers/fallback.js';

export function setup({registry}: PluginSetupContext) {
    registry.registerSkillProvider('paddle-help', HelpSkillProvider);
    registry.registerSkillProvider('code-generate', CodeGenerateSkillProvider);
    registry.registerSkillProvider('code-convert', CodeConvertSkillProvider);
    registry.registerSkillProvider('code-explain', CodeExplainSkillProvider);
    registry.registerSkillProvider('chat', ChatSkillProvider);
    registry.registerFallbackProvider('fallback', CustomFallbackProvider, true);
}
