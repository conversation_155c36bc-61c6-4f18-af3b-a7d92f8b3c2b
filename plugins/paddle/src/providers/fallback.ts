import {
    FallbackProvider,
    TaskProgressChunk,
    TextModel,
    StringChunkStream,
} from '@comate/plugin-host';
import chatPromptTemplate from '../prompts/chat.prompt';

export class CustomFallbackProvider extends FallbackProvider {
    static description = '智能解答深度学习与飞桨框架的问题';

    async *handleQuery(): AsyncIterableIterator<TaskProgressChunk> {
        try {
            this.logger.info('paddle智能问答耗时统计', {message: `开始执行智能问答 ${performance.now()}`});

            const {activeFilePath, selectedCode, activeFileLanguage, query} = this.currentContext;
            const stream = new StringChunkStream();

            yield stream.flush('正在思考中，请耐心等候...');

            const queryWithCode = selectedCode.trim()
                ? `${query}\n\`\`\`${activeFileLanguage}\n${selectedCode}\n\`\`\``
                : query;

            this.logger.info('paddle智能问答耗时统计', {message: `开始执行知识集检索 ${performance.now()}`});
            let start = performance.now();
            const [knowledge1, knowledge2] = await Promise.all([
                this.retriever.knowledgeFromQuery(
                    queryWithCode,
                    {
                        knowledgeSets: [
                            // 飞桨API接口文档集
                            {uuid: '564a9ebb-a3b1-4d5b-ad28-27c0ab6d78fe', type: 'NORMAL'},
                        ],
                        queryRewrite: true,
                        topK: 7,
                    }
                ),
                this.retriever.knowledgeFromQuery(
                    queryWithCode,
                    {
                        knowledgeSets: [
                            // 飞桨框架与生态文档集
                            {uuid: '7ece94ce-e39a-414e-8ca5-d80ed4cf300b', type: 'NORMAL'},
                        ],
                        queryRewrite: true,
                        topK: 8,
                    }
                ),
            ]);
            this.logger.info('paddle智能问答耗时统计', {message: `知识集检索结束，耗时${performance.now() - start}ms`});

            start = performance.now();
            const trim_knowledge = [...knowledge1, ...knowledge2];
            const trim_code = [''];

            const prompt = this.llm.createPrompt(chatPromptTemplate, {
                activeFilePath,
                trim_knowledge,
                trim_code,
                queryWithCode,
            });
            const chunks = this.llm.askForTextStreaming(prompt, {
                model: TextModel.ErnieBot128,
                modelOptions: {
                    temperature: 0.3,
                    topP: 0.8,
                    penaltyScore: 1.0,
                    enableMultiturnDialogue: {byPlugin: true, onlyQuery: query},
                },
            });

            // ******************  调试代码，调试时再打开 ***************  /
            /*
            yield stream.flush('\n\n---\n\n');

            yield stream.flush(`\n\n共检索到${trim_knowledge.length}条知识：\n\n`);
            yield stream.flush('---\n\n');
            const state = {
                text_length: 0,
                code_length: 0,
            };
            for (const text of trim_knowledge) {
                yield stream.flush(text.split('\n').map(v => '> ' + v).join('\n'));
                yield stream.flush('\n\n---\n\n');
                state.text_length += text.length;
            }
            yield stream.flush(`文本总字符长度: ${state.text_length}\n\n`);
            yield stream.flush('---\n\n');

            yield stream.flush(`\n\n共检索到${trim_code.length}条代码：\n\n`);
            yield stream.flush('---\n\n');
            for (const text of trim_code) {
                yield stream.flush(text.split('\n').map(v => '> ' + v).join('\n'));
                yield stream.flush('\n\n---\n\n');
                state.code_length += text.length;
            }
            yield stream.flush(`代码总字符长度: ${state.code_length}\n\n`);
            yield stream.flush('---\n\n');

            yield stream.flush('\n\n以下为 queryWithCode:\n\n');
            yield stream.flush(`${queryWithCode}`);
            yield stream.flush('\n\n---');
            */
            // ************************************************************  /

            yield stream.flushReplaceLast('');
            for await (const chunk of chunks) {
                yield stream.flush(chunk);
            }

            this.logger.info('paddle智能问答耗时统计', {message: `llm调用结束，耗时${performance.now() - start}ms`});
        }
        catch (ex) {
            console.error(ex);
        }
    }
}
