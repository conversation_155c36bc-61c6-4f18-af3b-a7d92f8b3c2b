import {
    SkillProvider,
    FunctionParameterDefinition,
    TaskProgressChunk,
    TextModel,
    StringChunkStream,
} from '@comate/plugin-host';
import codeExplainPromptTemplate from '../prompts/codeExplain.prompt';

export class CodeExplainSkillProvider extends SkillProvider {
    static skillName = 'code-explain';

    static displayName = '飞桨代码解释';

    static description = '解释深度学习与飞桨框架的代码';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        this.logger.info('paddle代码解释耗时统计', {message: `开始执行代码解释 ${performance.now()}`});

        const {activeFilePath, activeFileLanguage, query, selectedCode} = this.currentContext;
        const stream = new StringChunkStream();
        if (query) {
            yield stream.flush('无需输入，请直接框选要解释的代码');
            return;
        }

        if (!selectedCode) {
            yield stream.flush('未选中代码，请框选要解释的代码');
            return;
        }

        yield stream.flush('正在思考中，请耐心等候...');

        this.logger.info('paddle代码解释耗时统计', {message: `开始执行知识集检索 ${performance.now()}`});
        let start = performance.now();
        const knowledge = await this.retriever.knowledgeFromQuery(
            selectedCode,
            {
                knowledgeSets: [
                    // 飞桨API接口文档集
                    {uuid: '564a9ebb-a3b1-4d5b-ad28-27c0ab6d78fe', type: 'NORMAL'},
                    // 飞桨框架与生态文档集
                    {uuid: '7ece94ce-e39a-414e-8ca5-d80ed4cf300b', type: 'NORMAL'},
                ],
                queryRewrite: false,
            }
        );
        this.logger.info('paddle代码解释耗时统计', {message: `知识集检索结束，耗时${performance.now() - start}ms`});

        start = performance.now();
        const trim_knowledge = knowledge;

        const prompt = this.llm.createPrompt(codeExplainPromptTemplate, {
            activeFileLanguage,
            activeFilePath,
            trim_knowledge,
            selectedCode,
        });
        const chunks = this.llm.askForTextStreaming(prompt, {
            model: TextModel.ErnieBot128,
            modelOptions: {
                temperature: 0.3,
                topP: 0.8,
                penaltyScore: 1.0,
                enableMultiturnDialogue: false,
            },
        });

        yield stream.flushReplaceLast('');
        for await (const chunk of chunks) {
            yield stream.flush(chunk);
        }

        this.logger.info('paddle代码解释耗时统计', {message: `llm调用结束，耗时${performance.now() - start}ms`});
    }
}
