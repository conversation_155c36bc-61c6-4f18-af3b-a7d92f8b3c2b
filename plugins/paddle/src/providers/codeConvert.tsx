import {
    SkillProvider,
    ProviderInit,
    FunctionParameterDefinition,
    TaskProgressChunk,
    TextModel,
    ElementChunkStream,
} from '@comate/plugin-host';
import path from 'node:path';
import assert from 'assert';
import {fileURLToPath} from 'node:url';
import childProcess from 'child_process';
import codeConvertPromptTemplate from '../prompts/codeConvert.prompt';
import {Information} from '@comate/plugin-shared-internals';
import {getLanguageFromFilePath} from '../utils/utils.js';
import os from 'os';
import {
    getAllFilesPath,
    ifOverSize,
    readFile,
    readFiles,
    rmDir,
} from '../utils/common.js';

const TEMP = os.tmpdir();
function exec(command: string) {
    return new Promise((resolve, reject) => {
        childProcess.exec(command, (error, stdout) => {
            if (error) {
                reject(error);
                return;
            }
            resolve(stdout);
        });
    });
}

export class CodeConvertSkill<PERSON><PERSON>ider extends SkillProvider {
    static skillName = 'code-convert';

    static displayName = '飞桨代码转换';

    static description = '将其他深度学习框架（例如Pytorch、Tensorflow、Keras等）的代码转换为飞桨的代码';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };
    readonly #cwd: string;
    readonly #pluginDir: string;
    workspaceFileSystem?: FileSystem;
    tempDir: string;
    constructor(init: ProviderInit) {
        super(init);
        this.#cwd = init.cwd;
        this.tempDir = path.resolve(TEMP, 'paddle_project');
        console.log('temp', TEMP);
        const entryFileName = fileURLToPath(import.meta.url);
        // 取entryFileName的上两级目录
        this.#pluginDir = path.resolve(entryFileName, '..', '..');

        const paconvertWhl = path.join(
            this.#pluginDir,
            'assets',
            'paconvert-2.3.0-py3-none-any.whl'
        );
        exec('paconvert -h')
            .then(stdout => {
                this.logger.info(`paconvert已安装，无需再次安装: ${stdout}`);
            })
            .catch(() => {
                exec(`python3 -m pip install -U ${paconvertWhl}`)
                    .then(stdout => {
                        this.logger.info(`python3安装paconvert成功: ${stdout}`);
                    })
                    .catch(() => {
                        exec(`python3.9 -m pip install -U ${paconvertWhl}`)
                            .then(stdout => {
                                this.logger.info(
                                    `python3.9安装paconvert成功: ${stdout}`
                                );
                            })
                            .catch(() => {
                                exec(
                                    `python3.10 -m pip install -U ${paconvertWhl}`
                                )
                                    .then(stdout => {
                                        this.logger.info(
                                            `python3.10安装paconvert成功: ${stdout}`
                                        );
                                    })
                                    .catch(() => {
                                        exec(
                                            `python3.11 -m pip install -U ${paconvertWhl}`
                                        )
                                            .then(stdout => {
                                                this.logger.info(
                                                    `python3.11安装paconvert成功: ${stdout}`
                                                );
                                            })
                                            .catch(() => {
                                                exec(
                                                    `python3.12 -m pip install -U ${paconvertWhl}`
                                                )
                                                    .then(stdout => {
                                                        this.logger.info(
                                                            `python3.12安装paconvert成功: ${stdout}`
                                                        );
                                                    })
                                                    .catch(() => {
                                                        exec(
                                                            `python3.13 -m pip install -U ${paconvertWhl}`
                                                        )
                                                            .then(stdout => {
                                                                this.logger.info(
                                                                    `python3.13安装paconvert成功: ${stdout}`
                                                                );
                                                            })
                                                            .catch(() => {
                                                                exec(
                                                                    `python -m pip install -U ${paconvertWhl}`
                                                                )
                                                                    .then(
                                                                        stdout => {
                                                                            this.logger.info(
                                                                                `python安装paconvert成功: ${stdout}`
                                                                            );
                                                                        }
                                                                    )
                                                                    .catch(
                                                                        error => {
                                                                            this.logger.error(
                                                                                `paconvert安装失败: ${error.message}`
                                                                            );
                                                                        }
                                                                    );
                                                            });
                                                    });
                                            });
                                    });
                            });
                    });
            });
    }

    async *handleCommand(commandName: string, data: any): AsyncIterableIterator<TaskProgressChunk> {
        console.log('commandName', commandName);
        console.log('commandNameData', data);
        const {files} = data;
        const stream = new ElementChunkStream();

        if (commandName === 'commitMessage:confirmIssue') {
            yield stream.flushReplaceLast(
                <p>
                    <ul>
                        {files.map((item: string) => (
                            <li>
                                <file-link
                                    showIcon
                                    to={path.join(
                                        './paddle_project',
                                        item.replace(this.tempDir, '')
                                    )}
                                    line={2}
                                >
                                    {item.replace(this.tempDir, '')}
                                </file-link>
                            </li>
                        ))}
                    </ul>
                </p>
            );
            yield stream.flush(
                <markdown>
                    {`完成转换，已经将所有文件写入目录\`${
                        path.join(
                            this.#cwd,
                            'paddle_project'
                        )
                    }\`中，您可点击文件链接查看。\n\n\n`}
                </markdown>
            );
        }
    }

    /**
     * 执行转换任务并返回任务进度块
     *
     * @returns 异步迭代器，返回 TaskProgressChunk 类型的任务进度块
     */
    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        this.logger.info('paddle代码转换耗时统计', {message: `开始执行环境适配 ${Date.now()}`});
        const stream = new ElementChunkStream();
        const {
            query,
            selectedCode,
            activeFileContent,
            activeFilePath,
            activeFileLanguage,
        } = this.currentContext;
        let use_paconvert = true;

        let in_content: string = '';
        // 绝对路径
        let in_dir: string = '';
        // 相对路径
        let in_rdir: string = '';
        let language = activeFileLanguage;

        const fs = await this.requestWorkspaceFileSystem();

        // 优先级1: 用户选择的文件/目录
        const selectedKnowledgeList: Information[] = this.retriever.selectedKnowledgeList();
        if (selectedKnowledgeList.length) {
            if (selectedKnowledgeList[0].type === 'FILE') {
                in_content = selectedKnowledgeList[0].content as string;
                in_dir = path.resolve(
                    this.#cwd,
                    selectedKnowledgeList[0].path as string
                );
                in_rdir = selectedKnowledgeList[0].path as string;
                language = getLanguageFromFilePath(
                    selectedKnowledgeList[0].name as string
                );
            }
            else if (selectedKnowledgeList[0].type == 'FOLDER') {
                in_content = 'FOLDER';
                in_dir = path.resolve(
                    this.#cwd,
                    selectedKnowledgeList[0].path as string
                );
                in_rdir = selectedKnowledgeList[0].path as string;
            }
            else if (selectedKnowledgeList[0].type === 'CURRENT_FILE') {
                in_content = activeFileContent;
                in_dir = path.resolve(this.#cwd, activeFilePath);
                in_rdir = activeFilePath;
            }
            else if (selectedKnowledgeList[0].type === 'REPO') {
                in_content = 'FOLDER';
                in_dir = path.resolve(this.#cwd);
            }
        }

        // 优先级2: query为相对路径
        if (!in_content && fs && query.trim().length > 0) {
            const queryTrim = query.trim();
            try {
                const stat = await fs.stat(queryTrim);
                if (stat.type === 'file') {
                    in_content = await fs.readFile(queryTrim, 'utf-8');
                    in_dir = path.resolve(this.#cwd, queryTrim);
                    in_rdir = queryTrim;
                    language = getLanguageFromFilePath(queryTrim);
                }

                if (stat.type === 'directory') {
                    in_content = 'FOLDER';
                    in_dir = path.resolve(this.#cwd, queryTrim);
                    in_rdir = queryTrim;
                }
            }
            catch (e: any) {
                this.logger.error('解析query中的路径失败: ' + e.message);
            }
        }

        // 优先级3: 选中代码
        if (!in_content && selectedCode) {
            in_content = selectedCode;
            use_paconvert = false;
        }

        // 优先级4: 当前文件
        if (!in_content && activeFilePath) {
            in_content = activeFileContent;
            in_dir = path.resolve(this.#cwd, activeFilePath);
        }

        // 无任何输入，报错
        if (!in_content) {
            this.logger.error('未检测到待转换的代码！');
            yield stream.flush(
                <markdown>
                    {'未检测到待转换的代码，您可通过以下方式指定：\n1. 直接回车则转换当前文件\n2. 使用 # 选择文件或目录\n3. 直接输入相对路径\n4. 框选代码\n\n'}
                </markdown>
            );
            return;
        }

        if (in_content.includes('tensorflow') || in_content.includes('keras')) {
            use_paconvert = false;
        }

        if (!fs) {
            use_paconvert = false;
        }

        // Python环境检查
        if (use_paconvert) {
            try {
                await exec('python3 --version');
            }
            catch (e) {
                try {
                    await exec('python --version');
                }
                catch (e: any) {
                    this.logger.error(`未检测到Python解释器: ${e.message}`);
                    yield stream.flush(
                        <markdown>
                            {'未检测到Python解释器，建议安装3.8版本及以上的Python，安装后可支持Pytorch项目整个目录的 **批量/长文本转换**，转换速度更快、准确率更高，一键迁移到飞桨框架。'}
                        </markdown>
                    );
                    use_paconvert = false;
                }
            }
        }

        // PaConvert环境检查
        if (use_paconvert) {
            // 检测是否安装了paconvert
            try {
                await exec('paconvert -h');
            }
            catch (e) {
                yield stream.flush(
                    <markdown>{'正在配置环境中，请稍等...\n\n'}</markdown>
                );
                const paconvertWhl = path.join(
                    this.#pluginDir,
                    'assets',
                    'paconvert-2.3.0-py3-none-any.whl'
                );
                try {
                    await exec(`python3 -m pip install -U ${paconvertWhl}`);
                    await exec(`paconvert -h`);
                    yield stream.flushReplaceLast(
                        <markdown>{'配置成功，开始转换...\n\n'}</markdown>
                    );
                }
                catch (e) {
                    try {
                        await exec(`python -m pip install -U ${paconvertWhl}`);
                        await exec(`paconvert -h`);
                        yield stream.flushReplaceLast(
                            <markdown>{'配置成功，开始转换...\n\n'}</markdown>
                        );
                    }
                    catch (e: any) {
                        this.logger.error(`paconvert安装失败: ${e.message}`);
                        yield stream.flushReplaceLast(
                            <markdown>
                                {'未成功安装飞桨代码转换工具paconvert，安装后可支持Pytorch项目整个目录的 **批量/长文本转换**，转换速度更快、准确率更高，一键迁移到飞桨框架。推荐安装：\n\n'}
                            </markdown>
                        );
                        yield stream.flush(
                            <code-block
                                language="bash"
                                closed={false}
                            >
                                {'pip3 install -U paconvert'}
                            </code-block>
                        );
                        yield stream.flush(
                            <markdown>
                                {'由于未安装该工具，将使用 **普通转换方式**。\n\n'}
                            </markdown>
                        );
                        use_paconvert = false;
                    }
                }
            }
        }

        if (in_dir) {
            if (in_content === 'FOLDER') {
                yield stream.flush(
                    <markdown>{`正在转换整个目录：\`${in_dir}\`，批量转换时间可能较长，请耐心等待...\n\n`}</markdown>
                );
            }
            else {
                yield stream.flush(
                    <markdown>{`正在转换单个文件：\`${in_dir}\`，请稍等...\n\n`}</markdown>
                );
            }
        }
        else if (selectedCode) {
            yield stream.flush(
                <markdown>{'正在转换选中代码...\n\n'}</markdown>
            );
        }

        // 使用PaConvert转换
        if (use_paconvert) {
            assert(fs, '文件系统不存在');
            const log_file = path.resolve(this.#cwd, '.convert.log');
            if (in_content === 'FOLDER') {
                const out_dir = this.tempDir;
                //批量之前删除临时目录里的内容 兼容windows
                rmDir(out_dir, this.logger)
                //批量之前删除本地的paddle_project
                rmDir(path.resolve(this.#cwd, 'paddle_project'), this.logger)
                try {
                    // 检查目录合法性
                    console.log('待转换目录', in_dir);
                    const in_files = getAllFilesPath(in_dir, this.logger);
                    console.log('待转换目录中的文件', in_files);
                    const overSize = ifOverSize(
                        in_files,
                        this.#cwd,
                        this.logger
                    );
                    if (overSize) {
                        yield stream.flushReplaceLast(
                            <markdown>
                                {'选择目录中文件过大，无法进行转换，请减少文件数量，或选择单个文件进行转换。\n\n'}
                            </markdown>
                        );
                        return;
                    }
                    // 调用paconvert tool转换
                    await exec(
                        `paconvert --in_dir ${in_dir} --out_dir ${out_dir} --log_dir ${log_file} --exclude_dirs ${out_dir} --log_level WARNING --log_markdown`
                    );

                    // 展示转换结果
                    const out_files = getAllFilesPath(
                        this.tempDir,
                        this.logger
                    );
                    console.log('目录转换文件', out_files);
                    const out_content = readFiles(
                        out_files,
                        this.#cwd,
                        this.logger
                    );
                    console.log('目录转换文件内容', out_content);
                    yield stream.flushReplaceLast(
                        <p>
                            <ul>
                                {out_files.map((item: string) => (
                                    <li>
                                        <p>{item.replace(this.tempDir, '')}</p>
                                    </li>
                                ))}
                            </ul>
                            <flex
                                inline
                                verticalStartAlign
                            >
                                <command-button
                                    commandName="commitMessage:confirmIssue"
                                    variant="text"
                                    action="acceptDir"
                                    actionData={{
                                        includeHiddenStatsCode: true,
                                        newText: out_content.join('\n'),
                                        from: this.tempDir,
                                        to: path.join(
                                            this.#cwd,
                                            'paddle_project'
                                        ),
                                    }}
                                    propagation={true}
                                    data={{
                                        files: out_files,
                                        from: this.tempDir,
                                        to: path.join(
                                            this.#cwd,
                                            'paddle_project'
                                        ),
                                    }}
                                    tooltipText="批量采纳"
                                >
                                    批量采纳
                                </command-button>
                            </flex>
                            <markdown>
                                {`点击 **批量采纳** 可以将所有文件写入目录\`${
                                    path.join(
                                        this.#cwd,
                                        'paddle_project'
                                    )
                                }\`中。\n\n\n`}
                            </markdown>
                        </p>
                    );
                }
                catch (e: any) {
                    this.logger.error(
                        `paconvert转换目录${in_dir}失败，失败原因：${e.message}`
                    );
                    yield stream.flushReplaceLast(
                        <markdown>
                            {`转换失败，失败原因：\n\`\`\`\n${e.message}\n\`\`\`\n\n您可尝试选择单个文件进行转换。\n\n`}
                        </markdown>
                    );
                    return;
                }
            }
            else {
                const fileName = path.basename(in_dir);
                const out_dir = path.resolve(this.tempDir, fileName);
                try {
                    // 检查文件合法性
                    if (in_content.length > 1000000) {
                        yield stream.flushReplaceLast(
                            <markdown>
                                {'文本代码长度超过限制，请减少文本代码长度，或框选代码进行转换。\n\n'}
                            </markdown>
                        );
                        return;
                    }

                    // 调用paconvert tool转换
                    await exec(
                        `paconvert --in_dir ${in_dir} --out_dir ${out_dir} --log_dir ${log_file} --log_level WARNING --log_markdown`
                    );

                    // 展示转换结果
                    const out_content = readFile(out_dir, this.logger);
                    console.log('单文件转换结果', out_content);
                    yield stream.flushReplaceLast(
                        <code-block
                            language={language}
                            closed={false}
                        >
                            {`${out_content}`}
                        </code-block>
                    );

                    exec(`rm -rf ${out_dir}`)
                        .then(() => {
                            this.logger.info(
                                `转换结果临时保存文件 ${out_dir} 删除成功`
                            );
                        })
                        .catch(() => {
                            this.logger.error(
                                `转换结果临时保存文件 ${out_dir} 删除失败`
                            );
                        });
                }
                catch (e: any) {
                    this.logger.error(
                        `paconvert转换单文件${in_dir}失败，失败原因：${e.message}`
                    );
                    use_paconvert = false;
                    if (in_content.length > 200000) {
                        yield stream.flushReplaceLast(
                            <markdown>
                                {'待转换的代码长度超过限制，请减少代码量。\n\n'}
                            </markdown>
                        );
                        return;
                    }
                }
            }

            // 打印转换日志，如果改为使用大模型，则不打印
            if (use_paconvert) {
                try {
                    const log_content = await fs.readFile(
                        '.convert.log',
                        'utf8'
                    );
                    yield stream.flush(
                        <markdown>{`\n\n${log_content}\n`}</markdown>
                    );

                    exec(`rm -rf ${log_file}`)
                        .then(() => {
                            this.logger.info(`转换的日志 ${log_file} 删除成功`);
                        })
                        .catch(() => {
                            this.logger.error(
                                `转换的日志 ${log_file} 删除失败`
                            );
                        });
                }
                catch (e: any) {
                    this.logger.error(
                        'paconvert转换日志文件读取失败: ',
                        e.message
                    );
                }
                return;
            }
        }

        // 大模型转换
        if (!use_paconvert) {
            this.logger.info('paddle代码转换耗时统计', {message: `paconvert无法转换，使用大模型转换 ${Date.now()}`});
            if (in_content == 'FOLDER') {
                // 无法支持，报错
                this.logger.error(
                    '转换整个目录时，仅能使用paconvert转换，需修复paconvert环境问题'
                );
                yield stream.flushReplaceLast(
                    <markdown>
                        {'**大模型转换方式** 不支持整个目录批量转换，请安装飞桨代码转换工具paconvert以支持 **批量转换**，或者选择单个文件进行转换。\n\n'}
                    </markdown>
                );
                yield stream.flush(
                    <code-block
                        language="bash"
                        closed={false}
                    >
                        {'pip3 install -U paconvert'}
                    </code-block>
                );
                return;
            }
            this.logger.info('paddle代码转换耗时统计', {message: `开始执行知识集检索 ${Date.now()}`});
            let start = Date.now();
            const knowledge = await this.retriever.knowledgeFromQuery(
                in_content,
                {
                    knowledgeSets: [
                        // 飞桨API接口映射文档集
                        {uuid: 'a53c6ee5-3bcf-46cd-80b7-103c6d028389', type: 'NORMAL'},
                    ],
                    queryRewrite: false,
                }
            );
            this.logger.info('paddled代码转换耗时统计', {message: `知识集检索结束，耗时${Date.now() - start}ms`});

            start = Date.now();
            const trim_knowledge = knowledge;
            const trim_code = [''];

            const prompt = this.llm.createPrompt(codeConvertPromptTemplate, {
                trim_knowledge,
                trim_code,
                language,
                in_content,
            });
            const chunks = this.llm.askForTextStreaming(prompt, {
                model: TextModel.ErnieBot128,
                modelOptions: {
                    temperature: 0.1,
                    topP: 0.8,
                    penaltyScore: 1.0,
                    enableMultiturnDialogue: false,
                },
            });
            let res = '';
            yield stream.flushReplaceLast(<markdown>{''}</markdown>);
            for await (const chunk of chunks) {
                res += chunk;
                yield stream.flushReplaceLast(<markdown>{res}</markdown>);
            }
            this.logger.info('paddled代码转换耗时统计', {message: `llm调用结束，耗时${Date.now() - start}ms`});
        }
    }
}
