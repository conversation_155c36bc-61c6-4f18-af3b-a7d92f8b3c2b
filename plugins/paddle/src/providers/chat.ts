import {
    SkillProvider,
    FunctionParameterDefinition,
    TaskProgressChunk,
    TextModel,
    StringChunkStream,
} from '@comate/plugin-host';
import chatPromptTemplate from '../prompts/chat.prompt';

export class ChatSkillProvider extends SkillProvider {
    static skillName = 'chat';

    // Hack: 通用问答，为默认能力，无需显示能力名称，等价于fallback
    static displayName = '';

    static description = '智能解答深度学习与飞桨框架的问题';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        this.logger.info('paddle智能问答耗时统计', {message: `开始执行智能问答 ${performance.now()}`});

        const {activeFilePath, selectedCode, activeFileLanguage, query} = this.currentContext;
        const stream = new StringChunkStream();

        yield stream.flush('正在思考中，请耐心等候...');

        const queryWithCode = selectedCode.trim()
            ? `${query}\n\`\`\`${activeFileLanguage}\n${selectedCode}\n\`\`\``
            : query;

        this.logger.info('paddle智能问答耗时统计', {message: `开始执行知识集检索 ${performance.now()}`});
        let start = performance.now();
        const [knowledge1, knowledge2] = await Promise.all([
            this.retriever.knowledgeFromQuery(
                queryWithCode,
                {
                    knowledgeSets: [
                        // 飞桨API接口文档集
                        {uuid: '564a9ebb-a3b1-4d5b-ad28-27c0ab6d78fe', type: 'NORMAL'},
                    ],
                    queryRewrite: true,
                    topK: 7,
                }
            ),
            this.retriever.knowledgeFromQuery(
                queryWithCode,
                {
                    knowledgeSets: [
                        // 飞桨框架与生态文档集
                        {uuid: '7ece94ce-e39a-414e-8ca5-d80ed4cf300b', type: 'NORMAL'},
                    ],
                    queryRewrite: true,
                    topK: 8,
                }
            ),
        ]);
        this.logger.info('paddle智能问答耗时统计', {message: `知识集检索结束，耗时${performance.now() - start}ms`});

        start = performance.now();
        const trim_knowledge = [...knowledge1, ...knowledge2];
        const trim_code = [''];

        const prompt = this.llm.createPrompt(chatPromptTemplate, {
            activeFilePath,
            trim_knowledge,
            trim_code,
            queryWithCode,
        });
        const chunks = this.llm.askForTextStreaming(prompt, {
            model: TextModel.ErnieBot128,
            modelOptions: {
                temperature: 0.3,
                topP: 0.8,
                penaltyScore: 1.0,
                enableMultiturnDialogue: {byPlugin: true, onlyQuery: query},
            },
        });

        yield stream.flushReplaceLast('');
        for await (const chunk of chunks) {
            yield stream.flush(chunk);
        }

        this.logger.info('paddle智能问答耗时统计', {message: `llm调用结束，耗时${performance.now() - start}ms`});
    }
}
