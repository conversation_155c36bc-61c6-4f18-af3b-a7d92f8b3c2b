import {
    SkillProvider,
    FunctionParameterDefinition,
    TaskProgressChunk,
    TextModel,
    StringChunkStream,
} from '@comate/plugin-host';
import codeGeneratePromptTemplate from '../prompts/codeGenerate.prompt';

export class CodeGenerateSkillProvider extends SkillProvider {
    static skillName = 'code-generate';

    static displayName = '飞桨代码生成';

    static description = '深度学习与飞桨框架的智能编程助手';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        this.logger.info('paddle代码生成耗时统计', {message: `开始执行代码生成 ${performance.now()}`});

        const {activeFilePath, selectedCode, activeFileLanguage, query} = this.currentContext;
        const stream = new StringChunkStream();

        yield stream.flush('正在思考中，请耐心等候...');

        const queryWithCode = selectedCode.trim()
            ? `${query} \n你可参考以下代码来编程 \n\`\`\`${activeFileLanguage}\n${selectedCode}\n\`\`\``
            : query;

        this.logger.info('paddle代码生成耗时统计', {message: `开始执行知识集检索 ${performance.now()}`});
        let start = performance.now();
        const [knowledge1, knowledge2] = await Promise.all([
            this.retriever.knowledgeFromQuery(
                queryWithCode,
                {
                    knowledgeSets: [
                        // 飞桨API接口文档集
                        {uuid: '564a9ebb-a3b1-4d5b-ad28-27c0ab6d78fe', type: 'NORMAL'},
                    ],
                    queryRewrite: true,
                }
            ),
            this.retriever.knowledgeFromQuery(
                queryWithCode,
                {
                    knowledgeSets: [
                        // 飞桨框架与生态文档集
                        {uuid: '7ece94ce-e39a-414e-8ca5-d80ed4cf300b', type: 'NORMAL'},
                    ],
                    queryRewrite: true,
                    topK: 5,
                }
            ),
        ]);

        this.logger.info('paddle代码生成耗时统计', {message: `知识集检索结束，耗时${performance.now() - start}ms`});

        start = performance.now();
        const trim_knowledge = [...knowledge1, ...knowledge2];
        const trim_code = [''];

        const prompt = this.llm.createPrompt(codeGeneratePromptTemplate, {
            activeFilePath,
            trim_knowledge,
            trim_code,
            queryWithCode,
        });
        const chunks = this.llm.askForTextStreaming(prompt, {
            model: TextModel.ErnieBot128,
            modelOptions: {
                temperature: 0.1,
                topP: 0.8,
                penaltyScore: 1.0,
                enableMultiturnDialogue: false,
            },
        });

        let start_flush = false;
        for await (const chunk of chunks) {
            if (start_flush) {
                yield stream.flush(chunk);
            }
            else {
                if (chunk.includes('```')) {
                    start_flush = true;
                    yield stream.flushReplaceLast('');
                    yield stream.flush(chunk);
                }
            }
        }

        if (!start_flush) {
            yield stream.flushReplaceLast('未生成任何代码，请清晰描述您的代码需求，以便我充分理解。');
        }

        this.logger.info('paddle代码生成耗时统计', {message: `llm调用结束，耗时${performance.now() - start}ms`});
    }
}
