您好，我是您的飞桨深度学习编程助手，我对深度学习与飞桨框架（PaddlePaddle）有着深入了解，可以为您编写深度学习领域内的代码并解答问题。我具备以下能力：

# 默认能力（通用问答）
* 无需选中特定能力，默认情况下为通用问答，包括深度学习领域内的问题解答、代码生成、代码转换、代码解释和代码改写等各种能力，同时支持多轮问答
* 您可以灵活的与我开展对话，例如：
    1. 描述飞桨相关问题，例如：介绍下自动微分机制、介绍下飞桨的分布式自动并行方式、如何安装飞桨框架、介绍下飞桨的动静态图机制等
    2. 描述飞桨代码需求，例如：帮我编写模型组网函数、编写网络训练函数、调用深度学习框架接口、Resnet50网络代码等
    3. 任意提问...

# 代码生成
* 自动生成使用飞桨编写的代码，支持一键生成 **模型组网函数** 、**网络训练函数** 、**调用深度学习框架接口** 等
* 请具体描述您的飞桨代码需求，例如：线性层网络、矩阵乘法、二维卷积、最大池化、cross-entropy损失函数、正则化、梯度裁剪等
* 相比通用问答能力，代码生成能力更为定制化，可以更精准的满足您编写代码的需求

# 代码转换
* 将使用其他深度学习框架的代码（如Pytorch、Tensorflow、Keras、MXNet、MindSpore等），转换为使用飞桨框架的代码，支持项目一键迁移
* 针对Pytorch框架（推荐用法）：做了特殊调优，转换速度极快，支持文件与目录的 **批量/长文本转换**，且准确率更高
* 针对其他框架：使用普通大模型转换方式，转换速度一般
* 输入方式：
    1. 直接回车则默认转换 **当前文件**
    2. 使用 # 选择路径（文件或目录）
    3. 直接输入相对路径（文件或目录）
    4. 框选代码
* 相比通用问答能力，代码转换能力更为定制化，可以更精准的满足您转换代码的需求，且转换速度更快，准确率更高

# 代码解释
* 结合飞桨专业知识与历史优秀代码集，对代码进行解释。直接 **框选代码** 后回车即可
* 相比通用问答能力，代码解释能力更为定制化，可以更精准的满足您的解释代码需求

# 代码改写
* 优化代码的写法，例如将单卡写法改为多卡写法。正在建设中，敬请期待...
