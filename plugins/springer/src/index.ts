import { PluginSetupContext } from '@comate/plugin-host';
import { HelpSkillProvider } from './providers/help.js';
import { ChatSkillProvider } from './providers/chat.js';
import { CustomFallbackProvider } from './providers/fallback.js';
import { ComplianceSkillProvider } from './providers/compliance.js';
import { ComplianceRecordSkillProvider } from './providers/complianceRecord.js';

export function setup({registry}: PluginSetupContext) {
    registry.registerSkillProvider('springer-help', HelpSkillProvider);
    registry.registerSkillProvider('chat', ChatSkillProvider);
    registry.registerFallbackProvider('fallback', CustomFallbackProvider, true);
    registry.registerSkillProvider('compliance', ComplianceSkillProvider);
    registry.registerSkillProvider('complianceRecord', ComplianceRecordSkillProvider);
}
