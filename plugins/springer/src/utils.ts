import FormData from "form-data";
import fs from "fs";
import crypto from "crypto";
import fetch from "node-fetch";
import {
    SPRINGER_RESULT_DIR,
    SPRINGER_SERVER_AK,
    SPRINGER_SERVER_BASE_URL,
    SPRINGER_SERVER_SK,
    springerAPI,
} from "./config/config.js";
import path from "path";

export function createFormData(
    username: string,
    upfile: fs.ReadStream
): FormData {
    const timestamp = Math.floor(Date.now() / 1000);
    const signStr = `${timestamp}app_key=${SPRINGER_SERVER_AK}username=${username}${SPRINGER_SERVER_SK}`;
    const sign = crypto.createHash("md5").update(signStr).digest("hex");

    const form = new FormData();
    form.append("timestamp", timestamp);
    form.append("app_key", SPRINGER_SERVER_AK);
    form.append("upfile", upfile);
    form.append("username", username);
    form.append("sign", sign);

    return form;
}

export async function submitCompliance(
    username: string,
    upfile: fs.ReadStream
) {
    const formData = createFormData(username, upfile);
    const options = {
        method: "POST",
        body: formData,
    };
    const uploadUrl = `${SPRINGER_SERVER_BASE_URL}${springerAPI.upload}`;
    try {
        const response = await fetch(uploadUrl, options);
        const result = await response.json();
        return result;
    } catch (error) {
        console.log(error);
        return { error_code: 1, error_msg: "提交失败" };
    }
}

export async function queryComplianceResult(
    username: string,
    commitId: string
) {
    try {
        const timestamp = Math.floor(Date.now() / 1000);
        const signStr = `${timestamp}app_key=${SPRINGER_SERVER_AK}commit_id=${commitId}type=2username=${username}${SPRINGER_SERVER_SK}`;
        const sign = crypto.createHash("md5").update(signStr).digest("hex");
        const params = {
            type: "2",
            username,
            commit_id: commitId,
            app_key: SPRINGER_SERVER_AK,
            timestamp: `${timestamp}`,
            sign,
        };
        const paramsStr = new URLSearchParams(params);
        const queryUrl = `${SPRINGER_SERVER_BASE_URL}${
            springerAPI.query
        }?${paramsStr.toString()}`;
        const response = await fetch(queryUrl);
        const result = await response.json();
        return result;
    } catch (error) {
        console.log(error);
        return { error_code: 1, error_msg: "提交失败" };
    }
}

export async function queryComplianceRecordList(username: string) {
    try {
        const timestamp = Math.floor(Date.now() / 1000);
        const signStr = `${timestamp}app_key=${SPRINGER_SERVER_AK}type=1username=${username}${SPRINGER_SERVER_SK}`;
        const sign = crypto.createHash("md5").update(signStr).digest("hex");
        const params = {
            type: "1",
            username,
            app_key: SPRINGER_SERVER_AK,
            timestamp: `${timestamp}`,
            sign,
        };
        const paramsStr = new URLSearchParams(params);
        const queryUrl = `${SPRINGER_SERVER_BASE_URL}${
            springerAPI.query
        }?${paramsStr.toString()}`;
        const response = await fetch(queryUrl);
        const result = await response.json();
        if (result.error_code === 0) {
            return result.data;
        }
        return [];
    } catch (error) {
        console.log(error);
        return [];
    }
}

export function sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
}
