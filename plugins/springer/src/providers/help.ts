import {
    FunctionParameterDefinition,
    SkillProvider,
    TaskProgressChunk,
    TaskProgressChunkStream,
} from '@comate/plugin-host';
import {readFileSync} from 'fs';
import {fileURLToPath} from 'node:url';
import {dirname} from 'path';

export class HelpSkillProvider extends SkillProvider {
    static skillName = 'springer-help';
    static displayName: '插件介绍';
    static description = '插件介绍';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new TaskProgressChunkStream();
        const filename = fileURLToPath(import.meta.url);
        const content = readFileSync(`${dirname(filename)}/help.md`, 'utf8');
        yield stream.flush(content);
    }
}
