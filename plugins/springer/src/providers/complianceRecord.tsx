import {
    SkillProvider,
    FunctionParameterDefinition,
    TaskProgressChunk,
    ElementChunkStream,
    ProviderInit,
} from "@comate/plugin-host";
import { queryComplianceRecordList, queryComplianceResult, sleep } from "../utils.js";

interface Args {
    apkPath?: string;
}

enum DataType {
    RecordResult = "record_result",
    RiskResult = "risk_result",
    EvidenceStack = "evidence_stack",
}
type CommandHandleData = {type: DataType, data: any};

export class ComplianceRecordSkillProvider extends SkillProvider<void> {
    private readonly cwd: string;
    static skillName = "complianceRecord";

    static displayName = "查询合规检测记录";

    static description = "查询您最近的合规检测记录及结果";

    static parameters: FunctionParameterDefinition = {
        type: "object",
        properties: {},
    };

    constructor(init: ProviderInit) {
        super(init);

        this.cwd = init.cwd;
    }

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        // const { query } = this.currentContext;
        const stream = new ElementChunkStream();

        yield stream.flushReplaceLast(
            <loading>正在查询合规检测记录...</loading>
        );

        const userDetail = await this.currentUser.requestDetail();
        const username = userDetail ? userDetail.name : "unknown";

        try {
            const result = await queryComplianceRecordList(username);
            yield stream.flushReplaceLast(<span>查询成功，结果如下：</span>);
            if (result?.length) {
                for (const item of result) {
                    const timeStr = new Date(item.ctime * 1000).toLocaleString();
                    const statusStr = item.status === 3 ? '已完成' : '检测中';
                    yield stream.flush(
                        <p>
                            <card color='orange'>
                                <p>{`文件名称：${item.src_name}`}</p>
                                <p>{`文件MD5：${item.file_md5}`}</p>
                                <p>{`检测时间：${timeStr}`}</p>
                                <p>{`检测状态：${statusStr}`}</p>
                                {item.risk_status > 0 && (
                                    <p>{`检测结果：`}
                                        <alert-tag level={Number(item.risk_status) === 1 ? 'normal' :'critical'}>
                                            {Number(item.risk_status) === 1 ? '未发现问题' :'存在问题'}
                                        </alert-tag>
                                    </p>
                                )}
                                <p>
                                    <command-button
                                        commandName="commitMessage:confirmIssue"
                                        data={{type: DataType.RecordResult, data: item.id}}
                                        replyText={`查询检测结果：${item.src_name} (${item.file_md5})`}
                                        variant="default"
                                    >
                                        查看详情
                                    </command-button>
                                </p>
                            </card>
                        </p>
                    );
                    await sleep(500);
                }
            } else {
                yield stream.flushReplaceLast(<p>未查询到检测记录，请提交合规检测。</p>);
            }
        } catch (error) {
            yield stream.flushReplaceLast(<p>出现错误，请稍后再试</p>);
        }
    }

    // 承接点击事件
    async *handleCommand(commandName: string, {type, data}: CommandHandleData): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new ElementChunkStream();
        if (commandName !== 'commitMessage:confirmIssue') {
            yield stream.flushReplaceLast(<p>未找到该命令</p>);
            return;
        }

        const userDetail = await this.currentUser.requestDetail();
        const username = userDetail ? userDetail.name : "unknown";

        // 查询检测结果（风险列表）
        switch (type) {
            case DataType.RecordResult:
                yield* this.handleRecordResult(username, data, stream);
                break;
            case DataType.RiskResult:
                yield* this.handleRiskResult(data, stream);
                break;
            case DataType.EvidenceStack:
                yield stream.flushReplaceLast(
                    <code-block language="java" actions={['copy']} closed={false}>{data}</code-block>
                );
                break;
            default:
                yield stream.flushReplaceLast(<p>未找到该命令</p>);
                break;
        }
    }

    // 查询检测结果
    async *handleRecordResult(username: string, recordId: string, stream: ElementChunkStream) {
        try {
            const result = await queryComplianceResult(username, recordId);
            // yield stream.flushReplaceLast(<p>{`参数 ${username} ${recordId} , 返回结果: ${JSON.stringify(result)}`}</p>);
            if (result?.error_code === 0 && result?.data?.length) {
                const resultItem = result.data[0];
                if (Number(resultItem.status) !== 3) {
                    yield stream.flushReplaceLast(<p>检测未完成，请稍后再试</p>);
                    return;
                }

                const riskSummary = !resultItem.risk_result?.length ? '本次检测未发现问题。' :
                    `本次共检测出 ${resultItem.risk_result.length} 个风险行为：`;
                if (resultItem.permission_info) {
                    yield stream.flushReplaceLast(
                        <p>
                            <h3>检测概览</h3>
                            <ul>
                                <li>敏感权限：{resultItem.permission_info.total || 0}</li>
                                <li>过度申请权限：{resultItem.permission_info.apply_not_used || 0}</li>
                                <li>冗余权限：{resultItem.permission_info.used_not_apply || 0}</li>
                                <li>超范围索权：{resultItem.permission_info.exceed_count || 0}</li>
                                <li>自启动广播：{resultItem.permission_info.self_start_num || 0}</li>
                                <li>第三方SDK集成：{resultItem.sdk_info?.sdk_list?.length || 0}</li>
                            </ul>
                            <h3>风险项</h3>
                            {riskSummary}
                        </p>
                    );
                    await sleep(1000);
                } else {
                    yield stream.flushReplaceLast(
                        <p>{riskSummary}</p>
                    );
                    await sleep(500);
                }
                for (const item of resultItem.risk_result) {
                    const sourceStr = item.source_name ? `在${item.source_name}中发现` : `在${item.source_names.join(' / ')}中发现`;
                    yield stream.flush(
                        <p>
                            <p>
                                <alert-tag level={item.level > 2 ? 'critical' : item.level === 2 ? 'high' : 'medium'}>
                                    {item.level > 2 ? '高风险' : item.level === 2 ? '中风险' : '低风险'}
                                </alert-tag>
                                {item.info}
                            </p>
                            <em>{sourceStr}</em>
                            <flex flexEndCentered>
                                <command-button
                                    commandName="commitMessage:confirmIssue"
                                    action="detail"
                                    data={{type: DataType.RiskResult, data: item}}
                                    tooltipText="风险详情"
                                    replyText={`查看风险详情：${item.info}`}
                                >
                                    详情
                                </command-button>
                            </flex>
                        </p>
                    );
                    await sleep(500);
                }
            } else {
                yield stream.flush(<p>查询失败，请稍后再试</p>);
            }
        } catch (error) {
            yield stream.flushReplaceLast(<p>查询失败，请稍后再试 - {JSON.stringify(error)}</p>);
        }
    }

    // 查询风险详情
    async *handleRiskResult(riskItem: any, stream: ElementChunkStream) {
        if (!riskItem) {
            yield stream.flushReplaceLast(<p>查询失败，请稍后再试</p>);
        }
        try {
            if (riskItem.isPrivacyPolicy) {
                if (riskItem.content) {
                    yield stream.flushReplaceLast(
                        <p>
                            <h3>举证信息</h3>
                            <p>{riskItem.content.split(`\n`).map((txt: string) => <p>{txt}</p>)}</p>
                        </p>
                    );
                    await sleep(1000);
                } else if (riskItem.list?.length && riskItem.name === 'sdk_consistency') {
                    yield stream.flushReplaceLast(
                        <p>
                            <h3>举证信息</h3>
                            <p>隐私政策中以下SDK的名称、隐私政策链接、开发者名称与《全国SDK管理服务平台》展示的信息不一致，请及时更新，降低合规风险。</p>
                        </p>
                    );
                    yield stream.flush(
                        <table>
                            <thead>
                                <tr>
                                    <th>《全国SDK管理服务平台》信息</th>
                                    <th>问题描述</th>
                                    <th>更新建议</th>
                                </tr>
                            </thead>
                            <tbody>
                                {riskItem.list.map((item: any) => (
                                    <tr>
                                        <td>
                                            <p>SDK名称：{item.sdk_name}</p>
                                            <p>隐私政策：{item.sdk_privacy_url}</p>
                                            <p>开发者：{item.sdk_developer}</p>
                                        </td>
                                        <td>{item.problem}</td>
                                        <td>线索：“{item.cue_info}”</td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    );
                    await sleep(2000);
                }
            } else if (riskItem.check_items?.length) {
                const checkItem = riskItem.check_items[0];
                const evidenceList = (checkItem.detect_detail?.[0]?.data || []).map((e: string, idx: number) => ({
                    text: e,
                    stack: checkItem.detect_detail?.[0]?.stack?.[idx] || '',
                }))
                yield stream.flushReplaceLast(
                    <p>
                        <h3>检测标准</h3>
                        <p>{checkItem.standard.split(`<br/>`).map((txt: string) => <p>{txt}</p>)}</p>
                        <h3>改进建议</h3>
                        <p>{checkItem.suggestions.split(`<br/>`).map((txt: string) => <p>{txt}</p>)}</p>
                    </p>
                );
                await sleep(2000);
                if (evidenceList.length) {
                    yield stream.flush(
                        <p>
                            <h3>存证信息</h3>
                            <ul>
                                {evidenceList.map((e: {text: string, stack: string}) => (
                                    <li>
                                        {e.text}
                                        {e.stack && (
                                            <command-button
                                                commandName="commitMessage:confirmIssue"
                                                action="detail"
                                                data={{type: DataType.EvidenceStack, data: e.stack}}
                                                tooltipText="查看堆栈信息"
                                                replyText={`查看堆栈信息：\n${e.text}`}
                                            >堆栈</command-button>
                                        )}
                                    </li>
                                ))}
                            </ul>
                            {}
                        </p>
                    );
                    await sleep(1000);
                }
                if (checkItem.detect_detail?.[0]?.files?.length) {
                    yield stream.flush(
                        <p>{checkItem.detect_detail[0].files.map((u: string) => <p><img src={u} /></p>)}</p>
                    );
                    await sleep(500);
                }
            }
        } catch (error) {
            yield stream.flushReplaceLast(<p>查询失败，请稍后再试</p>);
        }
    }
}
