import {
    TaskProgressChunk,
    TextModel,
    StringChunkStream,
    FallbackProvider,
} from '@comate/plugin-host';

const promptTemplate = `
请根据以下背景知识：

{{trimedKnowledge}}

回答问题：

{{query}}
`

export class CustomFallbackProvider extends FallbackProvider {
    static description = '智能解答关于APP隐私合规的相关问题';

    async *handleQuery(): AsyncIterableIterator<TaskProgressChunk> {
        const {query} = this.currentContext;
        const stream = new StringChunkStream();

        if (!query) {
            yield stream.flushReplaceLast('请用自然语言描述你的问题');
            return;
        }

        yield stream.flush('正在思考中，请耐心等候...');

        const knowledge = await this.retriever.knowledgeFromQuery(
            query,
            {
                knowledgeSets: [
                    {uuid: 'ada01689-487b-42d2-a507-5199a40463c2', type: 'NORMAL'},
                ],
            }
        );

        const trimedKnowledge = this.trimKnowledge(knowledge, 8);

        const prompt = this.llm.createPrompt(
            knowledge.length ? promptTemplate : '{{query}}',
            {trimedKnowledge, query}
        );

        const chunks = this.llm.askForTextStreaming(prompt, {model: TextModel.Default});

        yield stream.flushReplaceLast('');

        for await (const chunk of chunks) {
            yield stream.flush(chunk);
        }
    }

    private trimKnowledge(list: string[], maxCount = 6, maxLength = 10000) {
        const state = {
            length: 0,
        };
        const output: string[] = [];
        for (const item of list) {
            if (state.length + item.length > maxLength || output.length >= maxCount) {
                break;
            }
            output.push(item);
            state.length += item.length;
        }
        return output;
    }
}
