import {
    SkillProvider,
    FunctionParameterDefinition,
    TaskProgressChunk,
    ElementChunkStream,
    ProviderInit,
    FileSystem,
} from "@comate/plugin-host";
import fs from "node:fs";
import path from "node:path";
import { submitCompliance } from "../utils.js";

const defaultApkPath = ".";

export class ComplianceSkillProvider extends SkillProvider<void> {
    private readonly cwd: string;
    private allApkFiles: string[];

    static skillName = "compliance";

    static displayName = "APP隐私合规检测";

    static description = "对APP进行隐私合规检测，并给出检测结果";

    static parameters: FunctionParameterDefinition = {
        type: "object",
        properties: {},
    };

    constructor(init: ProviderInit) {
        super(init);

        this.cwd = init.cwd;
        this.allApkFiles = [];
    }

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const { query } = this.currentContext;
        const stream = new ElementChunkStream();

        // query为空，扫描所有APK文件
        if (!query) {
            const fileSystem = await this.requestWorkspaceFileSystem();
            if (!fileSystem) {
                yield stream.flushReplaceLast(
                    <p>未授权访问文件系统，请手动输入APK文件路径</p>,
                );
                return;
            }
            try {
                await this.findAllApkFile(".", fileSystem);
                if (!this.allApkFiles.length) {
                    yield stream.flushReplaceLast(
                        <p>未检测到APK文件，请手动输入APK文件路径</p>,
                    );
                    return;
                }
                yield stream.flushReplaceLast(
                    <p>
                        <p>找到以下APK文件，请选择要检测的APK文件：</p>
                        <ul>
                            {this.allApkFiles.map((file) => (
                                <li>
                                    <flex betweenCentered>
                                        <code>{file}</code>
                                        <command-button
                                            commandName="commitMessage:confirmIssue"
                                            data={{type: 'submit', data: file}}
                                            replyText={`提交检测：“${file}”`}
                                        >提交检测</command-button>
                                    </flex>
                                </li>
                            ))}
                        </ul>
                    </p>
                );
            } catch (error) {
                yield stream.flushReplaceLast(
                    <p>未检测到APK文件，请手动输入APK文件路径</p>,
                );
                return;
            }
        } else {
            const apkPath = this.findApkFile(query);
            if (!apkPath || !fs.existsSync(apkPath)) {
                yield stream.flushReplaceLast(
                    <span>{`未找到APK文件，请输入APK文件相对路径，如 build/my-app.apk`}</span>
                );
                return;
            }
            yield stream.flushReplaceLast(
                <loading>{`找到APK文件 ${apkPath}，正在上传检测...`}</loading>
            );
            yield *this.submitApk(apkPath);
        }
    }

    async *submitApk(apkPath: string) {
        const stream = new ElementChunkStream();

        const file = fs.createReadStream(apkPath);
        const userDetail = await this.currentUser.requestDetail();
        const username = userDetail ? userDetail.name : "unknown";

        try {
            const result = await submitCompliance(username, file);
            if (result?.error_code === 0 && result?.data?.commit_id) {
                yield stream.flushReplaceLast(
                    <p>提交成功！正在对APK文件进行合规检测，大约需要20分钟。请稍后调起【最近检测查询】指令查询检测结果。</p>
                );
            } else {
                yield stream.flushReplaceLast(
                    <p>{result?.error_msg || '上传失败，请稍后再试'}</p>,
                );
            }
        } catch (error) {
            yield stream.flushReplaceLast(<p>出现错误，请稍后再试</p>);
        }
    }

    // 承接点击事件
    async *handleCommand(commandName: string, data: any): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new ElementChunkStream();

        if (commandName === 'commitMessage:confirmIssue') {
            if (data.type === 'submit' && data.data) {
                yield stream.flushReplaceLast(<loading>正在提交APK文件...</loading>);
                yield *this.submitApk(this.findApkFile(data.data));
            } else {
                yield stream.flushReplaceLast(<p>未找到该指令</p>);
            }
        } else {
            yield stream.flushReplaceLast(<p>未找到该指令</p>);
        }
    }

    private findApkFile(targetPath = defaultApkPath): string {
        if (targetPath.endsWith(".apk")) {
            return path.resolve(this.cwd, targetPath);
        }
        try {
            const fileList = fs.readdirSync(targetPath);
            const target = fileList.find((f) => f.endsWith(".apk"));
            if (!target) {
                return "";
            }
            return path.resolve(this.cwd, targetPath, target);
        } catch (e) {
            return "";
        }
    }

    private async findAllApkFile(currPath: string, fs: FileSystem) {
        try {
            const fileList = await fs.readDirectory(currPath);
            for (let f of fileList) {
                if (f.type === 'file' && f.name.endsWith(".apk")) {
                    this.allApkFiles.push(path.join(currPath, f.name));
                }
                if (f.type === 'directory') {
                    await this.findAllApkFile(path.join(currPath, f.name), fs);
                }
            }
        } catch (e) {
            console.log(e);
        }
    }
}
