{"private": true, "name": "@comate-plugin/springer", "version": "0.8.0", "type": "module", "scripts": {"clean": "rm -rf dist", "dev": "cpd dev", "build": "tsc && cpd build"}, "dependencies": {}, "devDependencies": {"@comate/plugin-cli": "^0.9.2", "@comate/plugin-host": "^0.9.2", "form-data": "^4.0.0", "node-fetch": "^2.6.1", "@types/node-fetch": "^2.6.1"}, "comate": {"name": "springer", "version": "1.0.0", "icon": "./assets/icon.png", "entry": "./dist/index.js", "displayName": "APP隐私合规助手", "description": "APP隐私合规助手，支持代码生成、智能问答、问题整改、合规指引等。", "keyword": ["隐私合规", "APP隐私合规", "史宾格隐私合规", "隐私合规问题整改"], "capabilities": [{"type": "Skill", "name": "compliance", "displayName": "上传检测", "description": "对APP进行隐私合规检测", "placeholder": "输入APK文件路径，或直接回车扫描项目下的APK文件"}, {"type": "Skill", "name": "complianceRecord", "displayName": "最近检测查询", "description": "查询APP隐私合规最近检测记录", "placeholder": "查询最近5条检测记录，回车立即查询"}, {"type": "Skill", "name": "chat", "displayName": "智能问答", "description": "智能回答APP隐私合规的相关咨询"}, {"type": "Skill", "name": "springer-help", "displayName": "插件介绍", "description": "插件介绍", "placeholder": "无需输入内容，回车获得插件介绍"}, {"type": "Fallback", "name": "fallback", "displayName": "智能问答", "description": "智能解答关于APP隐私合规的相关问题"}], "configSchema": {"sections": []}}}