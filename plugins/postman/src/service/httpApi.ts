import {request} from './index.js';
import {IAPI_SERVER_HOST} from '../config/config.js';
export const getRequestIdList = (_this: any, apiKey: string, workspaceId: string) => {
    return request(
        _this,
        'get',
        `${IAPI_SERVER_HOST}/bapi/postmanConfig/getRequestDetails?apiKey=${apiKey}&workspaceId=${workspaceId}`,
        {
            headers: {
                'Content-Type': 'application/json',
            },
        },
        50000
    );
};

export const getHttpApiInfo = (_this: any, apiKey: string, workspaceId: string, requestId: string) => {
    return request(
        _this,
        'get',
        `${IAPI_SERVER_HOST}/bapi/postmanConfig/getApiSwaggerByRequestId?apiKey=${apiKey}&workspaceId=${workspaceId}&requestId=${requestId}`,
        {
            headers: {
                'Content-Type': 'application/json',
            },
        },
        50000
    );
};
