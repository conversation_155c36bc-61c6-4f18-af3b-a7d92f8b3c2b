export function request(_this: any, method: string, url: string, options: any = {}, timeout: number = 60000) {
    let abort: any = null;
    let abortPromise = new Promise((resolve, reject) => {
        abort = () => {
            const errorMessage = '请求超时，请稍后再试。';
            _this.logger.error(`Plugin Error: ${errorMessage}请求参数：${JSON.stringify({method, url, ...options})}`);
            return reject(errorMessage);
        };
    });

    const fecthPromise = fetch(`${url}`, {
        method,
        ...options,
    });

    let resultPromise = Promise.race([fecthPromise, abortPromise]) as Promise<Response>;
    const timer = setTimeout(() => {
        abort();
    }, timeout);

    return resultPromise
        .then(async response => {
            if (!response.ok) {
                const errorMessage = '请求失败，请稍后再试。';
                _this.logger.error(
                    `Plugin Error: ${errorMessage}请求参数：${JSON.stringify({method, url, ...options})}`
                );
                return {
                    code: 500001,
                    message: errorMessage,
                };
            }

            return await response.json();
        })
        .catch(error => {
            return {
                code: 500001,
                message: error,
            };
        })
        .finally(() => {
            clearTimeout(timer);
        });
}
