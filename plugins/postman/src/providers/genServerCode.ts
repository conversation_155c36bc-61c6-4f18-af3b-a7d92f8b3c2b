import {
    SkillProvider,
    FunctionParameterDefinition,
    TaskProgressChunkStream,
    TaskProgressChunk,
} from '@comate/plugin-host';
import {getRequestIdList, getHttpApiInfo} from '../service/httpApi.js';
import {generateServerCode} from '../service/genServerCode.js';

export class GenServerCodeSkillProvider extends SkillProvider {
    static skillName = 'generateServerCode';
    static description =
        '可用于根据 Postman Request 智能生成接口实现代码，覆盖 controller 层、service 层、dao 层、model 层等，为接口服务开发提效。';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new TaskProgressChunkStream();
        try {
            const apiKey = await this.config.getAsString('ApiKey', '');
            if (!apiKey) {
                yield stream.flushReplaceLast('\n\n请先在插件中心配置好 Postman 中的 API key 信息。');
                return;
            }

            const workspaceId = await this.config.getAsString('WorkspaceId', '');
            if (!workspaceId) {
                yield stream.flushReplaceLast('\n\n请先在插件中心配置好 Postman 中的 Workspace ID 信息。');
                return;
            }

            const {query} = this.currentContext;
            const prompt = query.trim();
            if (!prompt) {
                // 查询 RequestId 列表
                yield stream.flush('\n\n正在查询 RequestId 列表...');
                const res = await getRequestIdList(this, apiKey, workspaceId);
                const {code, message} = res as any;
                if (code !== 200) {
                    this.logger.info(
                        `Plugin Info: 未查询到 RequestId 列表：${message}，请求参数：${
                            JSON.stringify({ApiKey: apiKey, WorkspaceId: workspaceId})
                        }`
                    );
                    yield stream.flushReplaceLast(`未查询到 RequestId 列表：${message}\n\n`);
                    return;
                }

                const requestIdList = res.data || [];
                yield stream.flush('\n\n以下是为你查询到的 RequestId 列表：');
                for (const requestItem of requestIdList) {
                    const {uid, method, name} = requestItem;
                    yield stream.flush(
                        `\n\nRequest: ${name}(${method})\n\nRequestId: ${uid}\n\n---`
                    );
                }

                return;
            }

            // 查询接口
            yield stream.flushReplaceLast('\n\n正在查询 Request...');
            const retApiDetail = await getHttpApiInfo(this, apiKey, workspaceId, prompt);
            const {code, message, data} = retApiDetail as any;
            if (code !== 200) {
                this.logger.info(
                    `Plugin Info: 未查询到 Request：${message}，请求参数：${
                        JSON.stringify({ApiKey: apiKey, WorkspaceId: workspaceId, query, data})
                    }`
                );
                yield stream.flushReplaceLast(`未查询到 Request：${message}\n\n`);
                return;
            }

            if (!data) {
                this.logger.info(
                    `Plugin Info: 未查询到 Request：${message}，请求参数：${
                        JSON.stringify({ApiKey: apiKey, WorkspaceId: workspaceId, RequestId: query})
                    }`
                );
                yield stream.flushReplaceLast(`未查询到 Request，请检查配置和 Request Id 是否有效\n\n`);
                return;
            }

            const requestStr = JSON.stringify(data);
            this.logger.info(
                `Plugin Info: 查询到 Request：${requestStr}，请求参数：${
                    JSON.stringify({ApiKey: apiKey, WorkspaceId: workspaceId, RequestId: query})
                }`
            );

            const {basePath, paths} = data;
            const apiPath = Object.keys(paths)[0];
            const apiMethod = Object.keys(paths[apiPath])[0];
            const {summary, description} = paths[apiPath][apiMethod];

            yield stream.flush(`\n\n以下是为你查询到的 Request：`);
            yield stream.flush(`\n\nRequest 名称：${summary}`);
            yield stream.flush(`\n\nRequest 描述：${description || '-'}`);
            yield stream.flush(`\n\nRequest 路径：${basePath === '/' ? '' : basePath}${apiPath}`);
            yield stream.flush(`\n\nRequest 方法：${apiMethod || '-'}`);

            // 生成服务代码
            const serverLanguage = await this.config.getAsString('serverLanguage', '');
            const serverFramework = await this.config.getAsString('serverFramework', '');
            const serverDaoFramework = await this.config.getAsString('serverDaoFramework', '');

            yield stream.flush('\n\n开始生成服务代码...');
            const serverCodeList = ['model', 'dao', 'service', 'controller'];
            let currTaskId = 0;
            for (const item of serverCodeList) {
                yield stream.flush(`\n\n正在生成【${item}】层服务代码...`);
                const retServerCode = await generateServerCode(this, {
                    swaggerContent: requestStr,
                    apiName: '',
                    story: '',
                    username: 'Postman',
                    codeType: item,
                    serverLanguage,
                    serverFramework,
                    serverDaoFramework,
                    ...(currTaskId ? {taskId: currTaskId} : {}),
                });

                const {code, message: msg, data} = retServerCode as any;
                if (code === 500001) {
                    yield stream.flush(`\n\n${msg}`);
                    continue;
                }

                if (code !== 200) {
                    yield stream.flush(`\n\n生成【${item}】层服务代码出错：${msg}`);
                    continue;
                }

                if (!data) {
                    yield stream.flush(`\n\n未生成【${item}】层服务代码`);
                    continue;
                }

                const layerName = item;

                const {taskId, codeContent} = data;
                currTaskId = taskId || 0;
                const {codeFiles} = codeContent;
                if (codeFiles && codeFiles.length) {
                    for (const item of codeFiles) {
                        const {fileName, content} = item;
                        yield stream.flush(`\n\n已生成【${layerName}】层代码文件：${fileName}`);
                        yield stream.flush(
                            '\n\n' + [
                                '\`\`\`java',
                                content,
                                '\`\`\`',
                            ]
                                .join('\n')
                        );
                    }
                }
                else {
                    yield stream.flush(`\n\n未生成【${item}】层服务代码`);
                }
            }
        }
        catch (error) {
            this.logger.error(`Plugin Error: ${error as string}`);
            yield stream.flushReplaceLast(`\n\n请求出错，请联系负责人或稍后再试。`);
        }
    }
}
