{"private": true, "name": "@comate-plugin/postman", "version": "0.9.2", "type": "module", "scripts": {"clean": "rm -rf dist", "dev": "cpd dev", "build": "tsc && cpd build"}, "devDependencies": {"@comate/plugin-cli": "^0.9.2", "@comate/plugin-host": "^0.9.2"}, "comate": {"name": "postman", "version": "1.0.0", "icon": "./assets/icon.png", "entry": "./dist/index.js", "displayName": "Postman", "description": "Postman智能协作插件", "keyword": ["Postman", "API", "接口"], "author": [{"name": "muli", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "capabilities": [{"type": "Skill", "name": "genServerCode", "displayName": "接口实现代码", "description": "根据 Postman Request 智能生成接口实现代码，覆盖 controller 层、service 层、dao 层、model 层等，为接口服务开发提效。", "placeholder": "输入RequestId并发送（直接点击发送可查询RequestId列表）"}], "configSchema": {"enabled": true, "sections": [{"title": "插件配置", "properties": {"ApiKey": {"type": "string", "title": "Postman 中生成的 API key", "description": "Postman 中生成的 API key"}, "WorkspaceId": {"type": "string", "title": "Postman 中的 Workspace ID", "description": "Postman 中的 Workspace ID"}, "serverLanguage": {"type": "string", "enum": ["Java"], "title": "接口实现代码的语言", "description": "接口实现代码的语言", "default": "Java"}, "serverFramework": {"type": "string", "enum": ["SpringBoot"], "title": "接口实现代码的框架", "description": "接口实现代码的框架", "default": "SpringBoot"}, "serverDaoFramework": {"type": "string", "enum": ["JPA"], "title": "接口实现代码的 Dao 层框架", "description": "接口实现代码的 Dao 层框架", "default": "JPA"}}}]}}}