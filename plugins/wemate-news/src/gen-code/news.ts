import {ListItem} from '../types/index.js';
import * as fs from 'fs/promises';
import {genImgImportStatement} from './importStatement.js';
import {imgPath2Name} from '../utils/index.js';

interface GenNewsTsx {
    filePath: string;
    articleType: string;
    listItem: ListItem;
}

export function genOneDataStr(listItem: ListItem): string {
    return `
    {
        title: '${listItem.title}',
        titleImage: ${imgPath2Name(listItem.titleImage)},
        createTime: '${listItem.createTime}',
        id: '${listItem.id}',
        intro: '${listItem.intro}',
    }`;
}

export default async function genNewsTsx({listItem, filePath, articleType}: GenNewsTsx): Promise<string> {
    try {
        // 读取文件内容
        const originalContent = await fs.readFile(filePath, 'utf-8');

        // 生成需要新加入的导入语句
        const newImportStatement: string = genImgImportStatement(listItem.titleImage);

        // 提取原有的导入语句
        const importRegex = /import .*? from .*?;[\r\n]*/g;
        const importStatements = originalContent.match(importRegex) || [];

        let updatedImportStatements = [...importStatements];
        if (!importStatements.length) {
            updatedImportStatements = [newImportStatement];
        }
        else {
            // 根据文章类型插入新导入语句
            updatedImportStatements = [...importStatements.map(item => item.trim())];

            // 正则表达式匹配文章类型和文章编号
            const regex = /import\s+(\w+)(\d+)\w+\s+from\s+['"].*['"];/;

            // 找到插入位置
            let insertIndex = importStatements.length;
            for (let i = 0; i < importStatements.length; i++) {
                const existingStatement = importStatements[i];
                const existingMatch = existingStatement.match(regex);
                if (existingMatch) {
                    const existingArticleType = existingMatch[1];

                    const existingArticleNumber = parseInt(existingMatch[2], 10);

                    // 如果文章类型相同，且新的文章编号小于等于现有的文章编号，找到插入位置
                    if (existingArticleType === articleType && parseInt(listItem.id, 10) >= existingArticleNumber) {
                        insertIndex = i + 1;
                    }
                }
            }

            updatedImportStatements.splice(insertIndex, 0, newImportStatement);
        }

        // 提取数据部分
        const dataRegex = /export const (NEWS_ARTICLES|CONFERENCE|HONOR) = \[([\s\S]*?)\];/g;
        const dataMatches = Array.from(originalContent.matchAll(dataRegex));

        // 根据文章类型插入新数据
        const newDataStr = genOneDataStr(listItem);
        const updatedData = dataMatches.map(match => {
            const [fullMatch, arrayName, arrayContent] = match;

            if (
                arrayName === 'NEWS_ARTICLES' && articleType === 'news'
                || arrayName === 'CONFERENCE' && articleType === 'conference'
                || arrayName === 'HONOR' && articleType === 'honor'
            ) {
                return `export const ${arrayName} = [${newDataStr},${arrayContent}];`;
            }
            return fullMatch;
        });

        // 组合新的文件内容
        const newFileContent = '/* eslint-disable max-len */\n' + updatedImportStatements.join('\n') + '\n\n'
            + updatedData
                .join('\n\n')
            + '\n';

        return newFileContent;
    }
    catch (error) {
        throw new Error(`读取 ${filePath} 文件失败: ${error}`);
    }
}
