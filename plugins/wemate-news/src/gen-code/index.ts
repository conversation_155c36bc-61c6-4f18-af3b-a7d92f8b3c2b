import {ArticleType, GenContentTsx, ListItem} from '../types/index.js';
import * as fs from 'fs/promises';
import {
    genImgImportStatement,
    genComponentImportStatement,
    genImportStatements,
} from './importStatement.js';
import genPageTsx from './page.js';
import genContentTsx from './content.js';
import genNewsTsx, {genOneDataStr} from './news.js';
import {getCodeDirPath, getContentPath, getNewsPath, getPagePath} from '../utils/index.js';

interface GenALlCodeFile {
    listItem: ListItem;
    genContentTsxParams: GenContentTsx;
    articleId: string;
    articleType: ArticleType;
    workspacePath: string;
}

/**
 * 生成所有代码文件(news.ts, Content.tsx, Page.tsx)
 * @param {GenALlCodeFile} params 包含文章类型、文章ID和工作区路径，要要生成的listItem, genContentTsxParams的对象
 * @returns {Promise<void>}
 */
async function genAllCodeFile(
    {listItem, genContentTsxParams, articleId, articleType, workspacePath}: GenALlCodeFile
): Promise<void> {
    // 获得要生成的代码的文件夹路径
    const codeDirPath = getCodeDirPath({articleType, articleId, workspacePath});

    // 生成并写入 `news.ts`
    const newsFilePath = getNewsPath(workspacePath);
    try {
        const newsTsx = await genNewsTsx({listItem, filePath: newsFilePath, articleType});
        await fs.writeFile(newsFilePath, newsTsx);
    }
    catch (error) {
        throw new Error(`生成 news.ts 失败: ${error}`);
    }

    // 生成并写入 `Content.tsx`
    const contentFilePath = getContentPath(codeDirPath);
    const contentTsx = genContentTsx(genContentTsxParams);

    try {
        await fs.writeFile(contentFilePath, contentTsx);
    }
    catch (error) {
        throw new Error(`写入 Content.tsx 失败: ${error}`);
    }

    // 生成并写入 `Page.tsx`
    const pageFilePath = getPagePath(codeDirPath);
    const pageTsx = genPageTsx(listItem.title);
    try {
        await fs.writeFile(pageFilePath, pageTsx);
    }
    catch (error) {
        throw new Error(`写入 Page.tsx 失败: ${error}`);
    }
}

export {
    genContentTsx,
    genImgImportStatement,
    genComponentImportStatement,
    genImportStatements,
    genNewsTsx,
    genOneDataStr,
    genPageTsx,
};
export default genAllCodeFile;
