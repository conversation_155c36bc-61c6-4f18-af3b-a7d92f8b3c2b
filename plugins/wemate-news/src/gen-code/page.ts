/**
 * 生成`page.tsx`文件内容
 *
 * @param title 页面标题
 * @returns 生成的 `page.tsx`文件字符串内容
 */
export default function genPageTsx(title: string): string {
    return `/* eslint-disable max-len */
import type {Metadata} from 'next';
import {getStaticParams} from '@/utils/build';
import DisableDebugger from '@/components/DisableDebugger';
import '../../../main.css';
import Content from './Content';

export async function generateStaticParams() {
    const staticParams = getStaticParams(process.env.NEXT_PUBLIC_APP_ENV, '/pricing');
    return staticParams;
}

interface RouterParams {
    lng: string;
}

interface GenerateMetadataProps {
    params: RouterParams;
}

export async function generateMetadata({params}: GenerateMetadataProps): Promise<Metadata> {
    const {lng} = params;
    return {
        title: lng === 'zh' ? 'Baidu Comate | ${title}' : 'Baidu Comate',
        description: 'Comate是百度基于文心大模型研发的编程辅助工具，提供自动代码生成、单元测试生成、注释生成、研发智能问答等能力，支持上百种编程语言，提升“十倍”编码效率。',
        keywords: 'Comate, 智能编码助手, AI编程, 代码生成, 单元测试, 智能问答, copilot, 编程工具, AI辅助编程, AI写代码, AI代码生成, 代码工具, 模型生成代码, 提升编码效率, AI编程助手, 自动代码补全, 代码优化, AI代码审查, 智能代码提示, 快速编程, 代码生成器, 开发者工具, 代码质量提升, AI驱动编程, 代码重构, AI代码优化, 编程效率提升, AI代码建议, 代码自动化测试, 编程效率工具',
        viewport: 'width=device-width, initial-scale=1.0',
    };
}

export default function News5({params}: {params: RouterParams}) {
    const {lng} = params;

    return (
        <>
            <Content lng={lng} />
            <DisableDebugger />
        </>
    );
}

`;
}
