import {imgPath2Name} from '../utils/index.js';

/**
 * 生成图片导入语句
 *
 * @param filePath 文件名，如 'articleTypeId-title.imgType' 或 'articleTypeId-content-imgId.imgType'
 * @returns 生成的导入语句
 * @throws 如果文件名格式无效，则抛出错误
 */
function genImgImportStatement(filePath: string) {
    const fileName = imgPath2Name(filePath);

    const regex = /public\/images\/news\/.+/;
    const match = filePath.match(regex);

    if (match) {
        return `import ${fileName} from '@${match[0]}';`;
    }
    else {
        throw new Error('文件名格式无效');
    }
}

/**
 * 生成组件导入语句，如 `import Text from '../../Text';`
 *
 * @param ComponentName 组件名称
 * @returns 返回生成的导入语句
 */
function genComponentImportStatement(ComponentName: string) {
    return `import ${ComponentName} from '../../${ComponentName}';`;
}

// 根据所有的图片文件名和组件名生成所有的导入语句
function genImportStatements(imgFileNames: string[], componentNames: string[]): string {
    const imgImportStatements = imgFileNames.map(genImgImportStatement);
    const componentImportStatements = componentNames.map(
        genComponentImportStatement
    );

    return [...imgImportStatements, ...componentImportStatements].join('\n');
}

export {genImgImportStatement, genComponentImportStatement, genImportStatements};
