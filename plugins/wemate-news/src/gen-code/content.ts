import {GenContentTsx} from '../types/index.js';

/**
 * 生成包含文章Content.tsx的TSX代码
 *
 * @param param 包含生成文章内容所需的参数
 * @returns 返回生成的TSX代码字符串
 */
const genContentTsx = (
    {title, createTime, articleType, contentTsx, importImgsStatements, hasTitle}: GenContentTsx
): string =>
    `/* eslint-disable max-len */
'use client';

import Image from 'next/image';
${importImgsStatements}

import Nav from '@/components/Nav';
import WhiteLayout from '@/components/Layout/WhiteLayout';
import useUserInfo from '@/hooks/useUserInfo';
import useFcnapAndElasticTrack from '@/hooks/useFcnapAndElasticTrack';
import Header from '../../Header';
import Text from '../../Text';
${hasTitle ? 'import Title from \'../../Title\';' : ''}
import Survey from '../../Survey';

interface Props {
    lng: string;
}

export default function Content({lng}: Props) {
    const loginState = useUserInfo();
    const {isLogin, initialized, userInfo} = loginState;
    useFcnapAndElasticTrack({lng, isLogin, userName: userInfo?.displayName, maskId: userInfo?.maskId, initialized});

    return (
        <div className="app code-block-dark">
            <WhiteLayout className="pb-20">
                <Nav lng={lng} activeTab="serve" theme="black" className="bg-white" />
                <section className="mobile:px-5 w-[1200px] m-auto mobile:min-w-full text-black pt-8 relative py-8 px-12 mt-8 mb-6 bg-white rounded-lg">
                    <Header
                        title="${title}"
                        time="${createTime}"
                        tab="${articleType}"
                    />
                    <div className="flex flex-col gap-3 px-3 pt-6">
${contentTsx.trim().split('\n').map(line => ' '.repeat(24) + line).join('\n')}
                    </div>
                </section>
                <Survey loginState={loginState} />
            </WhiteLayout>
        </div>
    );
}

`;

export default genContentTsx;
