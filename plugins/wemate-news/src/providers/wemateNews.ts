import {
    SkillProvider,
    FunctionParameterDefinition,
    TaskProgressChunk,
    TaskProgressChunkStream,
    GitRepositoryInfo,
} from '@comate/plugin-host';
import {calculateArticleId, getContentPath, getNewsPath, getPagePath} from '../utils/index.js';
import {ArticleType} from '../types/index.js';
import parseArticle from '../parse-article/index.js';
import axios from 'axios';
import genAllCodeFile from '../gen-code/index.js';

export class WemateNewsSkillProvider extends SkillProvider {
    static skillName = 'wemateNews';

    static description = '将微信推文转换为官网前端页面代码';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    // 当前项目目录
    private readonly cwd = '';

    constructor(init: any) {
        super(init);
        this.cwd = init.cwd;
    }

    protected async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new TaskProgressChunkStream();
        const workspacePath = this.cwd;
        yield stream.flush(`- 开始执行 ${workspacePath} 下的代码生成任务... \n\n`);

        const [inputUrl, inputArticleType] = this.currentContext.query.split('\n');

        if (!inputUrl || !inputArticleType) {
            yield stream.flush(`请输入文章的url和类型，用换行符隔开\n`);
            return;
        }
        const url = inputUrl.trim();

        yield stream.flush(`- 输入的url：${url}\n\n`);
        yield stream.flush(`- 输入的articleType：${inputArticleType}\n\n`);

        yield stream.flush(`- 开始进行检查...\n`);

        // 检查文章类型是否是合法的
        if (!['news', 'conference', 'honor'].includes(inputArticleType.trim().toLowerCase())) {
            yield stream.flush(`输入的文章类型非法，请输入 news | conference | honor 之一\n`);
            return;
        }

        const articleType: ArticleType = inputArticleType.trim() as ArticleType;

        // 检查代码仓库是否是 coding-suggestion/frontend，并且分支是 external-test
        const repository = await this.retriever.repositoryInfo();
        if (repository && (repository as GitRepositoryInfo).repositoryUrl && repository.versionControl === 'git') {
            const isRepoValid = repository.repositoryUrl ? /.*@icode.baidu.com.*\/baidu\/coding-suggestion\/frontend$/.test(repository.repositoryUrl) : false;
            if (!isRepoValid) {
                yield stream.flush(`当前代码仓库不是 @icode.baidu.com/baidu/coding-suggestion/frontend\n\n`);
                return;
            }
            // 检查分支是否是 external-test
            const isBranchValid = repository.branch === 'external-test';

            if (!isBranchValid) {
                yield stream.flush(`当前分支不是 external-test`);
                yield stream.flush(`请运行 git checkout external-test 命令 切换到 external-test 分支`);
                return;
            }
        }

        yield stream.flush(`- 代码仓库和分支检查通过\n`);

        // 计算文章id
        const articleId = calculateArticleId(articleType as ArticleType, workspacePath);

        yield stream.flush(`- 新增的文章id为：${articleId}\n`);

        // 获取文章内容
        const {data} = await axios.get(url, {
            headers: {
                'User-Agent':
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3',
            },
        });

        // 解析文章
        const {listItem, genContentTsxParams} = await parseArticle({data, articleType, articleId, workspacePath});

        // 生成所有代码文件
        genAllCodeFile({listItem, genContentTsxParams, articleId, articleType, workspacePath});

        yield stream.flush(`- 生成文章所有代码成功\n`);
        yield stream.flush(`- 生成 news.ts 文件路径：${getNewsPath(workspacePath)}\n`);
        yield stream.flush(`- 生成 Content.tsx 文件路径：${getContentPath(workspacePath)}\n`);
        yield stream.flush(`- 生成 page.tsx 文件路径：${getPagePath(workspacePath)}\n`);
    }
}
