import {
    FallbackProvider,
    TaskProgressChunk,
    TaskProgressChunkStream,
    FunctionParameterDefinition,
} from '@comate/plugin-host';

export class HelloFallbackProvider extends FallbackProvider {
    static description = '介绍插件能力与开发方式';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *handleQuery(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new TaskProgressChunkStream();
        yield stream.flush('你好，我是wemate-news插件。\n\n');
        yield stream.flush('能看到这些文字，你已经成功地运行了插件。\n\n');
        yield stream.flush(
            '如果要使用插件会自动识别文章的来源，类型，正文，图片等内容，更新news.ts数据，生成Content.tsx和page.tsx代码文件, 请在对话中选择“微信推文转换”指令进行操作。\n\n'
        );
        yield stream.flush(
            '然后输入需要转换的微信推文的url和文章类型，二者在不同的行。（使用 shift + enter 进行换行）。\n\n'
        );
        yield stream.flush('如果需要帮助，请输入 /help\n\n');
    }
}
