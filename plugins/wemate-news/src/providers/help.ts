import {
    FunctionParameterDefinition,
    SkillProvider,
    TaskProgressChunk,
    TaskProgressChunkStream,
} from '@comate/plugin-host';
import {readFileSync} from 'fs';
import {fileURLToPath} from 'node:url';
import {dirname} from 'path';

export class HelpSkillProvider extends SkillProvider {
    static skillName = 'wemate-news-help';
    static displayName: '插件介绍';
    static description = '插件介绍';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new TaskProgressChunkStream();
        yield stream.flush('你好，我是wemate-news插件。\n\n');
        yield stream.flush('能看到这些文字，你已经成功地运行了插件。\n\n');
        yield stream.flush(
            '如果要使用插件会自动识别文章的来源，类型，正文，图片等内容，更新news.ts数据，生成Content.tsx和page.tsx代码文件, 请在对话中选择“微信推文转换”指令进行操作。\n'
        );
        yield stream.flush(
            '然后输入需要转换的微信推文的url和文章类型，二者在不同的行。（使用 shift + enter 进行换行）。\n'
        );
        const filename = fileURLToPath(import.meta.url);
        const content = readFileSync(`${dirname(filename)}/help.md`, 'utf8');
        yield stream.flush(content);
    }
}
