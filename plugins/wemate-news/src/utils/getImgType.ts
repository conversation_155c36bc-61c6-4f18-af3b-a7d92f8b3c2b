/**
 * 根据给定的微星公众号推文图片 URL 获取图片格式。
 *
 * @param url 微星公众号推文图片的 URL 地址。
 * @returns 返回图片格式，如果 URL 中不包含 wx_fmt 参数，则返回 null。
 * @throws {Error} 当 URL 无效或为空时抛出错误。
 */
export function getImgType(url: string | null | undefined): string | null {
    // 检查 URL 是否为空
    if (!url) {
        throw new Error('Invalid URL: URL cannot be empty');
    }

    try {
        // 创建一个 URL 对象
        const urlObj = new URL(url);

        // 从 URL 查询参数中获取 wx_fmt 的值
        const format = urlObj.searchParams.get('wx_fmt');

        // 返回格式
        if (format) {
            // 如果格式为 other
            if (format === 'other') {
                return 'png';
            }

            return format;
        }
        // 如果没有找到 wx_fmt 参数，则抛出错误
        else {
            throw new Error('Invalid URL: URL does not contain wx_fmt parameter');
        }
    }
    catch (error) {
        // 检查错误类型并抛出一个通用的错误
        if (error instanceof TypeError) {
            throw new Error('Invalid URL: ' + error.message);
        }
        else {
            throw new Error('Unexpected error: ' + error);
        }
    }
}
