import dayjs from 'dayjs';

/**
 * 将日期时间字符串格式化
 *
 * @param dateTimeString 日期时间字符串，格式应为 'YYYY-MM-DD HH:mm:ss'
 * @returns 返回格式化后的日期字符串，格式为 'YYYY年MM月DD日'，若日期无效，则返回 'Invalid Date'
 */
export function formatDateTime(dateTimeString: string): string {
    // 尝试解析和格式化日期时间字符串
    const parsedDate = dayjs(dateTimeString);

    // 检查日期是否有效
    if (!parsedDate.isValid()) {
        throw new Error(`Invalid DateTime String: '${dateTimeString}'`);
    }

    // 格式化为所需格式
    return parsedDate.format('YYYY年MM月DD日 HH:mm');
}
