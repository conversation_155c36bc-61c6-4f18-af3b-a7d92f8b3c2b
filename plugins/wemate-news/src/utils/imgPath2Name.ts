import {capitalizeFirstLetter} from './capitalizeFirstLetter.js';
import * as path from 'path';

/**
 * 生成图片导入语句
 *
 * @param filePath 文件路径，如 'public/images/news/articleTypeId-title.imgType' 或 'public/images/news/articleTypeId-content-imgId.imgType'
 * @returns 转换后的图片名字 如 'articleTypeTitleImgType' 或 'articleTypeContentImgIdImgType'
 * @throws 如果文件名格式无效，则抛出错误
 */
export function imgPath2Name(filePath: string) {
    // console.log('fileName', fileName);
    const fileName = path.basename(filePath);

    const parts = fileName.split('-');
    let imgImportName;

    // 如果是 '@public/images/news/articleTypeId-title.imgType' 格式
    if (parts.length === 2) {
        const [articleTypeId, imgLocationType] = parts;
        const [imgLocation, imgType] = imgLocationType.split('.');

        if (!imgType) {
            throw new Error('Invalid file name format');
        }

        imgImportName = `${articleTypeId}${capitalizeFirstLetter(imgLocation)}${capitalizeFirstLetter(imgType)}`;
    }
    // 如果是 '@public/images/news/articleTypeId-content-imgId.imgType' 格式
    else if (parts.length === 3) {
        const [articleTypeId, imgLocation, imgIdType] = parts;
        const [imgId, imgType] = imgIdType.split('.');

        if (!imgType) {
            throw new Error('Invalid file name format');
        }

        imgImportName = `${articleTypeId}${capitalizeFirstLetter(imgLocation)}${imgId}${
            capitalizeFirstLetter(imgType)
        }`;
    }
    // 其余不符合的格式
    else {
        throw new Error('Invalid file name format');
    }

    return imgImportName;
}
