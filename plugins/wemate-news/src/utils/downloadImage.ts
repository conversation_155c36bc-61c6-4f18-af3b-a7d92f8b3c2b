import axios from 'axios';
import * as fs from 'fs';

/**
 * 从指定URL下载图片并保存到指定路径
 *
 * @param imgSrc 图片的URL地址
 * @param imgPath 图片保存的路径
 * @returns Promise<void> 返回一个Promise，图片下载完成时resolve，发生错误时reject
 */
export async function downloadImage(imgSrc: string, imgPath: string): Promise<void> {
    const writer = fs.createWriteStream(imgPath);

    try {
        const response = await axios({
            url: imgSrc,
            method: 'GET',
            responseType: 'stream',
        });

        response.data.pipe(writer);

        return new Promise((resolve, reject) => {
            writer.on('finish', resolve);
            writer.on('error', reject);
        });
    }
    catch (error) {
        // 发生错误时，确保不创建文件并抛出错误
        writer.close();
        fs.unlink(imgPath, () => {}); // 清理可能存在的文件
        throw new Error('下载图片失败'); // 将错误抛出
    }
}
