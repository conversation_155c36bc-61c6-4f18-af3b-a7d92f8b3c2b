import {ArticleType} from '../types/index.js';
import * as fs from 'fs';

interface GetCodePath {
    articleType: ArticleType;
    articleId: string;
    workspacePath: string;
}

/**
 * 获取要生成的代码目录路径
 *
 * @param params 包含文章类型、文章ID和工作区路径的对象
 * @returns 返回代码目录路径
 */
function getCodeDirPath({articleType, articleId, workspacePath}: GetCodePath) {
    const codeDirPath = `${workspacePath}/src/app/[lng]/news/${articleType}/${articleId}`;

    // 确保新增文章的目录存在
    if (!fs.existsSync(codeDirPath)) {
        fs.mkdirSync(codeDirPath, {recursive: true});
    }
    return codeDirPath;
}

/**
 * 获取内容文件的路径
 *
 * @param codeDirPath 代码目录路径
 * @returns `Content.tsx`文件的路径
 */
function getContentPath(codeDirPath: string) {
    return `${codeDirPath}/Content.tsx`;
}

/**
 * 获取内容文件的路径
 *
 * @param codeDirPath 代码目录路径
 * @returns `page.tsx`文件的路径
 */
function getPagePath(codeDirPath: string) {
    return `${codeDirPath}/page.tsx`;
}

/**
 * 获取图片目录路径
 *
 * @param workspacePath 工作区路径
 * @returns 返回图片目录路径
 */
function getImageDirPath(workspacePath: string) {
    return `${workspacePath}/public/images/news`;
}

/**
 * 获取新闻文件的路径
 *
 * @param workspacePath 工作区路径
 * @returns `news.ts`文件的完整路径
 */
function getNewsPath(workspacePath: string) {
    return `${workspacePath}/src/constants/news.ts`;
}

export {getCodeDirPath, getContentPath, getPagePath, getImageDirPath, getNewsPath};
