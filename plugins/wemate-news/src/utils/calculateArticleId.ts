import {ArticleType} from '../types/index.js';
import * as fs from 'fs';
import * as path from 'path';

/**
 * 计算文章ID
 *
 * @param articleType 文章类型
 * @param workspacePath 工作空间路径
 * @returns 返回计算出的文章ID
 * @throws 如果无法读取目录，则抛出错误
 */
export function calculateArticleId(articleType: ArticleType, workspacePath: string): string {
    try {
        const basePath = `${workspacePath}/src/app/[lng]/news/${articleType}`;
        // 读取目录内容
        const files = fs.readdirSync(basePath);

        // 过滤出全是数字命名的目录
        const numericDirs = files.filter(file => {
            const fullPath = path.join(basePath, file);
            return fs.statSync(fullPath).isDirectory() && /^\d+$/.test(file);
        });

        if (numericDirs.length === 0) {
            console.log('No numeric directories found.');
            throw new Error('No numeric directories found.');
        }

        // 找到数字最大的目录名
        const maxNumericDir = numericDirs.reduce((max, dir) => {
            const num = parseInt(dir, 10);
            return num > max ? num : max;
        }, 0);
        return (maxNumericDir + 1).toString();
    }
    catch (error) {
        throw new Error('Error reading directory');
    }
}
