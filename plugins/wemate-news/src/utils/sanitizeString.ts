/**
 * 清理字符串中的非法字符，并替换为合法的字符。
 *
 * @param input 要清理的字符串
 * @returns 清理后的字符串
 */
export function sanitizeString(input: string): string {
    // 使用正则表达式替换非法空格
    const illegalSpace = /[\u000B\u000C\u00A0\u0085\u1680\u180E\ufeff\u2000-\u200B\u2028\u2029\u202F\u205F\u3000]/g;
    const legalSpace = ' '; // 将非法空格替换为普通空格

    // 使用正则表达式替换半角双引号为合法的 HTML 实体
    const illegalDoubleQuote = /"/g;
    const legalDoubleQuote = '&quot;';

    // 执行替换
    let sanitizedString = input.replace(illegalSpace, legalSpace);
    sanitizedString = sanitizedString.replace(illegalDoubleQuote, legalDoubleQuote);

    return sanitizedString;
}
