import {PluginSetupContext} from '@comate/plugin-host';
import {HelpSkillProvider} from './providers/help.js';
import {HelloFallbackProvider} from './providers/hello.js';
import {WemateNewsSkillProvider} from './providers/wemateNews.js';

export function setup({registry}: PluginSetupContext) {
    registry.registerFallbackProvider('hello', HelloFallbackProvider);
    registry.registerSkillProvider('wemate-news-help', HelpSkillProvider);
    registry.registerSkillProvider('wemateNews', WemateNewsSkillProvider);
}
