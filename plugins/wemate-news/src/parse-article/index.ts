import {load} from 'cheerio';

import parseCreateTime from './createTime.js';
import parseContent from './content.js';

import {getImgType, downloadImage, getImageDirPath} from '../utils/index.js';
import {ListItem, ArticleType, GenContentTsx} from '../types/index.js';
import {genImportStatements} from '../gen-code/index.js';

interface ParseArticle {
    data: string;
    articleType: ArticleType;
    articleId: string;
    workspacePath: string;
}

interface ParseArticleResult {
    listItem: ListItem;
    genContentTsxParams: GenContentTsx;
}

/**
 * 解析文章内容
 *
 * @param param 参数对象，包含数据、文章类型、文章ID和工作区路径
 * @returns 返回一个包含列表项和生成TSX内容的参数的对象
 * @throws 如果解析失败，则抛出错误
 */
export default async function parseArticle(
    {data, articleType, articleId, workspacePath}: ParseArticle
): Promise<ParseArticleResult> {
    try {
        const $ = load(data);
        // 获取标题
        const title = $('#activity-name').text().trim();

        // 获取封面图片，图片保存的目录路径，保存封面图片
        const coverImg = $('#js_row_immersive_cover_img').find('img');
        const coverImgSrc = coverImg.attr('src') as string;

        const coverImgType = getImgType(coverImgSrc);
        const articleImageDirPath = `${getImageDirPath(workspacePath)}`;
        const coverImgPath = `${articleImageDirPath}/${articleType}${articleId}-title.${coverImgType}`;

        await downloadImage(coverImgSrc, coverImgPath);

        // 获取文章创建时间
        const $scripts = $('script');
        const publishTime = parseCreateTime($scripts);

        // 获取正文内容，并解析
        const content = $('#js_content');
        const {intro, contentTsx, processedImagesFilename, hasTitle} = await parseContent(
            {
                content,
                $,
                articleType,
                articleId,
                workspacePath,
            }
        );

        // 生成`Content.tsx`中图片的导入语句
        const importImgsStatements = genImportStatements(processedImagesFilename, []);

        const listItem: ListItem = {
            title,
            titleImage: coverImgPath,
            createTime: publishTime,
            id: articleId,
            intro,
        };

        const genContentTsxParams: GenContentTsx = {
            title,
            createTime: publishTime,
            articleType,
            contentTsx,
            importImgsStatements,
            hasTitle,
        };

        return {
            listItem,
            genContentTsxParams,
        };
    }
    catch (error) {
        throw new Error(`文章解析失败: ${error}`);
    }
}
