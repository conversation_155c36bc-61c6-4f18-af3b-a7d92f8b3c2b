import {Cheerio, CheerioAPI} from 'cheerio';
import {getImgType, downloadImage, imgPath2Name, getImageDirPath, sanitizeString} from '../utils/index.js';
import {ArticleType} from '../types/index.js';

interface ParseContent {
    content: Cheerio<any>;
    $: CheerioAPI;
    articleType: ArticleType;
    articleId: string;
    workspacePath: string;
}

// 定义解析内容函数返回的类型
interface ParseContentResult {
    contentTsx: string;
    processedImagesFilename: string[];
    intro: string;
    hasTitle: boolean;
}

/**
 * 解析页面元素并获取正文的文本内容
 *
 * @param {Cheerio} content - cheerio 对象，表示需要解析的页面元素
 * @param {Root} $ - cheerio 根对象
 * @param {ArticleType} artileType - 文章类型
 * @param {string} artileId - 文章ID
 * @return {ParseContentResult} 返回解析后的文本内容和简介内容
 */
export default function parseContent(
    {content, $, articleType, articleId, workspacePath}: ParseContent
): ParseContentResult {
    const articleImageDirPath = `${getImageDirPath(workspacePath)}`;

    let result = '';
    let imgIndex = 1;
    let intro = '';
    let hasTitle = false;

    // 存储已经处理的标题和图片
    const processedImages = new Set<string>();
    const processedImagesFilename: string[] = [];
    const processedTitles = new Set<string>();

    const processElements = (elements: Cheerio<any>) => {
        elements.each((index, element) => {
            const $element = $(element);

            // 特殊处理标题
            if ($element.is('section') && $element.data('role') === 'title') {
                const titleText = $element.text().trim();
                if (!processedTitles.has(titleText)) {
                    result += `<Title>${sanitizeString(titleText)}</Title>\n`;
                    processedTitles.add(titleText);
                    // 标记为包含标题
                    hasTitle = true;
                }
            }
            else if (
                $element.is('section') || $element.is('p') || $element.is('h1') || $element.is('h2') || $element
                    .is('h3')
                || $element.is('h4')
            ) {
                // 检查是否包含图片
                const imgs = $element.children('img');
                if (imgs.length > 0) {
                    // 解析图片并更新 result 和 imgIndex
                    imgs.each((_, imgElement) => {
                        const imgSrc = $(imgElement).attr('data-src');
                        if (imgSrc) {
                            const imgType = getImgType(imgSrc);

                            if (
                                (imgType === 'jpeg' || imgType === 'png' || imgType === 'jpg')
                                && !processedImages.has(imgSrc)
                            ) {
                                // 下载图片
                                const imageFilePath =
                                    `${articleImageDirPath}/${articleType}${articleId}-content-${imgIndex}.${imgType}`; // 可以根据需要更改命名规则
                                downloadImage(imgSrc, imageFilePath);
                                processedImagesFilename.push(imageFilePath);

                                const imageFileName = imgPath2Name(imageFilePath);

                                result += `<Image src={${imageFileName}} alt="" className="w-[36.75rem] m-auto" />\n`;
                                imgIndex += 1;
                            }
                            // 添加已处理的图片到集合中
                            processedImages.add(imgSrc);
                        }
                    });
                }
                // 递归处理子元素以寻找最深层的文本
                const children = $element.children('section, p, h1, h2, h3, h4, h5');
                if (children.length > 0) {
                    // 递归处理子元素
                    processElements(children);
                }
                else {
                    // 处理文本，只在最深层节点获取文本
                    let elementText = $element.text().trim();

                    // 移除空内容
                    if (!elementText) {
                        return true;
                    }

                    // 将第一个有效的内容作为简介
                    if (!intro) {
                        intro = sanitizeString(elementText);
                    }

                    // 添加文本内容
                    result += `<Text>${sanitizeString(elementText)}</Text>\n`;
                }
            }
        });
    };

    // 开始处理顶层的 section 和 p 元素
    processElements(content.children('section, p, h1, h2, h3, h4, h5'));

    return {
        contentTsx: result,
        processedImagesFilename,
        intro,
        hasTitle,
    };
}
