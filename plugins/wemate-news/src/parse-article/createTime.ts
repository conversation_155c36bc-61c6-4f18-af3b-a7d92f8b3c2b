import {Cheerio} from 'cheerio';
import {formatDateTime} from '../utils/index.js';
/**
 * 解析 $scripts 内容中的 createTime 变量值
 *
 * @param $scripts
 */
export default function parseCreateTime($scripts: Cheerio<any>) {
    const regex = /var createTime = '([^']+)'/; // 匹配 createTime 的正则表达式

    for (let i = 0; i < $scripts.length; i++) {
        const scriptContent = $scripts.eq(i).contents().text();
        // 使用正则表达式匹配 createTime 的值
        const match = scriptContent.match(regex);

        if (match && match[1]) {
            const createTime = match[1];
            return formatDateTime(createTime); // 格式化日期时间
        }
    }
    return ' ';
}
