{"private": true, "name": "@comate-plugin/wemate-news", "version": "0.8.0", "type": "module", "scripts": {"clean": "rm -rf dist", "dev": "cpd dev", "build": "tsc && cpd build"}, "dependencies": {"axios": "^1.7.9", "cheerio": "^1.0.0", "dayjs": "^1.11.13"}, "devDependencies": {"@comate/plugin-cli": "^0.9.2", "@comate/plugin-host": "^0.9.2", "typescript": "^5.3.2"}, "comate": {"name": "wemate-news", "version": "1.0.0", "icon": "./assets/icon.svg", "entry": "./dist/index.js", "displayName": "wemate-news", "description": "插件描述", "keyword": [], "capabilities": [{"type": "Fallback", "name": "hello", "displayName": "插件介绍", "description": "确认插件成功运行并介绍使用方式"}, {"type": "Skill", "name": "wemate-news-help", "displayName": "帮助文档", "description": "提供帮助文档"}, {"type": "Skill", "name": "wemateNews", "displayName": "微信推文转换", "description": "将微信推文转换为官网前端页面代码"}], "configSchema": {"sections": []}}}