import {HelpSkillProvider} from './providers/help.js';
import {PluginSetupContext} from '@comate/plugin-host';
import {ChatSkillProvider} from './providers/chat.js';
import {CodeGeneratorProvider} from './providers/codeGenerator.js';
import {fallbackProvider} from './providers/fallback.js';
import { CodeRepair } from './providers/codeRepair.js';

export function setup({registry}: PluginSetupContext) {
    registry.registerSkillProvider('gdp-help', HelpSkillProvider);
    registry.registerSkillProvider('chat', ChatSkillProvider);
    registry.registerSkillProvider('codeGenerator', CodeGeneratorProvider);
    registry.registerFallbackProvider('fallback', fallbackProvider, true);
    registry.registerSkillProvider('codeRepair', CodeRepair);
}
