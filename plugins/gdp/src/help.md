您好，我是您的 GO 语言开发助手 GDP，我是一个对厂内基础设施支持好，可扩展性好、易配置、易组装、易测试的 Go 开发框架。
可以在 comate 调起 GDP 插件`@GDP`

**代码生成**
* 可以准确描述您的 GDP 代码生成需求（例如基于 GDP 框架，生成一段 http 协议的 ral client 的请求），即可生成 GDP 的相关代码；
* 使用 GDP 可以快速生成 APP，能力包括配置加载、client 和 server等。

**智能问答**
* 可以描述您对 GDP 使用的任何使用问题（例如如何升级 GDP 框架的版本、如何给 RAL 写单测等），即可准确回答您在此领域的相关知识。

此外，如果您对 GDP 有任何需求或意见，可以进入“GDP 用户交流群” **1612141** 反馈给我们，另外可以通过百度问答提出你的疑问[GDP 问答](https://wenda.baidu.com/ask/topic/49993?type=question)，我们会有相关人员进行解答。
