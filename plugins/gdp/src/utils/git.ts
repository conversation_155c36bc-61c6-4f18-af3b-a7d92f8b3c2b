import {diffLines, Change} from 'diff';
import {SimpleGit, simpleGit} from 'simple-git';
import fs from 'node:fs/promises';
import path from 'path';

export class GitUtil {
    readonly git: SimpleGit;

    constructor(cwd: string) {
        this.git = simpleGit(cwd);
    }

    async filterDiffFile(filePathList: string[]) {
        const fileterFilePath = filePathList.filter(filePath => {
            if (filePath.startsWith('env/test')) {
                return true;
            }

            if (
                filePath.endsWith('.cpp') || filePath.endsWith('.h')
                || filePath.endsWith('.hpp') || filePath.endsWith('.cxx')
            ) {
                return true;
            }

            if (
                filePath.endsWith('.cpp') || filePath.endsWith('.h')
                || filePath.endsWith('.hpp') || filePath.endsWith('.cxx')
            ) {
                return true;
            }

            return false;
        });

        return fileterFilePath;
    }
    async getDiffForFile(file: string) {
        const diff = await this.git.diff(['HEAD', '--', file]);
        return diff;
    }

    async getGoModModuleName(dir: string): Promise<string> {
        try {
            const goModFilePath = await fs.stat(path.join(dir, 'go.mod'));
            if (goModFilePath.isFile()) {
                const goModFileContent = await fs.readFile(path.join(dir, 'go.mod'), {encoding: 'utf8'});
                const regex = /module\s+icode\.baidu\.com\/(baidu\/[a-zA-Z0-9\-_/]+)/;
                const match = regex.exec(goModFileContent);
                return match ? match[1] : 'unknown';
            }
            return 'unknown';
        } catch (e) {
            return 'unknown';
        }
    }

    async getModuleName(dir: string = '.'): Promise<string> {
        try {
            const configs = await this.git.listConfig();
            const remoteUrl = configs.values['.git/config']['remote.origin.url'];
            const regex = /baidu\/[^/]+\/[^/]+(?!.*baidu\/[^/]+\/[^/]+)/;
            let matches: RegExpMatchArray | null = null;
            if (typeof remoteUrl === 'string') {
                matches = regex.exec(remoteUrl);
            }
            else if (Array.isArray(remoteUrl) && remoteUrl.length !== 0) {
                matches = regex.exec(remoteUrl[0]);
            }

            if (matches && matches.length > 0) {
                return matches[0];
            }
            else {
                // 如果没有匹配到，返回 null
                return this.getGoModModuleName(dir);
            }
        }
        catch (e) {
            return this.getGoModModuleName(dir);
        }
    }
    async getStagedDiffContent() {
        const diff = await this.git.diff(['--cached']);
        return diff;
    }

    async getUnstagedDiffContent() {
        const diff = await this.git.diff();
        return diff;
    }

    async getCommitId() {
        const logSummary = await this.git.log({n: 1});
        return logSummary.latest?.hash;
    }

    async getBranch() {
        const branchSummary = await this.git.branchLocal();
        return branchSummary.current;
    }
    async getMergeBase() {
        try {
            const mergeBaseCommitId = await this.git.raw(['merge-base', 'HEAD', 'origin/master']);
            return mergeBaseCommitId;
        } catch (e) {
            return null;
        }
    }

    async getModifiedLinesOfFileWithCommitId(commitId:string, activeFilePath: string, fileContent: string) {
        try {
            const beforeContent = await this.git.show([`${commitId}:` + activeFilePath]);
            const changes = diffLines(beforeContent, fileContent);

            const changedLineNumbersSet: Set<number> = new Set();
            const changedLineNumber : number[] = [];
            const addedLines: number[] = [];

            let oldLineNum = 1;
            let newLineNum = 1;
            changes
                .forEach((part: Change) => {
                    if (part.added) {
                        for (let i = 0; i < part.count!; i++) {
                            const lineNumber = newLineNum;
                            if (!changedLineNumbersSet.has(lineNumber)) {
                                changedLineNumbersSet.add(lineNumber);
                                changedLineNumber.push(lineNumber);
                            }
                            addedLines.push(lineNumber);
                            newLineNum++;
                        }
                    }
                    else if (part.removed) {
                        for (let i = 0; i < part.count!; i++) {
                            oldLineNum++;
                        }
                    }
                    else {
                        oldLineNum += part.count!;
                        newLineNum += part.count!;
                    }
                });
            return {changedLineNumbers: changedLineNumber};
        }
        catch (e) {
            return {changedLineNumbers: []};
        }
    }

    async getDiffFromCommit(commitId: string) {
        try {
            const status = await this.git.status();
            const diffSummary = await this.git.diffSummary([commitId, '--name-status']);
            const newCode: Array<{ codeContent: string, beginLine: number, endLine: number, codePath: string }> = [];
            const nodeAddedFile = status.not_added;
            for (const file of nodeAddedFile) {
                if (file.endsWith('.go') || file.endsWith('.proto')) {
                    const fileContent = (await fs.readFile(file, 'utf8')).toString();
                    const lineTotalNumber = fileContent.split('\n').length;
                    newCode.push({
                        codeContent:fileContent,
                        beginLine: 1,
                        endLine: lineTotalNumber,
                        codePath: file,
                    })

                }
            }
            for (const file of diffSummary.files) {
                if (file.binary || (file.status && file.status === 'D')) {
                    continue;
                }
                const fileContent = (await fs.readFile(file.file, 'utf8')).toString();
                const changedLines = await this.getModifiedLinesOfFileWithCommitId(commitId, file.file, fileContent);
                const fileContentList = fileContent.split('\n');
                let currentContentBlock = '';
                let beginLineNumber = changedLines.changedLineNumbers[0];
                let oldLineNumber = beginLineNumber - 1;
                let endLineNumber = beginLineNumber;
                for (let index = 0; index < changedLines.changedLineNumbers.length; ++index) {
                    const lineNumber = changedLines.changedLineNumbers[index];
                    if (index < (changedLines.changedLineNumbers.length - 1)) {
                        if ((lineNumber - oldLineNumber) === 1) {
                            currentContentBlock += (fileContentList[lineNumber - 1] + '\n');
                            endLineNumber = lineNumber;
                            oldLineNumber = lineNumber;
                        } else {
                            newCode.push({
                                codeContent:currentContentBlock,
                                beginLine: beginLineNumber,
                                endLine: endLineNumber,
                                codePath: file.file,
                            })
                            currentContentBlock = (fileContentList[lineNumber - 1] + '\n');
                            beginLineNumber = lineNumber;
                            endLineNumber = lineNumber;
                            oldLineNumber = changedLines.changedLineNumbers[index + 1] - 1;
                        }
                    } else {
                        if ((lineNumber - oldLineNumber) === 1) {
                            currentContentBlock += (fileContentList[lineNumber - 1] + '\n')
                        } else {
                            newCode.push({
                                codeContent:currentContentBlock,
                                beginLine: beginLineNumber,
                                endLine: oldLineNumber,
                                codePath: file.file,
                            })
                            beginLineNumber = lineNumber;
                            endLineNumber = lineNumber;
                            currentContentBlock = (fileContentList[lineNumber - 1] + '\n')
                        }
                        newCode.push({
                            codeContent:currentContentBlock,
                            beginLine: beginLineNumber,
                            endLine: endLineNumber,
                            codePath: file.file,
                        })

                    }

                }
            }

            // 存储最终解析的结果
            return newCode;
        } catch (error) {
            return null;
        }
    }

    async getMergeBaseDiff() {
        try {
            let mergeBaseCommitId = await this.getMergeBase();

            if (mergeBaseCommitId) {
                mergeBaseCommitId = mergeBaseCommitId.trim();
                return await this.getDiffFromCommit(mergeBaseCommitId);
            }
            return null;
        } catch (e) {
            return null;
        }
    }
}
