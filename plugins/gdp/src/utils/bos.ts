// @ts-expect-error ESM兼容
import {BosClient} from '@baiducloud/sdk';

export class BosUtil {
    private readonly bosClient: BosClient;
    constructor(ak: string, sk: string, endpoint: string, sessionToken: string) {
        const config = {endpoint: endpoint, credentials: {ak: ak, sk: sk}, sessionToken: sessionToken};
        this.bosClient = new BosClient(config);
    }

    async putObjFromStr(bucket: string, key: string, str: string) {
        this.bosClient.putObjectFromString(bucket, key, str);
    }

    async putObjFromFile(bucket: string, key: string, file: string) {
        await this.bosClient.putObjectFromFile(bucket, key, file);
    }

    async generatePresignedUrl(bucket: string, objectKey: string): Promise<string> {
        return this.bosClient.generatePresignedUrl(bucket, objectKey, Math.floor(Date.now() / 1000), -1, {}, {
            responseContentDisposition: 'attachment',
        });
    }
}
