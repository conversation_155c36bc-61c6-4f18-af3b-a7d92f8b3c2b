import {FetchResponseSatus} from '../consts/status.js';

export interface ResponseRes {
    status: number;
    response: Response;
}

export async function fetchUrlCore(url: string, headers: any, data: any, method = 'POST'): Promise<ResponseRes> {
    let response: Response = new Response();

    if (method === 'GET') {
        response = await fetch(url, {
            method: method,
        });
    }
    else {
        response = await fetch(url, {
            method: method,
            headers: headers,
            body: JSON.stringify(data),
        });
    }

    const responseRes: ResponseRes = {
        status: FetchResponseSatus.SUCCESS,
        response: response,
    };

    if (!response.ok) {
        responseRes.status = FetchResponseSatus.FAIL;
        return responseRes;
    }

    return responseRes;
}

export async function fetchUrl(url: string, headers: any, data: any, method = 'POST') {
    try {
        const fetchResponse = await fetchUrlCore(url, headers, data, method);

        if (fetchResponse.status !== FetchResponseSatus.SUCCESS) {
            return null;
        }

        const result = await fetchResponse.response.json();
        return result;
    }
    // eslint-disable-next-line
    catch (error) {
    }
    return null;
}

export async function fetchStreamUrl(url: string, headers: any, data: any, method = 'POST') {
    try {
        const fetchResponse = await fetchUrlCore(url, headers, data, method);

        if (fetchResponse.status !== FetchResponseSatus.SUCCESS) {
            return null;
        }

        return fetchResponse.response.body?.getReader();
    }
    // eslint-disable-next-line
    catch (error) {
    }
    return null;
}
