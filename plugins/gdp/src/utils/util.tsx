import path from 'path';
import fs from 'node:fs/promises';
import {constants} from 'fs/promises';
import {GitDirStatus} from '../consts/status.js';
import {fetchStreamUrl, fetchUrl} from './fetch.js';
import {feedStrategyHiRobotUrl, qadcUpdatePrefixURL, qadcInsertPrefixURL} from '../consts/urls.js';
import {commonHeadersConfig, qadcHeadersConfig} from '../consts/configs.js';
import {ErnieBot4URL} from '../consts/urls.js';
import {ModelType} from '../consts/modelType.js';
import { DrawElement } from '@comate/plugin-shared-internals';
import * as tar from 'tar';
import * as Fs from 'fs';

export function jsonToMap(jsonStr: string): Map<string, string> {
    const obj: any = JSON.parse(jsonStr);
    const map: Map<string, string> = new Map();

    // 将对象的每个属性添加到 Map 对象中
    for (const key of Object.keys(obj)) {
        map.set(key, obj[key]);
    }

    return map;
}

export function trimCustom(str: string, target: string): string {
    const targetLength = target.length;
    let start = 0;
    let end = str.length;

    // 寻找左边需要去除的字符串子串的起始位置
    while (str.startsWith(target, start)) {
        start += targetLength;
    }

    // 寻找右边需要去除的字符串子串的结束位置
    while (str.endsWith(target, end)) {
        end -= targetLength;
    }

    // 返回去除指定字符串子串后的子字符串
    return str.slice(start, end);
}

export function dealModelResultStr(result: string) {
    // eslint-disable-next-line
    result = trimCustom(result, '```');
    // eslint-disable-next-line
    result = trimCustom(result, 'json');
    if (!result || result.length === 0) {
        return null;
    }
    try {
        const resultJsonObj = JSON.parse(result);
        return resultJsonObj;
    }
    catch (e) {
        return null;
    }
}

export function formatDuration(duration: number): string {
    if (duration >= 60000) {
        // 如果耗时超过1分钟，则用1分钟作为单位，并保留两位小数
        return (duration / 60000).toFixed(2) + ' min';
    }
    else if (duration >= 1000) {
        // 如果耗时超过1秒，则用秒作为单位，并保留两位小数
        return (duration / 1000).toFixed(2) + ' s';
    }
    else {
        // 否则，用毫秒作为单位，并保留两位小数
        return duration.toFixed(2) + ' ms';
    }
}

export function countOccurrences(mainStr: string, subStr: string): number {
    const regex = new RegExp(subStr, 'g');
    const matches = mainStr.match(regex);
    return matches ? matches.length : 0;
}

export function countOccurrencesNotFollowedBy(mainStr: string, subStr1: string, subStr2: string): number {
    const regex = new RegExp(`${subStr1}(?!${subStr2})`, 'g');
    const matches = mainStr.match(regex);
    return matches ? matches.length : 0;
}

export function sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
}

export function getRelativePath(filePath: string, workspacePath: string): string {
    let fileRelativePath = filePath;
    if (workspacePath.length >= filePath.length) {
        return fileRelativePath;
    }
    if (path.isAbsolute(filePath)) {
        fileRelativePath = path.relative(workspacePath, filePath);
    }
    return fileRelativePath;
}

export async function getGitUtilObj(filePath: string) {
    let currentPath = path.dirname(filePath);
    let findGit = false;
    // 逐级向上遍历父目录
    // eslint-disable-next-line
    while (true) {
        try {
            const gitFs = await fs.stat(path.join(currentPath, '.git'));
            if (gitFs.isDirectory()) {
                findGit = true;
            }
        }
        // eslint-disable-next-line
        catch (e) {
        }

        if (findGit) {
            // 检查当前目录是否不可读
            try {
                await fs.access(currentPath, fs.constants.R_OK);
            }
            catch (err) {
                return {gitDirStatus: GitDirStatus.ERROR_NOT_READ_PERMISSION, gitDir: null};
            }
            return {gitDirStatus: GitDirStatus.SUCCESS, gitDir: currentPath};
        }

        // 如果已经到达根目录，则退出循环
        if (currentPath === path.dirname(currentPath)) {
            return {gitDirStatus: GitDirStatus.ERROR_NOT_GIT_REPO, gitDir: null};
        }
        // 继续向上查找
        currentPath = path.dirname(currentPath);
    }
}

export async function sendMessage2Hi(groupid: number, message: string) {
    try {
        const payload = {
            message: {
                header: {
                    toid: [groupid],
                },
                body: [{content: message, type: 'TEXT'}, {atall: true, type: 'AT'}],
            },
        };
        await fetchUrl(feedStrategyHiRobotUrl, commonHeadersConfig, payload);
    }
    // eslint-disable-next-line
    catch (e) {
    }
}

export async function getReuqestModelStream(
    accessToken: string | null,
    promptStr: string | null,
    kirinModels: Record<string, any> | null = null
) {
    if (!accessToken) {
        return null;
    }

    try {
        if (kirinModels === null) {
            const url = `${ErnieBot4URL}?access_token=${accessToken}`;
            const payload = {
                messages: [
                    {
                        role: 'user',
                        content: promptStr,
                    },
                ],
                stream: true,
                // eslint-disable-next-line
                max_output_tokens: 2048,
            };

            return await fetchStreamUrl(url, commonHeadersConfig, payload);
        }
        else {
            let url = `${ErnieBot4URL}`;
            if (kirinModels.url) {
                url = kirinModels.url;
            }

            // eslint-disable-next-line
            let max_output_tokens = 2048;
            if (kirinModels.max_output_token) {
                // eslint-disable-next-line
                max_output_tokens = kirinModels.max_output_token;
            }

            let temperature = 0.8;
            if (kirinModels.temperature) {
                temperature = kirinModels.temperature;
            }

            // eslint-disable-next-line
            let top_p = 0.8;
            if (kirinModels.top_p) {
                // eslint-disable-next-line
                top_p = kirinModels.top_p;
            }

            url = `${url}?access_token=${accessToken}`;

            const payload = {
                messages: [
                    {
                        role: 'user',
                        content: promptStr,
                    },
                ],
                stream: true,
                // eslint-disable-next-line
                max_output_tokens: max_output_tokens,
                temperature: temperature,
                // eslint-disable-next-line
                top_p: top_p,
            };
            return await fetchStreamUrl(url, commonHeadersConfig, payload);
        }
    }
    // eslint-disable-next-line
    catch (e) {
    }
    return null;
}

// eslint-disable-next-line
export async function* getReaderStr(modelReader: ReadableStreamDefaultReader<Uint8Array>, modelType: ModelType) {
    if (!modelReader) {
        return;
    }

    try {
        const decoder = new TextDecoder(); // 默认情况下使用 UTF-8 编码
        while (true) {
            const {done, value} = await modelReader.read();
            if (done) {
                break;
            }
            const valueStr = decoder.decode(value);
            const valueStrList = valueStr.split('\n');

            for (let valueStrItem of valueStrList) {
                // eslint-disable-next-line
                if (valueStrItem.trim().length === 0) {
                    continue;
                }
                // eslint-disable-next-line
                if (!valueStrItem.trim().startsWith('data:')) {
                    continue;
                }
                valueStrItem = trimCustom(valueStrItem, 'data: ');
                // eslint-disable-next-line
                try {
                    let chunkString = '';
                    // eslint-disable-next-line
                    if (modelType === ModelType.WENXIN) {
                        chunkString = JSON.parse(valueStrItem).result;
                    }
                    else if (modelType === ModelType.DEEPSEEK) {
                        chunkString = JSON.parse(valueStrItem).choices[0].delta.content;
                    }
                    yield {type: 'message', message: chunkString};
                }
                catch (e) {
                    continue;
                }
            }
        }
    }
    // eslint-disable-next-line
    catch (e) {
    }
}

export async function updateChunk2Qadc(
    chunkStr: string,
    keyValue: string,
    tableName = 'fea_gen_record',
    keyName = 'task_id'
) {
    try {
        let tryTimes = 3;
        while (tryTimes) {
            const url = qadcUpdatePrefixURL + '/' + tableName + '/' + keyName;

            const response = await fetch(url, {
                method: 'POST',
                headers: qadcHeadersConfig,
                // eslint-disable-next-line
                body: JSON.stringify([{[keyName]: keyValue, logic_code: chunkStr}]),
            });
            const responseObj = await response.json();
            if (responseObj.code === 0) {
                return;
            }
            else {
                --tryTimes;
                await sleep(1.5 * 1000);
            }
        }
    }
    // eslint-disable-next-line
    catch {
    }
}

export async function flushRes2Qadc(tableName: string, data: any) {
    const qadcInsertUrl = `http://qadc.baidu-int.com/api/insert/${tableName}`;
    const response = await fetch(qadcInsertUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            name: 'zhangjingqing',
            token: 'dcc816aa80524c96be0820377509a323',
        },
        body: JSON.stringify(data),
    });

    const result = await response.json();
    if (response.ok && result.message === 'success' && result.code === 0) {
        return {
            status: true,
        };
    }

    return {status: false};
}

export function renderTemplate(promptTemplate: string, args?: Record<string, unknown>) {
    if (args) {
        return promptTemplate.replaceAll(
            /\{\{(\w+)\}\}/g,
            (match, key) => {
                const replacement = args[key];
                return replacement === undefined ? match : String(replacement);
            }
        );
    }

    return promptTemplate;
}

export function getJsonValue(obj: Record<string, any>, key: string): any {
    if (obj === null) {
        return null;
    }
    return key in obj ? obj[key] : null;
}

export function removeLeftmostSubstring(str: string, substring: string): string {
    // 检查字符串是否以特定子串开头
    if (str.startsWith(substring)) {
        // 如果是，使用 slice 方法去掉最左侧的子串
        return str.slice(substring.length);
    }

    // 如果字符串不以子串开头，返回原字符串
    return str;
}

export async function checkFileExists(filePath: string): Promise<boolean> {
    try {
        await fs.access(filePath, constants.W_OK);
        return true;
    } catch (e) {
        return false;
    }
}

export function removeTrailingNumbers(str: string): {cleanedString: string, trailingNumbers: string | null} {
    // 使用正则表达式匹配末尾的数字
    const match = /\d+$/.exec(str);

    // 如果找到末尾数字，移除它并返回
    if (match) {
        const trailingNumbers = match[0]; // 获取末尾的数字
        const cleanedString = str.replace(/\d+$/, ''); // 去掉末尾的数字
        return {cleanedString, trailingNumbers};
    }

    // 如果没有找到末尾数字，返回原字符串和null
    return {cleanedString: str, trailingNumbers: null};
}

export async function extractCodeBlocks(markdown: string, cwd: string, codeFilePath: string[]) {
        const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
        const parts: DrawElement[] = [];
        let lastIndex = 0;
        let match;
        let codeBlockIndex = 0;

        while ((match = codeBlockRegex.exec(markdown)) !== null) {
            const [fullMatch, language, code] = match;

            // 添加非代码块的部分
            if (lastIndex < match.index) {
                parts.push(<markdown>{markdown.slice(lastIndex, match.index)}</markdown>);
            }

            let filePath = codeFilePath[codeBlockIndex];
            if (await checkFileExists(filePath)) {
                // 自定义的代码块 JSX 元素
                parts.push(
                    <code-block
                        language={language}
                        insertToFileData={
                            {
                                filePath: filePath,
                                position: { line: 1, character: 0 },
                                newText: code.trim(),
                                metadata: { needsConfirmation: true }
                            }
                        }
                        actions={['viewFile', 'copy', 'accept']}
                        closed={false} >
                        {code.trim()}
                    </code-block>
                );
            } else {
                // 自定义的代码块 JSX 元素
                parts.push(
                    <code-block
                        language={language}
                        insertToFileData={
                            {
                                filePath: filePath,
                                position: { line: 1, character: 0 },
                                newText: code.trim(),
                                metadata: { needsConfirmation: true }
                            }
                        }
                        actions={['newFile', 'copy', 'accept']}
                        closed={false} >
                        {code.trim()}
                    </code-block>
                );
            }
            ++codeBlockIndex;

            // 更新索引
            lastIndex = match.index + fullMatch.length;
        }

        // 添加剩余部分
        if (lastIndex < markdown.length) {
            parts.push(<markdown>{markdown.slice(lastIndex)}</markdown>);
        }
        return parts;
    };

export function countCodeBlocks(markdown: string): number{
    // 正则表达式匹配代码块，``` 后跟语言标识（可选），然后是代码内容，最后是 ```
    const codeBlockRegex = /```[\s\S]*?```/g;

    // 使用正则表达式匹配所有的代码块，并返回匹配到的数量
    const matches = markdown.match(codeBlockRegex);

    // 如果匹配到了，返回匹配的数量，否则返回 0
    return matches ? matches.length : 0;
};

export async function tarFilesInSpecialFolder(sourceFolder: string, targetNmae: string, isIgnoreGit = false):Promise<[boolean, string]> {
    try {
        await tar.c(
            {
                gzip: true,
                file: targetNmae,
                filter: (path) => {
                    if (isIgnoreGit) {
                        return !path.includes('.git');
                    }
                    return true;
                },
            },
            [sourceFolder]
        )
        return [true, ''];
    } catch(e) {
        return [false, String(e)];
    }
}

export function readFiles(filePaths: string[], rootPath: string): [string, string, boolean]{
/**
 * 读取指定路径下的文件
 */
    let hint = '';
    let formattedOutput = '';
    let isSuccess = true;
    // 组装成指定格式
    filePaths.forEach((filePath) => {
        const fullPath = path.resolve(rootPath, filePath);

        // 检查文件是否存在
        if (Fs.existsSync(fullPath) && Fs.statSync(fullPath).isFile()) {
            try {
                const fileContent = Fs.readFileSync(fullPath, 'utf-8');
                formattedOutput += `${filePath}:\n${fileContent}\n`;
                hint += `${filePath} 文件读取成功～ \n\n`;
            } catch (error) {
                hint += `${filePath} 读取出错: ${error} \n\n`;
                isSuccess = false;
            }
        } else {
            hint += `${filePath} 不存在或不是一个文件, 请检查～\n\n`;
            isSuccess = false;
        }
    });
    return [hint, formattedOutput, isSuccess];
}

export function replacePlaceholders(obj: any, placeholder: string, replacement: string): any {
/**
 * 替换对象中的占位符
 */
    if (typeof obj === 'string') {
        return obj.replace(placeholder, replacement);
    } else if (typeof obj === 'object' && obj !== null) {
        for (let key in obj) {
            if (obj.hasOwnProperty(key)) {
                obj[key] = replacePlaceholders(obj[key], placeholder, replacement);
            }
        }
    }
    return obj;
}

export async function getSessionTokens() {
    const response = await fetch('http://10.143.161.42:8002/get_session_token', {
        method: 'POST',
    });

    if (response.status === 200) {
        const result = await response.json();
        return result;
    }

    return null;
}