export const qadcPrefixURL = 'http://qadc.baidu-int.com/api';
export const qadcSelectURL = `${qadcPrefixURL}/query`;
export const qadcCodeGenStageInfoTableName = 'delivery_agent_stage_info';
export const qadcSelectCodeGenStageInfoURL = `${qadcSelectURL}/${qadcCodeGenStageInfoTableName}`;
export const qadcUpdatePrefixURL = `${qadcPrefixURL}/update`;
export const qadcInsertPrefixURL = `${qadcPrefixURL}/insert`;

export const kirinPrefixURL = 'http://kirin.baidu-int.com/api';
export const kirinToolPrefixURL = `${kirinPrefixURL}/tool`;
export const kirinAsynResCallBackURL = `${kirinPrefixURL}/task`;

export const kirinGDPCodeGenerateToolID = '214250';
export const kirinGDPCodeGenerateURL = `${kirinToolPrefixURL}/${kirinGDPCodeGenerateToolID}`;

export const kirinGDPFilePathToolID = '214146';
export const kirinGDPFilePathURL = `${kirinToolPrefixURL}/${kirinGDPFilePathToolID}`;

export const kirinGDPTestToolID = '214099';
export const kirinGDPTestURL = `${kirinToolPrefixURL}/${kirinGDPTestToolID}`;

export const kirinGDPCodeRepairToolID = '214408';
export const kirinCodeRepairToolURL = `${kirinToolPrefixURL}/${kirinGDPCodeRepairToolID}`;

export const hiRobotPrefixUrl = 'http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token';
export const userServiceHiGroupNumber = 9801008;

export const feedStrategyHiGroupNumber = 9806174;
export const feedStrategyHiRobotToken = 'dd2c0a6ee1094d0cf6f288b0bbc48448c';
export const feedStrategyHiRobotUrl = `${hiRobotPrefixUrl}=${feedStrategyHiRobotToken}`;

export const ErnieBot4URL = 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions_pro';
