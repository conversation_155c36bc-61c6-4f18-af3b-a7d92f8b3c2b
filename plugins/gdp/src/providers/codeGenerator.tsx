/* eslint-disable no-useless-escape */
import {
    FunctionParameterDefinition,
    SkillProvider,
    ProviderInit,
    TaskProgressChunk,
    UserDetail,
    ElementChunkStream,
} from '@comate/plugin-host';

import {DrawElement} from '@comate/plugin-shared-internals';

import {GitUtil} from '../utils/git.js';
import {FeedStrategyStatus} from '../consts/status.js';
import {BosUtil} from '../utils/bos.js';
import {wboxCallBack, wboxRegister} from '../utils/wbox.js';
import {
    getReaderStr,
    renderTemplate,
    sleep,
    countCodeBlocks,
    extractCodeBlocks,
    tarFilesInSpecialFolder,
    flushRes2Qadc,
    checkFileExists,
    getSessionTokens,
} from '../utils/util.js';
import {fetchUrl} from '../utils/fetch.js';
import {qadcHeadersConfig} from '../consts/configs.js';
import {ModelType} from '../consts/modelType.js';
import {taskExecuteErrorInfo} from '../consts/errorInfo.js';
import {fetchStreamUrl} from '../utils/fetch.js';
import {commonHeadersConfig, kirinToken, kirinUserName} from '../consts/configs.js';
import {
    userServiceHiGroupNumber,
    kirinGDPCodeGenerateURL,
    kirinGDPFilePathURL,
    qadcSelectCodeGenStageInfoURL,
    kirinGDPTestURL,
} from '../consts/urls.js';
import {
    startKirinAsyncStrategy,
    getKirinSyncAmisRes,
    kirinReturnCheck,
    getKirinSyncGDPPathList,
} from '../utils/kirin.js';
import fs from 'node:fs/promises';
import path from 'path';
import {spawnSync} from 'node:child_process';
interface Args {
    query: string;
}

export class CodeGeneratorProvider extends SkillProvider<Args> {
    static skillName = 'gdpGenerator';

    static displayName = 'MVC代码生成';

    static description = 'gdp代码生成功能';

    static daoWarningMsg =
        '\n\n**注意**: 生成能力dao层依赖base.go封装的类，首次使用，需要手动将base.go文件放到代码库的model/dao路径下：https://console.cloud.baidu-int.com/devops/icode/repos/baidu/gdp/demo/blob/master/model/dao/base.go';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    private readonly git: GitUtil;

    private readonly cwd: string;

    private taskID: number;

    constructor(init: ProviderInit) {
        super(init);
        this.git = new GitUtil(init.cwd);
        this.cwd = init.cwd;
        this.taskID = 0;
    }

    async queryResult(data: any) {
        let running = true;
        const timeout = 720 * 1000; // 180s

        const timer = setTimeout(() => {
            running = false;
        }, timeout);
        try {
            // eslint-disable-next-line
            while (running) {
                await sleep(1 * 1000);
                const result = await fetchUrl(qadcSelectCodeGenStageInfoURL, qadcHeadersConfig, data);
                if (!result) {
                    continue;
                }

                // eslint-disable-next-line
                if (result.code !== 0 || result.data.total <= 0) {
                    continue;
                }

                // eslint-disable-next-line
                const source = result.data.records[0]._source;
                // eslint-disable-next-line
                if (source.length === 0) {
                    continue;
                }

                return {
                    status: FeedStrategyStatus.SUCCESS,
                    is_end: source.is_end,
                    exeStatus: source.status,
                    message: source.error_msg,
                    result: source.result,
                };
            }
        }
        // eslint-disable-next-line
        catch (e) {
            return {
                status: FeedStrategyStatus.ERROR_STRATEGY_FAIL,
                is_end: 1,
                exeStatus: 'fail',
                message: '策略执行异常，插件中止进程',
            };
        }
        finally {
            clearTimeout(timer);
        }

        return {
            status: FeedStrategyStatus.ERROR_TIMEOUT,
            is_end: 1,
            exeStatus: 'fail',
            message: '阶段任务超时，插件中止进程',
        };
    }

    async *compile(stream: ElementChunkStream): AsyncIterableIterator<TaskProgressChunk> {
        yield stream.flush(<markdown>## 1. 编译</markdown>);
        yield stream.flush(<markdown>- 编译命令: make build -f Makefile && make package -f Makefile</markdown>);
        let shellRes = spawnSync(`cd ${this.cwd} && make build -f Makefile && make package -f Makefile`, {
            shell: true,
            encoding: 'utf-8',
        });
        yield stream.flush(<markdown>- 编译日志:</markdown>);
        yield stream.flush(<markdown>{'```shell\n' + shellRes.stdout + '\n```'}</markdown>);
        let user = await this.currentUser.requestDetail();
        await flushRes2Qadc('delivery_agent_stage_info', [{
            task_id: this.taskID,
            stage: 'compile',
            status: shellRes.status === 0 ? 'succ' : 'fail',
            result: shellRes.stderr,
            error_msg: shellRes.stderr,
            is_end: 0,
            rd: user ? user.name : 'unknown',
            conf: this.currentContext.query,
            repository: await this.git.getModuleName(),
            scene: 'tieba_code_fix',
        }]);
        if (shellRes.status) {
            yield stream.flush(<markdown>- 编译报错</markdown>);
            yield stream.flush(<markdown>{'```shell\n' + shellRes.stderr + '\n```'}</markdown>);
            yield stream.flush(<markdown>编译结果: fail</markdown>);
            yield stream.flush(<markdown>## 2. 编译修复</markdown>);

            const errorList = shellRes.stderr.split('\n');
            let chunkStr = '';
            let errorCount = 1;
            for (let i = 0; i < errorList.length; ++i) {
                if (
                    errorList[i].startsWith('# ')
                    && i + 1 < errorList.length
                    && errorList[i + 1].includes(': ')
                ) {
                    let j = i + 1;
                    while (
                        j < errorList.length
                        && errorList[j].includes(': ')
                        && !errorList[j].includes(': ***')
                    ) {
                        const errorMsg = errorList[i] + '\n' + errorList[j];
                        const chunks = this.llm.askForDebugAgentProcess({
                            query: 'AutoDebug is working......',
                            code: errorMsg,
                        });
                        yield stream.flush(
                            <markdown>
                                {`(${errorCount}) [${errorList[j]}](${
                                    path.join(this.cwd, errorList[j].split(':')[0])
                                }:${errorList[j].split(':')[1]})\n`}
                            </markdown>
                        );
                        yield stream.flush(<markdown>{'\n'}</markdown>);
                        for await (const chunk of chunks) {
                            chunkStr += chunk.content;
                            if (chunk.content) {
                                yield stream.flushReplaceLast(<markdown>{chunk.content}</markdown>);
                            }
                        }
                        ++j;
                        ++errorCount;
                    }
                }
            }

            if (errorCount === 1) {
                const chunks = this.llm.askForDebugAgentProcess({
                    query: 'AutoDebug is working......',
                    code: shellRes.stderr,
                });
                yield stream.flush(<markdown>{'\n'}</markdown>);
                for await (const chunk of chunks) {
                    chunkStr += chunk.content;
                    if (chunk.content) {
                        yield stream.flushReplaceLast(<markdown>{chunk.content}</markdown>);
                    }
                }
            }
            await flushRes2Qadc('delivery_agent_stage_info', [{
                task_id: this.taskID,
                stage: 'compileFix',
                status: 'succ',
                result: chunkStr,
                error_msg: '',
                is_end: 1,
                rd: user ? user.name : 'unknown',
                conf: this.currentContext.query,
                repository: await this.git.getModuleName(),
                scene: 'tieba_code_fix',
            }]);
            return false;
        }
        yield stream.flush(<markdown>编译结果: success</markdown>);
        return true;
    }

    async *testProcess(stream: ElementChunkStream): AsyncIterableIterator<TaskProgressChunk> {
        try {
            const user = await this.currentUser.requestDetail();
            const cwdFs = await this.requestWorkspaceFileSystem();
            if (!cwdFs) {
                yield stream.flush(<p>未授权文件读取权限</p>);
                return;
            }
            yield stream.flush(<markdown>{'## 2. 编译产出打包'}</markdown>);
            let downloadUrl = '';
            let outputDownloadUrl = '';
            let isTarSucc = false;
            let errorMsg = '';
            const timestampeStr = Date.now().toString();
            const outputAbsPath = path.resolve(`${this.cwd}/output`);
            try {
                [isTarSucc, errorMsg] = await tarFilesInSpecialFolder(this.cwd, `${this.cwd}/${timestampeStr}.tar.gz`);
                if (!isTarSucc) {
                    yield stream.flush(<p>上传文件代码失败，请联系QA排查</p>);
                    return;
                }

                const bosStsSession = await getSessionTokens();
                if (!bosStsSession) {
                    throw new Error('can not get bos sts session');
                }
                const bosStsClient : BosUtil = new BosUtil(
                    bosStsSession.data.access_key_id,
                    bosStsSession.data.secret_access_key,
                    'https://qep-public.bj.bcebos.com',
                    bosStsSession.data.session_token
                );

                await bosStsClient.putObjFromFile(
                    'qep-public',
                    `ai-wise/${timestampeStr}.tar.gz`,
                    `${this.cwd}/${timestampeStr}.tar.gz`
                );
                downloadUrl = await bosStsClient.generatePresignedUrl('qep-public', `ai-wise/${timestampeStr}.tar.gz`);
                await fs.unlink(`${this.cwd}/${timestampeStr}.tar.gz`);
                [isTarSucc, errorMsg] = await tarFilesInSpecialFolder(
                    outputAbsPath,
                    `${outputAbsPath}/${timestampeStr}_output.tar.gz`,
                    true
                );
                if (!isTarSucc) {
                    yield stream.flush(<p>上传编译产出失败，请联系QA排查</p>);
                    return;
                }

                await bosStsClient.putObjFromFile(
                    'qep-public',
                    `ai-wise/${timestampeStr}_output.tar.gz`,
                    `${outputAbsPath}/${timestampeStr}_output.tar.gz`
                );
                outputDownloadUrl = await bosStsClient.generatePresignedUrl(
                    'qep-public',
                    `ai-wise/${timestampeStr}_output.tar.gz`
                );
                await fs.unlink(`${outputAbsPath}/${timestampeStr}_output.tar.gz`);
                yield stream.flush(<markdown>{`编译产出打包上传成功，[下载地址](${outputDownloadUrl})`}</markdown>);
            }
            catch (e) {
                yield stream.flush(<p>上传编译产出失败，请联系QA排查</p>);
                errorMsg = String(e);
                isTarSucc = false;
                return;
            }
            finally {
                if (await checkFileExists(`${this.cwd}/${timestampeStr}.tar.gz`)) {
                    await fs.unlink(`${this.cwd}/${timestampeStr}.tar.gz`);
                }
                if (await checkFileExists(`${outputAbsPath}/${timestampeStr}_output.tar.gz`)) {
                    await fs.unlink(`${outputAbsPath}/${timestampeStr}_output.tar.gz`);
                }
                await flushRes2Qadc('delivery_agent_stage_info', [{
                    task_id: this.taskID,
                    stage: 'compileOut',
                    status: isTarSucc ? 'succ' : 'fail',
                    result: isTarSucc ? '' : '编译产出打包上传失败',
                    error_msg: errorMsg,
                    is_end: isTarSucc ? 0 : 1,
                    rd: user ? user.name : 'unknown',
                    conf: this.currentContext.query,
                    repository: await this.git.getModuleName(),
                    scene: 'tieba_code_fix',
                }]);
            }

            const newCode = await this.git.getMergeBaseDiff();
            const moduleName = await this.git.getModuleName();
            yield stream.flush(<markdown>{'## 3. 部署代码'}</markdown>);
            const kirinTaskId = await startKirinAsyncStrategy(kirinGDPTestURL, {
                parameter: JSON.stringify({
                    totalCode: downloadUrl,
                    newCode: newCode,
                    compileOutput: outputDownloadUrl,
                    taskId: this.taskID,
                    conf: this.currentContext.query.substring(4),
                    rd: user ? user.name : 'unknown',
                    moduleName: moduleName,
                }),
                name: kirinUserName,
                token: kirinToken,
            });

            const {is_end, message, result} = await this.queryResult({
                // eslint-disable-next-line
                sql: `select * from "delivery_agent_stage_info" where
                        task_id='${this.taskID}' and scene='tieba_code_fix'
                            and stage='deploy' order by @timestamp desc`,
            });

            if (is_end === '1' || is_end === 1) {
                if (message.length !== 0) {
                    yield stream.flush(<markdown>{message}</markdown>);
                }

                if (result.length !== 0) {
                    yield stream.flush(<markdown>{'部署代码结束: \n\n服务器地址: ' + result}</markdown>);
                }
                return;
            }
            else {
                yield stream.flush(<markdown>{'部署代码结束: \n\n服务器地址: ' + result}</markdown>);
                yield stream.flush(<markdown>{'## 4. 正在测试'}</markdown>);
                const {message} = await this.queryResult({
                    // eslint-disable-next-line
                    sql: `select * from "delivery_agent_stage_info" where
                            task_id='${this.taskID}' and scene='tieba_code_fix'
                                and stage='test' order by @timestamp desc`,
                });

                if (message.length !== 0) {
                    yield stream.flush(<markdown>{result}</markdown>);
                }
                return;
            }
        }
        catch (error) {}
    }

    async *generateCodeProcess(stream: ElementChunkStream): AsyncIterableIterator<TaskProgressChunk> {
        let user = await this.currentUser.requestDetail();
        const moduleName = await this.git.getModuleName(this.cwd);

        const {status, kirinTaskID, kirinResponse} = await getKirinSyncAmisRes(kirinGDPCodeGenerateURL, {
            parameter: JSON.stringify({
                requirement: this.currentContext.query,
                conf: this.currentContext.selectedCode,
                conf_file: this.currentContext.activeFilePath,
                // eslint-disable-next-line
                rd: user ? user.name : 'unknown',
                moduleName: moduleName,
            }),
            name: kirinUserName,
            token: kirinToken,
        });

        for await (const data of kirinReturnCheck(status, kirinResponse, kirinTaskID)) {
            if (data.type === 'message' && data.message) {
                yield stream.flush(<markdown>{data.message}</markdown>);
                return;
            }
        }

        let result: DrawElement[] = [];
        for (const [index, kirinRes] of kirinResponse.entries()) {
            let chunkStr = '';
            let modelReader: ReadableStreamDefaultReader<Uint8Array> | null | undefined = null;
            const models = kirinRes.models;
            if (!models || !models.url) {
                // models为空时，直接打印信息
                if (kirinRes.add_before && kirinRes.add_before.length !== 0) {
                    yield stream.flush(<markdown>{kirinRes.add_before}</markdown>);
                }
                if (kirinRes.add_after && kirinRes.add_after.length !== 0) {
                    yield stream.flush(<markdown>{kirinRes.add_after}</markdown>);
                }
                return;
            }
            else if (models.url.includes('deepseek') || models.url.includes('10.55.119.216')) {
                modelReader = await fetchStreamUrl(models.url, models.headers, models.data, models.method);
            }
            else {
                modelReader = await fetchStreamUrl(
                    `${models.url}?access_token=${models.params.access_token}`,
                    commonHeadersConfig,
                    models.data,
                    models.method
                );
            }

            if (!modelReader) {
                const failMessage = renderTemplate(taskExecuteErrorInfo, {
                    groupID: userServiceHiGroupNumber,
                    taskID: kirinTaskID,
                });
                yield stream.flushReplaceLast(<p>{failMessage}</p>);
                return;
            }

            if (kirinRes.add_before && kirinRes.add_before.length !== 0) {
                result.push(<markdown>{kirinRes.add_before}</markdown>);
                yield stream.flush(<markdown>{kirinRes.add_before}</markdown>);
            }
            try {
                let modelType: ModelType = ModelType.WENXIN;

                if (models.url.includes('deepseek') || models.url.includes('10.55.119.216')) {
                    modelType = ModelType.DEEPSEEK;
                }
                else {
                    modelType = ModelType.WENXIN;
                }

                let isFirstFlush = true;
                for await (const data of getReaderStr(modelReader, modelType)) {
                    // 过滤掉 null 值
                    if (data.message !== null) {
                        chunkStr += data.message;
                        // 临时处理toml渲染问题
                        chunkStr = chunkStr.replace('```toml', '```shell');
                    }
                    if (isFirstFlush) {
                        yield stream.flush(<markdown>{chunkStr}</markdown>);
                        isFirstFlush = false;
                    }
                    else {
                        yield stream.flushReplaceLast(<markdown>{chunkStr}</markdown>);
                    }
                }

                const {PathResponse} = await getKirinSyncGDPPathList(kirinGDPFilePathURL, {
                    parameter: JSON.stringify({
                        model_res: chunkStr,
                        repository: this.cwd,
                    }),
                    name: kirinUserName,
                    token: kirinToken,
                });
                if (PathResponse.length === countCodeBlocks(chunkStr)) {
                    result = result.concat(await extractCodeBlocks(chunkStr, this.cwd, PathResponse));
                }
                else {
                    result.push(<markdown>{chunkStr}</markdown>);
                }
            }
            catch (error) {
                const failMessage = renderTemplate(taskExecuteErrorInfo, {
                    groupID: userServiceHiGroupNumber,
                    taskID: kirinTaskID,
                });
                yield stream.flushReplaceLast(<p>{failMessage}</p>);
            }
            finally {
                modelReader.releaseLock();
                await flushRes2Qadc('code_gen_record', [
                    {
                        kirin_id: kirinTaskID,
                        order_id: `${kirinTaskID}_${index}`,
                        model: JSON.stringify(kirinRes.models),
                        add_before: kirinRes.add_before,
                        add_after: kirinRes.add_after,
                        result: chunkStr,
                        rd_name: user ? user.name : 'unknown',
                        module: moduleName,
                    },
                ]);
                if (kirinRes.add_after && kirinRes.add_after.length !== 0) {
                    result.push(<markdown>{kirinRes.add_after}</markdown>);
                    yield stream.flush(<markdown>{kirinRes.add_after}</markdown>);
                }
                yield stream.flushReplaceAll(result);
            }
        }
    }

    // eslint-disable-next-line
    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new ElementChunkStream();
        let user: false | UserDetail = false;
        let taskID: number = 0;
        let isSuccess = false;
        try {
            user = await this.currentUser.requestDetail();
            // wbox平台注册本次扫描任务
            taskID = await wboxRegister(user, 'gdpGenerator', this.currentContext.query, this.git);
            this.taskID = taskID;
            if (this.currentContext.query.length === 0) {
                yield stream.flush(<p>需求描述不可为空，请添加需求描述</p>);
                isSuccess = true;
                return;
            }

            if (this.currentContext.query.startsWith('【测试')) {
                try {
                    const cwdFs = await this.requestFullDiskFileSystem();
                    if (!cwdFs) {
                        yield stream.flush(<p>未授予workspace权限，请授予workspace权限后重试</p>);
                        return;
                    }
                    // 先进行编译
                    const result = yield* this.compile(stream);
                    if (result === false) {
                        isSuccess = true;
                        return;
                    }
                    // 进入测试环节
                    yield* this.testProcess(stream);
                    isSuccess = true;
                }
                catch (e) {
                    const failMessage = renderTemplate(taskExecuteErrorInfo, {
                        groupID: userServiceHiGroupNumber,
                        taskID: taskID,
                    });
                    yield stream.flushReplaceLast(<p>{failMessage}</p>);
                }
                finally {
                    yield stream.flush(
                        <markdown>
                            {'## 本次任务id：' + taskID
                                + '，若任务出现任何异常，可随时携带任务id，通过如流群10319518向我们反馈。'}
                        </markdown>
                    );
                }
            }
            else {
                yield* this.generateCodeProcess(stream);
                isSuccess = true;
            }
        }
        catch (e) {
            const failMessage = renderTemplate(taskExecuteErrorInfo, {
                groupID: userServiceHiGroupNumber,
                taskID: taskID,
            });
            yield stream.flushReplaceLast(<p>{failMessage}</p>);
        }
        finally {
            await wboxCallBack({
                taskId: taskID ? taskID : 0,
                status: isSuccess ? 2 : 1,
                caller: user ? user.name : 'unknown',
            });
        }
    }
}
