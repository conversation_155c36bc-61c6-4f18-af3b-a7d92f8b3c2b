import {
    TaskProgressChunkStream,
    TaskProgressChunk,
    FallbackProvider,
    TextModel,
} from '@comate/plugin-host';

export class fallbackProvider extends FallbackProvider {
    static description = '智能问答';

    async *handleQuery(): AsyncIterableIterator<TaskProgressChunk> {
        const {query} = this.currentContext;
        const stream = new TaskProgressChunkStream();

        yield stream.flush('正在思考中，请耐心等候...');

        const knowledge = await this.retriever.knowledgeFromQuery(
            query,
            {
                knowledgeSets: [
                    {uuid: 'bcdc4aa4-27f6-4dd1-bade-339fad9af5e4', type: 'NORMAL'},
                ],
            }
        );

        const prompt = this.llm.createPrompt(
            knowledge.length ? '请根据以下背景知识：\n\n{{knowledge}}\n\n回答问题：{{query}}' : '{{query}}',
            {knowledge, query}
        );

        const chunks = this.llm.askForTextStreaming(prompt, {model: TextModel.Default});

        yield stream.flushReplaceLast('');

        for await (const chunk of chunks) {
            yield stream.flush(chunk);
        }
    }
}
