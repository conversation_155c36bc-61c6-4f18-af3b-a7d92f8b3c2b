import {
    SkillProvider,
    FunctionParameterDefinition,
    TaskProgressChunk,
    TextModel,
    StringChunkStream,
} from '@comate/plugin-host';
import dedent from 'dedent';

const promptWithKnowledgeAndCode = dedent`
请根据以下背景知识：

{{knowledge}}

以及相关的代码片段：

{{selectedCode}}

回答问题：

{{query}}
`;

const promptWithKnowledgeOnly = dedent`
请根据以下背景知识：

{{knowledge}}

回答问题：

{{query}}
`;

const promptWithCodeOnly = dedent`
请根据以下代码片段：

{{selectedCode}}

回答问题：

{{query}}
`;

const promptWithoutKnowledgeAndCode = dedent`
请回答以下问题：

{{query}}
`;

export class ChatSkillProvider extends SkillProvider {
    static skillName = 'answerQuestion';

    static displayName = 'GDP(Go Develop Platform)智能问答';

    static description = '智能解答关于GDP(Go Develop Platform)的相关问题';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const {query, selectedCode} = this.currentContext;
        const stream = new StringChunkStream();

        yield stream.flush('正在思考中，请耐心等候...');

        const knowledge = await this.retriever.knowledgeFromQuery(
            query,
            {
                knowledgeSets: [
                    {uuid: 'bcdc4aa4-27f6-4dd1-bade-339fad9af5e4', type: 'NORMAL'},
                ],
            }
        );

        const hasKnowledge = knowledge.length > 0;
        const hasSelectedCode = selectedCode.trim() !== '';

        let template;

        if (hasKnowledge && hasSelectedCode) {
            template = promptWithKnowledgeAndCode;
        }
        else if (hasKnowledge && !hasSelectedCode) {
            template = promptWithKnowledgeOnly;
        }
        else if (!hasKnowledge && hasSelectedCode) {
            template = promptWithCodeOnly;
        }
        else {
            template = promptWithoutKnowledgeAndCode;
        }

        const prompt = this.llm.createPrompt(
            template,
            {
                knowledge: hasKnowledge ? knowledge : '',
                selectedCode: hasSelectedCode ? selectedCode : '',
                query,
            }
        );

        const chunks = this.llm.askForTextStreaming(prompt, {model: TextModel.Default});

        yield stream.flushReplaceLast('');

        for await (const chunk of chunks) {
            yield stream.flush(chunk);
        }
    }
}
