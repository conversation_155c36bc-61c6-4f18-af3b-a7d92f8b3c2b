{"private": true, "name": "@comate-plugin/wenku", "version": "0.1.0", "type": "module", "scripts": {"clean": "rm -rf dist", "dev": "cpd dev", "build": "tsc && cpd build"}, "dependencies": {}, "devDependencies": {"@comate/plugin-cli": "^0.9.2", "@comate/plugin-host": "^0.9.2", "typescript": "^5.3.2"}, "comate": {"name": "wenku", "version": "1.0.0", "icon": "./assets/icon.svg", "entry": "./dist/index.js", "displayName": "文库助手", "description": "基于内部知识库回答相关问题", "keyword": [], "capabilities": [{"type": "Skill", "name": "wenku-help", "displayName": "插件介绍", "description": "文库助手插件介绍"}, {"type": "Skill", "name": "wenku-func-search", "displayName": "公共函数检索", "description": "智能推荐文库内部公共函数"}, {"type": "Skill", "name": "wenku-chat", "displayName": "文库知识问答", "description": "基于内部知识库回答问题"}, {"type": "Fallback", "name": "wenku-fallback", "displayName": "能力兜底", "description": "能力兜底"}], "configSchema": {"sections": []}}}