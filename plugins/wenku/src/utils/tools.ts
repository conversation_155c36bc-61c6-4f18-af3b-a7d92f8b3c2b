export const parseQuery = (currentContext: any) => {
    const baseQuery = currentContext.query;
    const flag = 'debug';
    const isDebug = baseQuery.startsWith(flag);
    const query = isDebug
        ? baseQuery.substring(flag.length)
        : baseQuery;
    return [query, isDebug];
};

export const trimKnowledge = (list: string[], maxCount = 6, maxLength = 10000) => {
    const state = {
        length: 0,
    };
    const output: string[] = [];
    for (const item of list) {
        if (state.length + item.length > maxLength || output.length >= maxCount) {
            break;
        }
        output.push(item);
        state.length += item.length;
    }
    return output;
}