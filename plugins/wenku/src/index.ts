import { PluginSetupContext } from '@comate/plugin-host';
import { HelpSkillProvider } from './providers/help.js';
import { WenkuFallbackProvider } from './providers/fallback.js';
import { FuncSearchSkillProvider } from './providers/funcSearch.js';
import { ChatSkillProvider } from './providers/chat.js'

export function setup({ registry }: PluginSetupContext) {
    registry.registerSkillProvider('wenku-help', HelpSkillProvider);
    registry.registerSkillProvider('wenku-func-search', FuncSearchSkillProvider);
    registry.registerSkillProvider('wenku-chat', ChatSkillProvider);
    registry.registerFallbackProvider('wenku-fallback', WenkuFallbackProvider);
}
