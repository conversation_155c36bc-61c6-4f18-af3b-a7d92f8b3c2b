import {
    SkillProvider,
    FunctionParameterDefinition,
    TaskProgressChunk,
    TextModel,
    StringChunkStream,
    ElementChunkStream,
} from '@comate/plugin-host';
import chatPrompt from '../prompts/chat.prompt';
import {parseQuery, trimKnowledge} from '../utils/tools.js';

export class ChatSkillProvider extends SkillProvider {
    static skillName = 'answerQuestion';
    static displayName = '知识问答';
    static description = '基于内部知识库回答问题';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const [query, isDebug] = parseQuery(this.currentContext);

        if (!query) {
            const stream = new ElementChunkStream();
            yield stream.flush(
                <p>
                    <alert-tag level="medium">注意</alert-tag>
                    未检测到任何输入，请输入相关提问内容后重试
                </p>
            );
        }
        else {
            const stream = new StringChunkStream();
            yield stream.flush('正在思考中，请耐心等候...');

            try {
                const [knowledgeList, sourceList] = await this.getKnowledge(query);
                if (knowledgeList.length) {
                    const prompt = this.llm.createPrompt(
                        chatPrompt,
                        {
                            trimedKnowledge: trimKnowledge(knowledgeList, 20),
                            query,
                        }
                    );
                    yield stream.flushReplaceLast('');
                    yield stream.flush(`共检索到${knowledgeList.length}条相关知识，检索来源如下：\n\n`);
                    for (const item of sourceList) {
                        yield stream.flush(`${item}\n\n`)
                    }
                    yield stream.flush('总结如下\n\n');

                    if (isDebug) {
                        yield stream.flush('\n\n========prompt========\n\n');
                        yield stream.flush(JSON.stringify(prompt));
                        yield stream.flush('\n\n======================\n\n');
                    }

                    const chunks = this.llm.askForTextStreaming(prompt, { model: TextModel.Default });
                    for await (const chunk of chunks) {
                        yield stream.flush(chunk);
                    }
                }
                else {
                    yield stream.flushReplaceLast(<p>未检索到相关知识</p>);
                }
            }
            catch (error) {
                yield stream.flushReplaceLast(<p>出现错误，请稍后再试</p>);
            }
        }
    }

    private async getKnowledge(query: string) {
        const baseKnowledgeList = await this.retriever.knowledgeFromQuery(
            query,
            {
                knowledgeSets: [
                    { uuid: '152f546e-c5b2-44df-aa70-b567938f3ba7', type: 'NORMAL' },
                ],
                withSource: true,
            }
        );
        const knowledgeList = baseKnowledgeList.map(x => x.content) || [];
        const sourceList = Array.from(new Set(baseKnowledgeList.map(x => x.source))) || [];
        return [knowledgeList, sourceList];
    }
}
