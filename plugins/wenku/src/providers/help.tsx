import {
    FunctionParameterDefinition,
    SkillProvider,
    TaskProgressChunk,
    ElementChunkStream,
} from '@comate/plugin-host';

export class HelpSkillProvider extends SkillProvider {
    static skillName = 'wenku-help';
    static displayName: '插件介绍';
    static description = '插件介绍';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new ElementChunkStream();
        yield stream.flush(
            <p>
                文库研发小助手，为开发人员提供内部知识问答、函数推荐等服务，助力开发效率提升。
            </p>
        );
    }
}
