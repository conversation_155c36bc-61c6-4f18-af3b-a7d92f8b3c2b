import {
    SkillProvider,
    FunctionParameterDefinition,
    TaskProgressChunk,
    TextModel,
    StringChunkStream,
    ElementChunkStream,
} from '@comate/plugin-host';
import funcSearchPrompt from '../prompts/funcSearch.prompt';
import {parseQuery, trimKnowledge} from '../utils/tools.js';


export class FuncSearchSkillProvider extends SkillProvider {
    static skillName = 'funcSearch';
    static displayName = '函数推荐';
    static description = '智能推荐文库内部公共函数';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {

        const [query, isDebug] = parseQuery(this.currentContext);

        if (!query) {
            const stream = new ElementChunkStream();
            yield stream.flush(
                <p>
                    <alert-tag level="medium">注意</alert-tag>
                    未检测到任何输入，请输入功能描述进行公共函数检索
                </p>
            );
        }
        else {
            const stream = new StringChunkStream();
            yield stream.flush('正在思考中，请耐心等候...');

            try {
                const knowledge = await this.getKnowledge(query);
                if (knowledge.length) {
                    const prompt = this.llm.createPrompt(
                        funcSearchPrompt,
                        {
                            trimedKnowledge: trimKnowledge(knowledge, 20),
                            query,
                        }
                    );
                    yield stream.flushReplaceLast('');
                    yield stream.flush(`共检索到${knowledge.length}条相关知识, 总结如下：\n\n`);

                    if (isDebug) {
                        yield stream.flush('\n\n========prompt========\n\n');
                        yield stream.flush(JSON.stringify(prompt));
                        yield stream.flush('\n\n======================\n\n');
                    }

                    const chunks = this.llm.askForTextStreaming(prompt, { model: TextModel.Default });
                    for await (const chunk of chunks) {
                        yield stream.flush(chunk);
                    }
                }
                else {
                    yield stream.flushReplaceLast('未检索到相关公共函数');
                }
            }
            catch (error) {
                yield stream.flushReplaceLast('出现错误，请稍后再试');
            }
        }
    }

    private async getKnowledge(query: string) {
        const knowledge = await this.retriever.knowledgeFromQuery(
            query,
            {
                knowledgeSets: [
                    { uuid: '112ff4b5-a576-4f30-b58b-032879a8ce82', type: 'NORMAL' },
                ],
            }
        );
        return knowledge;
    }
}
