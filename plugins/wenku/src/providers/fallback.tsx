import {FallbackProvider, TaskProgressChunk, ElementChunkStream} from '@comate/plugin-host';

const DESCRIPTION = `
该插件目前支持以下能力：
- 插件介绍
- 函数推荐

您可以选择上述指令进行体验，欢迎使用文库助手。
`;

export class WenkuFallbackProvider extends FallbackProvider {
    static description = '能力解释';

    async *handleQuery(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new ElementChunkStream();
        yield stream.flush(<p>{DESCRIPTION}</p>);
    }
}
