{"private": true, "name": "@comate-plugin/fcnap", "version": "0.8.0", "type": "module", "scripts": {"clean": "rm -rf dist", "dev": "cpd dev", "build": "tsc && cpd build"}, "dependencies": {}, "devDependencies": {"@comate/plugin-cli": "^0.9.2", "@comate/plugin-host": "^0.9.2", "typescript": "^5.3.2"}, "comate": {"name": "fcnap", "version": "1.0.0", "icon": "./assets/icon.png", "entry": "./dist/index.js", "displayName": "前端一站式", "description": "FCNAP官方插件，用以生成配置环境、部署的路由转发、中间件等高级配置", "keyword": [], "capabilities": [{"type": "Skill", "name": "fcnap-help", "displayName": "插件介绍", "description": "插件介绍", "placeholder": "无需输入内容，回车获得插件介绍"}, {"type": "Skill", "name": "chat", "displayName": "智能问答", "description": "智能解答关于前端一站式平台《高级配置》的相关问题"}, {"type": "Fallback", "name": "fallback", "displayName": "智能问答", "description": "智能解答关于前端一站式平台《高级配置》的相关问题"}], "configSchema": {"sections": []}}}