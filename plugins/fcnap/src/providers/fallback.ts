import {
    TaskProgressChunk,
    TextModel,
    StringChunkStream,
    FallbackProvider,
} from '@comate/plugin-host';

export class CustomFallbackProvider extends FallbackProvider {
    static description = '智能解答关于前端一站式平台《高级配置》的相关问题';

    async *handleQuery(): AsyncIterableIterator<TaskProgressChunk> {
        const {query} = this.currentContext;
        const stream = new StringChunkStream();

        yield stream.flush('正在思考中，请耐心等候...');

        const knowledge = await this.retriever.knowledgeFromQuery(
            query,
            {
                knowledgeSets: [
                    {uuid: '1d71e19d-35cd-4888-b0d3-3e9a7d9440e0', type: 'NORMAL'},
                ],
            }
        );

        const prompt = this.llm.createPrompt(
            knowledge.length ? '请根据以下背景知识：\n\n{{knowledge}}\n\n回答问题：{{query}}' : '{{query}}',
            {knowledge, query}
        );

        const chunks = this.llm.askForTextStreaming(prompt, {model: TextModel.Default});
        yield stream.flushReplaceLast('');

        for await (const chunk of chunks) {
            yield stream.flush(chunk);
        }
    }
}
