import {HelpSkillProvider} from './providers/help.js';
import {PluginSetupContext} from '@comate/plugin-host';
import {ChatSkillProvider} from './providers/chat.js';
import {CustomFallbackProvider} from './providers/fallback.js';

export function setup({registry}: PluginSetupContext) {
    registry.registerSkillProvider('fcnap-help', HelpSkillProvider);
    registry.registerSkillProvider('chat', ChatSkillProvider);
    registry.registerFallbackProvider('fallback', CustomFallbackProvider, true);
}
