// A launch configuration that compiles the extension and then opens it inside a new window
// Use IntelliSense to learn about possible attributes.
// Hover to view descriptions of existing attributes.
// For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Run Comate (Internal)",
      "type": "extensionHost",
      "request": "launch",
      "runtimeExecutable": "${execPath}",
      "args": [
        "--extensionDevelopmentPath=${workspaceFolder}/packages/vscode/",
        "--enable-proposed-api=baidu.comate",
        "--disable-extensions"
      ],
      "autoAttachChildProcesses": true,
      "outFiles": [
        "${workspaceFolder}/packages/vscode/dist/**/*.js",
        "${workspaceFolder}/packages/engine-connector/dist/**/*.js",
        "${workspaceFolder}/packages/kernel/dist/**/*.js"
      ],
      "env": {
        "NODE_ENV": "development",
        "NODE_TLS_REJECT_UNAUTHORIZED": "0"
      },
      "preLaunchTask": "watch(package:vscode)(internal)"
    },
    {
      "name": "Run Comate (Internal:test)",
      "type": "extensionHost",
      "request": "launch",
      "runtimeExecutable": "${execPath}",
      "autoAttachChildProcesses": true,
      "args": [
        "--extensionDevelopmentPath=${workspaceFolder}/packages/vscode/",
        "--enable-proposed-api=baidu.comate",
        "--disable-extensions"
      ],
      "outFiles": [
        "${workspaceFolder}/packages/vscode/dist/**/*.js"
      ],
      "env": {
        "NODE_ENV": "development"
      },
      "preLaunchTask": "watch(package:vscode)(internal:test)"
    },
    {
      "name": "Run Comate (SaaS)",
      "type": "extensionHost",
      "request": "launch",
      "runtimeExecutable": "${execPath}",
      "args": [
        "--extensionDevelopmentPath=${workspaceFolder}/packages/vscode/",
        "--enable-proposed-api=baidu.comate",
        "--disable-extensions"
      ],
      "autoAttachChildProcesses": true,
      "outFiles": [
        "${workspaceFolder}/packages/vscode/dist/**/*.js",
        "${workspaceFolder}/packages/engine-connector/dist/**/*.js",
        "${workspaceFolder}/packages/kernel/dist/**/*.js"
      ],
      "env": {
        "NODE_ENV": "development"
      },
      "preLaunchTask": "watch(package:vscode)(saas)"
    },
    {
      "name": "Run Comate (SaaS:test)",
      "type": "extensionHost",
      "request": "launch",
      "runtimeExecutable": "${execPath}",
      "args": [
        "--extensionDevelopmentPath=${workspaceFolder}/packages/vscode/",
        "--enable-proposed-api=baidu.comate",
        "--disable-extensions"
      ],
      "outFiles": [
        "${workspaceFolder}/packages/vscode/dist/**/*.js"
      ],
      "env": {
        "NODE_ENV": "development"
      },
      "preLaunchTask": "watch(package:vscode)(saas:test)"
    },
    {
      "name": "Run Comate (SaaS Gitee)",
      "type": "extensionHost",
      "request": "launch",
      "runtimeExecutable": "${execPath}",
      "args": [
        "--extensionDevelopmentPath=${workspaceFolder}/packages/vscode/",
        "--enable-proposed-api=baidu.comate",
        "--disable-extensions"
      ],
      "outFiles": [
        "${workspaceFolder}/packages/vscode/dist/**/*.js"
      ],
      "env": {
        "NODE_ENV": "development"
      },
      "preLaunchTask": "watch(package:vscode)(saas-gitee)"
    },
    {
      "name": "Run Comate (SaaS Gitee:test)",
      "type": "extensionHost",
      "request": "launch",
      "runtimeExecutable": "${execPath}",
      "args": [
        "--extensionDevelopmentPath=${workspaceFolder}/packages/vscode/",
        "--enable-proposed-api=baidu.comate",
        "--disable-extensions"
      ],
      "outFiles": [
        "${workspaceFolder}/packages/vscode/dist/**/*.js"
      ],
      "env": {
        "NODE_ENV": "development"
      },
      "preLaunchTask": "watch(package:vscode)(saas-gitee:test)"
    },
    {
      "name": "Run Comate (poc)",
      "type": "extensionHost",
      "request": "launch",
      "runtimeExecutable": "${execPath}",
      "args": [
        "--extensionDevelopmentPath=${workspaceFolder}/packages/vscode/",
        "--enable-proposed-api=baidu.comate",
        "--disable-extensions"
      ],
      "outFiles": [
        "${workspaceFolder}/packages/vscode/dist/**/*.js"
      ],
      "env": {
        "NODE_ENV": "development"
      },
      "preLaunchTask": "watch(package:vscode)(poc)"
    },
    {
      "name": "Run Comate JetBrains Webview (Internal)",
      "type": "extensionHost",
      "request": "launch",
      "runtimeExecutable": "${execPath}",
      "args": [
        "--extensionDevelopmentPath=${workspaceFolder}/packages/vscode/",
        "--enable-proposed-api=baidu.comate",
        "--disable-extensions"
      ],
      "outFiles": [
        "${workspaceFolder}/packages/vscode/dist/**/*.js"
      ],
      "env": {
        "NODE_ENV": "development"
      },
      "preLaunchTask": "watch(package:vscode)(jetbrains-internal)"
    },
    {
      "name": "Run Comate JetBrains Webview (SaaS)",
      "type": "extensionHost",
      "request": "launch",
      "runtimeExecutable": "${execPath}",
      "args": [
        "--extensionDevelopmentPath=${workspaceFolder}/packages/vscode/",
        "--enable-proposed-api=baidu.comate",
        "--disable-extensions"
      ],
      "outFiles": [
        "${workspaceFolder}/packages/vscode/dist/**/*.js"
      ],
      "env": {
        "NODE_ENV": "development"
      },
      "preLaunchTask": "watch(package:vscode)(jetbrains-saas)"
    },
    {
      "name": "Run Comate JetBrains Webview (poc)",
      "type": "extensionHost",
      "request": "launch",
      "runtimeExecutable": "${execPath}",
      "args": [
        "--extensionDevelopmentPath=${workspaceFolder}/packages/vscode/",
        "--enable-proposed-api=baidu.comate",
        "--disable-extensions"
      ],
      "outFiles": [
        "${workspaceFolder}/packages/vscode/dist/**/*.js"
      ],
      "env": {
        "NODE_ENV": "development"
      },
      "preLaunchTask": "watch(package:vscode)(jetbrains-poc)"
    },
    {
      "name": "Run Comate Xcode Webview (Internal)",
      "type": "extensionHost",
      "request": "launch",
      "runtimeExecutable": "${execPath}",
      "args": [
        "--extensionDevelopmentPath=${workspaceFolder}/packages/vscode/",
        "--enable-proposed-api=baidu.comate",
        "--disable-extensions"
      ],
      "outFiles": [
        "${workspaceFolder}/packages/vscode/dist/**/*.js"
      ],
      "env": {
        "NODE_ENV": "development"
      },
      "preLaunchTask": "watch(package:vscode)(xcode-internal)"
    },
    {
      "name": "Run Comate Xcode Webview (SaaS)",
      "type": "extensionHost",
      "request": "launch",
      "runtimeExecutable": "${execPath}",
      "args": [
        "--extensionDevelopmentPath=${workspaceFolder}/packages/vscode/",
        "--enable-proposed-api=baidu.comate",
        "--disable-extensions"
      ],
      "outFiles": [
        "${workspaceFolder}/packages/vscode/dist/**/*.js"
      ],
      "env": {
        "NODE_ENV": "development"
      },
      "preLaunchTask": "watch(package:vscode)(xcode-saas)"
    },
    {
      "name": "Run Comate VS Webview (Poc)",
      "type": "extensionHost",
      "request": "launch",
      "runtimeExecutable": "${execPath}",
      "args": [
        "--extensionDevelopmentPath=${workspaceFolder}/packages/vscode/",
        "--enable-proposed-api=baidu.comate",
        "--disable-extensions"
      ],
      "outFiles": [
        "${workspaceFolder}/packages/vscode/dist/**/*.js"
      ],
      "env": {
        "NODE_ENV": "development"
      },
      "preLaunchTask": "watch(package:vscode)(vs-poc)"
    },
    {
      "name": "Run Comate VS Webview (SaaS)",
      "type": "extensionHost",
      "request": "launch",
      "runtimeExecutable": "${execPath}",
      "args": [
        "--extensionDevelopmentPath=${workspaceFolder}/packages/vscode/",
        "--enable-proposed-api=baidu.comate",
        "--disable-extensions"
      ],
      "outFiles": [
        "${workspaceFolder}/packages/vscode/dist/**/*.js"
      ],
      "env": {
        "NODE_ENV": "development"
      },
      "preLaunchTask": "watch(package:vscode)(vs-saas)"
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Run Engine Playground",
      "cwd": "${workspaceFolder}/packages/playground",
      "autoAttachChildProcesses": true,
      "runtimeExecutable": "npm",
      "runtimeArgs": [
        "run",
        "start"
      ]
    },
    {
      "name": "Attach Comate Plus Engine",
      "port": 6019,
      "request": "attach",
      "skipFiles": [
        "<node_internals>/**"
      ],
      "type": "node"
    }
  ]
}
