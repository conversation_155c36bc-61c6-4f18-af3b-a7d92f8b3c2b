{"version": "2.0.0", "tasks": [{"label": "watch(package:vscode)(internal)", "type": "shell", "command": "npm run watch", "isBackground": true, "problemMatcher": ["$ts-webpack-watch", "$tslint-webpack-watch"], "options": {"cwd": "${workspaceFolder}", "env": {"NODE_ENV": "development", "PLATFORM": "internal", "ENVIRONMENT": "development"}}}, {"label": "watch(package:vscode)(saas)", "type": "shell", "command": "npm run watch", "isBackground": true, "problemMatcher": ["$ts-webpack-watch", "$tslint-webpack-watch"], "options": {"cwd": "${workspaceFolder}", "env": {"NODE_ENV": "development", "PLATFORM": "saas", "ENVIRONMENT": "development"}}}, {"label": "watch(package:vscode)(saas-gitee)", "type": "shell", "command": "npm run watch", "isBackground": true, "problemMatcher": ["$ts-webpack-watch", "$tslint-webpack-watch"], "options": {"cwd": "${workspaceFolder}", "env": {"NODE_ENV": "development", "PLATFORM": "saas", "ENTERPRISE_VERSION": "gitee"}}}, {"label": "watch(package:vscode)(poc)", "type": "shell", "command": "npm run watch", "isBackground": true, "problemMatcher": ["$ts-webpack-watch", "$tslint-webpack-watch"], "options": {"cwd": "${workspaceFolder}", "env": {"NODE_ENV": "development", "PLATFORM": "poc"}}}, {"label": "watch(package:vscode)(internal:test)", "type": "shell", "command": "npm run watch", "isBackground": true, "problemMatcher": ["$ts-webpack-watch", "$tslint-webpack-watch"], "options": {"cwd": "${workspaceFolder}", "env": {"NODE_ENV": "development", "PLATFORM": "internal", "ENVIRONMENT": "test"}}}, {"label": "watch(package:vscode)(saas:test)", "type": "shell", "command": "npm run watch", "isBackground": true, "problemMatcher": ["$ts-webpack-watch", "$tslint-webpack-watch"], "options": {"cwd": "${workspaceFolder}", "env": {"NODE_ENV": "development", "PLATFORM": "saas", "ENVIRONMENT": "test"}}}, {"label": "watch(package:vscode)(saas-gitee:test)", "type": "shell", "command": "npm run watch", "isBackground": true, "problemMatcher": ["$ts-webpack-watch", "$tslint-webpack-watch"], "options": {"cwd": "${workspaceFolder}", "env": {"NODE_ENV": "development", "PLATFORM": "saas", "ENTERPRISE_VERSION": "gitee", "ENVIRONMENT": "test"}}}, {"label": "watch(package:vscode)(jetbrains-internal)", "type": "shell", "command": "npm run watch", "isBackground": true, "problemMatcher": ["$ts-webpack-watch", "$tslint-webpack-watch"], "options": {"cwd": "${workspaceFolder}", "env": {"NODE_ENV": "development", "PLATFORM": "internal", "WEBVIEW_CONSUMER": "jetbrains"}}}, {"label": "watch(package:vscode)(jetbrains-saas)", "type": "shell", "command": "npm run watch", "isBackground": true, "problemMatcher": ["$ts-webpack-watch", "$tslint-webpack-watch"], "options": {"cwd": "${workspaceFolder}", "env": {"NODE_ENV": "development", "PLATFORM": "saas", "WEBVIEW_CONSUMER": "jetbrains"}}}, {"label": "watch(package:vscode)(jetbrains-poc)", "type": "shell", "command": "npm run watch", "isBackground": true, "problemMatcher": ["$ts-webpack-watch", "$tslint-webpack-watch"], "options": {"cwd": "${workspaceFolder}", "env": {"NODE_ENV": "development", "PLATFORM": "poc", "WEBVIEW_CONSUMER": "jetbrains"}}}, {"label": "watch(package:vscode)(xcode-internal)", "type": "shell", "command": "npm run watch", "isBackground": true, "problemMatcher": ["$ts-webpack-watch", "$tslint-webpack-watch"], "options": {"cwd": "${workspaceFolder}", "env": {"NODE_ENV": "development", "PLATFORM": "internal", "WEBVIEW_CONSUMER": "xcode"}}}, {"label": "watch(package:vscode)(xcode-saas)", "type": "shell", "command": "npm run watch", "isBackground": true, "problemMatcher": ["$ts-webpack-watch", "$tslint-webpack-watch"], "options": {"cwd": "${workspaceFolder}", "env": {"NODE_ENV": "development", "PLATFORM": "saas", "WEBVIEW_CONSUMER": "xcode"}}}, {"label": "watch(package:vscode)(vs-poc)", "type": "shell", "command": "npm run watch", "isBackground": true, "problemMatcher": ["$ts-webpack-watch", "$tslint-webpack-watch"], "options": {"cwd": "${workspaceFolder}", "env": {"NODE_ENV": "development", "PLATFORM": "poc", "WEBVIEW_CONSUMER": "vs"}}}, {"label": "watch(package:vscode)(vs-saas)", "type": "shell", "command": "npm run watch", "isBackground": true, "problemMatcher": ["$ts-webpack-watch", "$tslint-webpack-watch"], "options": {"cwd": "${workspaceFolder}", "env": {"NODE_ENV": "development", "PLATFORM": "saas", "WEBVIEW_CONSUMER": "vs"}}}]}