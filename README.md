# Comate插件宿主

在`packages`中各子包命名如下：

- `shared-internals`：可复用的内部实现部分。
- `engine`：Engine侧的实现，上游上IDE，下游是各个插件，负责从IDE接收消息调度分发到插件。
- `client`：插件侧的实现，**插件开发者会实际安装这个包**，同时插件进程的逻辑入口也在这个包内，上游是Engine。
- `cli`：供插件开发者使用的命令行工具。
- `playground`：一个手动测试用的React项目，会主动加载当前项目中有的内容，优先使用CLI而不是这个。
- `site`：文档站，使用Docusaurus构建，任何对公开API的变动请同步更新文档。

在`plugins`中放置一方和三方插件，其中以`demo-`开头的作为示例。

## 系统初始化

1. 由IDE侧启动一个进程，创建`PluginEngine`类的实例，调用`start()`方法。
2. Engine扫描插件文件夹，每个插件创建一个进程，调用`client/bin`脚本，传递单个插件的文件夹过去：
   1. `client/bin`会调用`client/main`中的`PluginHost`在指定目录上开始插件初始化。
   2. 读取插件的`package.json`拿到描述信息。
   3. 使用动态`import`加载插件声明的`entry`文件，获取`setup`函数。
   4. 调用`setup`函数并提供`ClientRegistry`对象。
   5. 在`setup`函数中，插件开发者的代码会调用`registerXxxProvider`方法，从而在`ClientRegistry`对象上保存各个能力。
   6. 做一系列校验，确保插件状态完好，并完成启动。
3. Engine侧启动插件进程后，同样读插件的`package.json`，从描述中获取插件的能力列表并放在`EngineRegistry`中。
4. 把插件进程封装成一个`PluginContainer`对象，并以插件名称为键存放在一个`Map`中。
5. 完成启动过程。

其中涉及到2个注册表，它们的区别是：

- `ClientRegistry`运行在插件进程中，只管理一个插件内的各个能力，但存放了具体能力的实现的`class`，可以调用能力。
- `EngineRegistry`运行在Engine进程中，存放了所有插件的全部能力，但只有能力的基本描述，并不能调用。

## 处理对话消息

**需要注意的是，要求IDE侧实现通过`message`事件转化为`Promise`的能力，这部分并没有在本代码库中有实现，本代码库只管Engine到插件之间的会话。**

1. 在IDE侧，向Engine进程发送一个`CHAT_QUERY`消息，由Engine进程中的`IdeChannel`收到，并打开一个`IdeSession`，消息包含了用户的输入、当前IDE信息等需要的信息。
2. 在`IdeSession`中，获取消息体中的`pluginName`字段，知道需要调用的插件。
3. 找到`pluginName`对应的插件的`PluginContainer`对象，把消息转发到插件进程中，此时会创建一个`PluginSession`对象，后续插件进程发送的消息在这里面处理。
4. 在插件进程中，由`EngineChannel`收到消息，打开一个`EngineSession`。
5. 在`EngineSession`中，调用`CapabilityInvoker`来消费知识。
6. 在`CapabilityInvoker`中，通过`ClientRegistry`找到匹配的能力，能直接调用的就调用，不能的就通过Function Calling找到能力并生成参数再调用。
7. 初始化所有的`internals`依赖，再实例化对应的`Provider`类型，调用`handleChatQuery`方法，方法返回迭代器
8. 使用`for await ... of`语法调用迭代器，每一个`yield`出来的都再原样`yield`回到`EngienSession`中。
9. `EngineSession`收到后，调用`draw`方法发消息回Engine进程。
10. Engine进程中的`PluginSession`收到`draw`消息时，再调用父级`IdeSession#draw`继续往上层转发消息。
11. 每一次更新消息最终回到IDE并更新会话界面。

最终`CapabilityInvoker`处理完，由`EngineSession`知晓（迭代器关闭）后，通过`EngineSession#finish`方法，一路将`SESSION_FINISH`消息转发回IDE。

整个过程中，基本Engine侧找到对应的`PluginContainer`后就是一个代理透传的过程了，在IDE侧要实现对`DRAW_CHAT`和`SESSION_FINISH`消息的处理。

## 处理扫描消息

扫描本身是一个类似商业上的发标、投标、竞标、中标的过程，即发起请求后全部插件参与返回自己“是否想进行这次扫描”的意愿，并最终选择一个有意愿的能力最终执行扫描过程。

1. 在IDE侧，判断用户保存、修改等事件，在合适的时机触发扫描，发送`SCAN_QUERY`消息，由Engine进程的`IdeChannel`接收。
2. 由Engine广播发送`WILL_SCAN`消息到所有的插件进程中，在各插件中执行以下流程：
   1. 收到`WILL_SCAN`消息后，找到本插件内实现了`ScanHandler`的能力，初始化对应的`Provider`类实例。
   2. 调用`Provider`实例的`check`方法，得到扫描意愿，包括是否参与这次扫描、计划扫描及修改的代码块等信息。
   3. 将返回的待修改代码块（行号）与该文件的变更（通过`git diff`获取）进行比较，仅保留与变更有关联的块。
   4. 如果最终依然存在需要修改的代码块，则认为这个能力是一个“候选”。此时将对应的`Provider`实例保留在内存中暂不释放，并发送一个`REPORT_WILL_SCAN`消息到Engine进程。
3. 在Engine进程，收集到所有插件返回的`REPORT_WILL_SCAN`消息，并使用一定算法选择其中一个作为真正的扫描执行者（一个都没有就结束此次任务）。
4. 发送`RELEASE_SCAN_TASK`消息给所有未选上的插件能力，在插件进程接收到后主动释放原本保留的`Provider`实例。
5. 发送`SCAN_TASK`消息给选中的插件能力，在插件中进行最终的扫描逻辑：
   1. 将原本保留的`Provider`实例找回，并同时从持久池中释放（此时已经拿到引用了，用完也就可以回收了）。
   2. 将前面通过`git diff`过滤过的待修改代码块作为参考信息，传递给实例的`scan`方法。
   3. 对`scan`方法每次`yield`出来的结果，转为`SCAN_TASK_PROGRESS`消息发送给Engine进程。
6. Engine进程接收到的`SCAN_TASK_PROGRESS`消息，原样透传至IDE侧展示。

与对话不同的是，扫描过程中主要逻辑都在Engine进程中进行调度，IDE侧并没有感知到“扫描意愿收集”、“选择最终执行者”、“进行扫描”这些过程，只会收到最后的`SCAN_TASK_PROGRESS`消息渲染界面。

## 分支策略

分支分为： master、release-xxx、feature-xxx 三种。

- 开发新功能，请基于发布版本，比如开发的功能将在 2024/04/17 版本上线，请基于`release-20240417`分支拉feature分支开发。
- 永远保证 master 分支的代码是可以发布的。Bugfix在 release-xxx 分支进行，然后合入回 master 分支。
- release 的分支发布后合入回master。
