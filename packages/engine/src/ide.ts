import {
    ACTION_CHAT_QUERY,
    ChatQueryPayload,
    Session,
    Channel,
    ChannelImplement,
    ChannelImplementMaybe,
    ACTION_REQUEST_PERMISSION,
    RequestPermissionPayload,
    ACTION_ASK_LLM,
    LlmPayload,
    ACTION_GET_PLUGIN_CONFIG,
    GetPluginConfigPayload,
    ACTION_INFORMATION_QUERY,
    InformationPayload,
    ACTION_SCAN_TASK,
    ACTION_WILL_SCAN,
    ScanTaskPayload,
    ACTION_SCAN_QUERY,
    ScanQueryPayload,
    ReportWillScanPayload,
    SessionInit,
    WillScanPayload,
    ACTION_RELEASE_SCAN_TASK,
    isCapabilityMatch,
    ReleaseScanTaskPayload,
    canHandleScanQuery,
    ProviderCapabilityInfo,
    ACTION_START_BACKGROUND_SERVICE,
    StartBackgroundServicePayload,
    Execution,
    ACTION_ASK_LLM_STREAMING,
    ACTION_CUSTOM_COMMAND,
    CustomCommandPayload,
    ACTION_DIAGNOSTIC_SCAN,
    DiagnosticScanInvokePayload,
    ACTION_SECUBOT,
    SecubotQueryPayload,
    DebugAgentPluginPayload,
    ACTION_DEBUG_TASK_PROCESS,
    ACTION_ASK_RAG,
    RAGPayload,
} from '@comate/plugin-shared-internals';
import {PluginContainer} from './container.js';
import {SelectStrategy, BalancedSelectStrategy} from './selectStrategy.js';
import {ScanIntermediateState, WillScanVote} from './scanState.js';
import {EngineRegistry} from './registry.js';
import {execa} from 'execa';
import {GitDiffChecker} from './diffChecker.js';

const WILL_HANDLE_TIMEOUT = 250;

interface ForwadPayloadMap {
    [ACTION_REQUEST_PERMISSION]: RequestPermissionPayload;
    [ACTION_ASK_LLM]: LlmPayload;
    [ACTION_ASK_RAG]: RAGPayload;
    [ACTION_ASK_LLM_STREAMING]: LlmPayload;
    [ACTION_GET_PLUGIN_CONFIG]: GetPluginConfigPayload;
    [ACTION_CHAT_QUERY]: ChatQueryPayload;
    [ACTION_CUSTOM_COMMAND]: CustomCommandPayload;
    [ACTION_INFORMATION_QUERY]: InformationPayload;
    [ACTION_START_BACKGROUND_SERVICE]: StartBackgroundServicePayload;
    [ACTION_DIAGNOSTIC_SCAN]: DiagnosticScanInvokePayload;
    [ACTION_SECUBOT]: SecubotQueryPayload;
    [ACTION_DEBUG_TASK_PROCESS]: DebugAgentPluginPayload;
}

interface PayloadMap extends ForwadPayloadMap {
    [ACTION_SCAN_QUERY]: ScanQueryPayload;
}

interface SessionContext {
    plugins: Map<string, PluginContainer>;
    registry: EngineRegistry;
    scanSelectStrategy: SelectStrategy;
}

/**
 * 处理IDE与Engine之间的会话
 */
export class IdeSession extends Session<PayloadMap> {
    private readonly plugins: Map<string, PluginContainer>;
    private readonly registry: EngineRegistry;
    private readonly scanState = new ScanIntermediateState();
    private readonly scanSelectStrategy;

    constructor(init: SessionInit, implement: ChannelImplement, context: SessionContext) {
        super(init, implement);
        this.registry = context.registry;
        this.plugins = context.plugins;
        this.scanSelectStrategy = context.scanSelectStrategy;
    }

    async reportWillScan(payload: ReportWillScanPayload) {
        const isStateSaved = this.scanState.put(payload.scanId, payload);

        if (isStateSaved) {
            return;
        }

        // 如果收到插件返回的扫描内容时，这次扫描调度已经结束了，就立刻发回去一个消息让插件释放掉原本保持的实例
        this.log(
            'system',
            this.constructor.name,
            'DiscardScanVote',
            {scanId: payload.scanId, pluginName: payload.pluginName, capabilityName: payload.capabilityName}
        );
        const plugin = this.plugins.get(payload.pluginName);
        if (plugin) {
            const releasePayload: ReleaseScanTaskPayload = {
                scanId: payload.scanId,
                pluginName: payload.pluginName,
                capabilityName: payload.capabilityName,
            };
            await plugin.startSession(
                this,
                ACTION_RELEASE_SCAN_TASK,
                releasePayload,
                {lifeCycleConnected: false}
            );
        }
    }

    protected initializeListeners() {
        super.initializeListeners();
        this.forwardSessionTask(ACTION_CHAT_QUERY);
        this.forwardSessionTask(ACTION_SECUBOT);
        this.forwardSessionTask(ACTION_CUSTOM_COMMAND);
        this.forwardSessionTask(ACTION_DIAGNOSTIC_SCAN);
        this.forwardSessionTask(ACTION_START_BACKGROUND_SERVICE);
        this.setListener(ACTION_SCAN_QUERY, payload => this.handleScan(payload));
        this.forwardExecution(ACTION_REQUEST_PERMISSION);
        this.forwardExecution(ACTION_ASK_LLM);
        this.forwardExecution(ACTION_ASK_RAG);
        this.forwardExecution(ACTION_ASK_LLM_STREAMING);
        this.forwardExecution(ACTION_DEBUG_TASK_PROCESS);
        this.forwardExecution(ACTION_GET_PLUGIN_CONFIG);
        this.forwardExecution(ACTION_INFORMATION_QUERY);
    }

    private async handleScan(payload: ScanQueryPayload) {
        // 1. 请示所有的插件进行`willScan`的处理，这个过程插件会回调`reportWillScan`方法存中间状态
        const diffText = await this.getFileDiff(payload.context.activeFilePath, payload.systemInfo.cwd);
        const diffChecker = diffText === null ? GitDiffChecker.skip() : GitDiffChecker.fromDiffText(diffText);
        this.scanState.begin(payload.scanId, diffChecker);
        await this.broadcastWillScan(payload).catch(() => {});
        // 2. 把收集到的意愿选一个，如果没有的话就中断了
        const votes = this.scanState.getVotes(payload.scanId);
        this.scanState.end(payload.scanId);

        if (!votes.length) {
            this.finish();
            return;
        }

        const keys = votes.map(v => `${v.pluginName}/${v.capabilityName}`);
        const selected = this.scanSelectStrategy.select(keys);
        this.log(
            'system',
            this.constructor.name,
            'SelectScanHandle',
            {scanId: payload.scanId, source: keys, selected}
        );
        // 3. 向所有插件发送任务，命中的插件收下，未命中的把原本保留的实例释放
        const [pluginName, capabilityName] = selected.split('/');
        const selectedVote = votes.find(v => isCapabilityMatch(v, {pluginName, capabilityName}));
        // 理论上不可能发生
        if (!selectedVote) {
            this.log(
                'error',
                this.constructor.name,
                'UnexpectedScanInvokeState',
                {vote: selectedVote}
            );
        }
        await this.broadcastScanTask(votes, selectedVote ?? null).catch(() => {});
        this.finish();
    }

    private async broadcastWillScan(payload: ScanQueryPayload) {
        const match = {
            canHandle: canHandleScanQuery,
            cwd: payload.systemInfo.cwd,
            context: payload.context,
        };
        const capabilities = await this.registry.suggestCapabilitiesByMatch(match);
        const send = async (capability: ProviderCapabilityInfo) => {
            const plugin = this.plugins.get(capability.owner.name);

            if (!plugin) {
                return;
            }

            const willScanPayload: WillScanPayload = {
                ...payload,
                pluginName: capability.owner.name,
                capabilityName: capability.name,
            };
            // 不能因为`willScan`结束就把总处理的`Session`结束了，这里断开生命周期同步
            await plugin.startSession(this, ACTION_WILL_SCAN, willScanPayload, {lifeCycleConnected: false});
        };
        // 插件内部给了100ms，这边最多给250ms，期望这段时间一定能回收全部的意愿
        await Promise.all([...capabilities.map(send), new Promise(r => setTimeout(r, WILL_HANDLE_TIMEOUT))]);
    }

    private async broadcastScanTask(votes: WillScanVote[], selected: WillScanVote | null) {
        const send = async (vote: WillScanVote) => {
            const plugin = this.plugins.get(vote.pluginName);

            if (!plugin) {
                return;
            }

            if (selected && isCapabilityMatch(vote, selected)) {
                const payload: ScanTaskPayload = {
                    scanId: vote.scanId,
                    pluginName: vote.pluginName,
                    capabilityName: vote.capabilityName,
                    description: selected.goal.description,
                    ranges: selected.goal.ranges,
                };
                await plugin.startSession(this, ACTION_SCAN_TASK, payload, {lifeCycleConnected: false});
            }
            else {
                const payload: ReleaseScanTaskPayload = {
                    scanId: vote.scanId,
                    pluginName: vote.pluginName,
                    capabilityName: vote.capabilityName,
                };
                await plugin.startSession(this, ACTION_RELEASE_SCAN_TASK, payload, {lifeCycleConnected: false});
            }
        };
        await Promise.all(votes.map(send));
    }

    private forwardExecution<A extends keyof ForwadPayloadMap>(action: A) {
        this.setListener(
            action,
            async (payload: {pluginName: string}, execution?: Execution) => {
                const plugin = this.plugins.get(payload.pluginName);
                if (plugin) {
                    await plugin.sendMessage(
                        this.sessionId,
                        {action, payload},
                        execution
                    );
                }
                else {
                    this.logPluginNotFound(payload.pluginName);
                }
            }
        );
    }

    private forwardSessionTask<A extends keyof ForwadPayloadMap>(action: A) {
        this.setListener(
            action,
            async (payload: {pluginName: string}) => {
                const plugin = this.plugins.get(payload.pluginName);
                if (plugin) {
                    await plugin.startSession(this, action, payload);
                    // TODO: 看下是不是要异常处理
                }
                else {
                    this.logPluginNotFound(payload.pluginName);
                }
            }
        );
    }

    private logPluginNotFound(pluginName: string) {
        this.log('error', this.constructor.name, 'PluginNotFound', {pluginName});
    }

    private async getFileDiff(file: string, cwd: string) {
        try {
            const {stdout: status} = await execa('git', ['status', '--short', file], {cwd});

            // 对未跟踪的文件就不去做比对了
            if (status.includes('?') || status.includes('!')) {
                return null;
            }

            const {stdout: diffText} = await execa('git', ['diff', 'HEAD', file], {cwd});
            return diffText;
        }
        catch {
            return null;
        }
    }
}

export class IdeChannel extends Channel<IdeSession> {
    private readonly plugins: Map<string, PluginContainer>;
    private readonly registry: EngineRegistry;
    private readonly scanSelectStrategy = new BalancedSelectStrategy();

    constructor(implement: ChannelImplementMaybe, registry: EngineRegistry, plugins: Map<string, PluginContainer>) {
        super(implement);
        this.registry = registry;
        this.plugins = plugins;
    }

    protected createSession(init: SessionInit): IdeSession {
        return new IdeSession(
            init,
            this.implement,
            {
                plugins: this.plugins,
                registry: this.registry,
                scanSelectStrategy: this.scanSelectStrategy,
            }
        );
    }
}
