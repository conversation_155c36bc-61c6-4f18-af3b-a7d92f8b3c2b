import {ModelOptions} from '@comate/plugin-shared-internals';

const ROUNDS = 10;

// Maximum time to keep a conversation in memory (24 hours)
const CONVERSATION_TTL = 24 * 60 * 60 * 1000;

// todo: 先写的128k模型的字符长度限制，要根据使用的模型改改
const MAX_CHARS = 516096;

export interface ChatMessage {
    role: 'user' | 'assistant';
    content: string;
    originalQuery?: string;
}

export class ConversationBufferMemory {
    private readonly conversationBuffer: Map<string, {
        messages: ChatMessage[];
        lastAccessed: number;
    }> = new Map([]);

    private readonly cleanupInterval: NodeJS.Timeout;

    constructor() {
        // Run cleanup every hour
        this.cleanupInterval = setInterval(() => this.cleanup(), 60 * 60 * 1000);
    }

    dispose() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
        }
        this.conversationBuffer.clear();
    }

    clear(conversationId?: string) {
        if (conversationId) {
            this.conversationBuffer.delete(conversationId);
        } else {
            this.conversationBuffer.clear();
        }
    }

    get(conversationId: string) {
        const conversation = this.conversationBuffer.get(conversationId);
        if (conversation) {
            conversation.lastAccessed = Date.now();
            return conversation.messages.slice(-ROUNDS);
        }
        return [];
    }

    putUser(
        enableMultiturnDialogue: ModelOptions['enableMultiturnDialogue'],
        conversationId: string,
        message: ChatMessage
    ) {
        const userMessage = {...message};
        // 如果开启了onlyQuery，则将原始query存下来，以备后续使用
        if (typeof enableMultiturnDialogue === 'object') {
            const {onlyQuery} = enableMultiturnDialogue;
            if (onlyQuery) {
                userMessage.originalQuery = onlyQuery;
            }
        }
        // 已经存在对话，则直接push
        const conversation = this.conversationBuffer.get(conversationId);
        if (conversation) {
            conversation.messages.push(userMessage);
            conversation.lastAccessed = Date.now();

            // 检查总字符数是否超过限制
            if (this.getTotalChars(conversation.messages) > MAX_CHARS) {
                // 移除最早的消息，直到总字符数低于限制
                while (this.getTotalChars(conversation.messages) > MAX_CHARS && conversation.messages.length > 1) {
                    conversation.messages.shift();
                }
            }
            return;
        }
        // 不存在对话，则新建
        this.conversationBuffer.set(conversationId, {
            messages: [userMessage],
            lastAccessed: Date.now(),
        });
    }

    putAssistant(conversationId: string, content: string) {
        const conversation = this.conversationBuffer.get(conversationId);
        if (conversation) {
            conversation.messages.push({
                role: 'assistant',
                content,
            });
            conversation.lastAccessed = Date.now();

            // 检查总字符数是否超过限制
            if (this.getTotalChars(conversation.messages) > MAX_CHARS) {
                // 移除最早的消息，直到总字符数低于限制
                while (this.getTotalChars(conversation.messages) > MAX_CHARS && conversation.messages.length > 1) {
                    conversation.messages.shift();
                }
            }

            // 最多只存最近的十条对话
            if (conversation.messages.length > ROUNDS) {
                conversation.messages = conversation.messages.slice(-ROUNDS);
            }
        }
    }

    getConversationId(
        enableMultiturnDialogue: ModelOptions['enableMultiturnDialogue'],
        pluginName: string,
        capabilityName: string
    ) {
        let conversationId = `${pluginName}/${capabilityName}`;
        if (typeof enableMultiturnDialogue === 'object') {
            const {byPlugin} = enableMultiturnDialogue;
            if (byPlugin) {
                conversationId = pluginName;
            }
        }
        return conversationId;
    }

    trimConversation(conversationId: string, maxChars = MAX_CHARS) {
        const conversation = this.conversationBuffer.get(conversationId);

        if (conversation) {
            let totalChars = 0;
            const trimmedConversation = [];
            // 从后往前遍历
            for (let i = conversation.messages.length - 1; i >= 0; i--) {
                const message = conversation.messages[i];
                // 如果存在originalQuery，则使用originalQuery作为content，以节约上下文
                if (i !== conversation.messages.length - 1 && message.originalQuery) {
                    message.content = message.originalQuery;
                }
                const messageLength = message.content.length;

                // 判断当前消息的字符数是否会超出限制
                if (totalChars + messageLength <= maxChars) {
                    trimmedConversation.unshift(message); // 在前面插入消息
                    totalChars += messageLength;
                }
                else {
                    // 如果这条消息导致超出限制，停止遍历
                    break;
                }
            }

            return trimmedConversation;
        }
        return null;
    }

    private cleanup() {
        const now = Date.now();
        for (const [id, data] of this.conversationBuffer.entries()) {
            if (now - data.lastAccessed > CONVERSATION_TTL) {
                this.conversationBuffer.delete(id);
            }
        }
    }

    private getTotalChars(messages: ChatMessage[]): number {
        return messages.reduce((total, msg) => total + msg.content.length, 0);
    }
}
