import {PluginDescription} from '@comate/plugin-shared-internals';
import {PluginChannel} from './plugin.js';

export interface PluginInitContext {
    description: PluginDescription;
    directory: string;
    inspectPort?: number;
}

interface AttachedPlugin extends PluginInitContext {
    onSetup: ((channel: PluginChannel) => void) | undefined;
    onExit: (() => void) | undefined;
}

export interface PluginGroupValue {
    channel: PluginChannel;
}

export interface PluginGroup<G extends PluginGroupValue = PluginGroupValue> {
    key: string;
    value: G;
    inspectPort?: number;
    plugins: Map<string, AttachedPlugin>;
}

export interface PluginSetupContext {
    description: PluginDescription;
    directory: string;
    inspectPort?: number;
}

export interface GroupJoinOptions {
    onExit?: AttachedPlugin['onExit'];
    onSetup?: AttachedPlugin['onSetup'];
}

export abstract class ChannelGroupManager<G extends PluginGroupValue = PluginGroupValue> {
    protected readonly groups = new Map<string, PluginGroup<G>>();

    async joinGroup(setupContext: PluginSetupContext, {onExit, onSetup}: GroupJoinOptions) {
        const {description, directory, inspectPort} = setupContext;
        const groupKey = this.getGroupKey({description, directory, inspectPort});
        const group = this.groups.get(groupKey) ?? this.createGroup(groupKey, inspectPort);
        group.plugins.set(description.name, {description, directory, onExit, onSetup});
        await this.onPluginJoinGroup(group, setupContext);
    }

    async exitGroup(setupContext: PluginSetupContext) {
        const gropuKey = this.getGroupKey(setupContext);
        const group = this.groups.get(gropuKey);

        if (!group) {
            return;
        }

        group.plugins.delete(setupContext.description.name);
        await this.onPluginExitGroup(group, setupContext);

        if (!group.plugins.size) {
            this.destroyGroup(group);
            this.groups.delete(group.key);
        }
    }

    protected createGroup(groupKey: string, inspectPort?: number) {
        const groupValue = this.createGroupValue(groupKey, inspectPort);
        const group: PluginGroup<G> = {
            key: groupKey,
            value: groupValue,
            plugins: new Map<string, AttachedPlugin>(),
        };

        this.groups.set(groupKey, group);
        return group;
    }

    /**
     * 插件加入到分组后的逻辑钩子
     *
     * @param group 插件加入的分组
     * @param plugin 插件信息
     */
    protected abstract onPluginJoinGroup(group: PluginGroup<G>, plugin: PluginSetupContext): Promise<void>;

    /**
     * 插件从分组移除后的逻辑钩子
     *
     * @param group 插件加入的分组
     * @param plugin 插件信息
     */

    protected abstract onPluginExitGroup(group: PluginGroup<G>, plugin: PluginSetupContext): Promise<void>;

    /**
     * 销毁一个分组，通常在所有插件都已经从中移除后调用
     *
     * @param group 待销毁的分组
     */
    protected abstract destroyGroup(group: PluginGroup<G>): void;

    /**
     * 通过插件信息计算出分组的键名
     *
     * @param context 插件信息
     */
    protected abstract getGroupKey(context: PluginSetupContext): string;

    /**
     * 创建分组的具体实现
     *
     * @param groupKey 分级键
     * @param inspectPort 调试用监听端口
     */
    protected abstract createGroupValue(groupKey: string, inspectPort?: number): G;
}
