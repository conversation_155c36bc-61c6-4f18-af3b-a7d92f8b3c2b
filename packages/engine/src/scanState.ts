import {ReportWillScanPayload, ScanHandleGoal} from '@comate/plugin-shared-internals';
import {GitDiffChecker} from './diffChecker.js';

export interface WillScanVote {
    scanId: string;
    pluginName: string;
    capabilityName: string;
    goal: ScanHandleGoal;
}

interface State {
    diffChecker: GitDiffChecker;
    willScanVotes: WillScanVote[];
}

export class ScanIntermediateState {
    private readonly stateByScanId = new Map<string, State>();

    begin(scanId: string, diffChecker: GitDiffChecker) {
        this.stateByScanId.set(scanId, {diffChecker, willScanVotes: []});
    }

    end(scanId: string) {
        this.stateByScanId.delete(scanId);
    }

    put(scanId: string, payload: ReportWillScanPayload) {
        if (!payload.goal) {
            return false;
        }

        const container = this.stateByScanId.get(scanId);

        if (!container) {
            return false;
        }

        const ranges = payload.goal.ranges.filter(v => container.diffChecker.isRangeIntersect(v));

        if (!ranges.length) {
            return false;
        }

        const state: WillScanVote = {
            scanId,
            pluginName: payload.pluginName,
            capabilityName: payload.capabilityName,
            goal: {
                ...payload.goal,
                ranges,
            },
        };
        container.willScanVotes.push(state);
        return true;
    }

    getVotes(scanId: string) {
        return this.stateByScanId.get(scanId)?.willScanVotes ?? [];
    }
}
