import {
    ChannelImplementMaybe,
    QuerySelectorPayload,
    canHandleQuerySelector,
    readPluginDescription,
} from '@comate/plugin-shared-internals';
import {ContainerInit, PluginContainer} from './container.js';
import {EngineRegistry} from './registry.js';
import {IdeChannel} from './ide.js';
import {PluginChannel} from './plugin.js';
import {
    ChannelGroupManager,
    PluginGroupValue,
    PluginSetupContext,
    PluginGroup,
} from './group.js';
import {forkPluginProcess, invokePluginSetupToProcess} from './process.js';

export {
    PluginGroupValue,
    ChannelGroupManager,
    PluginSetupContext,
    PluginChannel,
    PluginGroup,
    forkPluginProcess,
    invokePluginSetupToProcess,
};

export {DiagnosticCache} from './diagnosticCache.js';
export {ConversationBufferMemory, ChatMessage} from './conversationBufferMemory.js';

export interface EngineInit {
    pluginDirectories: string[];
    channelGroupManager: ChannelGroupManager;
    channel: ChannelImplementMaybe;
    localConfig?: Record<string, any>;
}

// TODO: 现在是没法处理用户主动取消生成的
export class PluginEngine {
    private readonly channelGroupManager: ChannelGroupManager;
    private readonly registry = new EngineRegistry();
    private readonly plugins = new Map<string, PluginContainer>();
    private readonly ideChannel: IdeChannel;
    private readonly pluginDirectories: string[];
    private readonly localConfig?: Record<string, any>;

    constructor(init: EngineInit) {
        this.pluginDirectories = init.pluginDirectories;
        this.localConfig = init.localConfig;
        this.channelGroupManager = init.channelGroupManager;
        this.ideChannel = new IdeChannel(init.channel ?? process, this.registry, this.plugins);
    }

    /**
     * 启动插件引擎
     *
     * @param entryDirectory 放置所有插件的目录
     */
    async start() {
        this.ideChannel.start();

        // 不确定并发和不并发会有什么影响，先用不并发试试
        for (const directory of this.pluginDirectories) {
            await this.run(directory);
        }
    }

    async stop() {
        for (const plugin of this.plugins.values()) {
            await plugin.dispose();
        }
    }

    /**
     * 获取所有插件注册的能力
     *
     * @returns 全部能力的数组
     */
    listAllCapabilities() {
        return this.registry.listAllCapabilities();
    }

    async suggestCapabilitiesByMatch(payload: QuerySelectorPayload) {
        const match = {
            canHandle: canHandleQuerySelector,
            cwd: payload.systemInfo.cwd,
            context: payload.context,
        };
        // 返回带分数的 capabilities
        const capabilities = await this.registry.suggestCapabilitiesByMatch(match);
        return capabilities;
    }

    private async run(pluginDirectory: string) {
        this.ideChannel.log(this.constructor.name, 'PluginLoadStart', {directory: pluginDirectory});

        try {
            // `pluginDirectory`不是目录也会在这里报错
            const description = await readPluginDescription(pluginDirectory);
            const inspectPort: number | undefined = this.localConfig?.[description.name]?.debug?.inspectPort;

            const containerInit: ContainerInit = {
                plugin: description,
                directory: pluginDirectory,
                registry: this.registry,
                groupManager: this.channelGroupManager,
                inspectPort,
            };
            const container = new PluginContainer(containerInit);

            await container.setup();

            this.plugins.set(description.name, container);

            this.ideChannel.log(this.constructor.name, 'PluginLoadFish', {directory: pluginDirectory});
        }
        catch {
            // TODO: 处理插件加载不起来的错误
            this.ideChannel.log(this.constructor.name, 'PluginLoadError', {directory: pluginDirectory});
        }
    }
}
