import Fuse from 'fuse.js';
import {
    ActivationContext,
    LanguageDetector,
    PluginCapabilityType,
    ProviderCapabilityInfo,
    VisibilitySelectorMatcher,
} from '@comate/plugin-shared-internals';

export interface CapabilityMatch {
    /** 确定能力场景 */
    canHandle: (type: PluginCapabilityType) => boolean;
    /** 在指定插件下查的，不提供则跨插件查找所有 */
    pluginName?: string | undefined;
    /** 查找的能力的关键字，用于fuzzy检索 */
    capabilityKeyword?: string;
    /** 当前工作台路径 */
    cwd: string;
    /** IDE现场 */
    context: ActivationContext;
}

interface CapabilityEntry extends ProviderCapabilityInfo {
    visibilityMatcher: VisibilitySelectorMatcher;
    queryMatcher: VisibilitySelectorMatcher;
}

export class EngineRegistry {
    private capabilities: CapabilityEntry[] = [];

    /**
     * 获取所有插件注册的能力
     *
     * @returns 全部能力的数组
     */
    listAllCapabilities(): ProviderCapabilityInfo[] {
        return this.capabilities.map(({visibilitySelector, ...info}) => info);
    }

    /**
     * 根据插件名称和能力关键字查找对应能力，通常用于`/`触发
     *
     * @param match 查找的匹配信息
     * @returns 找到的各项能力，每个能力会有一个分数
     */
    async suggestCapabilitiesByMatch(match: CapabilityMatch): Promise<ProviderCapabilityInfo[]> {
        const {pluginName, capabilityKeyword} = match;
        const source: ProviderCapabilityInfo[] = [];

        for (const capability of this.capabilities) {
            if (!match.canHandle(capability.type)) {
                continue;
            }
            if (pluginName && capability.owner.name !== pluginName) {
                continue;
            }
            const visibilityScore = await capability.visibilityMatcher.match(match.cwd, match.context);
            if (visibilityScore === 0) {
                continue;
            }
            // 只处理没有查找的情况，也就是编辑框完全为空
            if (capabilityKeyword) {
                source.push({...capability});
                continue;
            }

            const queryScore = await capability.queryMatcher.match(match.cwd, match.context);
            if (queryScore === 0) {
                continue;
            }
            source.push({...capability, queryScore});
        }

        if (!capabilityKeyword) {
            return source;
        }

        const fuse = new Fuse(source, {keys: ['displayName']});
        return fuse.search(capabilityKeyword).map(v => v.item);
    }

    loadCapabilitiesForPlugin(capabilities: ProviderCapabilityInfo[]) {
        const compiled = capabilities.map(v => this.compileCapabilityInfo(v));
        this.capabilities = this.capabilities.concat(compiled);
    }

    unloadCapabilitiesForPlugin(pluginName: string) {
        this.capabilities = this.capabilities.filter(v => v.owner.name !== pluginName);
    }

    private compileCapabilityInfo(info: ProviderCapabilityInfo): CapabilityEntry {
        return {
            ...info,
            visibilityMatcher: new VisibilitySelectorMatcher(info.visibilitySelector ?? {}, LanguageDetector.default),
            queryMatcher: new VisibilitySelectorMatcher(info.querySelector ?? {}, LanguageDetector.default),
        };
    }
}
