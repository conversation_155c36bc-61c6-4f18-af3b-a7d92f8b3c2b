export abstract class SelectStrategy {
    private readonly usage = new Map<string, number>();

    protected getUsage(key: string) {
        return this.usage.get(key) || 0;
    }

    protected setUsed(key: string) {
        this.usage.set(key, this.getUsage(key) + 1);
    }

    abstract select(values: string[]): string;
}

/** 始终选第一个被用的次数最少的选择策略 */
export class BalancedSelectStrategy extends SelectStrategy {
    select(values: string[]): string {
        const selected = {
            value: '',
            count: Infinity,
        };
        for (const value of values) {
            const count = this.getUsage(value);
            if (count < selected.count) {
                selected.value = value;
                selected.count = count;
            }
        }
        this.setUsed(selected.value);
        return selected.value;
    }
}
