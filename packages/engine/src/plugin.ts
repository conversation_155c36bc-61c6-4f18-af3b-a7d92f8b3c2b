import {
    ACTION_CHAT_TASK_PROGRESS,
    ACTION_REQUEST_PERMISSION,
    Channel,
    TaskProgressPayload,
    Session,
    RequestPermissionPayload,
    ACTION_ASK_LLM,
    LlmPayload,
    ACTION_GET_PLUGIN_CONFIG,
    GetPluginConfigPayload,
    ACTION_INFORMATION_QUERY,
    InformationPayload,
    ACTION_REPORT_WILL_SCAN,
    ReportWillScanPayload,
    ACTION_SCAN_TASK_PROGRESS,
    SessionInit,
    ACTION_ASK_LLM_STREAMING,
    ACTION_DIAGNOSTIC_SCAN_TASK_PROGRESS,
    DiagnosticScanTaskProgressPayload,
    ACTION_SECUBOT_TASK_PROGRESS,
    Se<PERSON>bot<PERSON><PERSON>yPayload,
    ACTION_DEBUG_TASK_PROCESS,
    DebugAgentPluginPayload,
    RAGPayload,
    ACTION_ASK_RAG,
} from '@comate/plugin-shared-internals';
import {IdeSession} from './ide.js';

interface PayloadMap {
    [ACTION_CHAT_TASK_PROGRESS]: TaskProgressPayload;
    [ACTION_REPORT_WILL_SCAN]: ReportWillScanPayload;
    [ACTION_SCAN_TASK_PROGRESS]: TaskProgressPayload;
    [ACTION_REQUEST_PERMISSION]: RequestPermissionPayload;
    [ACTION_ASK_LLM]: LlmPayload;
    [ACTION_ASK_RAG]: RAGPayload;
    [ACTION_ASK_LLM_STREAMING]: LlmPayload;
    [ACTION_GET_PLUGIN_CONFIG]: GetPluginConfigPayload;
    [ACTION_INFORMATION_QUERY]: InformationPayload;
    [ACTION_DIAGNOSTIC_SCAN_TASK_PROGRESS]: DiagnosticScanTaskProgressPayload;
    [ACTION_SECUBOT_TASK_PROGRESS]: SecubotQueryPayload;
    [ACTION_DEBUG_TASK_PROCESS]: DebugAgentPluginPayload;
}

/**
 * 处理Engine与插件之间的会话
 */
export class PluginSession extends Session<PayloadMap> {
    protected initializeListeners() {
        super.initializeListeners();

        this.forwardMessageToParent(ACTION_REQUEST_PERMISSION);
        this.forwardMessageToParent(ACTION_ASK_LLM);
        this.forwardMessageToParent(ACTION_ASK_RAG);
        this.forwardMessageToParent(ACTION_ASK_LLM_STREAMING);
        this.forwardMessageToParent(ACTION_DEBUG_TASK_PROCESS);
        this.forwardMessageToParent(ACTION_GET_PLUGIN_CONFIG);
        this.forwardMessageToParent(ACTION_INFORMATION_QUERY);
        this.forwardMessageToParent(ACTION_CHAT_TASK_PROGRESS);
        this.forwardMessageToParent(ACTION_DIAGNOSTIC_SCAN_TASK_PROGRESS);
        this.forwardMessageToParent(ACTION_SECUBOT_TASK_PROGRESS);
        this.forwardMessageToParent(ACTION_SCAN_TASK_PROGRESS);

        this.setListener(
            ACTION_REPORT_WILL_SCAN,
            async payload => {
                if (this.parent instanceof IdeSession) {
                    await this.parent.reportWillScan(payload);
                }
            }
        );
    }
}

export class PluginChannel extends Channel<PluginSession> {
    protected createSession(init: SessionInit) {
        return new PluginSession(init, this.implement);
    }
}
