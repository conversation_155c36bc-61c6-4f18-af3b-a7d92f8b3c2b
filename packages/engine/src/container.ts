import {
    PluginDescription,
    Session,
    SessionOptions,
    Execution,
    CapabilityDescription,
    ProviderCapabilityInfo,
} from '@comate/plugin-shared-internals';
import {PluginChannel} from './plugin.js';
import {PluginSetupContext} from './group.js';
import {EngineRegistry} from './registry.js';
import {ChannelGroupManager} from './group.js';

export interface ContainerInit {
    plugin: PluginDescription;
    directory: string;
    registry: EngineRegistry;
    inspectPort?: number;
    groupManager: ChannelGroupManager;
}

/**
 * 管理插件进程和通信的容器，每个插件一个实例
 */
export class PluginContainer {
    private readonly groupManager: ChannelGroupManager;
    private readonly description: PluginDescription;
    private readonly registry: EngineRegistry;
    private readonly setupContext: PluginSetupContext;
    private pluginChannel: PluginChannel | null = null;
    private readonly cleanupHandlers: Array<() => void> = [];

    constructor(init: ContainerInit) {
        this.groupManager = init.groupManager;
        this.description = init.plugin;
        this.registry = init.registry;
        this.setupContext = {
            description: init.plugin,
            directory: init.directory,
            inspectPort: init.inspectPort,
        };
    }

    async setup() {
        await this.groupManager.joinGroup(
            this.setupContext,
            {
                onSetup: (channel: PluginChannel) => this.setupRunningStatus(channel),
                onExit: () => this.cleanup(),
            }
        );
    }

    startSession(session: string | Session, action: string, payload: any, options?: SessionOptions) {
        return this.pluginChannel?.startSession(session, {action, payload}, options);
    }

    sendMessage(sessionId: string, payload: any, execution?: Execution) {
        return this.pluginChannel?.send(sessionId, payload, execution);
    }

    async dispose() {
        await this.groupManager.exitGroup(this.setupContext);
        this.cleanup();
    }

    private setupRunningStatus(channel: PluginChannel) {
        this.pluginChannel = channel;

        const toInfo = (capability: CapabilityDescription): ProviderCapabilityInfo => ({
            owner: this.description,
            name: capability.name,
            type: capability.type,
            displayName: capability.displayName,
            placeholder: capability.placeholder,
            defaultUserMessage: capability.defaultUserMessage,
            displayTag: capability.displayTag,
            visibilitySelector: capability.visibilitySelector,
            querySelector: capability.querySelector,
        });

        this.registry.loadCapabilitiesForPlugin(this.description.capabilities.map(toInfo));
    }

    private cleanup() {
        // Clear plugin channel
        if (this.pluginChannel) {
            // this.pluginChannel.removeAllListeners();
            this.pluginChannel = null;
        }

        // Unload capabilities
        this.registry.unloadCapabilitiesForPlugin(this.description.name);
    }
}
