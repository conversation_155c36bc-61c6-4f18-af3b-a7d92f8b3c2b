/* eslint-disable complexity */
import path from 'node:path';
import fs from 'node:fs';
import {
    DiagnosticCacheValue,
    DiagnosticScanPayload,
    DiagnosticScanTaskProgressChunk,
    DiagnosticScanChangedFiles,
    ActivationContext,
} from '@comate/plugin-shared-internals';
import {execa} from 'execa';

export abstract class DiagnosticCache {
    private readonly deletedFilePath: string[] = [];
    private readonly diagnosticCache: Map<string, DiagnosticCacheValue> = new Map([]);

    async start(payload: DiagnosticScanPayload) {
        await this.init(payload);
        this.watch(payload);
    }

    put(chunk: DiagnosticScanTaskProgressChunk) {
        this.diagnosticCache.set(chunk.filename, {diagnostics: chunk.diagnostics});
    }

    get(payload: DiagnosticScanPayload & {context: ActivationContext}) {
        this.beforeGet(payload);
        const result = this.diagnosticCache.get(payload.context?.activeFilePath);
        return result;
    }

    hasDiagnostics(): boolean {
        for (const value of this.diagnosticCache.values()) {
            if (value.diagnostics.length > 0) {
                return true;
            }
        }
        return false;
    }

    private isDirectory(fileFullPath: string) {
        try {
            return fs.lstatSync(fileFullPath).isDirectory();
        }
        catch (ex) {
            return false;
        }
    }

    private isModifiedRecently(fileFullPath: string, changeThreshold: number) {
        try {
            const stats = fs.statSync(fileFullPath);
            if ((Date.now() - stats.mtimeMs) > changeThreshold) {
                return false;
            }
            return true;
        }
        catch (ex) {
            return false;
        }
    }

    private isAddedRecently(fileFullPath: string, changeThreshold: number) {
        try {
            const stats = fs.statSync(fileFullPath);
            if ((Date.now() - stats.birthtimeMs) > changeThreshold) {
                return false;
            }
            return true;
        }
        catch (ex) {
            return false;
        }
    }

    private async getChangedFiles(cwd: string, changeThreshold: number): Promise<DiagnosticScanChangedFiles> {
        try {
            const {stdout} = await execa('git', ['status', '-s'], {cwd});
            const {stdout: untrackedFiles} = await execa('git', ['ls-files', '--others', '--exclude-standard'], {cwd});

            const statusFilesList = stdout.split('\n').filter(filePath => filePath.trim());
            const untrackedFilesList = untrackedFiles
                .split('\n')
                .filter(filePath => filePath.trim())
                .map(untrackedFile => `?? ${untrackedFile}`);

            // 初始化分类对象
            const changedFiles: DiagnosticScanChangedFiles = {
                added: [],
                deleted: [],
                modified: [],
            };

            // 将结果按行拆分为数组
            [...new Set([...statusFilesList, ...untrackedFilesList])].forEach(line => {
                const status = line.slice(0, 2);
                const file = line.slice(3).trim();
                const fileFullPath = path.join(cwd, file.replace(/^"|"$/g, ''));

                if (this.isDirectory(fileFullPath)) {
                    return; // 跳过目录
                }

                const isModifiedRecently = this.isModifiedRecently(fileFullPath, changeThreshold);
                const isAddedRecently = this.isAddedRecently(fileFullPath, changeThreshold);

                // 根据状态分类文件
                switch (status) {
                    case '??': // 新建文件
                        if (isAddedRecently) {
                            changedFiles.added.push(fileFullPath);
                        }
                        else if (isModifiedRecently) {
                            changedFiles.modified.push(fileFullPath);
                        }
                        break;
                    case 'D ': // 删除文件
                    case ' D': // 删除文件
                        if (!this.deletedFilePath.includes(fileFullPath)) {
                            this.deletedFilePath.push(fileFullPath);
                            changedFiles.deleted.push(fileFullPath);
                        }
                        break;
                    case 'M ': // 修改文件
                    case ' M': // 修改文件
                    case 'MM': // 修改文件
                    case 'AM': // 添加并修改
                    case ' AM': // 添加并修改
                    case 'MA': // 修改并添加
                        isModifiedRecently && changedFiles.modified.push(fileFullPath);
                        break;
                    default:
                        // 处理其他状态或忽略
                        break;
                }
            });

            return changedFiles;
        }
        catch {
            return {added: [], deleted: [], modified: []};
        }
    }

    private watch(payload: DiagnosticScanPayload) {
        let processing = false;
        const changeThreshold = 5 * 1000;
        setInterval(async () => {
            if (processing) {
                return;
            }
            processing = true;
            const changedFiles = await this.getChangedFiles(payload.systemInfo.cwd, changeThreshold);
            const {added, modified, deleted} = changedFiles;
            const total = modified.length + added.length + deleted.length;
            // 控制不要发送太多
            if (total > 0 && total < 100) {
                await this.handleChangedFiles(payload, changedFiles);
            }
            processing = false;
        }, changeThreshold);
    }

    protected abstract handleChangedFiles(
        payload: DiagnosticScanPayload,
        changedFiles: DiagnosticScanChangedFiles
    ): Promise<void>;

    protected abstract init(payload: DiagnosticScanPayload): Promise<void>;

    protected abstract beforeGet(payload: DiagnosticScanPayload & {context: ActivationContext}): void;
}
