import parseGitDiff, {AnyChunk} from 'parse-git-diff';
import {Range} from '@comate/plugin-shared-internals';

// @ts-expect-error https://github.com/yeonjuan/parse-git-diff/issues/29
const parse = parseGitDiff as typeof parseGitDiff.default;

export class GitDiffChecker {
    private readonly diffText;

    private readonly parse: boolean;

    private ranges: Range[] | null = null;

    private constructor(diffText: string, parse: boolean) {
        this.diffText = diffText;
        // 根本不打算考虑差异的时候（未跟踪的文件之类的），这个为`false`会让所有行范围检查通过
        this.parse = parse;
    }

    static fromDiffText(diffText: string): GitDiffChecker {
        return new GitDiffChecker(diffText, true);
    }

    static skip(): GitDiffChecker {
        return new GitDiffChecker('', false);
    }

    isRangeIntersect(range: Range): boolean {
        if (!this.parse) {
            return true;
        }

        const isIntersect = (x: Range, y: Range) => {
            return x.start <= y.end && y.start <= x.end;
        };
        return this.getRanges().some(v => isIntersect(v, range));
    }

    isRangesIntersect(ranges: Range[]): boolean {
        if (!this.parse) {
            return true;
        }

        return ranges.some(v => this.isRangeIntersect(v));
    }

    private getRanges() {
        if (this.ranges) {
            return this.ranges;
        }

        if (!this.diffText) {
            this.ranges = [];
            return this.ranges;
        }

        const file = parse(this.diffText).files.at(0);

        if (!file) {
            this.ranges = [];
            return this.ranges;
        }

        const toRange = (chunk: AnyChunk): Range | Range[] => {
            switch (chunk.type) {
                case 'BinaryFilesChunk':
                    return [];
                default:
                    return {start: chunk.toFileRange.start, end: chunk.toFileRange.start + chunk.toFileRange.lines};
            }
        };
        this.ranges = file.chunks.flatMap(toRange);
        return this.ranges;
    }
}
