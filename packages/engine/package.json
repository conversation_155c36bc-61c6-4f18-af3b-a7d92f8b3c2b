{"name": "@comate/plugin-engine", "version": "0.9.2", "type": "module", "engines": {"node": ">=20.10.0"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js"}}, "files": ["dist"], "contributors": ["zhanglili01 <<EMAIL>>"], "license": "MIT", "publishConfig": {"access": "public"}, "scripts": {"clean": "rm -rf dist", "build": "tsc", "lint": "eslint --max-warnings=0 src --fix", "bundle": "tsc && rollup -c rollup.config.js"}, "dependencies": {"@comate/plugin-shared-internals": "^0.9.2", "execa": "^8.0.1", "fuse.js": "^7.0.0", "import-meta-resolve": "^4.0.0", "p-event": "^6.0.1", "parse-git-diff": "^0.0.14"}, "devDependencies": {"@comate/plugin-host": "^0.9.2", "@rollup/plugin-json": "^6.1.0", "@types/node": "18.15.0", "eslint": "^8.56.0", "inquirer": "^9.2.12", "typescript": "^5.3.2"}, "peerDependencies": {"@comate/plugin-host": "^0.9.2"}}