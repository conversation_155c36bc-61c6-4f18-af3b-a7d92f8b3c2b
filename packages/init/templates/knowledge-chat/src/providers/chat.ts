import {
    SkillProvider,
    FunctionParameterDefinition,
    TaskProgressChunk,
    TextModel,
    StringChunkStream,
} from '@comate/plugin-host';

const promptTemplate = `
请根据以下背景知识：

{{trimedKnowledge}}

回答问题：

{{query}}
`;

export class ChatSkillProvider extends SkillProvider {
    static skillName = 'answerQuestion';

    static displayName = '<% knowledgeCategory %>智能问答';

    static description = '智能解答关于<% knowledgeCategory %>的相关问题';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const {query} = this.currentContext;
        const stream = new StringChunkStream();

        yield stream.flush('正在思考中，请耐心等候...');

        try {
            const knowledge = await this.retriever.knowledgeFromQuery(
                query,
                {
                    knowledgeSets: [
                        {uuid: '<% knowledgeSetUuid %>', type: 'NORMAL'},
                    ],
                }
            );

            const trimedKnowledge = this.trimKnowledge(knowledge, 8);

            const prompt = this.llm.createPrompt(
                knowledge.length ? promptTemplate : '{{query}}',
                {trimedKnowledge, query}
            );

            const chunks = this.llm.askForTextStreaming(prompt, {model: TextModel.Default});

            yield stream.flushReplaceLast('');

            for await (const chunk of chunks) {
                yield stream.flush(chunk);
            }
        }
        catch (error) {
            yield stream.flushReplaceLast('出现错误，请稍后再试');
        }
    }

    private trimKnowledge(list: string[], maxCount = 6, maxLength = 10000) {
        const state = {
            length: 0,
        };
        const output: string[] = [];
        for (const item of list) {
            if (state.length + item.length > maxLength || output.length >= maxCount) {
                break;
            }
            output.push(item);
            state.length += item.length;
        }
        return output;
    }
}
