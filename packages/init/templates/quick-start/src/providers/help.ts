import {FallbackProvider, TaskProgressChunk, TaskProgressChunkStream} from '@comate/plugin-host';

export class HelpFallbackProvider extends FallbackProvider {
    static description = '介绍插件能力与开发方式';

    async *handleQuery(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new TaskProgressChunkStream();
        yield stream.flush('你好，我是示例插件，现在还没有具体的实现。\n\n');
        yield stream.flush('能看到这些文字，你已经成功地运行了插件。以下是当前的环境信息：\n\n');
        yield stream.flush(`- 打开文件：${this.currentContext.activeFileName}\n`);
        yield stream.flush(`- 文件语言：${this.currentContext.activeFileLanguage}\n`);
        yield stream.flush(`- 提示词：${this.currentContext.query}`);
        yield stream.flush('需要进一步开发功能，请参考`README.md`文件。\n\n');
    }
}
