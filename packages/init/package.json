{"name": "@comate/plugin-init", "version": "0.9.2", "engines": {"node": ">=20.10.0"}, "exports": {".": "./dist/index.js"}, "files": ["dist", "templates"], "bin": "./dist/index.js", "contributors": ["zhanglili01 <<EMAIL>>"], "license": "MIT", "publishConfig": {"access": "public"}, "scripts": {"clean": "rm -rf dist", "build": "tsc", "lint": "eslint --max-warnings=0 src --fix"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/node": "18.15.0", "eslint": "^8.56.0", "typescript": "^5.3.2"}, "dependencies": {"@inquirer/prompts": "^4.2.1", "fs-extra": "^11.2.0", "globby": "13.2.2", "tsx": "^4.7.1"}}