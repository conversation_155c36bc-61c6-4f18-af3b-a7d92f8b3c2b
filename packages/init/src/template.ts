import fs from 'node:fs/promises';

export async function readTemplateFile(file: string, args: Record<string, any>) {
    const template = await fs.readFile(file, 'utf-8');
    return template.replaceAll(
        /<%\s*([a-zA-Z]+)\s*%>/g,
        (match, key) => {
            const value = args[key];
            if (!value) {
                throw new Error(`Template variable "${key}" not found when process ${file}`);
            }
            return value;
        }
    );
}
