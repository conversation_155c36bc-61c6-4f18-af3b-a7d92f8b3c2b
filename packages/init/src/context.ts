import {input, select} from '@inquirer/prompts';

export interface Context {
    pluginName: string;
    template: string;
    knowledgeCategory: string;
    knowledgeSetUuid: string;
}

const pluginNamePrompt = {
    message: '插件名称（foo-bar格式）：',
    default: 'my-comate-plugin',
    validate: (value: string) => /^[a-z]+(-[a-z]+)*$/.test(value),
};
const templatePrompt = {
    message: '模板类型（请选择合适的模板）：',
    choices: [
        {name: '快速开始（简单的插件模板，可在此基础上开发定制能力）', value: 'quick-start'},
        {name: '知识智能问答（通过绑定知识集快速提供对话的问答和生成）', value: 'knowledge-chat'},
    ],
};
const knowledgeCategoryPrompt = {
    message: '知识分类（用来拼接“智能解答关于XXX的问题”的描述）：',
    validate: (value: string) => !!value,
};

const knowledgeSetUuidPrompt = {
    message: '知识集UUID（从URL中获取）：',
    validate: (value: string) => {
        return /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(value);
    },
};

export async function collectContext(): Promise<Context> {
    const pluginName = await input(pluginNamePrompt);
    const template = await select(templatePrompt);

    if (template === 'quick-start') {
        return {pluginName, template, knowledgeCategory: '', knowledgeSetUuid: ''};
    }

    const knowledgeCategory = await input(knowledgeCategoryPrompt);
    const knowledgeSetUuid = await input(knowledgeSetUuidPrompt);
    return {pluginName, template, knowledgeCategory, knowledgeSetUuid};
}
