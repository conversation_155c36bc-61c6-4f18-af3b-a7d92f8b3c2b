#!/usr/bin/env node
import path from 'node:path';
import fs from 'fs-extra';
import {readTemplateFile} from './template';
import {Context, collectContext} from './context';

const templatesDirectory = path.join(__dirname, '..', 'templates');
const sharedTemplateDirectory = path.join(templatesDirectory, 'shared');

async function copyDirectory(from: string, to: string, args: Context) {
    const {globby} = await import('globby');
    const files = await globby('**', {cwd: from, ignore: ['package.json', 'gitignore']});
    for (const file of files) {
        const content = await readTemplateFile(path.join(from, file), args);
        await fs.outputFile(path.join(to, file), content);
    }
}

async function main() {
    const context = await collectContext();
    const templateDirectory = path.join(templatesDirectory, context.template);

    if (!fs.existsSync(templateDirectory)) {
        console.error(`Invalid project template "${context.template}"`);
        process.exit(400);
    }

    const directory = context.pluginName;
    if (fs.existsSync(directory)) {
        console.error(`Directory ${directory} already exists`);
        process.exit(1);
    }

    await fs.ensureDir(directory);
    // 复制`shared`共享内容
    await copyDirectory(sharedTemplateDirectory, directory, context);
    // 复制插件特有内容
    await copyDirectory(templateDirectory, directory, context);
    // 合并`package.json`
    const packageJson = Object.assign(
        JSON.parse(await readTemplateFile(path.join(sharedTemplateDirectory, 'package.json'), context)),
        JSON.parse(await readTemplateFile(path.join(templateDirectory, 'package.json'), context))
    );
    await fs.writeFile(path.join(directory, 'package.json'), JSON.stringify(packageJson, null, 2) + '\n');
    // 复制`.gitignore`
    await fs.copyFile(path.join(sharedTemplateDirectory, 'gitignore'), path.join(directory, '.gitignore'));
    // eslint-disable-next-line no-console
    console.log(`Comate插件已经创建，请进入${directory}目录并参考README.md继续开发`);
}

main().catch(console.error);
