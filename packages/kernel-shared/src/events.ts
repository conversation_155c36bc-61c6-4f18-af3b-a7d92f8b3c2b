// 初始化相关事件
export const KERNEL_CONFIG_FETCH_REQUEST = 'kernel/kernelConfig/fetch';
export const KERNEL_EXTENSION_CONFIG_FETCH_REQUEST = 'kernel/extensionConfig/fetch';
export const KERNEL_PASSTHROUGH_MESSAGE_TO_IDE_REQUEST = 'kernel/passthroughMessage/toIDE';
export const WEBVIEW_PASSTHROUGH_MESSAGE_TO_IDE_REQUEST = 'webview/passthroughMessage/toIDE';
export const IDE_PASSTHROUGH_MESSAGE_TO_KERNEL_REQUEST = 'ide/passthroughMessage/toKernel';
export const IDE_PASSTHROUGH_MESSAGE_TO_WEBVIEW_REQUEST = 'ide/passthroughMessage/toWebview';
export const IDE_CONFIG_DID_CHANGE_REQUEST = 'ide/kernelConfig/didChange';
export const IDE_EXTENSION_CONFIG_DID_CHANGE_REQUEST = 'ide/extensionConfig/didChange';
export const KERNEL_SYSTEM_INITIALIZED_NOTIFICATION = 'kernel/system/initialized';

export const KERNEL_GRAPH_CODEINDEXREADY_EVENT = 'kernel/graph/codeIndexReady';
export const KERNEL_GRAPH_CODEINDEXCANCEL_EVENT = 'kernel/graph/codeIndexCancel';
export const IDE_GRAPH_CODEINDEXCOMPLETED_EVENT = 'ide/graph/codeIndexCompleted';
export const IDE_GRAPH_INDEXREADY_EVENT = 'ide/graph/indexReady';

// passthrough 内部事件
export const PT_KERNEL_SYSTEM_INITIALIZED_REQUEST = 'pt/kernel/system/initialized';
export const PT_WEBVIEW_SYSTEM_INITIALIZED_REQUEST = 'pt/webview/system/initialized';
export const PT_WEBVIEW_DEMO_REQUEST = 'pt/webview/passthroughDemo/send';
export const PT_WEBVIEW_STREAM_DEMO_REQUEST = 'pt/webview/passthroughDemo/streamSend';
export const PT_KERNEL_DEMO_REQUEST = 'pt/kernel/passthroughDemo/ping';
