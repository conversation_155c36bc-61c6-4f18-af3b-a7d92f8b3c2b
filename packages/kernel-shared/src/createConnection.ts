import {
    createConnection,
    ProposedFeatures,
    InitializeParams,
    TextDocumentSyncKind,
    InitializeResult,
    StreamMessageReader,
    StreamMessageWriter,
    TextDocuments,
} from 'vscode-languageserver/node.js';
// import {Logger} from '@comate/plugin-shared-internals/logger';
import {KernelService} from './service/KernelService.js';

// TODO 和版本号联动，编译的时候替换下
// const VERSION = '1.0.0';
// const logger = new Logger('idea');
// const kernelLogger = logger.getLoggerAsRole('kernel');

// function parseArgs() {
//     const args = process.argv.slice(2);
//     if (args.includes('-v') || args.includes('--version')) {
//         console.log(`Version: ${VERSION}`);
//         process.exit(0);
//     }
// }

// parseArgs();

export function initConnection(kernelService: KernelService) {
    const connection = process.env.NODE_ENV === 'development'
        ? createConnection(
            ProposedFeatures.all
        )
        : createConnection(
            ProposedFeatures.all,
            new StreamMessageReader(process.stdin as NodeJS.ReadableStream),
            new StreamMessageWriter(process.stdout as NodeJS.WritableStream)
        );
    kernelService.init(connection);
    let hasConfigurationCapability = false;
    let hasWorkspaceFolderCapability = false;
    let hasDiagnosticRelatedInformationCapability = false;

    connection.onInitialize((params: InitializeParams) => {
        const capabilities = params.capabilities;
        kernelService.setInitializeParams(params);

        // Does the client support the `workspace/configuration` request?
        // If not, we fall back using global settings.
        hasConfigurationCapability = !!(
            capabilities.workspace && !!capabilities.workspace.configuration
        );
        hasWorkspaceFolderCapability = !!(
            capabilities.workspace && !!capabilities.workspace.workspaceFolders
        );
        hasDiagnosticRelatedInformationCapability = !!(
            capabilities.textDocument
            && capabilities.textDocument.publishDiagnostics
            && capabilities.textDocument.publishDiagnostics.relatedInformation
        );

        const result: InitializeResult = {
            capabilities: {
                textDocumentSync: TextDocumentSyncKind.Incremental,
                // Tell the client that this server supports code completion.
                completionProvider: {
                    resolveProvider: true,
                },
                diagnosticProvider: {
                    interFileDependencies: false,
                    workspaceDiagnostics: false,
                },
            },
        };
        if (hasWorkspaceFolderCapability) {
            result.capabilities.workspace = {
                workspaceFolders: {
                    supported: true,
                },
            };
        }
        connection.console.log('init----------1');
        // kernelLogger.setLogInstance(connection.console);
        // kernelLogger.info('init----------2');
        return result;
    });

    connection.onInitialized(() => {
        // kernelLogger.setLogInstance(connection.console);
        // kernelLogger.info('init----------3');
        // kernelLogger.info('init----------4');
        kernelService.LSPInitialized();
    });

    // Listen on the connection
    connection.listen();

    return kernelService;
}
