import 'reflect-metadata';
import fs from 'node:fs/promises';
import * as vscodeUri from 'vscode-uri';
import {initConnection} from './createConnection.js';
import {KernelService} from './service/KernelService.js';
import {IKernel} from './types/config.js';
import {iocContainer} from './context.js';
import {ConfigService} from './service/ConfigService.js';
import {HttpService} from './service/HttpService.js';
import {ConnectService} from './service/ConnectService.js';
import {StorageService} from './service/StorageService/index.js';
import {GitService} from './service/GitService.js';
import {LoggerService} from './service/LoggerService.js';
import {activate} from './activate.js';
import {
    ConfigServiceName,
    HttpServiceName,
    ConnectServiceName,
    StorageServiceName,
    GitServiceName,
    LoggerServiceName,
    KernelServiceName,
} from './service/serviceName.js';

export type {ConfigService} from './service/ConfigService.js';
export type {ConnectService} from './service/ConnectService.js';
export type {GitService} from './service/GitService.js';
export type {HttpService} from './service/HttpService.js';
export type {KernelService} from './service/KernelService.js';
export type {LoggerService} from './service/LoggerService.js';
export type {StorageService} from './service/StorageService/index.js';
export type {UserService} from './service/UserService.js';

export * from './events.js';
export * from './types/index.js';
export * from './types/public.js';
export * from './lib/index.js';
export {AxiosStatic} from 'axios';

activate();
const configService = iocContainer.get<ConfigService>(ConfigServiceName);
const httpService = iocContainer.get<HttpService>(HttpServiceName);
const connectService = iocContainer.get<ConnectService>(ConnectServiceName);
const storageService = iocContainer.get<StorageService>(StorageServiceName);
const gitService = iocContainer.get<GitService>(GitServiceName);
const loggerService = iocContainer.get<LoggerService>(LoggerServiceName);
export const kernelService = iocContainer.get<KernelService>(KernelServiceName);

export let kernel = {
    connect: {
        initConnection: () => initConnection(kernelService),
    },
    logger: loggerService.logger,
    uri: vscodeUri.URI,
    fs: fs,
} as IKernel;

export let env = {} as IKernel['env'];
export let config = {} as IKernel['config'];
export let git = {} as IKernel['git'];
export let uri = vscodeUri.URI;
export let connect = {} as IKernel['connect'];
export let http = {} as IKernel['http'];
export let logger = {} as IKernel['logger'];
export let storage = {} as IKernel['storage'];
kernelService.onInitialized(() => {
    env = {
        appRoot: configService.appRoot,
        appHost: configService.appHost,
        language: configService.language,
        deviceId: configService.deviceId,
        ideName: configService.ideName,
        ideSeries: configService.ideSeries,
        ideVersion: configService.ideVersion,
        extensionVersion: configService.extensionVersion,
        environment: configService.environment,
        edition: configService.edition,
        themeColor: configService.themeColor,
        httpHost: configService.httpHost,
        serverEndpointPrefix: configService.serverEndpointPrefix,
        customized: configService.customized,
        customizedUrl: configService.customizedUrl,
        isSaaS: configService.isSaaS,
        isInternal: configService.isInternal,
        isPoc: configService.isPoc,
        isVSCode: configService.isVSCode,
        /** @deprecated 不该从这个namespace读取*/
        workspaceInfo: configService.workspaceInfo,
    };
    config = {
        key: configService.key,
        license: configService.license,
        username: configService.username,
    };
    git = gitService;
    connect = {
        initConnection: () => initConnection(kernelService),
        onRequest: connectService.onRequest as any,
        sendRequest: connectService.sendRequest as any,
        sendNotification: connectService.sendNotification as any,
        onWebviewMessage: connectService.onWebviewMessage as any,
        onWebviewStreamMessage: connectService.onWebviewStreamMessage as any,
        sendWebviewMessage: connectService.sendWebviewMessage as any,
        onNotification: connectService.onNotification as any,
        // workspace: connectService.workspace,
        webviewInitialized: connectService.webviewInitialized,
    };
    http = httpService;
    logger = loggerService.logger;
    storage = storageService;
    kernel = {
        env,
        config,
        git,
        uri,
        connect,
        http,
        logger,
        storage,
        fs: fs,
    };
});
