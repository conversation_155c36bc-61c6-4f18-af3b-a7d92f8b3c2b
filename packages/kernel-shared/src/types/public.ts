import {
    IDE_CONFIG_DID_CHANGE_REQUEST,
    IDE_EXTENSION_CONFIG_DID_CHANGE_REQUEST,
    IDE_GRAPH_CODEINDEXCOMPLETED_EVENT,
    KERNEL_CONFIG_FETCH_REQUEST,
    KERNEL_EXTENSION_CONFIG_FETCH_REQUEST,
    PT_KERNEL_DEMO_REQUEST,
    PT_KERNEL_SYSTEM_INITIALIZED_REQUEST,
    PT_WEBVIEW_DEMO_REQUEST,
    PT_WEBVIEW_SYSTEM_INITIALIZED_REQUEST,
} from '../events.js';
import {ExtensionConfig, KernelConfig, ResponseBase} from './index.js';

// TODO 热更新要考虑
export interface OnRequestTypes {
    /**
     * @direction ide -> kernel
     * @description 更新基础配置
     */
    [IDE_CONFIG_DID_CHANGE_REQUEST]: (data: KernelConfig) => void;
    /**
     * @direction ide -> kernel
     * @description 更新插件在 IDE 中的配置
     */
    [IDE_EXTENSION_CONFIG_DID_CHANGE_REQUEST]: (data: ExtensionConfig) => void;

    // TODO 添加类型
    [IDE_GRAPH_CODEINDEXCOMPLETED_EVENT]: (data: any) => void;
}

// TODO 热更新要考虑
export interface SendRequestTypes {
    /**
     * @direction ide <- kernel
     * @description 获取插件在 IDE 中的配置。没有参数返回完整的配置，有参数返回指定配置项的值
     */
    [KERNEL_CONFIG_FETCH_REQUEST]: (params?: void) => ResponseBase<KernelConfig>;
    /**
     * @direction ide <- kernel
     * @description 获取基础配置。没有参数返回完整的配置，有参数返回指定配置项的值
     */
    [KERNEL_EXTENSION_CONFIG_FETCH_REQUEST]: (params?: void) => ResponseBase<ExtensionConfig>;
}
// TODO webview的事件，这里只声明webview到kernel的

// 上报打点的信息类型
export enum LoggerUploaderCategoryType {
    kernelInnerInit = 'kernelInnerInit',
    KernelInitError = 'KernelInitError', // kernel 初始化失败
    KernelExitError = 'KernelExitError', // kernel 退出
    KernelShutdownError = 'KernelShutdownError', // kernel关闭
    LogUploaderError = 'LogUploaderError', // 日志上传失败
}

/** 只用来webview和kernel之间使用passthroughMessage互相通信时内部事件的类型定义，与上面的 OnRequestTypes、SendRequestTypes 不是一个概念*/
export interface PTEventTypes {
    /**
     * @direction kernel -> webview
     * @description kernel 初始化成功后通知 webview
     */
    [PT_KERNEL_SYSTEM_INITIALIZED_REQUEST]: (params: {time: Date}) => void;
    /**
     * @direction webview -> kernel
     * @description webview 初始化成功后通知 kernel
     */
    [PT_WEBVIEW_SYSTEM_INITIALIZED_REQUEST]: (params: {time: string}) => Promise<{receive: boolean}>;
    /**
     * @direction webview -> kernel
     * @description 接受一个来自webview 的消息，然后返回该条消息，为了演示通信
     */
    [PT_WEBVIEW_DEMO_REQUEST]: (params: string) => string;
    /**
     * @direction kernel -> webview
     * @description 想webview定时发送消息，为了演示通信
     */
    [PT_KERNEL_DEMO_REQUEST]: (params: {time: Date}) => void;
}
