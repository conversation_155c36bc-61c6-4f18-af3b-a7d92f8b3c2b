// kernel与ide交互的类型声明，非LSP

/**
 * @direction ide <- kernel
 * @description kernel 透传消息给 webview
 * @param params 透传的消息
 * @return 返回透传的消息
 */
export type PassthroughMessageToIDEFromKernelEvent<T> = (
    params: PassthroughMessageParams<T>
) => PassthroughMessageResponse<T>;

// webview -> ide
/**
 * @direction webview -> ide
 * @description webview 透传消息给 kernel
 * @param params 透传的消息
 * @return 返回透传的消息
 */
export type PassthroughMessageToIDEFromWebviewEvent<T, K> = (
    params: PassthroughMessageParams<T>
) => Promise<PassthroughMessageResponse<K>>;

// webview <- ide
/**
 * @direction webview <- ide
 * @description kernel 透传消息给 webview
 * @param params 透传的消息
 * @return 返回透传的消息
 */
export type PassthroughMessageToWebviewFromIDEEvent<T> = (
    params: PassthroughMessageParams<T>
) => Promise<PassthroughMessageResponse<T>>;

/**
 * @direction ide -> kernel
 * @description webview 透传消息给 kernel
 * @param params 透传的消息
 * @return 返回透传的消息
 */
export type PassthroughMessageToKernelFromIDEEvent<T> = (
    params: PassthroughMessageParams<T>
) => Promise<PassthroughMessageResponse<T>>;

export interface ResponseBase<T> {
    code: number;
    message: string;
    data: T;
    // TODO
    // sessionId: string;
}

/** ide 透传 webview 和 kernel 之间的消息，包括流式也可以用这个类型 */
export interface PassthroughMessageParams<T> {
    type: 'request';
    /** 这个是被透传的事件名，本身passhrough是个通道 */
    event: string;
    msgId: string;
    seqId: number;
    /** 业务级别的会话 ID */
    // sessionId: string;
    data: T;
}

export interface PassthroughMessageResponse<T> {
    type: 'response' | 'stream' | 'error';
    event: string;
    msgId: string;
    // sessionId: string;
    /** 支持流式消息，多段发送来以此来聚合 */
    seqId: number;
    data: T;
    /** 是否为最后一条消息 */
    end: boolean;
}

// ide <-> kernel 底层通信的类型
export interface StreamRequest<T> {
    data: T;
    seqId: number;
}

export interface StreamResponse<T> extends ResponseBase<T> {
    seqId: number;
    end: boolean;
}
