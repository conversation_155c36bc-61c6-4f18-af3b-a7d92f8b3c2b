import {RemoteWorkspace, WorkspaceFolder} from 'vscode-languageserver';
import fs from 'node:fs/promises';
import * as vscodeUri from 'vscode-uri';
import git from 'isomorphic-git';
import type {HttpService} from '../service/HttpService.js';
import type {StorageService} from '../service/StorageService/index.js';
import type {KernelService} from '../service/KernelService.js';
import type {ConnectService} from '../service/ConnectService.js';
import type {GitService} from '../service/GitService.js';
export type Env =
    & KernelConfig
    & EnvCondition
    & {workspaceInfo: WorkspaceInfo};
export type Config = Pick<ExtensionConfig, 'license' | 'username'> & {key?: string};
export type Git = GitService;
export type Uri = typeof vscodeUri.URI;
export interface Connect {
    initConnection: () => KernelService;
    onRequest: typeof ConnectService.prototype.onRequest;
    onNotification: typeof ConnectService.prototype.onNotification;
    sendRequest: typeof ConnectService.prototype.sendRequest;
    sendNotification: typeof ConnectService.prototype.sendNotification;
    sendWebviewMessage: typeof ConnectService.prototype.sendWebviewMessage;
    onWebviewMessage: typeof ConnectService.prototype.onWebviewMessage;
    onWebviewStreamMessage: typeof ConnectService.prototype.onWebviewStreamMessage;
    webviewInitialized: typeof ConnectService.prototype.webviewInitialized;
    // workspace: RemoteWorkspace;
}

export interface WorkspaceInfo {
    workspaceFolders: WorkspaceFolder[] | null | undefined;
    rootUri: string | undefined;
    rootPath: string;
}

export interface IKernel {
    fs: typeof fs;
    env: Env;
    git: Git;
    config: Config;
    uri: Uri;
    connect: Connect;
    http: HttpService;
    logger: {
        log: (...args: any[]) => void;
        trace: (...args: any[]) => void;
        info: (...args: any[]) => void;
        warn: (...args: any[]) => void;
        error: (...args: any[]) => void;
    };
    storage: StorageService;
}

export interface EnvCondition {
    isSaaS: boolean;
    isInternal: boolean;
    isPoc: boolean;
    isVSCode: boolean;
}

export enum ENVIRONMENT {
    DEVELOPMENT = 'development',
    PRODUCTION = 'production',
    TEST = 'test',
}
export enum EDITION {
    SAAS = 'saas',
    INTERNAL = 'internal',
    POC = 'poc',
}
export type DeviceId = string;
export enum IDE_NAME {
    VSCode = 'vscode',
    JetBrains = 'jetbrains',
    VS = 'vs',
    Xcode = 'xcode',
    Eclipse = 'eclipse',
}
export type IdeVersion = string;

// TODO 要给企业单独一个字段，在poc下的一级，参考gitee
export interface KernelConfig {
    /** IDE 应用本身所在的目录 */
    appRoot: string;
    /** IDE 模式 */
    appHost: 'desktop' | 'web';
    /** IDE 使用的语言 IETF 标准 */
    language: string;
    /** 设备 ID */
    deviceId: DeviceId;
    /** IDE 名称 */
    ideName: IDE_NAME; // jetbrains vscode
    /** IDE 系列 */
    ideSeries: string; // jetbrains-android 之类的
    /** IDE 版本 */
    ideVersion: IdeVersion;
    /** 当前comate版本 */
    extensionVersion: string;
    /** 执行环境 */
    environment: ENVIRONMENT;
    /** 厂内外等版本标识，同 platform */
    edition: EDITION;
    /** 主题色 */
    themeColor: 'light' | 'dark';
    /** 服务接口地址 */
    httpHost?: string;
    /** 代理服务 */
    proxyUrl?: string;
    /** @deprecated */
    serverEndpointPrefix?: string;
    /** @deprecated */
    customized?: string;
    /** @deprecated */
    customizedUrl?: string;
}

// TODO 需要区分不同的ide
export interface ExtensionConfig {
    /** 用户名称 */
    username?: string;
    /** 证书 */
    license?: string;
    /** 代理服务器地址 */
    privateService?: string;
    inlineSuggestionMode: 'extremeFast' | 'fast' | 'balance' | 'accurate' | 'extremeAccurate';
    linePreferMode: 'auto' | 'singleLine' | 'multiLine';
    enableQuickFix: boolean;
    codelensDisplayMode: 'expand' | 'collapse';
    enableCodelens: {
        enableInlineUnitTest: boolean;
        enableInlineExplain: boolean;
        enableInlineDocstring: boolean;
        enableInlineSplit: boolean;
        enableInlineComment: boolean;
        enableInlineOptimize: boolean;
    };
    enableSecurityEnhancement: boolean;
    unitTestFrameworkForGo: string; // todo 补一下枚举
    unitTestFrameworkForJava: string; // todo 补一下枚举
    'unitTestFrameworkForJs/Ts': string; // todo 补一下枚举
    unitTestFrameworkForPython: string; // todo 补一下枚举
    'unitTestMockForC/C++': string; // todo 补一下枚举
    unitTestMockForGo: string; // todo 补一下枚举
    unitTestMockForJava: string; // todo 补一下枚举
    'unitTestMockForJs/Ts': string; // todo 补一下枚举
    unitTestMockForPython: string; // todo 补一下枚举
    langSuggestion: {
        [key: string]: boolean;
    };
}

export const KERNEL_KEYS = {
    APP_ROOT: 'appRoot',
    APP_HOST: 'appHost',
    LANGUAGE: 'language',
    DEVICE_ID: 'deviceId',
    IDE_NAME: 'ideName',
    IDE_VERSION: 'ideVersion',
    ENVIRONMENT: 'environment',
    EDITION: 'edition',
    THEME_COLOR: 'themeColor',
    HTTP_HOST: 'httpHost',
    HTTP_PROXY: 'httpProxy',
};

export const EXTENSION_KEYS = {
    LICENSE: 'license',
    USER_NAME: 'userName',
    ENABLE_SECURITY_ENHANCEMENT: 'enableSecurityEnhancement',
    LANG_SUGGESTION: 'langSuggestion',
    PRIVATE_SERVICE: 'privateService',
};
