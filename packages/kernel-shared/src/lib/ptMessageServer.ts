import type {PassthroughMessageParams, PassthroughMessageResponse} from '../types/index.js';

type RequestHandler<T = any, R = any> = (data: T) => Promise<R> | R;
type StreamRequestHandler<T = any, R = any> = (data: T) => AsyncIterableIterator<R>;

interface MessageTransport {
    send<T>(message: PassthroughMessageResponse<T>): void;
}

export class PTMessageServer {
    private handlers = new Map<string, RequestHandler | StreamRequestHandler>();

    constructor(private transport: MessageTransport) {}

    // 注册普通请求处理器
    register<T>(event: string, handler: T) {
        this.handlers.set(event, handler as RequestHandler);
        return () => {
            this.handlers.delete(event);
        };
    }

    // 注册流式请求处理器
    registerStream<T = any, R = any>(event: string, handler: StreamRequestHandler<T, R>) {
        this.handlers.set(event, handler);
        return () => {
            this.handlers.delete(event);
        };
    }

    // 处理来自客户端的消息
    async handleRequest(message: PassthroughMessageParams<any>) {
        const {msgId, event, data} = message;
        const handler = this.handlers.get(event);

        if (!handler) {
            this.sendError(msgId, event, `No handler registered for event: ${event}`);
            return;
        }

        try {
            const result = handler(data);

            // 检查返回值是否是 AsyncIterator
            if (result && typeof (result as any)[Symbol.asyncIterator] === 'function') {
                await this.handleStreamResponse(msgId, event, result as AsyncIterableIterator<any>);
            }
            else {
                // 普通请求处理
                const response = await (result as Promise<any>);
                this.sendResponse(msgId, event, response);
            }
        }
        catch (error) {
            this.sendError(msgId, event, error instanceof Error ? error.message : 'Unknown error');
        }
    }

    private async handleStreamResponse(msgId: string, event: string, iterator: AsyncIterableIterator<any>) {
        let seqId = 0;
        try {
            for await (const chunk of iterator) {
                this.sendStreamChunk(msgId, event, chunk, seqId++, false);
            }
            // 发送结束标记
            this.sendStreamChunk(msgId, event, null, seqId, true);
        }
        catch (error) {
            this.sendError(msgId, event, error instanceof Error ? error.message : 'Stream processing error');
        }
    }

    private sendResponse<T>(msgId: string, event: string, data: T) {
        const response: PassthroughMessageResponse<T> = {
            type: 'response',
            msgId,
            event,
            seqId: 0,
            data,
            end: true,
        };
        this.transport.send(response);
    }

    private sendStreamChunk<T>(msgId: string, event: string, data: T, seqId: number, end: boolean) {
        const response: PassthroughMessageResponse<T> = {
            type: 'stream',
            msgId,
            event,
            data,
            seqId,
            end,
        };
        this.transport.send(response);
    }

    private sendError(msgId: string, event: string, error: string) {
        const response: PassthroughMessageResponse<string> = {
            type: 'error',
            msgId,
            event,
            seqId: 0,
            data: error,
            end: true,
        };
        this.transport.send(response);
    }
}
