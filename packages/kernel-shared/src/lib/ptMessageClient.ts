import type {PassthroughMessageParams, PassthroughMessageResponse} from '../types/index.js';

interface TimerAPI {
    setTimeout(callback: () => void, ms: number): number | NodeJS.Timeout;
    clearTimeout(id: number | NodeJS.Timeout): void;
}

interface StreamChunk<T = any> {
    event: string;
    data: T;
    seqId: number;
}

// 定义发送和监听的接口
interface MessageTransport {
    send<T>(message: PassthroughMessageParams<T>): void;
}

// client.ts
class StreamIterator<T = any> implements AsyncIterator<StreamChunk<T>> {
    private resolveQueue: ((value: IteratorResult<StreamChunk<T>>) => void)[] = [];
    private chunkQueue: StreamChunk<T>[] = [];
    private isEnded = false;
    private error: Error | null = null;

    push(chunk: StreamChunk<T>) {
        if (this.isEnded) {
            return;
        }

        if (this.resolveQueue.length > 0) {
            const resolve = this.resolveQueue.shift()!;
            resolve({value: chunk, done: false});
        }
        else {
            this.chunkQueue.push(chunk);
        }
    }

    end() {
        this.isEnded = true;
        for (const resolve of this.resolveQueue) {
            resolve({value: undefined, done: true});
        }
        this.resolveQueue = [];
    }

    reject(error: Error) {
        this.error = error;
        this.end();
    }

    async next(): Promise<IteratorResult<StreamChunk<T>>> {
        if (this.error) {
            throw this.error;
        }

        if (this.chunkQueue.length > 0) {
            const chunk = this.chunkQueue.shift()!;
            return {value: chunk, done: false};
        }

        if (this.isEnded) {
            return {value: undefined, done: true};
        }

        return new Promise(resolve => {
            this.resolveQueue.push(resolve);
        });
    }

    [Symbol.asyncIterator]() {
        return this;
    }
}

export class PTMessageClient {
    private timeout: number = 30000;
    private pendingRequests = new Map<string, {
        resolve?: (value: any) => void;
        reject?: (reason: any) => void;
        iterator?: StreamIterator;
        timeoutId: number | NodeJS.Timeout;
        chunks?: Map<number, any>;
        nextSeqId?: number;
    }>();

    constructor(private transport: MessageTransport, private timer: TimerAPI) {
    }

    private generateMsgId(): string {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    async send<T = any, R = any>(event: string, data: T): Promise<R> {
        const msgId = this.generateMsgId();

        return new Promise<R>((resolve, reject) => {
            const timeoutId = this.timer.setTimeout(() => {
                this.pendingRequests.delete(msgId);
                reject(new Error('Request timeout'));
            }, this.timeout);

            this.pendingRequests.set(msgId, {
                resolve,
                reject,
                timeoutId,
            });

            const message: PassthroughMessageParams<T> = {
                type: 'request',
                msgId,
                event,
                data,
                seqId: 0,
            };

            this.transport.send(message);
        });
    }

    streamSend<T = any, R = any>(event: string, data: T): AsyncIterableIterator<StreamChunk<R>> {
        const msgId = this.generateMsgId();
        const iterator = new StreamIterator<R>();

        const timeoutId = this.timer.setTimeout(() => {
            this.pendingRequests.delete(msgId);
            iterator.reject(new Error('Request timeout'));
        }, this.timeout);

        this.pendingRequests.set(msgId, {
            iterator,
            timeoutId,
            chunks: new Map(),
            nextSeqId: 0,
        });

        const message: PassthroughMessageParams<T> = {
            type: 'request',
            msgId,
            event,
            data,
            seqId: 0,
        };

        this.transport.send(message);
        return iterator;
    }

    private processChunks(msgId: string, isEnd?: boolean) {
        const pending = this.pendingRequests.get(msgId);
        if (!pending?.iterator || !pending.chunks || !pending.nextSeqId) {
            return;
        }

        while (pending.chunks.has(pending.nextSeqId)) {
            const chunk = pending.chunks.get(pending.nextSeqId);
            pending.iterator.push({
                data: chunk.data,
                event: chunk.event,
                seqId: pending.nextSeqId,
            });

            pending.chunks.delete(pending.nextSeqId);
            pending.nextSeqId++;
        }

        if (isEnd && pending.chunks.size === 0) {
            this.timer.clearTimeout(pending.timeoutId);
            this.pendingRequests.delete(msgId);
            pending.iterator.end();
        }
    }

    handleResponse(message: PassthroughMessageResponse<any>) {
        const pending = this.pendingRequests.get(message.msgId);
        if (!pending) {
            return;
        }

        switch (message.type) {
            case 'response':
                if (pending.resolve) {
                    this.timer.clearTimeout(pending.timeoutId);
                    this.pendingRequests.delete(message.msgId);
                    pending.resolve(message.data);
                }
                break;

            case 'stream':
                if (!pending.iterator) {
                    return;
                }

                if (message.seqId === pending.nextSeqId) {
                    pending.iterator.push({
                        data: message.data,
                        event: message.event,
                        seqId: message.seqId,
                    });
                    pending.nextSeqId!++;
                    this.processChunks(message.msgId, message.end);
                }
                else if (message.seqId > pending.nextSeqId!) {
                    pending.chunks!.set(message.seqId, {
                        data: message.data,
                        event: message.event,
                    });
                    if (message.end) {
                        this.processChunks(message.msgId, true);
                    }
                }
                break;

            case 'error':
                this.timer.clearTimeout(pending.timeoutId);
                this.pendingRequests.delete(message.msgId);
                if (pending.iterator) {
                    pending.iterator.reject(new Error(message.data));
                }
                else if (pending.reject) {
                    pending.reject(message.data);
                }
                break;
        }
    }
}
