import {inject, injectable} from 'inversify';
import * as git from 'isomorphic-git';
import fs from 'node:fs';
import {ConfigService} from './ConfigService.js';
import {ConfigServiceName} from './serviceName.js';

type FirstParameter<T> = T extends (arg: infer P) => any ? P : never;

type OmitFSAndDir<T> = T extends {fs: any, dir: string} ? Omit<T, 'fs' | 'dir'>
    : T extends {fs: any} ? Omit<T, 'fs'>
    : T extends {dir: string} ? Omit<T, 'dir'>
    : T;

type GitMethodType<T> = T extends (params: any) => infer R ? (params: OmitFSAndDir<FirstParameter<T>>) => R
    : T;

type GitType = {
    [K in keyof typeof git]: GitMethodType<typeof git[K]>;
};

@injectable()
export class GitService {
    private wrappedGit!: GitType;

    constructor(@inject(ConfigServiceName) private readonly config: ConfigService) {
        this.init();
    }

    init() {
        this.wrappedGit = Object.entries(git).reduce((acc, [key, method]) => {
            if (typeof method === 'function') {
                (acc as any)[key] = this.wrapMethod(method);
            }
            else {
                (acc as any)[key] = method;
            }
            return acc;
        }, {} as GitType);
    }

    private wrapMethod<T extends (params: any) => any>(method: T): GitMethodType<T> {
        return ((params: any) => {
            const firstParam = params as FirstParameter<T>;
            if (typeof firstParam === 'object') {
                return method({
                    ...params,
                    fs,
                    dir: this.config.workspaceInfo.rootPath,
                });
            }
            return method(params);
        }) as GitMethodType<T>;
    }

    /**
     * 克隆远程仓库
     * 自动注入文件系统和工作目录参数
     */
    public get clone() {
        return this.wrappedGit.clone;
    }

    /**
     * 将文件添加到 Git 暂存区
     * 自动注入文件系统和工作目录参数
     */
    public get add() {
        return this.wrappedGit.add;
    }

    /**
     * 创建一个新的提交
     * 自动注入文件系统和工作目录参数
     */
    public get commit() {
        return this.wrappedGit.commit;
    }

    /**
     * 将本地提交推送到远程仓库
     * 自动注入文件系统和工作目录参数
     */
    public get push() {
        return this.wrappedGit.push;
    }

    /**
     * 从远程仓库拉取更新
     * 自动注入文件系统和工作目录参数
     */
    public get pull() {
        return this.wrappedGit.pull;
    }

    /**
     * 获取提交历史记录
     * 自动注入文件系统和工作目录参数
     */
    public get log() {
        return this.wrappedGit.log;
    }

    /**
     * 获取工作区状态
     * 自动注入文件系统和工作目录参数
     */
    public get status() {
        return this.wrappedGit.status;
    }

    /**
     * 获取原始的 isomorphic-git 对象
     * 用于访问未包装的 git 方法
     */
    public get raw() {
        return git;
    }

    /**
     * 获取当前工作目录路径
     */
    public get workdir() {
        return this.config.workspaceInfo.rootPath;
    }
}
