import {
    axiosInstance,
    LicenseFullDetail,
} from '@comate/plugin-shared-internals';
import {inject, injectable} from 'inversify';
import {ConfigService} from './ConfigService.js';
import {ResponseBase} from '../types/index.js';
import {ConfigServiceName} from './serviceName.js';
// import {ConfigurationService} from './ConfigurationService.js';
// import {EnvironmentService} from './EnvironmentService.js';
// import {KernelLogger} from '../../inject.js';

// export interface UserDetailForServer {
//     uid: string;
//     username: string;
//     name: string;
//     email: string;
//     /**
//      * 企业配置
//      * enterprise 是否企业账号
//      * enableCodeSecurity 是否开启代码安全
//      */
//     enterpriseConfig?: {
//         enterprise: boolean; // true 是企业账号，非true 不是企业账号
//         enableCodeSecurity: boolean; // true 代码安全开启，非true 代码安全没有开启
//     };
// }

// async function getUserDetail(token: string): Promise<UserDetailForServer> {
//     return axiosInstance('/api/v2/api/comateplus/users', {
//         headers: {
//             'X-Username': token,
//         },
//     })
//         .then(res => res.data.data);
// }

// 获取证书类型，比如免费个人版本，only for saas
export async function getLicenseFullDetail(license: string) {
    const res = await axiosInstance.get<ResponseBase<LicenseFullDetail>>('/key/type/' + license, {
        timeout: 5000, // 针对可能的网络问题
    });
    return res.data?.data;
}

export type UserInfo = LicenseFullDetail;

// TODO 3个接口，开放平台，key/type，/config
@injectable()
export class UserService {
    userDetail: UserInfo | undefined;
    private isInitialized: boolean = false;
    private lastFetchTime: number = 0;
    private readonly CACHE_TTL = 10 * 60 * 1000; // 10分钟的缓存

    constructor(@inject(ConfigServiceName) private readonly config: ConfigService) {
        this.config.onDidChangeConfig('license', () => this.init());
    }

    async init() {
        if (this.config.isLogin && this.config.isSaaS && this.config.key) {
            this.fetchLicenseDetail();
        }
    }

    private async fetchLicenseDetail() {
        const res = await getLicenseFullDetail(this.config.key!);
        this.userDetail = res;
        this.isInitialized = true;
        this.lastFetchTime = Date.now();
        return res;
    }
    /**
     * 获取用户详细信息
     * @param forceUpdate - 是否强制更新数据，默认为false。
     *                    如果设置为true，将忽略缓存直接从服务器获取最新数据
     * @returns Promise<LicenseFullDetail> - 返回用户详细信息，如果未登录则抛出错误。
     * @throws Error - 当用户未登录时抛出错误
     */
    async getUserDetail(forceUpdate: boolean = false): Promise<LicenseFullDetail> {
        if (!this.config.isLogin) {
            throw new Error('Not login!');
        }
        const now = Date.now();
        // 如果未初始化或者缓存已过期，则重新获取数据
        if (forceUpdate || !this.isInitialized || (now - this.lastFetchTime > this.CACHE_TTL)) {
            return this.fetchLicenseDetail();
        }
        return this.userDetail!;
    }
}
