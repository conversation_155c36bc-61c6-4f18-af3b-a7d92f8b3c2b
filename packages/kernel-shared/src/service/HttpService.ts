import {injectable, inject} from 'inversify';
import axios, {AxiosInstance} from 'axios';
import {ConfigService} from './ConfigService.js';
import {LoggerService} from './LoggerService.js';
import {ConfigServiceName, LoggerServiceName} from './serviceName.js';
import {EDITION, ENVIRONMENT} from '../types/index.js';

export const SAAS_API_HOST = 'https://comate.baidu.com';
export const INTERNAL_API_HOST = 'https://comate.baidu-int.com';
export const SAAS_TEST_API_HOST = 'https://comate-cop.now.baidu.com';
export const INTERNAL_TEST_API_HOST = 'https://comate-test.now.baidu.com';

const API_MAP = {
    [EDITION.SAAS]: {
        [ENVIRONMENT.PRODUCTION]: SAAS_API_HOST,
        [ENVIRONMENT.DEVELOPMENT]: SAAS_API_HOST,
        [ENVIRONMENT.TEST]: SAAS_TEST_API_HOST,
    },
    [EDITION.INTERNAL]: {
        [ENVIRONMENT.PRODUCTION]: INTERNAL_API_HOST,
        [ENVIRONMENT.DEVELOPMENT]: INTERNAL_API_HOST,
        [ENVIRONMENT.TEST]: INTERNAL_TEST_API_HOST,
    },
    [EDITION.POC]: {
        [ENVIRONMENT.PRODUCTION]: SAAS_API_HOST,
        [ENVIRONMENT.DEVELOPMENT]: SAAS_API_HOST,
        [ENVIRONMENT.TEST]: SAAS_TEST_API_HOST,
    },
};

/**
 * kernel基本的axios，不处理业务baseurl，header等逻辑
 *
 * // TODO
 * 只提供统一的的proxy，重试，日志等逻辑，不处理业务逻辑，必要
 *
 * 所有业务复用一个instance，会有比较大的风险，注意不要污染
 *
 * baseUrl逻辑业务自己处理
 */
@injectable()
export class HttpService {
    public axiosInstance!: AxiosInstance;
    constructor(
        @inject(LoggerServiceName) private readonly loggerService: LoggerService,
        @inject(ConfigServiceName) private readonly configService: ConfigService
    ) {
    }

    init() {
        const axiosInstance = axios.create();
        axiosInstance.interceptors.request.use(async config => {
            const requestConfig = {
                baseURL: this.getHttpHost(),
                ...config,
            };
            if (this.configService.proxyUrl) {
                const {protocol, hostname: host, port} = this.getProxyUrl();
                requestConfig.proxy = {
                    host,
                    port: Number(port),
                    protocol,
                };
            }
            return requestConfig;
        });
        this.axiosInstance = axiosInstance;
    }

    /**
     * 获取 HTTP 服务器主机地址
     * @returns {string} 返回基于当前环境配置的 API 主机地址
     */
    getHttpHost() {
        const edition = this.configService.edition;
        const environment = this.configService.environment;
        if (this.configService.httpHost) {
            return this.configService.httpHost;
        }
        return API_MAP?.[edition]?.[environment] ?? INTERNAL_API_HOST;
    }

    /**
     * 解析代理 URL 配置
     * @returns {{protocol: string, hostname: string, port: string}} 返回代理服务器配置对象，包含协议、主机名和端口
     */
    getProxyUrl() {
        const proxyUrl = this.configService.proxyUrl;
        const defaults = {
            protocol: 'http',
            hostname: 'localhost',
            port: '9090',
        };

        if (!proxyUrl) {
            return defaults;
        }

        try {
            const urlWithoutProtocol = proxyUrl.replace(/^\w+:\/\//, '');
            const [hostname, port] = urlWithoutProtocol.split(':');
            const protocol = (/^(\w+):\/\//.exec(proxyUrl))?.[1] || defaults.protocol;

            return {
                protocol,
                hostname: hostname || defaults.hostname,
                port: port || defaults.port,
            };
        }
        catch (error) {
            console.error('Error parsing proxy URL:', error);
            return defaults;
        }
    }

    /**
     * 创建新的 axios 实例
     * @param {axios.CreateAxiosDefaults} config - axios 配置对象
     * @returns {AxiosInstance} 返回新创建的 axios 实例
     */
    createInstance(config: axios.CreateAxiosDefaults) {
        return axios.create(config);
    }

    get get() {
        return this.axiosInstance.get;
    }

    get post() {
        return this.axiosInstance.post;
    }

    get delete() {
        return this.axiosInstance.delete;
    }

    get options() {
        return this.axiosInstance.options;
    }

    get put() {
        return this.axiosInstance.put;
    }
}
