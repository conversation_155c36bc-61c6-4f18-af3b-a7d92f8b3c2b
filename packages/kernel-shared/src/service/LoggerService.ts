import path from 'node:path';
import os from 'node:os';
import fs from 'node:fs';
import zlib from 'node:zlib';
import dayjs from 'dayjs';
import axios, {AxiosInstance} from 'axios';
import stringify from 'safe-stable-stringify';
import {injectable} from 'inversify';
import {iocContainer} from '../context.js';
import {ConfigService} from './ConfigService.js';
import {ConfigServiceName, ConnectServiceName, UserServiceName} from './serviceName.js';
import {ConnectService} from './ConnectService.js';
import {LoggerUploaderCategoryType} from '../types/index.js';
import {URI} from 'vscode-uri';

const BASE_URL_MAPPING = {
    internal: 'https://comate.baidu-int.com',
    saas: 'https://comate.baidu.com',
    poc: 'https://comate.baidu.com',
} as const;

const ERROR_WHITE_LIST = [
    'Importing JSON module',
    'Debugger listening',
    'SESSION_FINISH',
    'COMATE_PLUS_INITIALIZED',
    'QUERY_SELECTOR',
    'COMATE_PLUS_QUERY_SELECTOR',
    'COMATE_PLUS_DIAGNOSTIC_SCAN',
];

interface LogUploaderEvent {
    category: LoggerUploaderCategoryType;
    label?: string;
    content?: unknown;
    action?: string;
    source?: string;
}

type LogType = 'event' | 'error';
type LogLevel = 'TRACE' | 'INFO' | 'WARN' | 'ERROR';
type BasicLogFunction = (category: string, ...texts: string[]) => void;
type ErrorLogFunction = (categoryOrEvent: string | LoggerUploaderCategoryType, ...texts: string[]) => void;

// TODO 配置kernel的日志路径
@injectable()
export class LoggerService {
    private logPath: string;
    private folder: string;
    private stream: fs.WriteStream;
    private readonly axiosInstance: AxiosInstance;
    private connectService?: ConnectService;
    private configService?: ConfigService;
    private currentDate: string;
    private readonly maxSize: number = 10 * 1024 * 1024; // 10MB
    private readonly maxLogLineLength = 10000;
    private currentSequence: number = 0;

    /**
     * TRACE 级别的基础日志记录函数
     * @type {BasicLogFunction}
     */
    public readonly trace: BasicLogFunction;
    /**
     * INFO 级别的基础日志记录函数
     * @type {BasicLogFunction}
     */
    public readonly info: BasicLogFunction;
    /**
     * WARN 级别的基础日志记录函数
     * @type {BasicLogFunction}
     */
    public readonly warn: BasicLogFunction;
    /**
     * ERROR 级别的增强日志记录函数，支持错误类型
     * @type {ErrorLogFunction}
     */
    public readonly error: ErrorLogFunction;

    constructor() {
        this.trace = this.createBasicLogFunction('TRACE');
        this.info = this.createBasicLogFunction('INFO');
        this.warn = this.createBasicLogFunction('WARN');
        this.error = this.createErrorLogFunction();

        this.folder = path.join(os.homedir(), '.comate-engine', 'log');
        if (!fs.existsSync(this.folder)) {
            fs.mkdirSync(this.folder, {recursive: true});
        }

        this.currentDate = this.getDateString();
        this.logPath = this.getLogPath(this.currentDate);
        this.stream = this.createWriteStream();

        this.axiosInstance = axios.create({
            baseURL: BASE_URL_MAPPING[process.env.EDITION as keyof typeof BASE_URL_MAPPING],
        });
    }
    /**
     * 获取日志记录器对象，包含所有日志记录方法
     * @returns 包含 log、trace、info、warn 和 error 方法的对象
     */
    get logger() {
        return {
            log: this.info,
            trace: this.trace,
            info: this.info,
            warn: this.warn,
            error: this.error,
        };
    }

    private getLogPath(dateStr: string, sequence: number = 0): string {
        // let logName = 'default';
        // if (this.configService) {
        //     const workspacePath = this.configService.workspaceInfo?.workspaceFolders?.[0].uri;
        //     if (workspacePath) {
        //         logName = URI.parse(workspacePath).fsPath.replace(/[\/\\]/g, '-');
        //     }
        // }
        const sequenceSuffix = sequence > 0 ? `.${sequence}` : '';
        // const logPath = path.join(this.folder, logName);
        // if (!fs.existsSync(logPath)) {
        //     fs.mkdirSync(logPath, {recursive: true});
        // }
        return path.join(this.folder, `kernel-${dateStr}${sequenceSuffix}.log`);
    }

    private createWriteStream(): fs.WriteStream {
        return fs.createWriteStream(this.logPath, {flags: 'a'});
    }

    private getDateString(past: number = 0): string {
        return dayjs().subtract(past, 'day').format('YYYY-MM-DD');
    }

    private getCurrentTime(): string {
        return dayjs().format('YYYY-MM-DD HH:mm:ss');
    }

    private connectMessage(texts: string[]): string {
        return texts
            .map(text => typeof text === 'object' ? stringify(text) : text)
            .map(text => {
                if (!text) {
                    return '';
                }
                if (text.length > this.maxLogLineLength) {
                    const start = text.slice(0, 100);
                    const end = text.slice(-100);
                    return `${start}...${end}`;
                }
                return text;
            })
            .join(' ');
    }

    // TODO json序列化安全
    private formatLogMessage(level: LogLevel, texts: string[]): string {
        const content = this.connectMessage(texts);

        return `[${process.pid}] [${this.getCurrentTime()}] [${level}] ${content}\n`;
    }

    private createBasicLogFunction(level: LogLevel): BasicLogFunction {
        return (...texts: string[]) => {
            const message = this.formatLogMessage(level, texts);

            if (process.env.ENVIRONMENT !== 'production') {
                this.connectService?.logMessage(message);
            }

            if (ERROR_WHITE_LIST.some(e => message.includes(e))) {
                return;
            }

            void this.writeLog(message);
        };
    }

    private createErrorLogFunction(): ErrorLogFunction {
        return (category: string | LoggerUploaderCategoryType, ...texts: string[]) => {
            let isCategory = false;
            if (Object.values(LoggerUploaderCategoryType).includes(category as LoggerUploaderCategoryType)) {
                isCategory = true;
            }

            const content = this.connectMessage(texts);
            const message = this.formatLogMessage('ERROR', [content]);
            if (this.stream?.writable) {
                this.stream.write(message);
            }

            if (process.env.ENVIRONMENT !== 'production') {
                this.connectService?.logMessage(message);
            }

            // 如果是 LogUploaderEvent，则进行错误上报
            if (isCategory) {
                void this.uploader({
                    category: category as LoggerUploaderCategoryType,
                    content,
                });
            }
        };
    }

    /**
     * 初始化连接服务和配置服务
     * 使用当前日期和序列号更新日志路径
     */
    public initConnect(): void {
        this.connectService = iocContainer.get<ConnectService>(ConnectServiceName);
        this.configService = iocContainer.get<ConfigService>(ConfigServiceName);
        this.logPath = this.getLogPath(this.currentDate, this.currentSequence);
    }

    /**
     * 将日志事件上传到远程服务器
     * @param event - 要上传的日志事件，包含类别、标签、内容等信息
     * @param type - 日志类型（'event' 或 'error'），默认为 'event'
     * @returns 上传完成后的 Promise
     */
    public async uploader(event: LogUploaderEvent, type: LogType = 'event'): Promise<void> {
        if (process.env.ENVIRONMENT !== 'production') {
            return;
        }

        try {
            await this.axiosInstance.post('/logger/comate.log', {
                type,
                event,
                platform: this.configService?.edition,
                ide: this.configService?.ideName,
                ideSeries: this.configService?.ideSeries,
                common: {
                    license: this.configService?.license || '',
                    username: this.configService?.username || '',
                    app: `comate-${this.configService?.ideName}`,
                    version: this.configService?.extensionVersion,
                    ideVersion: this.configService?.ideVersion,
                    os: {
                        platform: os.platform(),
                        arch: os.arch(),
                        release: os.release(),
                    },
                    timestamp: Date.now(),
                },
            });
        }
        catch {
            // nothing
        }
    }

    private async compressLog(filePath: string): Promise<void> {
        const gzipPath = `${filePath}.gz`;

        return new Promise((resolve, reject) => {
            const readStream = fs.createReadStream(filePath);
            const writeStream = fs.createWriteStream(gzipPath);
            const gzip = zlib.createGzip();

            readStream
                .pipe(gzip)
                .pipe(writeStream)
                .on('finish', () => {
                    // 压缩完成后删除原文件
                    fs.unlink(filePath, err => {
                        if (err) {
                            reject(err);
                        }
                        else {
                            resolve();
                        }
                    });
                })
                .on('error', reject);
        });
    }

    private async findNextSequence(dateStr: string): Promise<number> {
        const files = await fs.promises.readdir(this.folder);
        let maxSequence = 0;

        const pattern = new RegExp(`kernel-${dateStr}(?:\\.(\\d+))?\\.log(?:\\.gz)?$`);

        for (const file of files) {
            const match = file.match(pattern);
            if (match) {
                const sequence = match[1] ? parseInt(match[1], 10) : 0;
                maxSequence = Math.max(maxSequence, sequence);
            }
        }

        return maxSequence + 1;
    }

    private async checkAndRotateLog(): Promise<void> {
        const currentDate = this.getDateString();
        const stats = fs.statSync(this.logPath);

        // 需要轮转的情况：1. 日期变化 2. 文件大小超限
        if (currentDate !== this.currentDate || stats.size >= this.maxSize) {
            // 关闭当前流
            await new Promise<void>(resolve => {
                this.stream.end(() => resolve());
            });

            // 如果是因为大小超限导致的轮转
            if (currentDate === this.currentDate) {
                // 压缩当前日志文件
                await this.compressLog(this.logPath);
                // 获取下一个序号
                this.currentSequence = await this.findNextSequence(currentDate);
            }
            else {
                // 如果是因为日期变化，重置序号
                this.currentSequence = 0;
                // 压缩前一天的日志
                await this.compressLog(this.logPath);
            }

            // 创建新的日志文件
            this.currentDate = currentDate;
            this.logPath = this.getLogPath(this.currentDate, this.currentSequence);
            this.stream = this.createWriteStream();
        }
    }

    private async writeLog(message: string): Promise<void> {
        try {
            await this.checkAndRotateLog();

            if (this.stream?.writable) {
                this.stream.write(message);
            }
        }
        catch (error) {
            console.error('Error writing log:', error);
        }
    }

    /**
     * 清理超过保留期限的旧日志文件
     * @param daysToKeep - 日志文件保留天数，默认为 3 天
     * @returns 清理完成后的 Promise
     */
    public async cleanOldLogs(daysToKeep: number = 3): Promise<void> {
        const files = await fs.promises.readdir(this.folder);
        const now = dayjs();

        for (const file of files) {
            if (file.startsWith('kernel-') && (file.endsWith('.log') || file.endsWith('.log.gz'))) {
                const filePath = path.join(this.folder, file);
                const stats = await fs.promises.stat(filePath);
                const fileAge = now.diff(dayjs(stats.mtime), 'day');

                if (fileAge > daysToKeep) {
                    await fs.promises.unlink(filePath);
                }
            }
        }
    }
}
