import {BaseStore} from './BaseStore.js';

export class ObjectStore<T extends Record<string, any> = Record<string, any>> extends BaseStore<T> {
    constructor(dbName: string, defaultData: T) {
        super(dbName, defaultData);
    }

    async set(value: T) {
        await this._update(() => value);
        return this.db.data;
    }

    async get() {
        await this.read();
        return this.db.data;
    }
}