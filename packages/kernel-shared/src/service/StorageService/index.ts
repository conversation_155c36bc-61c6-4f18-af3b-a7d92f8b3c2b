// TODO 暂时这么写吧，想想要不要复用ide的存储
import {injectable} from 'inversify';
import {ArrayStore} from './ArrayStore.js';
import {KVStore} from './KVStore.js';
import {ObjectStore} from './ObjectStore.js';
import {TextStore} from './TextStore.js';
import path from 'node:path';
import os from 'node:os';
import {ensureFile, ensureFileSync, readJson, readJsonSync, writeJson} from 'fs-extra/esm';

export {ArrayStore} from './ArrayStore.js';
export {KVStore} from './KVStore.js';
export {ObjectStore} from './ObjectStore.js';
export {TextStore} from './TextStore.js';

@injectable()
export class StorageService {
    ArrayStore = ArrayStore;
    KVStore = KVStore;
    ObjectStore = ObjectStore;
    TextStore = TextStore;
    private readonly filePath = path.join(os.homedir(), '.comate', 'data', 'globalStorage.json');

    get<T>(key: string, defaultValue: T): T;
    get<T>(key: string, defaultValue?: T): T | undefined {
        ensureFileSync(this.filePath);
        try {
            const storage: Record<string, any> = readJsonSync(this.filePath);
            const value = storage[key] as T;
            return value ?? defaultValue;
        }
        catch (e) {
            return defaultValue;
        }
    }

    async update(key: string, value: any) {
        await ensureFile(this.filePath);
        const storage: Record<string, any> = await readJson(this.filePath, {throws: false}) ?? {};
        storage[key] = value;
        await writeJson(this.filePath, storage);
    }
}
