import {
    iocContainer,
} from './context.js';
import {ConfigService} from './service/ConfigService.js';
import {ConnectService} from './service/ConnectService.js';
import {GitService} from './service/GitService.js';
import {HttpService} from './service/HttpService.js';
import {KernelService} from './service/KernelService.js';
import {LoggerService} from './service/LoggerService.js';
import {StorageService} from './service/StorageService/index.js';
import {UserService} from './service/UserService.js';
import {
    LoggerServiceName,
    ConnectServiceName,
    ConfigServiceName,
    HttpServiceName,
    UserServiceName,
    StorageServiceName,
    GitServiceName,
    KernelServiceName,
} from './service/serviceName.js';

export function activate() {
    iocContainer.bind<LoggerService>(LoggerServiceName).to(LoggerService).inSingletonScope();
    iocContainer.bind<ConnectService>(ConnectServiceName).to(ConnectService).inSingletonScope();
    iocContainer.bind<ConfigService>(ConfigServiceName).to(ConfigService).inSingletonScope();
    iocContainer.bind<HttpService>(HttpServiceName).to(HttpService).inSingletonScope();
    iocContainer.bind<UserService>(UserServiceName).to(UserService).inSingletonScope();
    iocContainer.bind<StorageService>(StorageServiceName).to(StorageService).inSingletonScope();
    iocContainer.bind<GitService>(GitServiceName).to(GitService).inSingletonScope();
    iocContainer.bind<KernelService>(KernelServiceName).to(KernelService).inSingletonScope();

    const loggerService = iocContainer.get<LoggerService>(LoggerServiceName);
    loggerService.initConnect();
    loggerService.cleanOldLogs();
}
