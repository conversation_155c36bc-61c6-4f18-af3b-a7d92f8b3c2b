{"name": "@comate/kernel-shared", "version": "0.9.2", "description": "> TODO: description", "author": "wuweiqi <<EMAIL>>", "type": "module", "license": "ISC", "main": "dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js"}, "./types": {"import": "./dist/types/index.d.ts", "require": "./dist/types/index.js"}, "./browser": {"import": "./dist/browser/index.d.ts", "require": "./dist/browser/index.js"}}, "files": ["dist"], "publishConfig": {"registry": "http://registry.npm.baidu-int.com"}, "repository": {"type": "git", "url": "ssh://<EMAIL>:8235/baidu/ide/comate-plugin-host"}, "scripts": {"build": "tsc -p ./", "watch": "tsc -p ./ --watch", "clean": "rm -rf ./dist", "doc": "typedoc src/index.ts"}, "dependencies": {"@comate/kernel": "workspace:0.9.2", "@comate/plugin-shared-internals": "workspace:^", "axios": "^1.7.8", "dayjs": "^1.11.13", "fs-extra": "^11.2.0", "inversify": "^6.0.1", "isomorphic-git": "^1.27.1", "lowdb": "^7.0.1", "reflect-metadata": "^0.1.13", "safe-stable-stringify": "^2.5.0", "vscode-languageserver": "8.0.2", "vscode-languageserver-textdocument": "^1.0.11", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/node": "18.15.3", "@types/vscode": "1.69.0", "typedoc": "^0.25.13", "typescript": "^5.7.2", "vscode-languageserver-types": "3.17.5"}}