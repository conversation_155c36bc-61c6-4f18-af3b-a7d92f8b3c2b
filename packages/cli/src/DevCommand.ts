import {Server} from 'node:http';
import Queue from 'p-queue';
import chokidar from 'chokidar';
import clear from 'clear';
import * as kolorist from 'kolorist';
import express from 'express';
import {publicDirectory, setupEngineApp, PluginLocation} from '@comate/plugin-playground';
import {Command, Option} from 'clipanion';
import {readPluginDescription} from '@comate/plugin-shared-internals';
import {buildWithRollup} from './utils/rollup.js';
import {PluginEngine} from '@comate/plugin-engine';
import {Socket} from 'node:net';

export default class DevCommand extends Command {
    static paths = [['dev']];

    static usage = {
        description: 'Start a dev server to debug your Comate+ plugin',
    };

    workspace = Option.String(
        '--workspace',
        {
            required: false,
            description: 'Specify a directory to be user workspace available for plugin',
        }
    );

    private engine: PluginEngine | null = null;

    private httpServer: Server | null = null;

    private readonly connections = new Set<Socket>();

    private readonly queue: Queue = new Queue({concurrency: 1});

    async execute(): Promise<number | void> {
        process.on('SIGINT', () => this.cleanAndExit());
        process.on('SIGTERM', () => this.cleanAndExit());
        process.on('beforeExit', () => this.clean());
        const watcher = chokidar.watch(['src', 'package.json']);
        watcher.on(
            'ready',
            () => {
                watcher.on('all', () => this.buildAndRun());
                this.buildAndRun();
            }
        );
    }

    private async clean() {
        await this.engine?.stop();
        if (this.httpServer) {
            for (const socket of this.connections) {
                if (!socket.destroyed) {
                    // @ts-expect-error 类型遗漏 https://github.com/DefinitelyTyped/DefinitelyTyped/pull/67158
                    socket.destroySoon();
                }
            }
            this.connections.clear();
            await new Promise(r => this.httpServer!.close(r));
        }
    }

    private async cleanAndExit() {
        await this.clean();
        process.exit(0);
    }

    private async startServer() {
        const plugin = await this.collect();
        const app = express();
        app.use(express.json());
        app.use(express.static(publicDirectory, {index: 'index-stable.html'}));
        this.engine = await setupEngineApp(app, {plugins: [plugin], base: '/api', workspace: this.workspace});
        this.httpServer = app.listen(8080);
        this.httpServer.on(
            'connection',
            socket => {
                this.connections.add(socket);
                socket.on('close', () => this.connections.delete(socket));
            }
        );
    }

    private buildAndRun() {
        const task = async () => {
            /* eslint-disable no-console */
            clear();
            console.log(kolorist.bgBlue(' WAIT '), kolorist.blue('Building...'));
            const start = Date.now();
            await this.clean();
            await buildWithRollup({outputDirectory: 'dist'});
            await this.startServer();
            clear();
            console.log(kolorist.bgGreen(' DONE '), kolorist.green(`Built successfully in ${Date.now() - start}ms`));
            console.log();
            console.log(kolorist.bgBlue(' INFO '), 'Your application is running here: http://localhost:8080/');
            /* eslint-enable no-console */
        };
        this.queue.clear();
        void this.queue.add(task);
    }

    private async collect(): Promise<PluginLocation> {
        const description = await readPluginDescription(process.cwd());
        return {directory: process.cwd(), name: description.name};
    }
}
