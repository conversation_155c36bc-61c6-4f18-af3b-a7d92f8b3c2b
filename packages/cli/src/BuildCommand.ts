import fs from 'node:fs/promises';
import {Command, Option} from 'clipanion';
import {buildWithRollup} from './utils/rollup.js';

export default class BuildCommand extends Command {
    static paths = [['build']];

    static usage = {
        description: 'Build Coamte+ plugin',
    };

    clean = Option.Boolean(
        '--clean',
        {
            required: false,
            description: 'Clean dist directory before build',
        }
    );

    async execute(): Promise<number | void> {
        if (this.clean) {
            await fs.rmdir('dist', {recursive: true});
        }

        await buildWithRollup({outputDirectory: 'dist'});
    }
}
