import {rollup, RollupOptions, Plugin} from 'rollup';
import commonjs from '@rollup/plugin-commonjs';
// import esmShim from '@rollup/plugin-esm-shim';
import nodeResolve from '@rollup/plugin-node-resolve';
import swc from '@rollup/plugin-swc';
import json from '@rollup/plugin-json';

function tsResolve(): Plugin {
    return {
        name: 'typescript-shim',
        async resolveId(id, importer, options) {
            if (!id.endsWith('.js')) {
                return this.resolve(id, importer, options);
            }

            const resolveOptions = {...options, skipSelf: true};
            const resolved = await this.resolve(id, importer, resolveOptions);
            return resolved ?? this.resolve(id.replace(/\.js$/, '.ts'), importer, resolveOptions);
        },
    };
}

function prompt(): Plugin {
    return {
        name: 'prompt',
        transform(code, id) {
            if (id.endsWith('.prompt')) {
                return {
                    code: `export default ${JSON.stringify(code)};`,
                    map: {mappings: ''},
                };
            }
            return null;
        },
    };
}

function createRollupConfig(): RollupOptions {
    const swcConfig = {
        jsc: {
            externalHelpers: true,
            parser: {
                syntax: 'typescript',
                dynamicImport: true,
                decorators: true,
                tsx: true,
                jsx: true,
                importAssertions: true,
            },
            target: 'es2022',
            transform: {
                legacyDecorator: true,
                decoratorMetadata: true,
                react: {
                    runtime: 'automatic',
                    importSource: '@comate/plugin-host',
                },
            },
        },
    };
    return {
        input: 'src/index.ts',
        external: ['@comate/plugin-host'],
        plugins: [
            // @ts-expect-error ESM兼容
            nodeResolve({extensions: ['.js', '.mjs', '.ts', '.tsx'], preferBuiltins: true}),
            // @ts-expect-error ESM兼容
            commonjs(),
            // @ts-expect-error ESM兼容
            json(),
            tsResolve(),
            prompt(),
            // TODO: 等[这个Issue](https://github.com/rollup/plugins/issues/1649)修复后重新启用
            // esmShim(),
            // @ts-expect-error ESM兼容
            swc({swc: swcConfig, exclude: '**/node_modules/**'}),
        ],
    };
}

export interface RollupBuildOptions {
    outputDirectory: string;
}

export async function buildWithRollup(options: RollupBuildOptions) {
    const config = createRollupConfig();
    const bundle = await rollup(config);
    await bundle.write({file: `${options.outputDirectory}/index.js`, format: 'esm'});
}
