{"name": "@comate/plugin-cli", "version": "0.9.2", "type": "module", "engines": {"node": ">=20.10.0"}, "files": ["dist", "bin"], "bin": {"cpd": "./bin/cli.mjs"}, "contributors": ["zhanglili01 <<EMAIL>>"], "license": "MIT", "publishConfig": {"access": "public"}, "scripts": {"clean": "rm -rf dist", "build": "tsc", "lint": "eslint --max-warnings=0 src --fix"}, "devDependencies": {"@types/clear": "^0.1.4", "@types/express": "^4.17.21", "@types/node": "18.15.0", "eslint": "^8.56.0", "typescript": "^5.3.2"}, "dependencies": {"@comate/plugin-engine": "^0.9.2", "@comate/plugin-playground": "^0.9.2", "@comate/plugin-shared-internals": "^0.9.2", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-esm-shim": "^0.1.5", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-swc": "^0.3.0", "chokidar": "^3.5.3", "clear": "^0.1.0", "clipanion": "^3.2.1", "express": "^4.18.2", "kolorist": "^1.8.0", "p-queue": "^8.0.1", "rollup": "^4.9.5"}}