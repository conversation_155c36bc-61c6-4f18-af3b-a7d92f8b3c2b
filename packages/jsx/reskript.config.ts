import {configure} from '@reskript/settings';

export default configure(
    'webpack',
    {
        featureMatrix: {
            stable: {},
            dev: {},
        },
        build: {
            appTitle: 'Comate Playground',
            style: {
                modules: false,
            },
            finalize: config => {
                // eslint-disable-next-line no-param-reassign
                config.optimization.splitChunks = {
                    chunks: 'all',
                    minSize: 30 * 1024,
                    maxSize: 800 * 1024,
                    maxInitialRequests: 5,
                };
                return config;
            },
        },
        devServer: {
            port: 8788,
            proxyRewrite: {
                '/api': 'http://localhost:8788/__skr__',
            },
        },
    }
);
