import {CSSProperties, ReactNode} from 'react';
import classNames from 'classnames';
import {AcceptMethod} from '@comate/plugin-shared-internals';
import {Accept} from './Accept.js';

interface Props {
    className?: string;
    style?: CSSProperties;
    acceptMethods: AcceptMethod[];
    content?: string;
    title?: ReactNode;
    children?: ReactNode;
}

export default function Suggestion({className, style, acceptMethods, content, title, children}: Props) {
    return (
        <div className={classNames('comate-suggestion', className)} style={style}>
            <div className="comate-suggestion-header">
                <div className="comate-suggestion-title">
                    {title}
                </div>
                <div className="comate-suggestion-accept-method-list">
                    {acceptMethods.map(v => <Accept key={v.method} method={v} content={content ?? ''} />)}
                </div>
            </div>
            {children}
        </div>
    );
}
