import classNames from 'classnames';
import {useArticleContext} from './ArticleContextProvider.js';

interface Props {
    variant?: 'primary' | 'default' | 'text';
    selected?: string;
    onClick: () => void;
    children: string;
}

export default function Button({variant, selected, onClick, children}: Props) {
    const {interactive} = useArticleContext();
    const className = classNames(
        'comate-button',
        `comate-button-${interactive ? variant ?? 'default' : 'disabled'}`,
        selected && 'comate-button-selected'
    );

    return (
        <button type="button" className={className} onClick={interactive ? onClick : undefined}>
            {children}
        </button>
    );
}
