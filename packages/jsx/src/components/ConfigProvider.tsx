import {ReactNode, createContext, useContext} from 'react';
import {Message, MessageStatus} from '../dicts/message.js';

interface MessageIdentity {
    pluginName: string;
    taskId: string;
}

export type MessageData = Message & MessageIdentity;

interface RenderConfig {
    theme: 'dark' | 'light';
    status?: MessageStatus;
    onMessage: (message: MessageData) => void;
}

const Context = createContext<RenderConfig>({theme: 'dark', onMessage: () => {}});
Context.displayName = 'RenderConfigContext';

interface Props extends RenderConfig {
    children: ReactNode;
}

export default function ConfigProvider({children, ...config}: Props) {
    return (
        <Context.Provider value={config}>
            {children}
        </Context.Provider>
    );
}

export function useRenderConfig() {
    return useContext(Context);
}
