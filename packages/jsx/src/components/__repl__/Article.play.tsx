import {ReactNode, useCallback} from 'react';
import {ToastContainer, toast} from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import ConfigProvider, {MessageData} from '../ConfigProvider.js';
import {MESSAGE_ACCEPT_SUGGESTION, MESSAGE_OPEN_FILE, MESSAGE_PLUGIN_COMMAND} from '../../dicts/message.js';
import '../../style.css';

interface Props {
    children: ReactNode;
}

const style = {
    padding: 12,
    height: 'calc(100vh - 64px)',
    overflow: 'auto',
    backgroundColor: '#f2f2f2',
};

export function Wrapper({children}: Props) {
    const handleMessage = useCallback(
        /* eslint-disable-next-line complexity */
        async (message: MessageData) => {
            if (message.type === MESSAGE_ACCEPT_SUGGESTION) {
                if (message.payload.accept.method === 'copy') {
                    const content = message.payload.accept.content ?? message.payload.content;
                    await navigator.clipboard.writeText(content);
                    toast('复制内容到剪贴版');
                }
                else if (message.payload.accept.method === 'replaceContent') {
                    toast('采纳内容替换原文本');
                }
                else if (message.payload.accept.method === 'replaceSelection') {
                    toast(`采纳文本替换${message.payload.accept.cursorMode === 'current' ? '当前' : '生成时'}选中文本`);
                }
                else if (message.payload.accept.method === 'toTerminal') {
                    toast(`采纳至命令行${message.payload.accept.run ? '并执行' : ''}`);
                }
            }
            else if (message.type === MESSAGE_OPEN_FILE) {
                toast(`打开文件${message.payload.to}${message.payload.line ? `:${message.payload.line}` : ''}`);
            }
            else if (message.type === MESSAGE_PLUGIN_COMMAND) {
                toast(`发送指令${message.payload.commandName}到插件${message.pluginName}`);
            }
        },
        []
    );
    return (
        <div className="comate-theme-light" style={style}>
            <ConfigProvider theme="light" onMessage={handleMessage}>
                {children}
            </ConfigProvider>
            <ToastContainer />
        </div>
    );
}
