# Article reSKRipt Case

## JSX渲染

- Create: 2024-01-30 11:26:17 (zhanglili01)
- Run: 2024-05-20 12:06:46 (fuqiang05)

```jsx
const code = `
const range = (start, stop, step=1) => {
    return Array.from(
      { length: (stop - start) / step + 1 },
      (value, i) => start + i * step
    )
}
`;

const elements = [
    {
        type: 'h1',
        children: 'JSX渲染展示',
    },
    {
        type: 'h2',
        children: '普通文字',
    },
    {
        type: 'p',
        children: [
            'Hi ',
            {
                type: 'strong',
                children: 'dear',
            },
            ':',
        ],
    },
    {
        type: 'p',
        children: [
            'Wish you a ',
            {
                type: 'em',
                children: 'sunny',
            },
            ' day.',
        ],
    },
    {
        type: 'h2',
        children: '代码块',
    },
    {
        type: 'code-block',
        language: 'javascript',
        closed: true,
        acceptMethods: [
            {
                method: 'copy',
            },
            {
                method: 'replaceSelection',
                cursorMode: 'current',
            },
        ],
        children: code.trim(),
    },
    {
        type: 'h2',
        children: '图片',
    },
    {
        type: 'suggestion',
        title: '^_^ Keep Smile',
        content: 'Wishing you a joyful and blessed day!',
        acceptMethods: [{method: 'copy'}],
        children: {
            type: 'img',
            src: 'https://simg.nicepng.com/png/small/124-1248438_face-transparent-anime-girl-gakkou-gurashi.png',
        },
    },
    {
        type: 'h2',
        children: '链接',
    },
    {
        type: 'p',
        children: [
            'Reference your ',
            {
                type: 'file-link',
                to: 'README.md',
                line: 1,
            },
            ' to learn more.',
        ],
    },
    {
        type: 'h2',
        children: '表格',
    },
    {
        type: 'table',
        children: [
            {
                type: 'thead',
                children: [
                    {
                        type: 'tr',
                        children: [
                            {
                                type: 'td',
                                children: '时间',
                            },
                            {
                                type: 'td',
                                children: '事项',
                            },
                        ],
                    },
                ],
            },
            {
                type: 'tbody',
                children: [
                    {
                        type: 'tr',
                        children: [
                            {
                                type: 'th',
                                children: '09:00',
                            },
                            {
                                type: 'td',
                                children: '到会议地点',
                            },
                        ],
                    },
                    {
                        type: 'tr',
                        children: [
                            {
                                type: 'th',
                                children: '12:00',
                            },
                            {
                                type: 'td',
                                children: '午饭',
                            },
                        ],
                    },
                    {
                        type: 'tr',
                        children: [
                            {
                                type: 'th',
                                children: '18:00',
                            },
                            {
                                type: 'td',
                                children: '回家',
                            },
                        ],
                    },
                ],
            },
        ],
    },
    {
        type: 'h2',
        children: '按钮',
    },
    {
        type: 'command-button',
        commandName: 'buttonClick',
        data: 'default',
        children: '普通按钮',
    },
    {
        type: 'command-button',
        commandName: 'buttonClick',
        data: 'primary',
        variant: 'primary',
        children: '主要按钮',
    },
    {
        type: 'button-group',
        children: [
            {
                type: 'command-button',
                commandName: 'buttonClick',
                data: 'default',
                children: '取消',
            },
            {
                type: 'command-button',
                commandName: 'buttonClick',
                data: 'primary',
                variant: 'primary',
                children: '确认',
            },
        ],
    },
    {
        type: 'button-group',
        children: [
            {
                type: 'command-button',
                commandName: 'buttonClick',
                data: 'previoud',
                children: '上一个',
            },
            {
                type: 'command-button',
                commandName: 'buttonClick',
                data: 'current',
                children: '采用',
            },
            {
                type: 'command-button',
                commandName: 'buttonClick',
                data: 'next',
                children: '下一个',
            },
        ],
    },
];

export default function Repl() {
    return (
        <Article
            interactive
            pluginName="official"
            taskId="D2DE2DB8-D46C-11EE-9EB9-DA4C3343F1A2"
            content={elements}
        />
    );
}
```

## 纯Markdown

- Create: 2024-02-01 13:42:43 (zhanglili01)
- Run: 2024-03-06 16:20:01 (zhanglili01)

```jsx
const markdown = `
# Hello

Hi **dear**:

Which you a _sunny_ day.

Today's cool bits:

\`\`\`js
const range = (start, stop, step=1) => {
    return Array.from(
      { length: (stop - start) / step + 1 },
      (value, i) => start + i * step
    )
}
\`\`\`
`;

export default function Repl() {
    return (
        <Article
            interactive
            pluginName="official"
            taskId="D2DE2DB8-D46C-11EE-9EB9-DA4C3343F1A2"
            content={markdown}
        />
    );
}
```

## 非交互JSX

- Create: 2024-02-26 16:18:57 (zhanglili01)
- Run: 2024-02-26 17:55:47 (zhanglili01)

```jsx
const elements = [
    {
        type: 'h1',
        children: '非交互状态JSX展示',
    },
    {
        type: 'h2',
        children: '按钮',
    },
    {
        type: 'command-button',
        commandName: 'buttonClick',
        data: 'default',
        children: '普通按钮',
    },
    {
        type: 'command-button',
        commandName: 'buttonClick',
        data: 'primary',
        variant: 'primary',
        children: '主要按钮',
    },
    {
        type: 'button-group',
        children: [
            {
                type: 'command-button',
                commandName: 'buttonClick',
                data: 'default',
                children: '取消',
            },
            {
                type: 'command-button',
                commandName: 'buttonClick',
                data: 'primary',
                variant: 'primary',
                children: '确认',
            },
        ],
    },
    {
        type: 'button-group',
        children: [
            {
                type: 'command-button',
                commandName: 'buttonClick',
                data: 'previoud',
                children: '上一个',
            },
            {
                type: 'command-button',
                commandName: 'buttonClick',
                data: 'current',
                children: '采用',
            },
            {
                type: 'command-button',
                commandName: 'buttonClick',
                data: 'next',
                children: '下一个',
            },
        ],
    },
];

export default function Repl() {
    return (
        <Article
            interactive={false}
            pluginName="official"
            taskId="D2DE2DB8-D46C-11EE-9EB9-DA4C3343F1A2"
            content={elements}
        />
    );
}
```
