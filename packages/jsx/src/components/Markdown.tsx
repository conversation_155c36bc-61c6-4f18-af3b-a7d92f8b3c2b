import remarkGfm from 'remark-gfm';
import {ComponentType} from 'react';
import ReactMarkdown from 'react-markdown';
import CodeBlock from './CodeBlock/index.js';

const remarkPlugins = [remarkGfm];

const components: Record<string, ComponentType<any>> = {
    pre: CodeBlock,
};

interface Props {
    children: string;
}

export default function Markdown({children}: Props) {
    return (
        <ReactMarkdown components={components} remarkPlugins={remarkPlugins}>
            {children}
        </ReactMarkdown>
    );
}
