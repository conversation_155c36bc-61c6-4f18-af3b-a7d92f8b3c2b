import {ReactElement, useCallback} from 'react';
import {useTransitionState} from 'huse';
import {VscReplaceAll, VscCopy, VscCheckAll, VscTerminal, VscGoToFile} from 'react-icons/vsc';
import {AcceptMethod} from '@comate/plugin-shared-internals';
import {AcceptSuggestionMessage, MESSAGE_ACCEPT_SUGGESTION} from '../dicts/message.js';
import {useSendMessage} from './ArticleContextProvider.js';

const ACCEPT_METHOD_TO_ICON: Record<AcceptMethod['method'], ReactElement> = {
    replaceSelection: <VscGoToFile title="替换选中代码" />,
    replaceContent: <VscReplaceAll title="替换指定代码" />,
    toTerminal: <VscTerminal title="采纳到命令行" />,
    copy: <VscCopy title="复制代码" />,
};

interface Props {
    method: AcceptMethod;
    content: string;
}

export function Accept({method, content}: Props) {
    const sendMessage = useSendMessage();
    const [noticing, setNoticing] = useTransitionState(false, 2500);
    const accept = useCallback(
        () => {
            setNoticing(true);
            const message: AcceptSuggestionMessage = {
                type: MESSAGE_ACCEPT_SUGGESTION,
                payload: {accept: method, content},
            };
            sendMessage(message);
        },
        [setNoticing, sendMessage, method, content]
    );

    return (
        <span className="comate-accept" onClick={accept}>
            {noticing ? <VscCheckAll /> : ACCEPT_METHOD_TO_ICON[method.method]}
        </span>
    );
}
