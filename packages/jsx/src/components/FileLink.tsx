import {useCallback} from 'react';
import {MESSAGE_OPEN_FILE, OpenFileMessage} from '../dicts/message.js';
import FileLinkIcon from './icons/FileLinkIcon.js';
import {useSendMessage} from './ArticleContextProvider.js';

interface Props {
    to: string;
    line?: number;
    children?: string;
    showIcon?: boolean;
}

export default function FileLink({to, line, children, showIcon}: Props) {
    const sendMessage = useSendMessage();
    const click = useCallback(
        () => {
            const message: OpenFileMessage = {
                type: MESSAGE_OPEN_FILE,
                payload: {to, line},
            };
            sendMessage(message);
        },
        [to, line, sendMessage]
    );

    return (
        // direction rtl 前置省略号
        <span className="comate-link" style={{direction: 'rtl'}} onClick={click}>
            {showIcon && <FileLinkIcon />}
            <span>{children || to}</span>
        </span>
    );
}
