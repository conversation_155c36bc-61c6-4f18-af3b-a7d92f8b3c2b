import {useCallback} from 'react';
import {MESSAGE_PLUGIN_COMMAND, PluginCommandMessage} from '../dicts/message.js';
import {useSendMessage} from './ArticleContextProvider.js';
import ActionButton from './ActionButton.js';
import Button from './Button.js';

interface Props {
    commandName: string;
    data: any;
    replyText?: string;
    variant?: 'primary' | 'default' | 'text';
    actions?: Record<string, () => void>;
    action?: string;
    propagation?: boolean;
    tooltipText?: string;
    actionData?: any;
    children: string;
}

export default function CommandButton({
    commandName,
    data,
    replyText,
    variant,
    actions,
    action,
    propagation,
    tooltipText,
    actionData,
    children,
}: Props) {
    const sendMessage = useSendMessage();
    const click = useCallback(
        () => {
            const message: PluginCommandMessage = {
                type: MESSAGE_PLUGIN_COMMAND,
                payload: {
                    commandName,
                    data,
                    replyText: replyText ?? children.toString(),
                },
            };
            sendMessage(message);
        },
        [sendMessage, commandName, data, replyText, children]
    );

    if (action) {
        return (
            <ActionButton
                onClick={click}
                displayName={children}
                actions={actions}
                action={action}
                propagation={propagation}
                tooltipText={tooltipText}
                // 这里没有直接用data actionData数据格式需要适配ide，data数据格式可以由三方自定义做一些额外处理
                actionData={actionData}
                variant={variant}
            />
        );
    }

    return (
        <Button variant={variant} onClick={click}>
            {children}
        </Button>
    );
}
