import {ReactNode, createContext, useCallback, useContext} from 'react';
import {Message} from '../dicts/message.js';
import {useRenderConfig} from './ConfigProvider.js';
import {IdeCodeBlockProps, JsxMarkdownProps} from './Article.js';

interface ArticleContext {
    pluginName: string;
    taskId: string;
    interactive: boolean;
    codeBlock?: React.FC<IdeCodeBlockProps>;
    markdown?: React.FC<JsxMarkdownProps>;
}

const Context = createContext<ArticleContext>({pluginName: '', taskId: '', interactive: true});
Context.displayName = 'ArticleContext';

interface Props extends ArticleContext {
    children: ReactNode;
}

export default function ArticleContextProvider(
    {pluginName, taskId, interactive, children, codeBlock, markdown}: Props
) {
    return (
        <Context.Provider value={{pluginName, taskId, interactive, codeBlock, markdown}}>
            {children}
        </Context.Provider>
    );
}

export function useArticleContext() {
    return useContext(Context);
}

export function useSendMessage() {
    const {onMessage} = useRenderConfig();
    const {pluginName, taskId} = useContext(Context);
    const send = useCallback(
        (message: Message) => {
            onMessage({...message, pluginName, taskId});
        },
        [pluginName, taskId, onMessage]
    );
    return send;
}
