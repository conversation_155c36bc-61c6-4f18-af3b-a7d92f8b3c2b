import {use<PERSON><PERSON>back, useMemo, useState} from 'react';
import {Prism as Syntax<PERSON>ighlighter} from 'react-syntax-highlighter';
import {getIcon} from 'material-file-icons';
import {AcceptMethod} from '@comate/plugin-shared-internals';
import {sampleFileNameFromLanguage} from '../language.js';
import darkTheme from '../CodeBlock/codeDarkTheme.js';
import lightTheme from '../CodeBlock/codeLightTheme.js';
import Suggestion from '../Suggestion.js';
import {useRenderConfig} from '../ConfigProvider.js';
import Toggle from './Toggle.js';

interface LanguageTypeIconProps {
    language: string;
}

function LanguageTypeIcon({language}: LanguageTypeIconProps) {
    const icon = getIcon(sampleFileNameFromLanguage(language));

    /* eslint-disable react/no-danger */
    // bca-disable-line
    return <i style={{width: 14, height: 14}} dangerouslySetInnerHTML={{__html: icon.svg}} />;
    /* eslint-enable react/no-danger */
}

const DEFAULT_ACCEPT_METHOD: AcceptMethod[] = [{method: 'copy'}];

const EMPTY_ACCEPT_METHOD: AcceptMethod[] = [];

const LINE_COLLAPSE_THREASHOLD = 5;

interface Props {
    language?: string;
    code: string;
    closed: boolean;
    acceptMethods?: AcceptMethod[] | undefined;
}

export default function Content({language, code, closed, acceptMethods = DEFAULT_ACCEPT_METHOD}: Props) {
    const {theme} = useRenderConfig();
    const canCollapse = useMemo(
        () => {
            const lines = code.split('\n').length;
            return lines > LINE_COLLAPSE_THREASHOLD;
        },
        [code]
    );
    const [collapsed, setCollapsed] = useState(canCollapse);
    const styleLine = useCallback(
        (lineNumber: number) => {
            if (collapsed && lineNumber > LINE_COLLAPSE_THREASHOLD) {
                return {
                    style: {
                        display: 'none',
                    },
                };
            }
            return {};
        },
        [collapsed]
    );

    if (!code) {
        return null;
    }

    if (!language) {
        return <pre>{code}</pre>;
    }

    return (
        <Suggestion
            className="comate-code-block"
            acceptMethods={closed ? acceptMethods : EMPTY_ACCEPT_METHOD}
            content={code}
            title={
                <div className="comate-code-block-header">
                    <LanguageTypeIcon language={language ?? ''} />
                    <span>{language}</span>
                </div>
            }
        >
            <SyntaxHighlighter
                wrapLines
                showLineNumbers
                style={theme === 'light' ? lightTheme : darkTheme}
                lineProps={styleLine}
                language={language}
            >
                {code}
            </SyntaxHighlighter>
            {canCollapse && <Toggle collapsed={collapsed} onClick={() => setCollapsed(v => !v)} />}
        </Suggestion>
    );
}
