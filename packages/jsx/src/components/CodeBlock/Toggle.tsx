interface Props {
    collapsed: boolean;
    onClick: () => void;
}

export default function Toggle({collapsed, onClick}: Props) {
    return (
        <div className="comate-code-block-toggle">
            <button className="comate-code-block-button" onClick={onClick}>
                {collapsed ? '展开' : '收起'}
                <i className={`comate-code-block-${collapsed ? 'down' : 'up'}`} />
            </button>
        </div>
    );
}
