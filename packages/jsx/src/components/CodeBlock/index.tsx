import {useMemo} from 'react';
import {ExtraProps} from 'react-markdown';
import {ElementContent} from 'hast';
import Content from './Content.js';

function extractText(element: ElementContent | undefined) {
    if (!element) {
        return null;
    }

    if (element.type === 'text') {
        return element.value;
    }

    if (element.type === 'element') {
        return extractText(element.children.at(0));
    }

    return null;
}

interface Props {
    node: ExtraProps['node'];
}

function CodeBlock({node}: Props) {
    const code = useMemo(
        () => (extractText(node)?.trim() ?? ''),
        [node]
    );
    const language = useMemo(
        () => {
            const child = node?.children.at(0);

            if (!child || child.type !== 'element') {
                return '';
            }

            return /language-(\w+)/.exec(child.properties.className?.toString() || '')?.[1];
        },
        [node]
    );

    return <Content closed code={code} language={language} />;
}

export default Object.assign(CodeBlock, {Content});
