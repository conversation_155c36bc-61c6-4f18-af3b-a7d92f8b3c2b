import {ReactNode} from 'react';

interface Props {
    children: ReactNode;
    className?: string;
    onClick?: (e: any) => void;
    color: 'blue' | 'red' | 'yellow' | 'orange' | 'gray' | 'green';
}

export default function GradientCard({className, children, color, onClick}: Props) {
    return (
        <div className={`comate-gradient-card comate-gradient-card-${color} ${className}`} onClick={onClick}>
            {children}
        </div>
    );
}
