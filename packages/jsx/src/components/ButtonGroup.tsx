import {CommandButton as CommandButtonInfo} from '@comate/plugin-shared-internals';
import CommandButton from './CommandButton.js';

function renderButton(button: CommandButtonInfo, index: number) {
    return (
        <CommandButton
            key={index}
            variant={button.variant}
            commandName={button.commandName}
            data={button.data}
            replyText={button.replyText}
        >
            {button.children}
        </CommandButton>
    );
}

interface Props {
    children: CommandButtonInfo | CommandButtonInfo[];
}

export default function ButtonGroup({children}: Props) {
    const buttons = Array.isArray(children) ? children : [children];

    return (
        <div className="comate-button-group">
            {buttons.map(renderButton)}
        </div>
    );
}
