import classnames from 'classnames';
import {ReactNode} from 'react';
import {Flex} from '@comate/plugin-shared-internals/schema';

export interface Props extends Omit<Flex, 'children'> {
    children: ReactNode | ReactNode[];
}

export default function FlexComponent({
    centered,
    vertical,
    inline,
    verticalCentered,
    horizontalCentered,
    verticalStartAlign,
    flexEndCentered,
    betweenCentered,
    aroundCentered,
    keepWrap = true,
    gap: gapSize,
    children,
    ...props
}: Partial<Props>) {
    const prefix = inline ? 'comate-layout-inline-flex' : 'comate-layout-flex';
    return (
        <div
            className={classnames(
                {
                    [`${prefix}`]: !centered || !horizontalCentered,
                    [`${prefix}-center`]: centered,
                    [`${prefix}-vertical`]: verticalStartAlign || verticalCentered || vertical,
                    [`${prefix}-center-vertical`]: verticalCentered,
                    [`${prefix}-vertical-start`]: verticalStartAlign,
                    [`${prefix}-no-wrap`]: !keepWrap,
                    [`${prefix}-center-horizontal`]: horizontalCentered,
                    [`${prefix}-center-end`]: flexEndCentered,
                    [`${prefix}-center-between`]: betweenCentered,
                    [`${prefix}-center-around`]: aroundCentered,
                    [`gap-${gapSize}`]: !!gapSize,
                }
            )}
            {...props}
        >
            {children}
        </div>
    );
}
