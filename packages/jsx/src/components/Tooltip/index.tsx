import {FC, ReactElement, ReactNode} from 'react';
import Rc<PERSON>ooltip from 'rc-tooltip';
import {TooltipProps} from 'rc-tooltip/lib/Tooltip.js';
import cx from 'classnames';

interface ReassignProps {
    children: ReactElement;
    overlay: ReactNode;
    arrowContent?: ReactNode;
    showArrow?: boolean;
    placement?: string;
}

export type Props = Omit<TooltipProps, keyof ReassignProps> & ReassignProps;
// @ts-ignore
const Tooltip = RcTooltip as FC<Props>;

export default (props: Props) => {
    const {showArrow = true, placement = 'top', ...restProps} = props; // 设置默认为有箭头，方向top

    if (!props.overlay) {
        return props.children;
    }

    return (
        <Tooltip
            showArrow={showArrow}
            placement={placement}
            mouseLeaveDelay={0.2}
            {...restProps}
            overlayClassName={cx('comate-tooltip', props.overlayClassName)}
        />
    );
};
