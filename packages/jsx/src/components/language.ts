const LANGUAGE_TO_EXTENSION: Record<string, string | undefined> = {
    abap: 'abap',
    actionscript: 'as',
    ada: 'ada',
    apacheconf: 'apacheconf',
    apl: 'apl',
    applescript: 'applescript',
    arff: 'arff',
    asciidoc: 'asciidoc',
    asm6502: 'asm',
    autohotkey: 'ahk',
    autoit: 'au3',
    bash: 'sh',
    basic: 'vb',
    batch: 'bat',
    bison: 'bison',
    brainfuck: 'b',
    bro: 'bro',
    c: 'c',
    csharp: 'cs',
    cpp: 'cpp',
    coffeescript: 'coffee',
    clojure: 'clj',
    crystal: 'cr',
    css: 'css',
    d: 'd',
    dart: 'dart',
    diff: 'diff',
    django: 'jinja',
    docker: 'dockerfile',
    eiffel: 'e',
    elixir: 'ex',
    elm: 'elm',
    erb: 'erb',
    erlang: 'erl',
    fsharp: 'fs',
    fortran: 'f90',
    gedcom: 'ged',
    gherkin: 'feature',
    glsl: 'glsl',
    go: 'go',
    graphql: 'graphql',
    groovy: 'groovy',
    haml: 'haml',
    handlebars: 'handlebars',
    haskell: 'hs',
    haxe: 'hx',
    http: 'http',
    icon: 'icn',
    inform7: 'ni',
    ini: 'ini',
    io: 'io',
    j: 'ijs',
    java: 'java',
    javascript: 'js',
    jolie: 'ol',
    json: 'json',
    julia: 'jl',
    keyman: 'kmn',
    kotlin: 'kt',
    latex: 'tex',
    less: 'less',
    liquid: 'liquid',
    lisp: 'lisp',
    livescript: 'ls',
    lolcode: 'lol',
    lua: 'lua',
    makefile: 'mak',
    markdown: 'md',
    markup: 'apib',
    matlab: 'matlab',
    mel: 'mel',
    mizar: 'miz',
    monkey: 'monkey',
    n4js: 'n4jsd',
    nasm: 'nasm',
    nginx: 'nginxconf',
    nim: 'nim',
    nix: 'nix',
    nsis: 'nsi',
    objectivec: 'm',
    ocaml: 'ml',
    opencl: 'opencl',
    oz: 'oz',
    pascal: 'pas',
    perl: 'pl',
    php: 'php',
    plsql: 'pls',
    powershell: 'ps1',
    processing: 'pde',
    prolog: 'pl',
    properties: 'properties',
    protobuf: 'proto',
    pug: 'jade',
    puppet: 'pp',
    pure: 'pure',
    python: 'py',
    q: 'q',
    qore: 'q',
    r: 'r',
    jsx: 'jsx',
    tsx: 'tsx',
    renpy: 'rpy',
    reason: 're',
    rest: 'rst',
    rip: 'rip',
    ruby: 'rb',
    rust: 'rs',
    sas: 'sas',
    sass: 'sass',
    scss: 'scss',
    scala: 'scala',
    scheme: 'scm',
    smalltalk: 'st',
    smarty: 'tpl',
    sql: 'sql',
    soy: 'soy',
    stylus: 'styl',
    swift: 'swift',
    tcl: 'tcl',
    textile: 'textile',
    tt2: 'pm',
    twig: 'twig',
    typescript: 'ts',
    velocity: 'vm',
    verilog: 'v',
    vhdl: 'vhdl',
    vim: 'vim',
    'visual-basic': 'vb',
    wasm: 'wast',
    xojo: 'xojo_code',
    xquery: 'xquery',
    yaml: 'yml',
};

export function sampleFileNameFromLanguage(language: string) {
    const extension = LANGUAGE_TO_EXTENSION[language];
    return `sample.${extension ?? 'txt'}`;
}
