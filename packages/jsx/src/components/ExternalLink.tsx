import {useCallback} from 'react';
import {MESSAGE_OPEN_EXTERNAL_LINK, OpenExternalLinkMessage} from '../dicts/message.js';
import {useSendMessage} from './ArticleContextProvider.js';

interface Props {
    href: string;
}

export function ExternalLink({href, ...props}: Props) {
    const sendMessage = useSendMessage();
    const handleClick = useCallback(
        (ev: React.MouseEvent<HTMLAnchorElement, MouseEvent>) => {
            ev.preventDefault();

            const message: OpenExternalLinkMessage = {
                type: MESSAGE_OPEN_EXTERNAL_LINK,
                payload: {href},
            };
            sendMessage(message);
        },
        [href, sendMessage]
    );

    return <a {...props} onClick={handleClick}></a>;
}
