.comate-theme-light {
    /* 前景（文字）色 */
    --markdown-foreground-color: #000;

    /* 题前景色，用于链接、可交互文本、普通按钮边框等 */
    --markdown-primary-color: #17c3e5;

    /* 主题背景色，用于主要按钮背景等 */
    --markdown-primary-background-color: #17c3e526;

    /* 主题背景色上的文字色 */
    --markdown-primary-contrast-color: #fff;
    --markdown-primary-contrast-color-hover: var(--markdown-primary-contrast-color);
    --markdown-primary-contrast-color-active: var(--markdown-primary-contrast-color);

    /* 禁用元素的背景色 */
    --markdown-disabled-background-color: #f5f5f5;

    /* 禁用元素的边框色 */
    --markdown-disabled-border-color: #d9d9d9;

    /* 禁用元素的前景色 */
    --markdown-disabled-foreground-color: #b8b8b8;

    /* 链接文字色，默认使用主题前景。 */
    --markdown-link-color: #17c3e5;

    /* table背景色 */
    --jsx-table-head-background-color: #fafafa;
    --jsx-table-body-background-color: #eaeaea;
    --jsx-table-border-color: #ccc;

    /* alert tag 颜色系列 */
    --jsx-alert-tag-normal-foreground: #00cc6d;
    --jsx-alert-tag-normal-background: #00cc6d40;
    --jsx-alert-tag-low-foreground: #969696;
    --jsx-alert-tag-low-background: #96969640;
    --jsx-alert-tag-medium-foreground: #fa0;
    --jsx-alert-tag-medium-background: #ffaa0040;
    --jsx-alert-tag-high-foreground: #f56a00;
    --jsx-alert-tag-high-background: #f56a0040;
    --jsx-alert-tag-critical-foreground: #fd171b;
    --jsx-alert-tag-critical-background: #fd171b50;
}

.comate-theme-dark {
    /* 前景（文字）色 */
    --markdown-foreground-color: #ddd;

    /* 主题色，用于链接、可交互文本、普通按钮边框等 */
    --markdown-primary-color: #17c3e5;

    /* 主题背景色，用于主要按钮背景等 */
    --markdown-primary-background-color: #17c3e526;

    /* 主题背景色上的文字色 */
    --markdown-primary-contrast-color: #fff;
    --markdown-primary-contrast-color-hover: var(--markdown-primary-contrast-color);
    --markdown-primary-contrast-color-active: var(--markdown-primary-contrast-color);

    /* 禁用元素的背景色 */
    --markdown-disabled-background-color: #6c757d;

    /* 禁用元素的边框色 */
    --markdown-disabled-border-color: #495057;

    /* 禁用元素的前景色 */
    --markdown-disabled-foreground-color: #dee2e6;

    /* 链接文字色，默认使用主题前景。 */
    --markdown-link-color: #17c3e5;

    /* table背景色 */
    --jsx-table-head-background-color: #0b0d11;
    --jsx-table-body-background-color: #161920;
    --jsx-table-border-color: #252525;

    /* alert tag 颜色系列 */
    --jsx-alert-tag-normal-foreground: #00cc6d;
    --jsx-alert-tag-normal-background: #00cc6d40;
    --jsx-alert-tag-low-foreground: #bfbfbf;
    --jsx-alert-tag-low-background: #bfbfbf40;
    --jsx-alert-tag-medium-foreground: #ffc700;
    --jsx-alert-tag-medium-background: #ffc70040;
    --jsx-alert-tag-high-foreground: #f58300;
    --jsx-alert-tag-high-background: #f5830040;
    --jsx-alert-tag-critical-foreground: #e62c4b;
    --jsx-alert-tag-critical-background: #e62c4b50;
}

.comate-article {
    word-wrap: normal;
    word-break: break-all;
    overflow-wrap: break-word;
    font-size: var(--vscode-font-size);
    color: var(--markdown-foreground-color);
}

/* 兼容老版IDEA Webview */
.comate-article p:first-child {
    margin-top: 0;
}

.comate-article p:last-child {
    margin-bottom: 0;
}

.comate-article code {
    font-family: monospace;
    padding: 0 .5em;
}

.comate-article blockquote {
    padding: 1em;
}

.comate-article table {
    width: 100%;
}

.comate-article table thead {
    background-color: var(--jsx-table-head-background-color);
}

.comate-article table tbody {
    background-color: var(--jsx-table-body-background-color);
}

.comate-article table th {
    border: 1px solid var(--comate-card-border);
    border-bottom: none;
}

.comate-article table td {
    /* border: 1px solid var(--jsx-table-border-color) !important; */
    border: 1px solid var(--comate-card-border);
    border-top: none;
}

.comate-article a:not(.chatbox-operation-button),
.comate-link {
    color: var(--markdown-link-color);
    cursor: pointer;
    margin-right: 4px;
    display: inline-block;
    max-width: 100%;
    white-space: nowrap;
    vertical-align: middle;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: left;
    font-size: .8125rem;
    line-height: 1.375rem;

    span {
        direction: ltr;
        unicode-bidi: bidi-override;
        vertical-align: text-bottom;
    }

    &:hover {
        text-decoration: underline;
    }
}

.comate-loading {
    display: flex;
    gap: 8px;
    align-items: center;

    .loading-icon {
        width: 16px;
        flex-shrink: 0;
    }

    p {
        margin: 0 !important;
    }
}

.comate-loading-canceled {
    display: inline-flex;
    gap: 4px;
    align-items: center;
}

.comate-suggestion {
    border-radius: 4px;
    overflow: hidden;
}

.comate-suggestion-title {
    display: inline-flex;
}

.comate-suggestion-header {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: space-between;
    height: 32px;
    padding: 0 12px;
    border-bottom: 1px solid var(--markdown-suggestion-header-splitter-color);
    background-color: var(--markdown-suggestion-header-background-color);
    color: var(--markdown-suggestion-header-color);
}

.comate-suggestion-accept-method-list {
    display: flex;
    flex-direction: row-reverse;
    gap: 8px;
}

.comate-accept {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.comate-code-block-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-bottom: 4px;
}

.comate-code-block-toggle-up {
    border-bottom: 6px solid var(--markdown-foreground-color);
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-radius: 2px;
}

.comate-code-block-toggle-down {
    border-top: 6px solid var(--markdown-foreground-color);
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-radius: 2px;
}

.comate-code-block-button {
    appearance: none;
    border: none;
    background: transparent;
    cursor: pointer;
    color: var(--markdown-foreground-color);
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
}

.comate-code-block-header {
    display: flex;
    gap: 8px;
    align-items: center;
}

.comate-code-block {
    margin: 0;
    font-size: 12px;
    background-color: var(--markdown-code-block-background-color);
}

.comate-button {
    appearance: none;
    display: flex;
    line-height: 22px;
    align-items: center;
    justify-content: center;
    margin: .5em 0;
    padding: 0 8px;
    border: 1px none transparent;
    border-radius: 4px;
    background: transparent;
    cursor: pointer;

    &:hover {
        opacity: 70%;
    }

    &:active {
        opacity: 70%;
    }
}

.comate-button-primary {
    background-color: var(--markdown-primary-background-color);
    border: 1px solid var(--markdown-primary-color);
    color: var(--markdown-primary-color);
}

.comate-button-default {
    color: var(--markdown-primary-color);
    border: 1px solid var(--markdown-primary-color);
}

.comate-button-text {
    color: var(--markdown-primary-color);
}

.comate-button-disabled {
    background-color: var(--markdown-disabled-background-color);
    border-color: var(--markdown-disabled-background-color);
    color: var(--markdown-disabled-foreground-color);
    cursor: not-allowed;
}

.comate-button-group {
    display: grid;
    grid-auto-flow: column;
    grid-auto-columns: 1fr;
    gap: 16px;
    margin: .5em 0;

    .comate-button {
        margin: 0;
    }
}

.comate-alert-tag {
    vertical-align: middle;
    margin-right: 4px;
    border-radius: 2px;
    padding: 1px 4px;
    font-size: 12px;
}

.comate-alert-tag-normal {
    color: var(--jsx-alert-tag-normal-foreground);
    background-color: var(--jsx-alert-tag-normal-background);
}

.comate-alert-tag-low {
    color: var(--jsx-alert-tag-low-foreground);
    background-color: var(--jsx-alert-tag-low-background);
}

.comate-alert-tag-medium {
    color: var(--jsx-alert-tag-medium-foreground);
    background-color: var(--jsx-alert-tag-medium-background);
}

.comate-alert-tag-high {
    color: var(--jsx-alert-tag-high-foreground);
    background-color: var(--jsx-alert-tag-high-background);
}

.comate-alert-tag-critical {
    color: var(--jsx-alert-tag-critical-foreground);
    background-color: var(--jsx-alert-tag-critical-background);
}

[class*='comate-layout-flex'] {
    display: flex;
    align-items: stretch;
}

[class*='comate-layout-inline-flex'] {
    display: inline-flex;
    align-items: stretch;
}

.comate-layout-flex-vertical {
    flex-direction: column;
}

.comate-layout-flex-vertical > [class*='comate-layout-flex'],
.comate-layout-flex-vertical > [class*='comate-layout-inline-flex'] {
    width: 100%;
}

.comate-layout-flex-vertical-start {
    flex-direction: column;
    align-items: flex-start;
}

.comate-layout-flex-no-wrap {
    flex-wrap: wrap;
}

.comate-layout-flex.gap-sm,
.comate-layout-inline-flex.gap-sm {
    gap: 2px;
}

.comate-layout-flex.gap-s,
.comate-layout-inline-flex.gap-s {
    gap: 4px;
}

.comate-layout-flex.gap-m,
.comate-layout-inline-flex.gap-m {
    gap: 8px;
}

.comate-layout-flex.gap-l,
.comate-layout-inline-flex.gap-l {
    gap: 16px;
}

.comate-layout-flex.gap-xl,
.comate-layout-inline-flex.gap-xl {
    gap: 20px;
}

.comate-layout-flex-center,
.comate-layout-inline-flex-center {
    align-items: center;
}

.comate-layout-flex-center-horizontal,
.comate-layout-inline-flex-center-horizontal {
    align-items: center;
    justify-content: center;
}

.comate-layout-flex-center-vertical,
.comate-layout-inline-flex-center-vertical {
    align-items: center;
    justify-content: center;
}

.comate-layout-flex-center-between,
.comate-layout-inline-flex-center-between {
    align-items: center;
    justify-content: space-between;
}

.comate-layout-flex-center-around,
.comate-layout-inline-flex-center-around {
    align-items: center;
    justify-content: space-around;
}

.comate-layout-flex-center-end,
.comate-layout-inline-flex-center-end {
    align-items: center;
    justify-content: flex-end;
}

.comate-action-button {
    display: flex;
    gap: .25rem;
    align-items: center;
    opacity: .6;
    position: relative;
}

.comate-gradient-card {
    padding: 10px 12px;
    position: relative;
    border-radius: 4px;
    background: linear-gradient(0deg, rgba(255, 255, 255, .02), rgba(255, 255, 255, .02)), linear-gradient(99.74deg, rgba(64, 128, 255, .02) 0%, rgba(14, 27, 53, .02) 21.44%);
}

.comate-gradient-card::before {
    content: "";
    position: absolute;
    inset: 0;
    border-radius: 4px;
    padding: 1px;
    background: var(--linear-gradient-color);
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    z-index: 1;
    pointer-events: none;
}

.comate-gradient-card-blue::before {
    --linear-gradient-color: linear-gradient(112.91deg, rgb(165, 226, 255, .2) .89%, rgba(112, 217, 140, .2) 4.71%, rgba(124, 125, 128, .3) 26.72%);
}

.comate-gradient-card-red::before {
    --linear-gradient-color: linear-gradient(112.91deg, rgba(255, 86, 100, .3) .89%, rgba(229, 69, 82, .3) 4.71%, rgba(124, 125, 128, .3) 26.72%);
}

.comate-gradient-card-yellow::before {
    --linear-gradient-color: linear-gradient(113.12deg, rgba(255, 214, 1, .3) .9%, rgba(255, 194, 51, .3) 5.3%, rgba(124, 125, 128, .3) 27.92%);
}

.comate-gradient-card-orange::before {
    --linear-gradient-color: linear-gradient(114.21deg, rgba(255, 143, 39, .3) .47%, rgba(207, 93, 23, .3) 4.73%, rgba(124, 125, 128, .3) 28.42%);
}

.comate-gradient-card-gray::before {
    --linear-gradient-color: linear-gradient(113.12deg, rgba(220, 221, 226, .3) .9%, rgba(153, 154, 158, .3) 5.3%, rgba(135, 140, 156, .3) 27.92%);
}

.comate-gradient-card-green::before {
    --linear-gradient-color: linear-gradient(112.91deg, rgba(63, 191, 97, .3) .89%, rgba(112, 217, 140, .3) 4.71%, rgba(124, 125, 128, .3) 26.72%);
}
