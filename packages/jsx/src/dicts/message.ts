import {AcceptMethod} from '@comate/plugin-shared-internals/schema';

/**
 * 打开项目内文件
 */
export const MESSAGE_OPEN_EXTERNAL_LINK = 'comate:openExternalLink';

export interface OpenExternalLinkMessage {
    type: typeof MESSAGE_OPEN_EXTERNAL_LINK;
    payload: {href: string};
}

/**
 * 打开项目内文件
 */
export const MESSAGE_OPEN_FILE = 'comate:openFile';

/**
 * 打开文件的信息
 *
 * IDE行为：打开由`to`指定的文件，如果`line`属性有值，定位到`line`行
 */
export interface OpenFilePayload {
    to: string;
    line?: number;
}

export interface OpenFileMessage {
    type: typeof MESSAGE_OPEN_FILE;
    payload: OpenFilePayload;
}

/**
 * 采纳代码
 */
export const MESSAGE_ACCEPT_SUGGESTION = 'comate:acceptSuggestion';

/**
 * 采纳代码的消息
 *
 * IDE行为：由`accept`决定采纳行为，如果`accept`中没有指定采纳的代码，使用`content`作为默认内容
 */
export interface AcceptSuggestionPayload {
    accept: AcceptMethod;
    content: string;
}

export interface AcceptSuggestionMessage {
    type: typeof MESSAGE_ACCEPT_SUGGESTION;
    payload: AcceptSuggestionPayload;
}

/**
 * 调用插件自定义命令
 */
export const MESSAGE_PLUGIN_COMMAND = 'comate:pluginCommand';

/**
 * 发送指令至插件的行为
 *
 * IDE行为：同时以`replyText`为内容新增一条用户消息（对话模式），同时发送包含`commandName`和`data`的消息至插件进程并响应该消息
 */
export interface PluginCommandPayload {
    commandName: string;
    data: any;
    replyText: string;
}

export interface PluginCommandMessage {
    type: typeof MESSAGE_PLUGIN_COMMAND;
    payload: PluginCommandPayload;
}

export type Message = OpenFileMessage | OpenExternalLinkMessage | AcceptSuggestionMessage | PluginCommandMessage;

export type MessageStatus =
    | 'inProgress' // 正在生成中
    | 'success' // 生成完成
    | 'canceled' // 已取消
    | 'failed'; // 生成失败
