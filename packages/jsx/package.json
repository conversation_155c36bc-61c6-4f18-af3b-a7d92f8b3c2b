{"name": "@comate/plugin-jsx", "version": "0.9.2", "type": "module", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js"}, "./style.css": "./dist/style.css"}, "main": "dist/index.js", "files": ["dist"], "contributors": ["zhanglili01 <<EMAIL>>"], "scripts": {"clean": "rm -rf dist", "build": "tsc -p tsconfig.build.json && sh copyAssets.sh", "dev": "pnpm run clean && pnpm run build && tsc -p tsconfig.build.json -w", "play": "skr play src/components/Article.tsx", "lint": "skr lint", "bundle": "echo 'not implemented' && exit 1"}, "dependencies": {"@comate/plugin-shared-internals": "0.9.2", "classnames": "^2.5.1", "huse": "^2.0.4", "material-file-icons": "^2.4.0", "preact": "^10.19.5", "rc-tooltip": "^6.1.3", "react-copy-to-clipboard": "^5.1.0", "react-icons": "^5.0.1", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "remark-gfm": "^4.0.0"}, "devDependencies": {"@reskript/cli": "6.2.0", "@reskript/cli-dev": "6.2.0", "@reskript/cli-lint": "6.2.0", "@reskript/cli-play": "6.2.0", "@reskript/config-lint": "6.2.0", "@reskript/settings": "6.2.0", "@types/hast": "^3.0.3", "@types/react": "^18.2.48", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-syntax-highlighter": "^15.5.11", "core-js": "^3.35.0", "eslint": "^8.56.0", "postcss": "^8.4.35", "postcss-cli": "^11.0.0", "postcss-preset-env": "^9.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-toastify": "^10.0.4", "typescript": "^5.3.2"}}