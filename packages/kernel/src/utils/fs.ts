import {access} from 'node:fs/promises';
import {join} from 'path';
import {tmpdir} from 'os';
import fs from 'fs';

export const makeDirectorySync = (path: string) => {
    if (!fs.existsSync(path)) {
        fs.mkdirSync(path, {recursive: true});
    }
};

/**
 * @deprecated use nodejs isFileExist directly
 */
export async function isFileExist(path: string): Promise<boolean> {
    try {
        await access(path);
        return true;
    }
    catch (e) {
        return false;
    }
}

export const COMATE_TMP_FOLDER = join(tmpdir(), 'comate');
