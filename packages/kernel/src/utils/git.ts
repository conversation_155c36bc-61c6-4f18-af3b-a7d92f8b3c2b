import path from 'node:path';
import pMemoize from 'p-memoize';
import {execCommand} from './cp.js';
import {parseRemoteUri} from './parseRemoteUri.js';

export async function getRepoHostAndName(cwd: string) {
    const remoteUrl = await execCommand(
        'git config --get remote.origin.url',
        cwd
    )
        .catch(() => undefined);
    if (!remoteUrl) {
        return undefined;
    }
    const {host, repository} = parseRemoteUri(remoteUrl);
    if (host === 'localhost') {
        return undefined;
    }
    return `${host}/${repository}`;
}

export async function getRepoName(cwd: string) {
    const remoteUrl = await execCommand(
        'git config --get remote.origin.url',
        cwd
    );
    const {repository} = parseRemoteUri(remoteUrl);
    return repository;
}

export const getRepoNameMemoized = pMemoize(getRepoName);

export async function getCurrentBranch(cwd: string) {
    const branch = await execCommand(
        'git symbolic-ref --short HEAD',
        cwd
    )
        .catch(() => undefined);
    return branch;
}

export async function getRepoRootPath(cwd: string) {
    const rootPath = await execCommand(
        'git rev-parse --show-toplevel',
        cwd
    )
        .catch(() => undefined);
    return rootPath;
}

export async function isGitRepo(cwd: string) {
    const repoRoot = await getRepoRootPath(cwd);
    return repoRoot !== undefined;
}

export const getTraceRepo = async (cwd: string) => {
    try {
        const repository = await getRepoNameMemoized(cwd);
        if (repository) {
            return repository;
        }
    }
    catch (ex) {
        //
    }

    // 取前两级
    return path.relative(path.resolve(cwd, path.join('..', '..')), cwd);
};
