/**
 * @file child process 的 promisify 封装常用函数
 * <AUTHOR>
 */

import * as cp from 'child_process';

interface ExecCommandOptions {
    timeout?: number;
    shell?: string;
}

/**
 * cp.exec 的 Promise 封装
 */
export function execCommand(command: string, cwd: string, options?: ExecCommandOptions): Promise<string> {
    return new Promise((resolve, reject) => {
        cp.exec(command, {
            cwd,
            encoding: 'utf8',
            ...options,
        }, (err, stdout) => {
            err ? reject(err) : resolve(stdout.trim());
        });
    });
}
