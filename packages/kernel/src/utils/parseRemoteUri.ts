/**
 * @file This file is ported from: https://github.com/dgoguerra/parse-repo/blob/master/index.js
 */
import * as path from 'node:path';

interface RemoteInfo {
  remote: string;
  protocol: string;
  host: string;
  repository: string;
  owner: string | null;
  project: string;
}

export function parseRemoteUri(inputUri: string): RemoteInfo {
    // Ignore any trailing modifiers, such as the ones supported
    // on npm uris (ex: git+ssh://**************:npm/npm.git#v1.0.27)
    const uri = inputUri.replace(/#.+$/, '');

    let matches: RegExpMatchArray | null = null;

    // Check if it's a URL with the following known format:
    // http://site.com/owner/project(.git)
    // Any protocols are accepted, to support complex protocols
    // (ex. git+https://<EMAIL>/npm/npm.git)
    const generalFormat = /^([^:]+):\/\/([^@]+@)?([^/:]+)\/([^/]+)\/([^/]+)$/;

    if ((matches = generalFormat.exec(uri))) {
        const owner = matches[4];
        const project = matches[5].replace(/\.git$/, '');

        return {
            remote: inputUri,
            protocol: matches[1],
            host: matches[3],
            repository: `${owner}/${project}`,
            owner,
            project,
        };
    }

    // Check if it's a URL with the following known format:
    // (ssh://)************:owner/project(.git)
    const gitKnownRemote = /^(([^:]+):\/\/)?git@([^:]+):([^/]+)\/([^/]+)$/;

    if ((matches = gitKnownRemote.exec(uri))) {
        const owner = matches[4];
        const project = matches[5].replace(/\.git$/, '');

        return {
            remote: inputUri,
            protocol: matches[2] || 'ssh',
            host: matches[3],
            repository: `${owner}/${project}`,
            owner,
            project,
        };
    }

    // Check if it's a URI with any protocol, host, and path.
    // ex: ssh://<EMAIL>/~/repos/R.git
    const looseUriFormat = /^([^:]+):\/\/([^@]+@)?([^/]+)\/(.+)$/;

    if ((matches = looseUriFormat.exec(uri))) {
        const repoPath = matches[4];
        const project = path.basename(repoPath).replace(/\.git$/, '');

        return {
            remote: inputUri,
            protocol: matches[1],
            host: matches[3],
            repository: repoPath,
            owner: null,
            project,
        };
    }

    // The remote is a local path or an unknown URL, just
    // return its repository name without the owner's info

    const dirName = path.basename(uri);
    const repoName = dirName.replace(/\.git$/, '');

    return {
        remote: inputUri,
        protocol: 'file',
        host: 'localhost',
        owner: null,
        repository: repoName,
        project: repoName,
    };
}
