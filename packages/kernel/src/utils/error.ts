import {ErrorObject} from 'ajv';
import {ResponseError} from 'vscode-languageserver';

export function extractAjvErrors(errors?: ErrorObject[] | null) {
    return errors?.map(e => `${e.instancePath} ${e.message}`).join(';') ?? '';
}

export const LSP_ERROR_CODE = {
    ServerError: -32001,
    ParseError: -32700,
    InvalidRequest: -32600,
    MethodNotFound: -32601,
    InvalidParams: -32602,
    InternalError: -32603,
    /**
     * The client has canceled a request and a server has detected
     * the cancel.
     */
    RequestCancelled: -32800,
    DeviceFlowFailed: 1001,
    ContextNotInitialized: 1002,
    AbortedByServer: 1003,
    CancelledByServer: 1004,
    GetGitRepoIdError: 1005,
};

export function createLSPErrorResponse(code: number, message: string) {
    return new ResponseError(code, message, message);
}

export const SUCCESS_CODE = 0;

export const TYPE_TO_CODE = {
    success: SUCCESS_CODE,
    failed: LSP_ERROR_CODE.ServerError,
    aborted: LSP_ERROR_CODE.AbortedByServer,
    cancelled: LSP_ERROR_CODE.CancelledByServer,
};
