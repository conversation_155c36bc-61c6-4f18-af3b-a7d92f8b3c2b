import crypto from 'crypto';
import {readFileSync, statSync as getFileState} from 'fs';
import {iocContainer} from '../Context.js';
import {
    IndexerConfig,
    PatchFileIndex,
} from '../api/codeSearch.js';
import {getRepoHostAndName, getRepoRootPath, getCurrentBranch} from '../utils/git.js';
import {kernel} from '@comate/kernel-shared';

export async function getMetadata(workspacePath: string): Promise<EmbeddingsWorkspaceMetadata> {
    // 需要实现依赖注入
    const device = kernel.env.deviceId;
    const [gitRepoName, gitRootPath] = await Promise.all([
        getRepoHostAndName(workspacePath),
        getRepoRootPath(workspacePath),
    ]);
    if (gitRepoName && gitRootPath) {
        const branch = await getCurrentBranch(workspacePath);
        return {
            device,
            project: gitRepoName,
            branch,
            uploadBasePath: gitRootPath,
        };
    }
    return {
        device,
        project: workspacePath,
        uploadBasePath: workspacePath,
    };
}

export interface EmbeddingsWorkspaceMetadata {
    device?: string;
    project?: string;
    branch?: string;
    repoId?: string;
    uploadBasePath: string; // 文件上传给服务端的相对路径，相对于此路径
}

export function createHash(content: string | Buffer) {
    const hash = crypto
        .createHash('sha1')
        .update(content)
        .digest('hex');
    return hash;
}

export function computeRepoIdByMetadata(metadata: EmbeddingsWorkspaceMetadata) {
    const {device, project, branch} = metadata;
    if (!device || !project) {
        // 缺少 device 和 project 时无法计算 repoId
        return undefined;
    }
    const composition = [device, project, branch].filter(item => !!item).join('+');
    return createHash(composition);
}

export async function computeRepoId(workspacePath: string | undefined) {
    if (!workspacePath) {
        return undefined;
    }
    const metadata = await getMetadata(workspacePath);
    return computeRepoIdByMetadata(metadata);
}

export async function createFileIndexPatchAction(
    filePath: string,
    oldFileHash: string | undefined,
    maxFileSizeKb: number
): Promise<
    {
        action: PatchFileIndex['action'];
        sha1: string;
        content: string;
    } | undefined
> {
    try {
        const stat = getFileState(filePath);
        if (!stat) {
            return undefined;
        }
        const {size} = stat;
        if (size === -1) {
            return undefined;
        }
        if (size > maxFileSizeKb * 1024) {
            return undefined;
        }
        const content = readFileSync(filePath, 'utf-8');
        if (!content) {
            // 文件内容为空，或获取失败，跳过
            return undefined;
        }
        const newFileHash = createHash(content);
        if (!oldFileHash) {
            // 新文件，添加
            return {
                action: 'create',
                sha1: newFileHash,
                content,
            };
        }
        if (oldFileHash !== newFileHash) {
            // 文件 hash 不同，更新
            return {
                action: 'update',
                sha1: newFileHash,
                content,
            };
        }
        return undefined;
    }
    catch (e) {
        return undefined;
    }
}
