import {Container, inject, injectable} from 'inversify';
import {EmbeddingJob, FirstBuildEmbeddingJob, RebuildEmbeddingJob, Internval24hEmbeddingJob} from './EmbeddingJob.js';
import {ApiCodeSearchService} from '../api/codeSearch.js';
import {EmbeddingsIndexProgressStore} from './InprogressStore.js';
import {kernel} from '@comate/kernel-shared';

@injectable()
export class EmbeddingJobManager {
    private readonly jobs: EmbeddingJob[] = [];

    /** 创建任务时的策略，`existSingleRunningJob` 表示只允许单个任务存在，`everyRepoIdExistSingleJob` 表示允许多个任务存在 */
    private readonly createJobStrategy: 'existSingleRunningJob' | 'everyRepoIdExistSingleJob' =
        'everyRepoIdExistSingleJob'; // 'existSingleRunningJob
    constructor(
        @inject(ApiCodeSearchService) protected readonly api: ApiCodeSearchService,
        @inject(EmbeddingsIndexProgressStore) protected readonly progressStore: EmbeddingsIndexProgressStore
    ) {
    }

    get runningJobs() {
        return this.jobs.filter(job => job.running);
    }

    getExistRunningJob(repoId: string) {
        return this.runningJobs.find(job => job.description.repoId === repoId);
    }

    createJob(job: typeof FirstBuildEmbeddingJob, rootPath: string, repoId: string): FirstBuildEmbeddingJob;
    createJob(job: typeof RebuildEmbeddingJob, rootPath: string, repoId: string): RebuildEmbeddingJob;
    createJob(
        job: typeof Internval24hEmbeddingJob,
        rootPath: string,
        repoId: string,
        version: 'v1' | 'v2'
    ): Internval24hEmbeddingJob;
    createJob(
        factory: typeof FirstBuildEmbeddingJob | typeof RebuildEmbeddingJob | typeof Internval24hEmbeddingJob,
        rootPath: string,
        repoId: string,
        version?: 'v1' | 'v2'
    ) {
        // 取消所有正在运行的job
        if (this.createJobStrategy === 'existSingleRunningJob') {
            for (const job of this.runningJobs) {
                job.cancel();
            }
        }
        // 取消所有正在运行的job中repoId和创建任务相同的job
        else if (this.createJobStrategy === 'everyRepoIdExistSingleJob') {
            for (const job of this.runningJobs) {
                if (job.description.repoId === repoId) {
                    kernel.logger.info(`embedding(info): existed ${factory.name} job found`);
                    return job;
                }
            }
        }

        kernel.logger.info(`embedding(info): ${factory.name} job created`);
        if (factory === FirstBuildEmbeddingJob) {
            const job = new FirstBuildEmbeddingJob(this.api, this.progressStore, rootPath, repoId);
            this.jobs.push(job);
            return job;
        }
        else if (factory === RebuildEmbeddingJob) {
            const job = new RebuildEmbeddingJob(this.api, this.progressStore, rootPath, repoId);
            this.jobs.push(job);
            return job;
        }
        else {
            const job = new Internval24hEmbeddingJob(
                this.api,
                this.progressStore,
                rootPath,
                repoId,
                version!
            );
            this.jobs.push(job);
            return job;
        }
    }

    cancelJob(rootPath: string) {
        for (const job of this.runningJobs) {
            if (job.description.workspacePath === rootPath) {
                job.cancel();
            }
        }
    }
}
