import {kernel} from '@comate/kernel-shared';
import {injectable} from 'inversify';

@injectable()
export class EmbeddingsIndexProgressStore {
    async getLastUploadTime(repoId: string) {
        const records = kernel.storage.get<Record<string, number | undefined>>(
            'embeddings_upload_records',
            {}
        );
        return records[repoId];
    }

    async updateUploadTime(repoId: string) {
        const records = kernel.storage.get<Record<string, number | undefined>>(
            'embeddings_upload_records',
            {}
        );
        kernel.storage.update('embeddings_upload_records', {
            ...records,
            [repoId]: Date.now(),
        });
    }
}
