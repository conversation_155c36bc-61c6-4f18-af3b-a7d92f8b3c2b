import {inject, injectable} from 'inversify';
import {Disposable, ResponseError} from 'vscode-languageserver';
import {Ajv} from 'ajv';
import {getRepoHostAndName} from '../utils/git.js';
import {ApiCodeSearchService} from '../api/codeSearch.js';
import {
    RebuildEmbeddingJob,
    EmbeddingJob,
    FirstBuildEmbeddingJob,
    Internval24hEmbeddingJob,
    EmbeddingFileExceedError,
    EmbeddingJobDescription,
} from './EmbeddingJob.js';
import {EmbeddingsIndexProgressStore} from './InprogressStore.js';
import {computeRepoId} from './EmbedingUtil.js';
import {EmbeddingJobManager} from './JobManager.js';
import {createLSPErrorResponse, LSP_ERROR_CODE, extractAjvErrors, SUCCESS_CODE} from '../utils/error.js';
import type {ResponseBase} from '@comate/kernel-shared/types';
import {kernel} from '@comate/kernel-shared';
import {CodeGraphController} from '../codeGraphService/controller.js';

const ajv = new Ajv();

const schema = {
    type: 'object',
    properties: {
        rootPath: {type: 'string'},
    },
    required: ['rootPath'],
};

interface WorkspaceFolder {
    uri: string;
}

interface QualifiedWorkspace {
    qualified: true;
    repoId: string;
}

interface UnqualifiedWorkspace {
    qualified: false;
    reason: string;
}

type WorkspaceQualification = QualifiedWorkspace | UnqualifiedWorkspace;

@injectable()
export class EmbeddingsController implements Disposable {
    private branchChangeMonitoringInterval: number = 10;
    private readonly disposables: Disposable[] = [];

    constructor(
        @inject(EmbeddingJobManager) private readonly embeddingJobManager: EmbeddingJobManager,
        @inject(EmbeddingsIndexProgressStore) private readonly indexProgressStore: EmbeddingsIndexProgressStore,
        @inject(ApiCodeSearchService) private readonly api: ApiCodeSearchService,
        @inject(CodeGraphController) private readonly codeGraphController: CodeGraphController
    ) {
    }

    private listenConnection() {
        // @ts-ignore
        kernel.connect.onRequest('computeRepoId', async param => {
            const validate = ajv.validate(schema, param);
            if (!validate) {
                return createLSPErrorResponse(LSP_ERROR_CODE.InvalidParams, extractAjvErrors(ajv.errors));
            }
            try {
                const repoId = await computeRepoId(param.rootPath);
                return {code: SUCCESS_CODE, data: {repoId}};
            }
            catch (e) {
                return createLSPErrorResponse(LSP_ERROR_CODE.InternalError, (e as Error).message ?? 'unknown error');
            }
        });

        kernel.connect.onRequest(
            // @ts-ignore
            'recreateWorkspaceIndex',
            async (param: {rootPath: string, buildGraph?: boolean, buildEmbedding?: boolean}) => {
                const validate = ajv.validate(schema, param);
                try {
                    if (!validate) {
                        return createLSPErrorResponse(LSP_ERROR_CODE.InvalidParams, extractAjvErrors(ajv.errors));
                    }
                    const isValidGitRepo = await getRepoHostAndName(param.rootPath);
                    if (!isValidGitRepo) {
                        return createLSPErrorResponse(
                            LSP_ERROR_CODE.GetGitRepoIdError,
                            'Recreate workspace Index failed, because current directory is not a valid git repo'
                        );
                    }
                    kernel.logger.info('env isPoc', kernel.env.isPoc);
                    if (kernel.env.isPoc) {
                        kernel.logger.warn(
                            'recreateWorkspaceIndex(warn): POC agent does not support building workspace index'
                        );
                        return {code: SUCCESS_CODE, data: false};
                    }

                    kernel.logger.info(`recreateWorkspaceIndex(info): build for ${param.rootPath}`);
                    if (param.buildEmbedding) {
                        this.recreateWorkspaceIndex(param.rootPath);
                    }
                    if (param.buildGraph) {
                        this.codeGraphController.recreateWorkspaceIndex(param.rootPath);
                    }
                    return {code: SUCCESS_CODE, data: true};
                }
                catch (e) {
                    return createLSPErrorResponse(
                        LSP_ERROR_CODE.InternalError,
                        (e as Error).message ?? 'unknown error'
                    );
                }
            }
        );

        // 标准返回ts类型demo，当前事件可能是废弃的
        kernel.connect.onRequest(
            // @ts-ignore
            'getWorkspaceIndexProgress',
            // @ts-ignore
            async (param): Promise<ResponseBase<{progress: number}> | ResponseError<string>> => {
                const validate = ajv.validate(schema, param);
                if (!validate) {
                    return createLSPErrorResponse(LSP_ERROR_CODE.InvalidParams, extractAjvErrors(ajv.errors));
                }
                const isValidGitRepo = await getRepoHostAndName(param.rootPath);
                if (!isValidGitRepo) {
                    return createLSPErrorResponse(
                        LSP_ERROR_CODE.GetGitRepoIdError,
                        'Recreate workspace Index failed, because current directory is not a valid git repo'
                    );
                }

                if (kernel.env.isPoc) {
                    kernel.logger.warn(
                        'getWorkspaceIndexProgress(warn): POC agent does not support building workspace index'
                    );
                    const res: ResponseBase<{progress: number}> = {
                        code: SUCCESS_CODE,
                        data: {progress: 100},
                        message: 'success',
                    };
                    return res;
                }

                try {
                    kernel.logger.info(`getWorkspaceIndexProgress(info): build for ${param.rootPath}`);
                    const data = await this.getProgress(param.rootPath);
                    return {code: SUCCESS_CODE, data, message: 'success'};
                }
                catch (e) {
                    return createLSPErrorResponse(
                        LSP_ERROR_CODE.InternalError,
                        (e as Error).message ?? 'unknown error'
                    );
                }
            }
        );
    }

    run() {
        // 当前从agent迁移，只有intelliJ用到，vscode迁移待定
        if (!kernel.env.isVSCode) {
            this.listenConnection();
            const workSpaceInfo = kernel.env.workspaceInfo;
            kernel.logger.info(`embeddings(autoRun): start workspaces ${workSpaceInfo.rootUri}`);
            const uri = workSpaceInfo.workspaceFolders?.[0]?.uri;
            if (uri) {
                this.start(kernel.uri.parse(uri).fsPath);
            }
        }
    }

    private readonly intervals: Map<string, Record<string, NodeJS.Timer>> = new Map();
    clearIntervals(rootPath: string) {
        const intervals = Object.values(this.intervals.get(rootPath) ?? {});
        intervals.forEach(interval => clearInterval(interval));
        this.intervals.delete(rootPath);
    }
    /**
     * 开始自动化文件上传/索引更新任务
     * @param workspaceFolders 初始化时用户打开的工作区
     * @param listenWorkspaceChanges 是否要监听 lsp client 发送的 workspace 变化的事件
     */
    // startAutomaticJobs(workspaceFolders: WorkspaceFolder[], branchChangeMonitoringInterval: number) {
    //     kernel.logger.info(`embeddings(startAutomaticJobs): start workspaces ${workspaceFolders[0].uri}`);
    //     this.branchChangeMonitoringInterval = branchChangeMonitoringInterval;
    //     for (const item of workspaceFolders) {
    //         if (!this.intervals.get(item.uri)) {
    //             this.start(kernel.uri.parse(item.uri).fsPath);
    //         }
    //     }
    // }

    async getProgress(rootPath: string) {
        const job = this.embeddingJobManager.runningJobs.find(job => job.description.workspacePath === rootPath);
        if (job) {
            return {progress: job.description.progress};
        }

        try {
            const isValidGitRepo = await getRepoHostAndName(rootPath);
            const repoId = await computeRepoId(rootPath);
            if (!repoId || !isValidGitRepo) {
                throw new Error('Unable to compute repoId for the give root path');
            }
            const version = await this.api.getRemoteIndexVersion(repoId);
            const {progress} = await this.api.getRemoteIndexInfo(repoId, version);
            return {progress};
        }
        catch (ex) {
            return {progress: 100};
        }
    }

    async recreateWorkspaceIndex(rootPath: string) {
        const repoId = await computeRepoId(rootPath);
        if (!repoId) {
            kernel.logger.info('embeddings(info): skip for failing to compute repoId');
            return;
        }

        const existRepoJob = await this.embeddingJobManager.getExistRunningJob(repoId);
        kernel.logger.info(`embeddings(info): recreateWorkspaceIndex ${existRepoJob ? 'skipped' : 'success'}`);
        if (!existRepoJob) {
            const job = this.embeddingJobManager.createJob(
                RebuildEmbeddingJob,
                rootPath,
                repoId
            );
            job.addListener(this.updateIndexBuildProgress.bind(this));
            await job.execute();
        }
    }

    async start(rootPath: string) {
        this.clearIntervals(rootPath);
        kernel.logger.info(`embeddings(info): embeddingJobStart ${rootPath}`);
        try {
            const repoId = await computeRepoId(rootPath);
            const isValidGitRepo = await getRepoHostAndName(rootPath);
            if (!isValidGitRepo) {
                kernel.logger.info('embeddings(info): skip for invalid git repo');
                return;
            }
            if (!repoId) {
                kernel.logger.info('embeddings(info): skip for failing to compute repoId');
                return;
            }
            const version = await this.api.getRemoteIndexVersion(repoId);
            let job: EmbeddingJob | null = null;
            if (version === 'v1') {
                const {data} = await this.api.getRemoteIndexInfo(repoId, version);
                if (!data?.length) {
                    job = this.embeddingJobManager.createJob(
                        FirstBuildEmbeddingJob,
                        rootPath,
                        repoId
                    );
                    job!.addListener(this.updateIndexBuildProgress.bind(this));
                    await job!.execute();
                    return;
                }
            }

            if (await this.satisfyIndexUpdateInterval(repoId)) {
                kernel.logger.info(`embeddings(info): create Internval24hEmbeddingJob ${repoId}`);
                job = this.embeddingJobManager.createJob(
                    Internval24hEmbeddingJob,
                    rootPath,
                    repoId,
                    version
                );
                job!.addListener(this.updateIndexBuildProgress.bind(this));
                await job!.execute();
            }

            const repoIdInterval = setInterval(
                async () => {
                    const newRepoId = await computeRepoId(rootPath);
                    if (newRepoId && newRepoId !== repoId) {
                        job?.cancel();
                        this.clearIntervals(rootPath);
                        kernel.logger.info(`embeddings(info): git branch changed newRepodId=${repoId}`);
                        this.start(rootPath);
                    }
                },
                1000 * this.branchChangeMonitoringInterval // 默认每 10s 检查一次，服务下发
            );

            // 更新索引定时任务
            const generalInterval = setInterval(
                async () => {
                    if (job?.description.running) {
                        return;
                    }
                    if (await this.satisfyIndexUpdateInterval(repoId)) {
                        job = this.embeddingJobManager.createJob(
                            Internval24hEmbeddingJob,
                            rootPath,
                            repoId,
                            version
                        );
                        job!.addListener(this.updateIndexBuildProgress.bind(this));
                        job!.execute();
                    }
                },
                1000 * 60 * 60 // 每 1 小时执行一次
            );

            this.intervals.set(rootPath, {generalInterval, repoIdInterval});
        }
        catch (ex) {
            if (ex instanceof EmbeddingFileExceedError) {
                kernel.connect.sendNotification('showNotification', {
                    message: `无法创建向量索引：当前项目已超过${ex.threshold}个文件上限`,
                    level: 'error',
                });
                return;
            }
            kernel.logger.error((ex as Error).message);
        }
    }

    private async satisfyIndexUpdateInterval(repoId: string) {
        const lastUploadTime = await this.indexProgressStore.getLastUploadTime(repoId) ?? 0;
        kernel.logger.info('embeddings(info): last update time:', new Date(lastUploadTime).toString());
        const indexerConfig = await this.api.getIndexerConfig();
        const minUploadInterval = 1000 * 60 * Math.max(indexerConfig.config.index_interval_minute || 5, 5);
        const timeSinceLastUpdate = Date.now() - lastUploadTime;
        if (timeSinceLastUpdate <= minUploadInterval) {
            const wait = Math.ceil((minUploadInterval - timeSinceLastUpdate) / (1000 * 60));
            kernel.logger.info(
                `(embeddings) skip updating for not reaching the minimum update interval, ${wait} minutes to go`
            );
            return false;
        }
        return true;
    }

    private updateIndexBuildProgress(description: EmbeddingJobDescription) {
        kernel.connect.sendNotification('indexJobProgress', description);
    }

    dispose() {
        this.disposables.forEach(d => d.dispose());
    }
}
