import {Container, interfaces} from 'inversify';

function logInjection(planAndResolve: interfaces.Next): interfaces.Next {
    return (args: interfaces.NextArgs) => {
        let start = new Date().getTime();
        let result = planAndResolve(args);
        let end = new Date().getTime();
        return result;
    };
}

export const iocContainer = new Container();
iocContainer.applyMiddleware(logInjection);
