import {iocContainer} from './Context.js';
import {EmbeddingsIndexProgressStore} from './embeddingService/InprogressStore.js';
import {EmbeddingsController} from './embeddingService/Controller.js';
import {EmbeddingJobManager} from './embeddingService/JobManager.js';
import {ApiCodeSearchService} from './api/codeSearch.js';
import {CodeGraphIndexProgressStore} from './codeGraphService/IndexProgressStore.js';
import {CodeGraphController} from './codeGraphService/controller.js';
import {CodeGraphJobManager} from './codeGraphService/JobManager.js';
import {CodeGraphAPIService} from './codeGraphService/api.js';
import {PassthroughDemoService} from './passthroughDemoService/index.js';

export function active() {
    iocContainer.bind(EmbeddingsIndexProgressStore).toSelf().inSingletonScope();
    iocContainer.bind(EmbeddingsController).toSelf().inSingletonScope();
    iocContainer.bind(EmbeddingJobManager).toSelf().inSingletonScope();
    iocContainer.bind(ApiCodeSearchService).toSelf().inSingletonScope();

    iocContainer.bind(CodeGraphIndexProgressStore).toSelf().inSingletonScope();
    iocContainer.bind(CodeGraphController).toSelf().inSingletonScope();
    iocContainer.bind(CodeGraphJobManager).toSelf().inSingletonScope();
    iocContainer.bind(CodeGraphAPIService).toSelf().inSingletonScope();

    iocContainer.get(EmbeddingsController).run();
    iocContainer.get(CodeGraphController).run();

    iocContainer.bind(PassthroughDemoService).toSelf().inSingletonScope();
    iocContainer.get(PassthroughDemoService).start();
}
