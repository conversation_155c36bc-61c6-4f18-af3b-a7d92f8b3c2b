import {kernel} from '@comate/kernel-shared';
import {PatchFileIndex} from '../api/codeSearch.js';
import {injectable} from 'inversify';

interface Res<T> {
    code: number;
    message: string | null;
    data: T;
}

type TimestampVersion = number;
type UnBuiltVersion = -1;
export interface RemoteVersionInfo {
    version: TimestampVersion | UnBuiltVersion;
    scene: 'FULL_BUILD' | 'UPDATE' | 'NO_OP';
}

interface RemoteVersionInitResult {
    oldVersion: RemoteVersionInfo['version'];
    version: TimestampVersion;
    user: string;
    name: string;
    repoId: string;
}

export interface RemoteCodeGraphConfig {
    blackPattern: string[];
    whitePattern: string[];
    config: {
        maxFileSizeKb: number;
        maxFileCount: number;
        indexBuildIntervalMinute: number;
        fileIntervalMilliseconds: number;
        responseTimeoutSeconds: number;
        concurrentFileCount: number;
        /** 代码库全量构建的间隔时间，单位毫秒（端上定时器24小时会触发一次，因此大于24小时即可，保证全量构建前必然有一次增量的） */
        fullBuildIntervalMilliSeconds: number;
    };
}

interface GraphFileIndexes {
    repoId: string;
    user: string;
    scene: RemoteVersionInfo['scene'];
    files: Array<{sha1: string, indexAction: PatchFileIndex['action']}>;
}

@injectable()
export class CodeGraphAPIService {
    private getAxios() {
        return kernel.http.axiosInstance;
    }

    get axiosConfig() {
        return {
            baseURL: kernel.env.isSaaS
                ? 'https://comate.baidu.com/api/aidevops/code-graph'
                : 'https://comate.baidu-int.com/api/aidevops/code-graph',
            headers: {
                'login-name': kernel.config.key,
                'Content-Type': 'application/json',
            },
        };
    }
    /**
     * 获取远端的图谱版本
     */
    async apiGetRemoteCodeGraphSnapshotVersion({repoId}: {repoId: string}) {
        const res = await this.getAxios().get<Res<RemoteVersionInfo>>(
            `/rest/v1/index/repo/${repoId}`,
            this.axiosConfig
        );
        return res.data.data;
    }
    /**
     * 初始化远端的图谱，如果存在会删除
     */
    async apiPostInitRemoteCodeGraphVersion(params: RemoteVersionInitResult) {
        const res = await this.getAxios().post<Res<RemoteVersionInitResult>>(
            '/rest/v1/index/repo',
            params,
            this.axiosConfig
        );
        return res.data.data;
    }

    /**
     * 获取构建图谱的参数配置，白名单和黑名单，并发数等等
     */
    async apiGetRemoteCodeGraphConfig() {
        const res = await this.getAxios().get<Res<RemoteCodeGraphConfig>>('/rest/v1/config', this.axiosConfig);
        return res.data.data;
    }

    /**
     * 向服务端推送图谱的模块信息
     */
    async apiPostRemoteCodeGraphModuleIndex(params: {repoId: string, user: string, modules: any[]}) {
        const res = await this.getAxios().post<Res<void>>('/rest/v1/index/modules', params, this.axiosConfig);
        return res.data.data;
    }

    /**
     * 向服务端推送图谱的点边信息
     */
    async apiPostRemoteCodeGraphIndex(params: GraphFileIndexes) {
        const res = await this.getAxios().post<Res<void>>('/rest/v1/index/files', params, this.axiosConfig);
        return res.data.data;
    }

    /**
     * 获取服务端图谱构建的最近一次的构建历史，用户过滤构建过但内容没变化的文件
     */
    async apiGetRemoteCodeGraphLatestBuildHistory({repoId}: {repoId: string}) {
        const res = await this.getAxios().get<Res<{files: Array<{id: string, sha1: string, path: string}>}>>(
            `/rest/v1/index/repo/${repoId}/resources`,
            this.axiosConfig
        );
        return res.data.data.files;
    }

    /**
     * 获取服务端图谱构建的最近一次的构建历史，用户过滤构建过但内容没变化的文件
     */
    async apiPostCodeGraphBuildComplete(params: {version: TimestampVersion, user: string, repoId: string}) {
        const res = await this.getAxios().put(
            `/rest/v1/index/repo/complete`,
            params,
            this.axiosConfig
        );
        return res.data;
    }
}
