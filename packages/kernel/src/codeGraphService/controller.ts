import {inject, injectable} from 'inversify';
import {Disposable} from 'vscode-languageserver';
import {iocContainer} from '../Context.js';
import {getRepoHostAndName} from '../utils/git.js';
import {computeRepoId} from '../embeddingService/EmbedingUtil.js';
import {CodeGraphIndexProgressStore} from './IndexProgressStore.js';
import {CodeGraphFileExceedError, CodeGraphJob} from './CodeGraphJob.js';
import {CodeGraphJobManager} from './JobManager.js';
import {CodeGraphAPIService} from './api.js';
import {
    IDE_GRAPH_CODEINDEXCOMPLETED_EVENT,
    KERNEL_GRAPH_CODEINDEXREADY_EVENT,
    IDE_GRAPH_INDEXREADY_EVENT,
    KERNEL_GRAPH_CODEINDEXCANCEL_EVENT,
} from '@comate/kernel-shared';
import {kernel} from '@comate/kernel-shared';
import {ApiCodeSearchService} from '../api/codeSearch.js';
import {LSP_ERROR_CODE, SUCCESS_CODE, createLSPErrorResponse, extractAjvErrors} from '../utils/error.js';
import {Ajv} from 'ajv';

export const CMD_RECREATE_INDEX = 'baidu.comate.recreateIndex';

interface WorkspaceFolder {
    uri: string;
}

const ajv = new Ajv();
const schema = {
    type: 'object',
    properties: {
        rootPath: {type: 'string'},
        repoId: {type: 'string'},
        moduleIndexFile: {type: 'string'},
        graphIndexFile: {type: 'string'},
    },
    required: ['rootPath', 'repoId'],
    additionalProperties: false,
};

@injectable()
export class CodeGraphController implements Disposable {
    private branchChangeMonitoringInterval: number = 10;
    private readonly disposables: Disposable[] = [];
    private readonly codeGraphJobManager: CodeGraphJobManager = new CodeGraphJobManager(
        iocContainer
    );

    constructor(
        @inject(CodeGraphIndexProgressStore) private readonly indexProgressStore: CodeGraphIndexProgressStore,
        @inject(CodeGraphAPIService) private readonly backend: CodeGraphAPIService,
        @inject(ApiCodeSearchService) private readonly codeSearchBackend: ApiCodeSearchService
    ) {
    }

    listenConnection() {
        kernel.connect.onRequest(
            // @ts-ignore
            IDE_GRAPH_INDEXREADY_EVENT,
            async (param: {rootPath: string, repoId: string, moduleIndexFile?: string, graphIndexFile?: string}) => {
                if (!ajv.validate(schema, param)) {
                    return createLSPErrorResponse(LSP_ERROR_CODE.InvalidParams, extractAjvErrors(ajv.errors));
                }

                kernel.logger.info(`handleWorkspaceCodeGraphProgress(info): build for ${param.rootPath}`);
                try {
                    await this.updateWorkspaceCodeGraph(param);
                    return {code: SUCCESS_CODE, data: true};
                }
                catch (e) {
                    return createLSPErrorResponse(
                        LSP_ERROR_CODE.InternalError,
                        (e as Error).message ?? 'unknown error'
                    );
                }
            }
        );
        kernel.connect.onRequest(
            IDE_GRAPH_CODEINDEXCOMPLETED_EVENT,
            async (param: {repoId: string}) => {
                kernel.logger.info(`handleWorkspaceCodeGraphCompleted: build for ${param.repoId}`);
                try {
                    const job = this.codeGraphJobManager.getExistRunningJob(param.repoId);
                    job?.complete();
                    return {code: SUCCESS_CODE, data: true};
                }
                catch (e) {
                    return createLSPErrorResponse(
                        LSP_ERROR_CODE.InternalError,
                        (e as Error).message ?? 'unknown error'
                    );
                }
            }
        );
    }

    run() {
        // 当前从agent迁移，只有intelliJ用到，vscode迁移待定
        if (!kernel.env.isVSCode) {
            this.listenConnection();
            const workSpaceInfo = kernel.env.workspaceInfo;
            kernel.logger.info(`codeGraph(autoRun): start workspaces ${workSpaceInfo.rootUri}`);
            const uri = workSpaceInfo.workspaceFolders?.[0]?.uri;
            this.branchChangeMonitoringInterval = 10;
            if (uri) {
                this.start(kernel.uri.parse(uri).fsPath);
            }
        }
    }

    private readonly intervals: Map<string, Record<string, NodeJS.Timer>> = new Map();
    clearIntervals(rootPath: string) {
        const intervals = Object.values(this.intervals.get(rootPath) ?? {});
        intervals.forEach(interval => clearInterval(interval));
        this.intervals.delete(rootPath);
    }
    /**
     * 开始自动化文件上传/索引更新任务
     * @param workspaceFolders 初始化时用户打开的工作区
     * @param listenWorkspaceChanges 是否要监听 lsp client 发送的 workspace 变化的事件
     */
    // startAutomaticJobs(workspaceFolders: WorkspaceFolder[], branchChangeMonitoringInterval: number) {
    //     kernel.logger.info(`codeGraph(startAutomaticJobs): start workspaces ${workspaceFolders[0].uri}`);
    //     this.branchChangeMonitoringInterval = branchChangeMonitoringInterval;
    //     for (const item of workspaceFolders) {
    //         if (!this.intervals.get(item.uri)) {
    //             try {
    //                 kernel.logger.info('codeGraph(startAutomaticJobs): start');
    //                 this.start(URI.parse(item.uri).fsPath);
    //             }
    //             catch (ex) {
    //                 kernel.logger.info(`codeGraph(startAutomaticJobs): faild, ex=${(ex as Error).message}`);
    //             }
    //         }
    //     }
    // }

    async getProgress() {
        // const job = this.codeGraphJobManager.runningJobs.find(job => job.description.workspacePath === rootPath);
        // if (job) {
        //     return {progress: job.description.progress};
        // }

        // try {
        //     const isValidGitRepo = await getRepoHostAndName(rootPath);
        //     const repoId = await computeRepoId(rootPath);
        //     if (!repoId || !isValidGitRepo) {
        //         throw new Error('Unable to compute repoId for the give root path');
        //     }
        //     const version = await getRemoteIndexVersion(repoId);
        //     const {progress} = await getRemoteIndexInfo(repoId, version);
        //     return {progress};
        // }
        // catch (ex) {
        //     return {progress: 100};
        // }
    }

    private async checkFeatureAccess() {
        const turnOnCodeGraph = await this.codeSearchBackend.checkFeatureAccess(
            kernel.config.username!,
            'comateCodeGraph'
        );
        kernel.logger.info(`codeGraph(info): checkFeatureAccess ${kernel.config.username}=${turnOnCodeGraph}`);
        return turnOnCodeGraph;
    }

    async recreateWorkspaceIndex(rootPath: string) {
        const turnOnCodeGraph = await this.checkFeatureAccess();
        if (!turnOnCodeGraph) {
            return;
        }

        const repoId = await computeRepoId(rootPath);
        if (!repoId) {
            return;
        }

        const existRepoJob = await this.codeGraphJobManager.getExistRunningJob(repoId);
        kernel.logger.info(`codeGraph(info): recreateWorkspaceIndex ${existRepoJob ? 'failed' : 'success'}`);
        if (!existRepoJob) {
            const {scene, version} = await this.backend.apiGetRemoteCodeGraphSnapshotVersion({repoId});
            const job = this.codeGraphJobManager.createJob(
                rootPath,
                repoId,
                {scene, version}
            );
            job.addListener(this.updateIndexBuildProgress.bind(this));
            await job.execute();
        }
    }

    async start(rootPath: string) {
        this.clearIntervals(rootPath);
        const turnOnCodeGraph = await this.checkFeatureAccess();
        if (!turnOnCodeGraph) {
            return;
        }

        kernel.logger.info(`codeGraph(info): codeGraphJobStart ${rootPath}`);
        try {
            const repoId = await computeRepoId(rootPath);
            const isValidGitRepo = await getRepoHostAndName(rootPath);
            if (!isValidGitRepo) {
                kernel.logger.info('codeGraph(info): skip for invalid git repo');
                return;
            }
            if (!repoId) {
                kernel.logger.info('codeGraph(info): skip for failing to compute repoId');
                return;
            }
            let job: CodeGraphJob | null = null;
            const {version, scene} = await this.backend.apiGetRemoteCodeGraphSnapshotVersion({repoId});
            if (scene !== 'NO_OP') {
                kernel.logger.info(`codeGraph(info): create job repoId=${repoId}`);
                job = this.codeGraphJobManager.createJob(
                    rootPath,
                    repoId,
                    {version, scene}
                );
                job.addListener(this.updateIndexBuildProgress.bind(this));
                await job.execute();
            }

            const repoIdInterval = setInterval(
                async () => {
                    const newRepoId = await computeRepoId(rootPath);
                    if (newRepoId && newRepoId !== repoId) {
                        job?.cancel();
                        this.clearIntervals(rootPath);
                        kernel.logger.info(`codeGraph(info): git branch changed newRepodId=${repoId}`);
                        this.start(rootPath);
                    }
                },
                1000 * this.branchChangeMonitoringInterval // 默认每 10s 检查一次，服务下发
            );

            // 更新索引定时任务
            const generalInterval = setInterval(
                async () => {
                    if (job?.description.running) {
                        return;
                    }
                    const {version, scene} = await this.backend.apiGetRemoteCodeGraphSnapshotVersion({repoId});
                    if (scene !== 'NO_OP') {
                        job = this.codeGraphJobManager.createJob(
                            rootPath,
                            repoId,
                            {version, scene}
                        );
                        job.addListener(this.updateIndexBuildProgress.bind(this));
                        job.execute();
                    }
                },
                1000 * 60 * 60 // 每 1 小时执行一次
            );

            this.intervals.set(rootPath, {generalInterval, repoIdInterval});
        }
        catch (ex) {
            if (ex instanceof CodeGraphFileExceedError) {
                kernel.connect.sendNotification('showNotification', {
                    message: `无法创建代码图谱索引：当前项目已超过${ex.threshold}个文件上限`,
                    level: 'error',
                });
                return;
            }
            kernel.logger.error(`codeGraph(failed): faild, ex=${(ex as Error).message}`);
        }
    }

    private updateIndexBuildProgress(payload: {event: 'init' | 'progress' | 'cancel', description: any}) {
        if (payload.event === 'init') {
            kernel.logger.info('(codeGraph) init');
            kernel.connect.sendNotification(KERNEL_GRAPH_CODEINDEXREADY_EVENT, payload.description);
        }
        else if (payload.event === 'cancel') {
            kernel.connect.sendNotification(KERNEL_GRAPH_CODEINDEXCANCEL_EVENT, payload.description);
        }
    }

    async updateWorkspaceCodeGraph(params: {repoId: string, moduleIndexFile?: string, graphIndexFile?: string}) {
        const job = this.codeGraphJobManager.getExistRunningJob(params.repoId);
        if (job) {
            if (params.moduleIndexFile) {
                await job.updateModuleIndexFile(params.moduleIndexFile);
            }
            else if (params.graphIndexFile) {
                await job.updateGraphIndexFile(params.graphIndexFile);
            }
        }
    }

    dispose() {
        this.disposables.forEach(d => d.dispose());
        // Clear all intervals to prevent memory leaks
        for (const [rootPath] of this.intervals) {
            this.clearIntervals(rootPath);
        }
    }
}
