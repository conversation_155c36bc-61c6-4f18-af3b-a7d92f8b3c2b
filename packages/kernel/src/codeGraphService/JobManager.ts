import {Container} from 'inversify';
import {CodeGraphJob} from './CodeGraphJob.js';
import {RemoteVersionInfo} from './api.js';
import {kernel} from '@comate/kernel-shared';

export class CodeGraphJobManager {
    private readonly jobs: CodeGraphJob[] = [];

    constructor(private readonly ctx: Container) {}
    get runningJobs() {
        return this.jobs.filter(job => job.running);
    }

    getExistRunningJob(repoId: string) {
        return this.runningJobs.find(job => job.description.repoId === repoId);
    }

    createJob(rootPath: string, repoId: string, version: RemoteVersionInfo) {
        for (const job of this.runningJobs) {
            if (job.description.repoId === repoId) {
                kernel.logger.info(`codeGraph(info): existed ${rootPath} job founded`);
                return job;
            }
        }

        const job = new CodeGraphJob(this.ctx, rootPath, repoId, version);
        this.jobs.push(job);
        return job;
    }

    cancelJob(rootPath: string) {
        for (const job of this.runningJobs) {
            if (job.description.workspacePath === rootPath) {
                job.cancel();
            }
        }
    }
}
