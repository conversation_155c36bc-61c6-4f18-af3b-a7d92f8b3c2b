/* eslint-disable max-statements */
/* eslint-disable complexity */
import {join} from 'node:path';
import {tmpdir} from 'node:os';
import EventEmitter from 'node:events';
import {mkdir, readFile, rm, writeFile} from 'node:fs/promises';
import axios, {CancelTokenSource} from 'axios';
import {globbyStream} from 'globby';
import {CancellationTokenSource} from 'vscode-languageserver';
import {Container} from 'inversify';
import {Description, Job} from '../embeddingService/EmbeddingJob.js';
import {PatchFileIndex} from '../api/codeSearch.js';
import {createFileIndexPatchAction, getMetadata} from '../embeddingService/EmbedingUtil.js';
import {isFileExist} from '../utils/fs.js';
import {getRepoName} from '../utils/git.js';
import {
    CodeGraphAPIService,
    RemoteCodeGraphConfig,
    RemoteVersionInfo,
} from './api.js';
import {CodeGraphIndexProgressStore} from './IndexProgressStore.js';
import {kernel} from '@comate/kernel-shared';

enum JobStatus {
    READY,
    RUNNING,
    CANCELLED,
    DONE,
}

export interface CodeGraphJobDescription extends Description {
    repoId: string;
    workspacePath: string;
    running: boolean;
    progress: number;
    version: RemoteVersionInfo;
}

export interface CodeGraphJobInitDescription extends Description {
    repoId: string;
    workspacePath: string;
    indexPath: string;
    scene: RemoteVersionInfo['scene'];
    indexFile: string;
    config: {
        interval: number;
        batchSize: number;
    };
}

export class CodeGraphFileExceedError extends Error {
    threshold: number;
    constructor(message: string, threshold: number) {
        super(message);
        this.threshold = threshold;
    }
}

interface FileIndex {
    path: string;
    sha1: string;
    indexAction: PatchFileIndex['action'];
}

export class CodeGraphJob implements Job<CodeGraphJobDescription> {
    private readonly createdTime: number = Date.now();
    private readonly cancelToken: CancellationTokenSource;
    private readonly axiosCancelTokenSource: CancelTokenSource;
    private readonly eventEmitter = new EventEmitter();
    private status: JobStatus = JobStatus.READY;
    private readonly files: Map<string, FileIndex> = new Map();
    private codeGraphTmpDir = '';
    private readonly codeGraphIndexFile = `codegraph_${Date.now()}.jsonl`;
    private backend: CodeGraphAPIService;
    private username: string;

    constructor(
        private readonly ctx: Container,
        private readonly workspacePath: string,
        private readonly repoId: string,
        private readonly version: RemoteVersionInfo
    ) {
        this.axiosCancelTokenSource = axios.CancelToken.source();
        this.cancelToken = new CancellationTokenSource();
        this.backend = ctx.get(CodeGraphAPIService);
        this.username = kernel.config.key!;
        this.codeGraphTmpDir = join(tmpdir(), '.comateCodeGraph', this.repoId);
    }

    // static async getExistJob(repoId: string) {
    //     const store = globalContext.get(GlobalStorage);
    //     const jobs = store.get<Record<string, EmbeddingJobDescription>>('embeddingJobs', {});
    //     return jobs?.[repoId];
    // }

    async setEmbeddingJobDescriptionToGlobal() {
        // const store = globalContext.get(GlobalStorage);
        // const jobs = store.get<Record<string, EmbeddingJobDescription>>('embeddingJobs', {});
        // store.update('embeddingJobs', {...jobs, [this.repoId]: this.description});
    }

    get running(): boolean {
        return this.status === JobStatus.RUNNING;
    }

    get progress(): number {
        return 100;
    }

    get description(): CodeGraphJobDescription {
        return {
            createdTime: this.createdTime,
            progress: this.progress,
            workspacePath: this.workspacePath,
            running: this.running,
            repoId: this.repoId,
            version: this.version,
        };
    }

    /**
     * 每个Job有两个定时器
     * 一个定时器是任务开始执行后，定时向外广播progress
     * 一个定时器是任务执行过程中，轮训服务端进度，往job里写progress
     */
    private timeoutInterval: NodeJS.Timer = setTimeout(() => {}, 0);
    private updateTimeoutInterval() {
        clearTimeout(this.timeoutInterval);
        this.timeoutInterval = setTimeout(
            () => {
                this.cancel();
            },
            180 * 1000
        );
    }

    addListener(listener: (ev: any) => void) {
        this.eventEmitter.addListener('progress', description => {
            listener({event: 'progress', description});
        });
        this.eventEmitter.addListener('init', description => {
            listener({event: 'init', description});
        });
        this.eventEmitter.addListener('cancel', description => {
            listener({event: 'cancel', description});
        });
        return this;
    }

    private async writeCodeGraphIndexFile() {
        const content = Array
            .from(this.files.values())
            .map(file => {
                return JSON.stringify({
                    indexAction: file.indexAction,
                    path: file.path,
                    sha1: file.sha1,
                });
            })
            .join('\n');
        await writeFile(join(this.codeGraphTmpDir, this.codeGraphIndexFile), content);
    }

    private async buildIndex(files: string[], config: RemoteCodeGraphConfig) {
        this.ctx.get<CodeGraphIndexProgressStore>(CodeGraphIndexProgressStore).updateUploadTime(
            this.description.repoId
        );

        const repoName = await getRepoName(this.workspacePath);
        kernel.logger.info(`start build index, config=${JSON.stringify(config)}, repo=${repoName}`);
        const isFullBuild = this.version.scene === 'FULL_BUILD';
        // 获取上一次的历史
        const remoteBuiltFiles = isFullBuild ? [] : await this.backend.apiGetRemoteCodeGraphLatestBuildHistory({
            repoId: this.repoId,
        });
        const remoteBuiltFileMap = new Map<string, typeof remoteBuiltFiles[number]>();
        for (const file of remoteBuiltFiles) {
            remoteBuiltFileMap.set(file.path, file);
        }

        for await (const fileRelativePath of files) {
            const absolutePath = join(this.description.workspacePath, fileRelativePath);
            const oldHash = remoteBuiltFileMap.get(fileRelativePath)?.sha1;
            const patch = await createFileIndexPatchAction(absolutePath, oldHash, config.config.maxFileSizeKb);
            if (patch) {
                this.files.set(
                    fileRelativePath,
                    {sha1: patch.sha1, indexAction: patch.action, path: fileRelativePath}
                );
            }
        }

        if (this.files.size === 0) {
            this.status = JobStatus.DONE;
            clearTimeout(this.timeoutInterval);
            return;
        }

        if (isFullBuild) {
            // 初始化构建
            const {version} = await this.backend.apiPostInitRemoteCodeGraphVersion({
                repoId: this.repoId,
                oldVersion: this.version.version,
                version: Date.now(),
                user: this.username,
                name: repoName,
            });
            this.version.version = version;
        }

        const tempDirExist = await isFileExist(this.codeGraphTmpDir);
        if (tempDirExist) {
            await rm(this.codeGraphTmpDir, {recursive: true});
        }
        await mkdir(this.codeGraphTmpDir, {recursive: true});
        await this.writeCodeGraphIndexFile();
        this.updateTimeoutInterval();

        this.eventEmitter.emit('init', {
            repoId: this.repoId,
            workspacePath: this.workspacePath,
            indexPath: this.codeGraphTmpDir,
            scene: this.version.scene,
            indexFile: this.codeGraphIndexFile,
            config: {
                interval: config.config.fileIntervalMilliseconds,
                batchSize: config.config.concurrentFileCount,
            },
        });
    }

    async updateModuleIndexFile(path: string) {
        if (this.status === JobStatus.CANCELLED) {
            return;
        }
        try {
            this.updateTimeoutInterval();
            const modules = await readFile(join(this.codeGraphTmpDir, path), 'utf-8');
            await this.backend.apiPostRemoteCodeGraphModuleIndex({
                repoId: this.repoId,
                user: this.username,
                modules: JSON.parse(modules),
            });
        }
        catch (ex) {
            kernel.logger.error('updateModuleIndexFile failed, message=' + (ex as Error).message);
        }
    }

    async updateGraphIndexFile(path: string) {
        if (this.status === JobStatus.CANCELLED) {
            return;
        }
        try {
            this.updateTimeoutInterval();
            const jsonl = await readFile(join(this.codeGraphTmpDir, path), 'utf-8');
            const jsons = jsonl.split(/\r?\n/);
            kernel.logger.info(`read graphIndexFile success, path=${path}, count=${jsons.length}`);
            const parsedSourceCodeAbsolutePaths: Array<FileIndex & {moduleId: string}> = [];
            for (const json of jsons) {
                try {
                    const data = JSON.parse(json) as {
                        moduleId: string;
                        path: string;
                        sha1: string;
                        indexAction: PatchFileIndex['action'];
                    };
                    parsedSourceCodeAbsolutePaths.push(data);
                }
                catch (ex) {
                    // nothing
                }
            }
            await this.backend.apiPostRemoteCodeGraphIndex({
                repoId: this.repoId,
                user: this.username,
                scene: this.version.scene,
                files: parsedSourceCodeAbsolutePaths,
            });
            for (const file of parsedSourceCodeAbsolutePaths) {
                this.files.delete(file.path);
            }

            const remine = Array.from(this.files).length;
            kernel.logger.info(`upload graphIndexFile success, path=${path}, remain=${remine}`);
            if (remine === 0) {
                clearTimeout(this.timeoutInterval);
                await rm(this.codeGraphTmpDir, {recursive: true});
            }
            this.dispose();
        }
        catch (ex) {
            kernel.logger.error('updateGraphIndexFile failed, message=' + (ex as Error).message);
        }
    }

    async complete() {
        this.status = JobStatus.DONE;
        const res = await this.backend.apiPostCodeGraphBuildComplete({
            version: this.version.version,
            user: this.username,
            repoId: this.repoId,
        });
        kernel.logger.info(`codeGraph(complete): success, res=${res}`);
    }

    async execute() {
        try {
            // 防止重复执行，在 JobManager.createJob 里有可能会返回 existJob
            if (this.status !== JobStatus.READY) {
                return;
            }

            this.status = JobStatus.RUNNING;
            const config = await this.backend.apiGetRemoteCodeGraphConfig();
            const metadata = await getMetadata(this.description.workspacePath);
            if (!metadata.project || !metadata.device) {
                throw new Error('Unable to update: missing project or device');
            }

            if (this.cancelToken.token.isCancellationRequested) {
                this.status = JobStatus.CANCELLED;
                return;
            }

            const stream = await globbyStream(config.whitePattern, {
                gitignore: true,
                cwd: this.description.workspacePath,
                ignore: config.blackPattern,
            });

            const files: string[] = [];
            for await (const file of stream) {
                if (this.cancelToken.token.isCancellationRequested) {
                    return;
                }

                files.push(file.toString());
            }

            kernel.logger.info(
                // eslint-disable-next-line max-len
                `read files success, count=${files.length}, whitePattern=${config.whitePattern}, blackPattern=${config.blackPattern}, cwd=${this.description.workspacePath}`
            );

            const maxFiles = config.config.maxFileCount;
            if (files.length > maxFiles) {
                throw new CodeGraphFileExceedError(
                    '',
                    maxFiles
                );
            }

            this.buildIndex(files, config).catch(() => {
                this.status = JobStatus.CANCELLED;
            });
        }
        catch (ex) {
            this.cancel();
            throw ex;
        }
    }

    cancel(): void {
        if (this.status === JobStatus.CANCELLED) {
            return;
        }
        this.status = JobStatus.CANCELLED;
        this.eventEmitter.emit('cancel', this.description);
        this.dispose();
    }

    dispose() {
        // Clear timeout interval
        if (this.timeoutInterval) {
            clearTimeout(this.timeoutInterval);
        }
        // Remove all event listeners
        this.eventEmitter.removeAllListeners();
        // Cancel any pending requests
        this.axiosCancelTokenSource.cancel();
        this.cancelToken.cancel();
    }
}
