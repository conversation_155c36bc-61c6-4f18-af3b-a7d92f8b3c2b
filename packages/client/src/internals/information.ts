import {Information, InformationQueryType, KnowledgeOptions} from '@comate/plugin-shared-internals';
import {getGitRepositoryInfo, isInGit} from '../utils/git.js';
import {Logger} from './logger.js';
import {EngineSession} from '../channel.js';

/** 表示一个Git代码库信息 */
export interface GitRepositoryInfo {
    versionControl: 'git';
    /** 仓库远端地址，纯本地仓库则值为`null` */
    repositoryUrl: string | null;
    /** 当前所在分支，没有分支则为客人字符串 */
    branch: string;
    /** 当前repoId，用于代码索引 */
    repoId: string;
}

/** 表示当前没有可识别的源码管理系统 */
export interface NoneRepositoryInfo {
    versionControl: 'none';
}

export type RepositoryInfo = GitRepositoryInfo | NoneRepositoryInfo;

/** 插件宿主环境信息 */
export interface HostInfo {
    /** 运行comate插件的版本号，如"3.7.0" */
    comateVersion: string;
    /** 运行插件的IDE环境，如`"VS Code"`、`"Playground"` */
    appName: string;
    /** 是否运行在调试模式下 */
    debugMode: boolean;
    /** 运行插件的 根据是否为SaaS版本区分为baidu-saas和baidu-int */
    platform: string;
}

/**
 * 支持获取与当前插件执行相关的代码或知识片段
 */
export class InformationRetriever {
    readonly #cwd: string;
    readonly #repoId: string;
    readonly #informationList: Information[];
    readonly #session: EngineSession;
    readonly #logger: Logger;
    readonly #pluginName: string;

    constructor(
        cwd: string,
        repoId: string,
        informationList: Information[],
        pluginName: string,
        session: EngineSession,
        logger: Logger
    ) {
        this.#cwd = cwd;
        this.#repoId = repoId;
        this.#informationList = informationList;
        this.#pluginName = pluginName;
        this.#session = session;
        this.#logger = logger.for(this.constructor.name);
    }

    disableLogging() {
        this.#logger.disable();
    }

    /**
     * 用自然语言表达并检索相关代码片段
     *
     * @param query 描述需要查询的代码的自然语言
     * @returns 若干段与诉求匹配的代码片段
     */
    codeFromQuery(query: string, options?: KnowledgeOptions): Promise<string[]> {
        this.#logger.verbose('QueryCode', {query, informationList: this.#informationList, options});

        return this.#session.informationQuery({
            type: InformationQueryType.Code,
            pluginName: this.#pluginName,
            informationList: this.#informationList,
            query,
            options,
        });
    }

    /**
     * 用自然语言检索外挂知识片段
     *
     * @param query 描述需要查询的知识的自然语言
     * @param options 额外选项，knowledgeSets供开发者指定固定知识集，additionalQueries供开发者指定额外查询
     * @returns 若干段与诉求匹配的文字片段
     */
    knowledgeFromQuery(
        query: string,
        options: {withSource: true} & KnowledgeOptions
    ): Promise<Array<{content: string, source: string}>>;
    knowledgeFromQuery(query: string, options?: KnowledgeOptions): Promise<string[]>;

    knowledgeFromQuery(
        query: string,
        options?: KnowledgeOptions
    ): Promise<Array<{content: string, source: string}> | string[]> {
        this.#logger.verbose('QueryKnowledge', {query, informationList: this.#informationList, options});

        return this.#session.informationQuery({
            type: InformationQueryType.Text,
            pluginName: this.#pluginName,
            informationList: this.#informationList,
            query,
            options,
        }) as Promise<Array<{content: string, source: string}> | string[]>;
    }

    /*
     * 获取当前插件宿主信息
     *
     * @returns 返回一个包含宿主信息的对象
     */
    async hostInfo(): Promise<HostInfo> {
        this.#logger.verbose('ReadHostInfo');

        return {
            comateVersion: process.env.COMATE_VERSION ?? '',
            appName: process.env.COMATE_ENGINE_APP_NAME ?? '',
            debugMode: process.env.COMATE_ENGINE_DEBUG === 'true',
            platform: process.env.COMATE_ENGINE_PLATFORM ?? 'baidu-int',
        };
    }

    /**
     * 获取当前代码仓库的信息
     *
     * @returns 代码仓库信息
     */
    async repositoryInfo(): Promise<RepositoryInfo> {
        this.#logger.verbose('ReadRepositoryInfo');

        if (!this.#cwd) {
            return {versionControl: 'none'};
        }

        const isGit = await isInGit(this.#cwd);
        if (isGit) {
            const info = await getGitRepositoryInfo(this.#cwd);
            return {versionControl: 'git', ...info, repoId: this.#repoId};
        }

        return {versionControl: 'none'};
    }

    selectedKnowledgeList(): Information[] {
        return this.#informationList;
    }
}
