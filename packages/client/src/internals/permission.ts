import {PermissionType} from '@comate/plugin-shared-internals';
import {EngineSession} from '../channel.js';
import {Logger} from './logger.js';

export class Permission {
    readonly #pluginName: string;
    readonly #session: EngineSession;
    readonly #logger: Logger;

    constructor(pluginName: string, session: EngineSession, logger: Logger) {
        this.#pluginName = pluginName;
        this.#session = session;
        this.#logger = logger.for(this.constructor.name);
    }

    /**
     * 代表插件向用户申请指定权限，由用户确认授权
     *
     * @param type 需要的权限类型
     * @returns 用户同意授权后为`true`
     */
    async requestPermission(type: PermissionType): Promise<boolean> {
        this.#logger.verbose('RequestPermission', {type});
        return this.#session.requestPermission(this.#pluginName, type);
    }
}
