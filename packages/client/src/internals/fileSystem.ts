import path from 'node:path';
import fs from 'node:fs/promises';
import {Logger} from './logger.js';

export type FileEntryType = 'file' | 'directory' | 'symlink' | 'block' | 'character' | 'fifo' | 'socket';

export interface FileStat {
    type: FileEntryType;
    size: number;
    atimeMs: number;
    ctimeMs: number;
    mtimeMs: number;
}

export interface FileEntry {
    name: string;
    type: FileEntryType;
}

interface StatInfoContainer {
    isFile(): boolean;
    isDirectory(): boolean;
    isSymbolicLink(): boolean;
    isBlockDevice(): boolean;
    isCharacterDevice(): boolean;
    isFIFO(): boolean;
    isSocket(): boolean;
}

function getFileEntryType(name: string, entry: StatInfoContainer): FileEntryType {
    if (entry.isFile()) {
        return 'file';
    }
    if (entry.isDirectory()) {
        return 'directory';
    }
    if (entry.isSymbolicLink()) {
        return 'symlink';
    }
    if (entry.isBlockDevice()) {
        return 'block';
    }
    if (entry.isCharacterDevice()) {
        return 'character';
    }
    if (entry.isFIFO()) {
        return 'fifo';
    }
    if (entry.isSocket()) {
        return 'socket';
    }
    throw new Error(`Unknown file entry type: ${name}`);
}

/**
 * 对文件系统进行访问
 */
export interface FileSystem {
    /**
     * 读取一个文件内容
     *
     * @param filePath 文件路径
     * @returns 文件的二进制内容
     */
    readFile(filePath: string): Promise<Buffer>;

    /**
     * 使用指定编码读取一个文件内容
     *
     * @param filePath 文件路径
     * @param encoding 对应的编码
     * @returns 文件内容使用指定编码解码后的字符串
     */
    readFile(filePath: string, encoding: BufferEncoding): Promise<string>;

    /**
     * 读取一个目录下的所有文件名
     *
     * @param filePath 目录路径
     * @returns 目录下的文件名列表
     */
    readDirectory(directoryPath: string): Promise<FileEntry[]>;

    /**
     * 向一个文件中写入内容
     *
     * @param filePath 文件路径
     * @param content 写入的内容
     */
    writeFile(filePath: string, content: string | Buffer): Promise<void>;

    /**
     * 获取文件的统计信息
     *
     * @param filePath 文件路径
     * @returns 文件统计信息
     */
    stat(filePath: string): Promise<FileStat>;
    disableLogging(): void;
}

export class FullDiskFileSystem implements FileSystem {
    readonly #logger: Logger;

    constructor(logger: Logger) {
        this.#logger = logger.for(this.constructor.name);
    }

    disableLogging() {
        this.#logger.disable();
    }

    readFile(filePath: string): Promise<Buffer>;
    readFile(filePath: string, encoding: BufferEncoding): Promise<string>;
    readFile(filePath: string, encoding?: BufferEncoding) {
        this.#logger.verbose('ReadFile', {filePath});
        return fs.readFile(filePath, encoding);
    }

    async readDirectory(directoryPath: string): Promise<FileEntry[]> {
        this.#logger.verbose('ReadDirectory', {directoryPath});
        const entries = await fs.readdir(directoryPath, {withFileTypes: true});
        return entries.map(v => ({name: v.name, type: getFileEntryType(v.name, v)}));
    }

    writeFile(filePath: string, content: string | Buffer): Promise<void> {
        this.#logger.verbose('WriteFile', {filePath});
        return fs.writeFile(filePath, content);
    }

    async stat(filePath: string): Promise<FileStat> {
        this.#logger.verbose('Stat', {filePath});

        const stat = await fs.stat(filePath);
        return {
            type: getFileEntryType(filePath, stat),
            size: stat.size,
            mtimeMs: stat.mtimeMs,
            atimeMs: stat.atimeMs,
            ctimeMs: stat.ctimeMs,
        };
    }
}

export class RestrictedFileSystem implements FileSystem {
    readonly #cwd: string;
    readonly #logger: Logger;

    constructor(cwd: string, logger: Logger) {
        this.#cwd = cwd;
        this.#logger = logger.for(this.constructor.name);
    }

    disableLogging() {
        this.#logger.disable();
    }

    readFile(filePath: string): Promise<Buffer>;
    readFile(filePath: string, encoding: BufferEncoding): Promise<string>;
    readFile(filePath: string, encoding?: BufferEncoding) {
        this.#logger.verbose('ReadFile', {filePath});
        this.checkPathAccess(filePath);

        return fs.readFile(path.resolve(this.#cwd, filePath), encoding);
    }

    async readDirectory(directoryPath: string): Promise<FileEntry[]> {
        this.#logger.verbose('ReadDirectory', {directoryPath});
        this.checkPathAccess(directoryPath);
        const entries = await fs.readdir(path.resolve(this.#cwd, directoryPath), {withFileTypes: true});
        return entries.map(v => ({name: v.name, type: getFileEntryType(v.name, v)}));
    }

    writeFile(filePath: string, content: string | Buffer): Promise<void> {
        this.#logger.verbose('WriteFile', {filePath});
        this.checkPathAccess(filePath);

        return fs.writeFile(path.resolve(this.#cwd, filePath), content);
    }

    async stat(filePath: string): Promise<FileStat> {
        this.#logger.verbose('Stat', {filePath});
        this.checkPathAccess(filePath);

        const stat = await fs.stat(path.resolve(this.#cwd, filePath));
        return {
            type: getFileEntryType(filePath, stat),
            size: stat.size,
            mtimeMs: stat.mtimeMs,
            atimeMs: stat.atimeMs,
            ctimeMs: stat.ctimeMs,
        };
    }

    private checkPathAccess(filePath: string) {
        if (filePath.startsWith('/') || filePath.startsWith('..')) {
            this.#logger.warn('FileSystemAccessForbidden', {filePath});
            throw new Error('File system access restricted to current workspace');
        }
    }
}
