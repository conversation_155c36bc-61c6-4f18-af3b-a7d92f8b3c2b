import {markdownTable} from 'markdown-table';

interface TableRenderColumnUsable<K extends keyof T, T> {
    /** 列标题 */
    title: string;
    /** 该列对应的属性 */
    field: keyof T;
    /** 将属性值转化为字符串 */
    format?: (value: T[K]) => string;
}

type TableRenderColumnMap<T> = {
    [K in keyof T]: TableRenderColumnUsable<K, T>;
};

/**
 * 配置表格展示的列
 */
export type TableRenderColumn<T> = TableRenderColumnMap<T>[keyof T];

/** 渲染表格的相关配置 */
export interface TableRenderOptions<T> {
    /** 配置列展示 */
    columns: Array<TableRenderColumn<T>>;
}

export class MarkdownRender {
    /**
     * 将数据格式化为Markdown表格
     *
     * @param data 数据数组
     * @param options 表格渲染选项
     * @returns 对应表格的Markdown字符串
     */
    table<T>(data: T[], options: TableRenderOptions<T>): string {
        const headers = options.columns.map(column => column.title);
        const toCell = (column: TableRenderColumn<T>, item: T) => {
            const value = item[column.field];
            return column.format ? column.format(value) : String(value);
        };
        const rows = data.map(item => options.columns.map(v => toCell(v, item)));
        return markdownTable([headers, ...rows], {align: 'left'});
    }
}
