import {
    FunctionDefinition,
    FunctionModel,
    LlmType,
    TextModel,
    ModelOptions,
    DebugAgentPayload,
} from '@comate/plugin-shared-internals';
import {Logger} from './logger.js';
import {EngineSession} from '../channel.js';

export interface RunnablePrompt {
    promptTemplate: string;
    args: Record<string, any> | undefined;
}

export interface TextModelOptions {
    /**
     * @param model 默认为eb3.5
     */
    model?: TextModel;
    /**
     * @description 模型参数
     * @param openMaxOutput 是否开启最大输出
     * @param temperature 较高的数值会使输出更加随机，而较低的数值会使其更加集中和确定，建议该参数和Top p只设置1个
     * @param topP 影响输出文本的多样性，取值越大，生成文本的多样性越强，建议该参数和Temperature只设置1个
     * @param penaltyScore 通过对已生成的token增加惩罚，减少重复生成的现象。说明：值越大表示惩罚越大，默认1.0，取值范围：[1.0, 2.0]
     */
    modelOptions?: ModelOptions;
}

export interface FunctionModelOptions {
    model?: FunctionModel;
}

export interface CodeSpecificOptions {
    filePath: string;
}

/**
 * 支持通过大模型生成代码及文本相关内容
 */
export class SuppotingLlm {
    readonly #pluginName: string;
    readonly #capabilityName: string | undefined;
    readonly #session: EngineSession;
    readonly #logger: Logger;

    constructor(pluginName: string, session: EngineSession, logger: Logger, capabilityName?: string) {
        this.#pluginName = pluginName;
        this.#session = session;
        this.#logger = logger.for(this.constructor.name);
        this.#capabilityName = capabilityName;
    }

    disableLogging() {
        this.#logger.disable();
    }

    /**
     * 创建一个提示词对象
     *
     * @param promptTemplate 提示词模板，可包含`{{arg}}`参数占位
     * @param args 参数对象
     * @returns 一个提示词对象，用于和大模型交互
     */
    createPrompt(promptTemplate: string, args?: Record<string, any>): RunnablePrompt {
        return {promptTemplate, args};
    }

    /**
     * 生成文本内容
     *
     * @param prompt 与大模型交互的提示词对象
     * @param options 配置项
     * @returns 文本大模型的生成内容
     */
    async askForText({promptTemplate, args}: RunnablePrompt, options?: TextModelOptions) {
        const model = options?.model ?? TextModel.Default;
        this.#logger.verbose('CallTextModel', {model, promptTemplate, args});
        return this.#session.askLlm(
            LlmType.Text,
            {
                promptTemplate,
                args,
                model: options?.model,
                modelOptions: options?.modelOptions,
                pluginName: this.#pluginName,
            }
        );
    }

    askForTextStreaming({promptTemplate, args}: RunnablePrompt, options?: TextModelOptions) {
        const model = options?.model ?? TextModel.Default;
        this.#logger.verbose('CallTextModelStreaming', {model, promptTemplate, args});
        const input = {
            promptTemplate,
            args,
            model,
            modelOptions: options?.modelOptions,
            pluginName: this.#pluginName,
            capabilityName: this.#capabilityName,
        };
        return this.#session.askLlmStreaming(input);
    }

    askForDebugAgentProcess(debugAgentPayload: DebugAgentPayload) {
        const input = {
            debugAgentPayload,
            pluginName: this.#pluginName,
            capabilityName: this.#capabilityName,
        };
        return this.#session.askDebugAgentProcess(input);
    }

    /**
     * 生成代码
     *
     * @param prompt 与大模型交互的提示词对象
     * @returns 代码大模型的生成内容
     */
    async askForCode({promptTemplate, args}: RunnablePrompt, options?: TextModelOptions): Promise<string> {
        // this.#logger.verbose('CallCodeModel', {promptTemplate, args});
        // return this.#session.askLlm(LlmType.Code, {promptTemplate, args, pluginName: this.#pluginName});
        const model = options?.model ?? TextModel.Default;
        this.#logger.verbose('CallCodeModel', {model, promptTemplate, args});
        return this.#session.askLlm(
            LlmType.Code,
            {
                promptTemplate,
                args,
                model,
                modelOptions: options?.modelOptions,
                pluginName: this.#pluginName,
            }
        );
    }

    async askRAG({prompt, retrieveFrom}: {prompt: string, retrieveFrom?: {workspace?: boolean}}) {
        return this.#session.askRAG({
            pluginName: this.#pluginName,
            capabilityName: this.#capabilityName,
            payload: {
                prompt,
                retrieveFrom,
            },
        });
    }

    /**
     * 进行函数调用
     *
     * @param prompt 与大模型交互的提示词对象
     * @param functions 能够调用的函数定义
     * @param options 配置项
     * @returns 大模型选中的函数以及调用的参数
     */
    async askForFunction(prompt: RunnablePrompt, functions: FunctionDefinition[], options?: FunctionModelOptions) {
        this.#logger.verbose(
            'CallFunctionModel',
            {
                model: options?.model ?? FunctionModel.Default,
                promptTemplate: prompt.promptTemplate,
                args: prompt.args,
                functions,
            }
        );
        return this.#session.askLlm(
            LlmType.Function,
            {
                functions,
                promptTemplate: prompt.promptTemplate,
                args: prompt.args,
                model: options?.model,
                pluginName: this.#pluginName,
            }
        );
    }

    /**
     * 进行代码解释
     *
     * @param code 要解释的代码字符串
     * @param filePath 文件路径
     * @returns 代码的解释文本
     */
    async explainCode(code: string, {filePath}: CodeSpecificOptions): Promise<string> {
        this.#logger.verbose('ExplainCode', {code, filePath});
        return this.#session.askLlm(LlmType.ExplainCode, {code, filePath, pluginName: this.#pluginName});
    }

    /**
     * 获取代码的文档注释
     *
     * @param code 要获取文档注释的代码字符串
     * @param filePath 文件路径
     * @returns 代码的文档注释内容
     */
    async getDocCommentForCode(code: string, {filePath}: CodeSpecificOptions): Promise<string> {
        this.#logger.verbose('GenerateDocComment', {code, filePath});
        return this.#session.askLlm(LlmType.DocCommentForCode, {code, filePath, pluginName: this.#pluginName});
    }

    /**
     * 为代码添加行间注释
     *
     * @param code 要添加注释的代码字符串
     * @param filePath 文件路径
     * @returns 添加行间注释后的代码
     */
    async addCommentForCode(code: string, {filePath}: CodeSpecificOptions): Promise<string> {
        this.#logger.verbose('GenerateInlineComment', {code, filePath});
        return this.#session.askLlm(LlmType.CommentForCode, {code, filePath, pluginName: this.#pluginName});
    }
}
