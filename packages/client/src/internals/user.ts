import {PermissionType, UserDetail} from '@comate/plugin-shared-internals';
import {Permission} from './permission.js';
import {isInternal} from '../utils/checkVersion.js';

export class User {
    static currentAuth: string;

    /** Comate的用户ID，仅在Comate中代表唯一用户 */
    readonly id: string;

    readonly #detail: UserDetail;

    readonly #permission: Permission;

    constructor(id: string, detail: UserDetail, permission: Permission) {
        this.id = id;
        const {license, ...restDetail} = detail;

        // 保证插件拿不到 license
        this.#detail = restDetail;
        this.#permission = permission;

        User.currentAuth = license || this.#detail.name;
    }

    /**
     * 申请获取用户详细信息
     *
     * @returns用户授权后，返回完整的用户信息，拒绝则返回`false`
     */
    async requestDetail(): Promise<UserDetail | false> {
        // 厂内直接授权
        if (isInternal) {
            return this.#detail;
        }
        const granted = await this.#permission.requestPermission(PermissionType.UserDetail);
        return granted ? this.#detail : false;
    }
}
