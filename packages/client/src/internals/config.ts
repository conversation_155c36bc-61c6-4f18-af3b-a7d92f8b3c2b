import {EngineSession} from '../channel.js';
import {Logger} from './logger.js';

/**
 * 支持获取插件的配置值
 */
export class PluginConfig {
    readonly #pluginName: string;
    readonly #session: EngineSession;
    readonly #logger: Logger;

    constructor(pluginName: string, session: EngineSession, logger: Logger) {
        this.#pluginName = pluginName;
        this.#session = session;
        this.#logger = logger.for(this.constructor.name);
    }

    /**
     * 根据键值获取插件的配置值，可以指定默认值
     *
     * @param key 键值
     * @param defaultValue 默认值（可选）
     * @returns 获取配置值后返回对应的字符串
     */
    async getAsString(key: string, defaultValue?: string): Promise<string> {
        const value = await this.#session.getPluginConfig(this.#pluginName, key) ?? defaultValue;
        this.#logger.verbose('GetString', {type: 'string', key, value});
        return value;
    }

    /**
     * 根据键值获取插件的配置值，可以指定默认值
     *
     * @param key 键值
     * @param defaultValue 默认值（可选）
     * @returns 获取配置值后返回对应的字符串
     */
    async getAsNumber(key: string, defaultValue?: number): Promise<number> {
        const value = await this.#session.getPluginConfig(this.#pluginName, key) ?? defaultValue;
        this.#logger.verbose('GetString', {type: 'number', key, value});
        return value;
    }

    /**
     * 根据键值获取插件的配置值，可以指定默认值
     *
     * @param key 键值
     * @param defaultValue 默认值（可选）
     * @returns 获取配置值后返回对应的字符串
     */
    async getAsBoolean(key: string, defaultValue?: boolean): Promise<boolean> {
        const value = await this.#session.getPluginConfig(this.#pluginName, key) ?? defaultValue;
        this.#logger.verbose('GetString', {type: 'boolean', key, value});
        return value;
    }

    /**
     * 根据键值获取插件的配置值
     *
     * @param key 键值
     * @returns 获取配置值后返回对应的字符串
     */
    async getAsStringArray(key: string): Promise<string[]> {
        const value = await this.#session.getPluginConfig(this.#pluginName, key) ?? [];
        this.#logger.verbose('GetString', {type: 'string[]', key, value});
        return value;
    }

    /**
     * 根据键值获取插件的配置值
     *
     * @param key 键值
     * @returns 获取配置值后返回对应的字符串
     */
    async getAsNumberArray(key: string): Promise<number[]> {
        const value = await this.#session.getPluginConfig(this.#pluginName, key) ?? [];
        this.#logger.verbose('GetString', {type: 'number[]', key, value});
        return value;
    }

    /**
     * 根据键值获取插件的配置值
     *
     * @param key 键值
     * @returns 获取配置值后返回对应的字符串
     */
    async getAsBooleanArray(key: string): Promise<boolean[]> {
        const value = await this.#session.getPluginConfig(this.#pluginName, key) ?? [];
        this.#logger.verbose('GetString', {type: 'boolean[]', key, value});
        return value;
    }
}
