/**
 * 用户当前与插件交互的现场信息
 */
export class ActivationContext {
    /** 用户在对话界面输入的内容 */
    readonly query: string;

    /** 当前编辑器中用户选中的代码块 */
    readonly selectedCode: string;

    /** 当前编辑器中打开的文件的全部内容 */
    readonly activeFileContent: string;

    /** 当前编辑器中光标所在行的内容 */
    readonly activeFileLineContent: string;

    /** 当前编辑器中打开的文件路径，是一个相对于项目的相对路径 */
    readonly activeFilePath: string;

    /** 与activeFilePath类似，但只有最后文件名部分，不包含文件目录 */
    readonly activeFileName: string;

    /** 当前编辑器打开的文件的编程语言 */
    readonly activeFileLanguage: string;

    /** 当前编辑器中用户选中的代码块的位置 */
    readonly selectedRange?: [{line: number, character: number}, {line: number, character: number}] | [];

    /** 插件开发者自定义数据 */
    readonly data?: any;

    constructor(info: ActivationContext) {
        this.query = info.query;
        this.data = info.data;
        this.selectedCode = info.selectedCode;
        this.activeFileContent = info.activeFileContent;
        this.activeFileLineContent = info.activeFileLineContent;
        this.activeFilePath = info.activeFilePath;
        this.activeFileName = info.activeFileName;
        this.activeFileLanguage = info.activeFileLanguage;
        this.selectedRange = info.selectedRange;
    }
}
