import {Session, UserLogLevel} from '@comate/plugin-shared-internals';

export interface Logger {
    /**
     * 以指定的源生成一个新的日志对象
     *
     * @param source 指定日志源
     */
    for(source: string): Logger;

    /**
     * 以`verbose`级别打印日志
     *
     * @param actionOrContent 日志内容或当前日志的动作
     * @param detail 可选的详情对象，当有该参数时，`actionOrContent`会被认为是当前日志的动作
     */
    verbose(actionOrContent: string, detail?: Record<string, any>): void;

    /**
     * 以`info`级别打印日志
     *
     * @param actionOrContent 日志内容或当前日志的动作
     * @param detail 可选的详情对象，当有该参数时，`actionOrContent`会被认为是当前日志的动作
     */
    info(actionOrContent: string, detail?: Record<string, any>): void;

    /**
     * 以`warn`级别打印日志
     *
     * @param actionOrContent 日志内容或当前日志的动作
     * @param detail 可选的详情对象，当有该参数时，`actionOrContent`会被认为是当前日志的动作
     */
    warn(actionOrContent: string, detail?: Record<string, any>): void;

    /**
     * 以`error`级别打印日志
     *
     * @param actionOrContent 日志内容或当前日志的动作
     * @param detail 可选的详情对象，当有该参数时，`actionOrContent`会被认为是当前日志的动作
     */
    error(actionOrContent: string, detail?: Record<string, any>): void;
    disable(): void;
}

export class SessionLogger implements Logger {
    private disabled?: boolean;
    readonly #session: Session;
    readonly #source: string;

    constructor(session: Session, source?: string) {
        this.#source = source ?? '';
        this.#session = session;
    }

    for(source: string) {
        return new SessionLogger(this.#session, source);
    }

    disable() {
        this.log('verbose', 'disable');
        this.disabled = true;
    }

    verbose(actionOrContent: string, detail?: Record<string, any>) {
        this.log('verbose', actionOrContent, detail);
    }

    info(actionOrContent: string, detail?: Record<string, any>) {
        this.log('info', actionOrContent, detail);
    }

    warn(actionOrContent: string, detail?: Record<string, any>) {
        this.log('warn', actionOrContent, detail);
    }

    error(actionOrContent: string, detail?: Record<string, any>) {
        this.log('error', actionOrContent, detail);
    }

    private log(level: UserLogLevel, actionOrContent: string, detail?: Record<string, any>) {
        if (this.disabled) {
            return;
        }
        if (detail) {
            this.#session.log(level, this.#source, actionOrContent, detail);
        }
        else {
            this.#session.log(level, this.#source, 'Log', {content: actionOrContent});
        }
    }
}
