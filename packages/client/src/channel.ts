import {
    ACTION_CHAT_QUERY,
    ACTION_SECUBOT,
    ACTION_SECUBOT_TASK_PROGRESS,
    SecubotQueryPayload,
    ChatQueryPayload,
    ACTION_CHAT_TASK_PROGRESS,
    TaskProgressChunk,
    Session,
    Channel,
    ChannelImplement,
    ChannelImplementMaybe,
    ACTION_REQUEST_PERMISSION,
    LlmType,
    LlmResponseTypes,
    ACTION_ASK_LLM,
    LlmPayloadTypes,
    ACTION_GET_PLUGIN_CONFIG,
    ACTION_INFORMATION_QUERY,
    WillScanPayload,
    ScanTaskPayload,
    ACTION_SCAN_TASK,
    ACTION_WILL_SCAN,
    ACTION_REPORT_WILL_SCAN,
    ACTION_SCAN_TASK_PROGRESS,
    ReportWillScanPayload,
    GetPluginConfigPayload,
    LlmPayload,
    PermissionType,
    RequestPermissionPayload,
    TaskProgressPayload,
    SessionInit,
    ACTION_RELEASE_SCAN_TASK,
    InformationPayload,
    ACTION_START_BACKGROUND_SERVICE,
    StartBackgroundServicePayload,
    ACTION_ASK_LLM_STREAMING,
    IdeSideLlmPayload,
    ACTION_CUSTOM_COMMAND,
    CustomCommandInvokePayload,
    ACTION_DIAGNOSTIC_SCAN_TASK_PROGRESS,
    ACTION_DIAGNOSTIC_SCAN,
    DiagnosticScanInvokePayload,
    DiagnosticScanTaskProgressChunk,
    DiagnosticScanTaskProgressPayload,
    ACTION_DEBUG_TASK_PROCESS,
    DebugAgentPayload,
    DebugAgentResponse,
    ACTION_ASK_RAG,
} from '@comate/plugin-shared-internals';
import {CapabilityInvoker} from './invoker/index.js';

interface PayloadMap {
    [ACTION_CHAT_QUERY]: ChatQueryPayload;
    [ACTION_WILL_SCAN]: WillScanPayload;
    [ACTION_RELEASE_SCAN_TASK]: ScanTaskPayload;
    [ACTION_SCAN_TASK]: ScanTaskPayload;
    [ACTION_START_BACKGROUND_SERVICE]: StartBackgroundServicePayload;
    [ACTION_CUSTOM_COMMAND]: CustomCommandInvokePayload;
    [ACTION_DIAGNOSTIC_SCAN]: DiagnosticScanInvokePayload;
    [ACTION_SECUBOT]: SecubotQueryPayload;
}

/**
 * 处理IDE与Engine之间的会话
 */
export class EngineSession extends Session<PayloadMap> {
    private readonly invoker: CapabilityInvoker;

    constructor(init: SessionInit, implement: ChannelImplement, invoker: CapabilityInvoker) {
        super(init, implement);
        this.invoker = invoker;
    }

    /**
     * 根据指定的片段，更新绘制用户界面
     *
     * @param messageId 对应的消息ID
     * @param chunk 绘制界面的片段信息
     */
    reportChatProgress(taskId: string, chunk: TaskProgressChunk, capabilityName?: string, messageId?: number) {
        const payload: TaskProgressPayload = {
            taskId,
            chunk,
            capabilityName,
            messageId,
        };

        void this.send({action: ACTION_CHAT_TASK_PROGRESS, payload});
    }

    /**
     * 报告诊断扫描结果
     * @param chunk
     * @param capabilityName
     * @returns
     */
    reportDiagnosticScanResult(chunk: DiagnosticScanTaskProgressChunk, capabilityName?: string) {
        const payload: DiagnosticScanTaskProgressPayload = {
            chunk,
            capabilityName,
        };

        void this.send({action: ACTION_DIAGNOSTIC_SCAN_TASK_PROGRESS, payload});
    }

    /**
     * 报告安全智能体生成的内容
     * @param chunk
     * @param capabilityName
     * @returns
     */
    reportSecubot(chunk: string, capabilityName?: string) {
        const payload: any = {
            chunk,
            capabilityName,
        };
        void this.send({action: ACTION_SECUBOT_TASK_PROGRESS, payload});
    }

    async requestPermission(pluginName: string, type: PermissionType) {
        const payload: RequestPermissionPayload = {pluginName, type};
        const {granted} = await this.send({action: ACTION_REQUEST_PERMISSION, payload});
        return granted;
    }

    async askLlm<T extends LlmType>(type: T, input: LlmPayloadTypes[T]): Promise<LlmResponseTypes[T]> {
        // 类型是对的，但只有写`any`才能过
        const payload = {type, ...input} as LlmPayload;
        const {result} = await this.send({action: ACTION_ASK_LLM, payload});
        return result;
    }

    async *askLlmStreaming(input: LlmPayloadTypes[LlmType.Text]): AsyncIterable<string> {
        // 类型是对的，但只有写`any`才能过
        const payload = {type: LlmType.Text, ...input} as LlmPayload;
        const result = this.sendStreaming<IdeSideLlmPayload>({action: ACTION_ASK_LLM_STREAMING, payload});
        for await (const chunk of result) {
            // TODO: 类型还是不够健壮啊
            yield chunk.result as string;
        }
    }

    async *askDebugAgentProcess(
        payload: {pluginName: string, capabilityName?: string, debugAgentPayload: DebugAgentPayload}
    ): AsyncIterable<DebugAgentResponse> {
        // TODO: 类型还是不够健壮啊
        const result = this.sendStreaming<IdeSideLlmPayload>({action: ACTION_DEBUG_TASK_PROCESS, payload});
        for await (const chunk of result) {
            yield chunk.result as DebugAgentResponse;
        }
    }

    async askRAG(payload: {
        pluginName: string;
        capabilityName?: string;
        payload: {prompt: string, retrieveFrom?: {workspace?: boolean}};
    }) {
        const {result} = await this.send({action: ACTION_ASK_RAG, payload});
        return result.output as string;
    }

    async getPluginConfig(pluginName: string, key: string) {
        const payload: GetPluginConfigPayload = {pluginName, key};
        const {value} = await this.send({action: ACTION_GET_PLUGIN_CONFIG, payload});
        return value;
    }

    async informationQuery(payload: InformationPayload) {
        const {result} = await this.send({
            action: ACTION_INFORMATION_QUERY,
            payload,
        });
        return result;
    }

    reportWillScan(payload: ReportWillScanPayload) {
        void this.send({action: ACTION_REPORT_WILL_SCAN, payload});
    }

    reportScanProgress(scanId: string, capabilityName: string, chunk: TaskProgressChunk) {
        const payload: TaskProgressPayload = {
            taskId: scanId,
            capabilityName,
            chunk,
        };
        void this.send({action: ACTION_SCAN_TASK_PROGRESS, payload});
    }

    protected initializeListeners() {
        super.initializeListeners();

        this.setListener(ACTION_CHAT_QUERY, payload => this.invoker.invokeChat(payload, this));
        this.setListener(ACTION_SECUBOT, payload => this.invoker.invokeSecubot(payload, this));
        this.setListener(ACTION_DIAGNOSTIC_SCAN, payload => this.invoker.invokeDiagnosticScan(payload, this));
        this.setListener(ACTION_WILL_SCAN, payload => this.invoker.invokeWillScan(payload, this));
        this.setListener(ACTION_RELEASE_SCAN_TASK, payload => this.invoker.invokeReleaseScan(payload, this));
        this.setListener(ACTION_SCAN_TASK, payload => this.invoker.invokeScanStreamTask(payload, this));
        this.setListener(ACTION_CUSTOM_COMMAND, payload => this.invoker.invokeCommand(payload, this));
        this.setListener(
            ACTION_START_BACKGROUND_SERVICE,
            payload => this.invoker.invokeBackgroundService(payload, this)
        );
    }
}

export class EngineChannel extends Channel<EngineSession> {
    private readonly invoker: CapabilityInvoker;

    constructor(implement: ChannelImplementMaybe, invoker: CapabilityInvoker) {
        super(implement);
        this.invoker = invoker;
    }

    protected createSession(init: SessionInit) {
        return new EngineSession(init, this.implement, this.invoker);
    }
}
