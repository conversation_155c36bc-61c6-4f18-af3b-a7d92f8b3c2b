import path from 'node:path';
import unixify from 'unixify';
import {readPluginDescription} from '@comate/plugin-shared-internals';
import {ClientRegistry} from './registry.js';
import {CapabilityInvoker} from './invoker/index.js';
import {EngineChannel} from './channel.js';

export class PluginHost {
    private readonly invoker = new CapabilityInvoker();
    private readonly channel = new EngineChannel(process, this.invoker);

    /**
     * 加载插件的主入口，插件进程启动后调用本函数
     */
    async start() {
        this.channel.start();
        this.channel.log(this.constructor.name, 'PluginHostStart', {pid: process.pid});
    }

    async attach(directory: string) {
        const description = await readPluginDescription(directory);

        this.channel.log(this.constructor.name, 'PluginAttachStart', {pluginName: description.name});
        const {setup} = await import(unixify(path.join(directory, description.entry)));

        if (typeof setup !== 'function') {
            throw new Error('Entry file of plugin does not export setup function');
        }

        const registry = new ClientRegistry(description);
        try {
            await registry.register(setup);
            this.invoker.attach(description.name, registry);
            this.channel.log(this.constructor.name, 'PluginAttachFinish', {pluginName: description.name});
            return description;
        }
        catch (ex) {
            this.channel.log(
                this.constructor.name,
                'PluginAttachFail',
                {
                    pluginName: description.name,
                    reason: ex instanceof Error ? ex.message : `${ex}`,
                }
            );
            return null;
        }
    }

    async detach(pluginName: string) {
        this.invoker.detach(pluginName);
    }
}
