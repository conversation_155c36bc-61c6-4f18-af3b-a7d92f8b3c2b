import {User} from '../internals/user.js';

const securityHost = [
    '************',
    'comate-sec.baidu-int.com',
    'comate-sec.baidu.comm',
    'comate-sec-test.baidu-int.com',
];

function customFetchWithAuthorization(url: string, options: RequestInit = {}): Promise<Response> {
    const currentAuth = User.currentAuth;
    const parsedUrl = new URL(url);
    if (!securityHost.includes(parsedUrl.hostname)) {
        return Promise.reject(new Error('Unauthorized Host'));
    }

    const defaultHeaders: HeadersInit = {
        'X-Comate-Authorization': currentAuth,
    };

    return fetch(url, {
        ...options,
        headers: {
            ...options.headers,
            ...defaultHeaders,
        },
    });
}

export default customFetchWithAuthorization;

const trustedFirstPartyHost = ['************', 'comate.baidu.com', 'comate.baidu-int.com'];

export function authorizedFirstPartyFetch(url: string, options: RequestInit = {}): Promise<Response> {
    const currentAuth = User.currentAuth;
    const parsedUrl = new URL(url);
    if (!trustedFirstPartyHost.includes(parsedUrl.hostname)) {
        return Promise.reject(new Error('Unauthorized Host'));
    }

    const defaultHeaders: HeadersInit = {
        'Uuap-login-name': currentAuth,
    };

    return fetch(url, {
        ...options,
        headers: {
            ...options.headers,
            ...defaultHeaders,
        },
    });
}
