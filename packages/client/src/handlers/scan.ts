import {ScanHandleGoal, TaskProgressChunk, Range} from '@comate/plugin-shared-internals';

export interface ScanTaskReference {
    description: string;
    ranges: Range[];
}

export interface ScanHandler {
    /**
     * 判断是否参与本次扫描任务
     *
     * @returns 选择参与任务则返回目标描述，不参与返回`false`
     */
    check(): Promise<ScanHandleGoal | false>;

    /**
     * 处理扫描任务并返回对代码的更新补丁
     *
     * @returns 返回的补丁将会被选择性应用到代码中
     */
    scan(reference: ScanTaskReference): AsyncIterableIterator<TaskProgressChunk>;
}

export function isScanHandler(value: any): value is ScanHandler {
    return typeof value.scan === 'function';
}
