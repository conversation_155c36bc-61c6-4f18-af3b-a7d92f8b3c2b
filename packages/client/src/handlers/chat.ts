import {TaskProgressChunk} from '@comate/plugin-shared-internals';

export interface ChatHandler {
    /**
     * 处理对话交互下的用户输入
     *
     * @param args 由智能参数提取获得的参数对象
     * @returns 通过迭代器返回与对话框的交互信息
     */
    handleChatQuery(args: any): AsyncIterableIterator<TaskProgressChunk>;
}

export function isChatHandler(value: any): value is ChatHandler {
    return typeof value.handleChatQuery === 'function';
}
