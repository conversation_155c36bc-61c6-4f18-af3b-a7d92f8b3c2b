import {TaskProgressChunk} from '@comate/plugin-shared-internals';

export interface CommandHandler {
    /**
     * 处理用户通过按钮等交互产生的指令
     *
     * @param commandName 指令名称
     * @param data 由交互产生的数据
     * @returns 通过迭代器返回与对话框的交互信息
     */
    handleCommand(commandName: string, data: any): AsyncIterableIterator<TaskProgressChunk>;
}

export function isCommandHandler(value: any): value is CommandHandler {
    return typeof value.handleCommand === 'function';
}
