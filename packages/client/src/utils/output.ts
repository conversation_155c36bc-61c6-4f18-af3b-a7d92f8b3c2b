import {DrawElement, TaskProgressChunk, DynamicSection, ActionSet} from '@comate/plugin-shared-internals';

interface Options {
    outputType: 'string' | 'mixed';
}

function childrenToString(children: string | number | Array<string | number>) {
    const elements = Array.isArray(children) ? children : [children];
    return elements.map(v => v.toString()).join('');
}

// DrawElement
type ChunkDrawElement = string | DrawElement;

/**
 * @deprecated
 * 推荐使用 StringChunkStream 替换
 */
export class TaskProgressChunkStream {
    private readonly outputType: 'string' | 'mixed';

    private readonly elements: Array<string | ChunkDrawElement> = [];

    constructor(options: Options = {outputType: 'string'}) {
        this.outputType = options.outputType;
    }

    append(content: ChunkDrawElement): void {
        this.elements.push(this.normalize(content));
    }

    flush(content?: ChunkDrawElement): TaskProgressChunk {
        if (content) {
            this.append(content);
        }

        // @ts-ignore xxx
        return {command: 'draw', content: this.elements};
    }

    async flushStreaming(iterable: AsyncIterable<string>) {
        for await (const chunk of iterable) {
            this.flush(chunk);
        }
    }

    replaceLast(content: ChunkDrawElement): void {
        if (this.elements.length) {
            this.elements[this.elements.length - 1] = this.normalize(content);
        }
        else {
            this.append(content);
        }
    }

    flushReplaceLast(content: ChunkDrawElement): TaskProgressChunk {
        this.replaceLast(content);
        return this.flush();
    }

    fail(content: ChunkDrawElement): TaskProgressChunk {
        this.elements.length = 0;
        return {command: 'fail', content: this.normalize(content)};
    }

    private normalize(content: ChunkDrawElement) {
        if (this.outputType === 'mixed') {
            return content;
        }

        if (typeof content === 'string') {
            return content;
        }

        switch (content.type) {
            case 'p':
                return content.children + '\n\n';
            case 'code-block':
                return '```'
                    + (content.language ?? '')
                    + '\n'
                    + childrenToString(content.children)
                    + (content.closed ? '\n```' : '');
            default:
                return '';
        }
    }
}

export class ElementChunkStream {
    private elements: DrawElement[] = [];

    append(element: DrawElement | DrawElement[]): void {
        if (Array.isArray(element)) {
            this.elements.push(...element);
        }
        else {
            this.elements.push(element);
        }
    }

    flush(element?: DrawElement): TaskProgressChunk {
        if (element) {
            this.append(element);
        }

        return {command: 'draw', content: this.elements};
    }

    replaceLast(element: DrawElement): void {
        if (this.elements.length) {
            this.elements[this.elements.length - 1] = element;
        }
        else {
            this.append(element);
        }
    }

    replaceAll(element: DrawElement | DrawElement[]): void {
        if (this.elements.length) {
            this.elements = Array.isArray(element) ? element : [element];
        }
        else {
            this.append(element);
        }
    }

    flushReplaceLast(element: DrawElement): TaskProgressChunk {
        this.replaceLast(element);
        return this.flush();
    }

    flushReplaceAll(element: DrawElement | DrawElement[]): TaskProgressChunk {
        this.replaceAll(element);
        return this.flush();
    }

    appendLast(element: DrawElement): boolean {
        const parent = this.elements.at(-1);

        if (!parent) {
            return false;
        }
        if (!parent.children) {
            parent.children = [element];
            return true;
        }
        if (Array.isArray(parent.children)) {
            parent.children.push(element);
            return true;
        }

        return false;
    }

    flushAppendLast(element: DrawElement): TaskProgressChunk {
        this.appendLast(element);
        return this.flush();
    }

    fail(element: DrawElement): TaskProgressChunk {
        this.elements.length = 0;
        return {command: 'fail', content: element};
    }
}

// 关于为什么2个类基本一样还要重复写，我只是想让它们的参数名字不一样……
export class StringChunkStream {
    private readonly chunks: string[] = [];

    append(content: string): void {
        this.chunks.push(content);
    }

    flush(content?: string): TaskProgressChunk {
        if (content) {
            this.append(content);
        }

        return {command: 'draw', content: this.chunks};
    }

    replaceLast(content: string): void {
        if (this.chunks.length) {
            this.chunks[this.chunks.length - 1] = content;
        }
        else {
            this.append(content);
        }
    }

    flushReplaceLast(content: string): TaskProgressChunk {
        this.replaceLast(content);
        return this.flush();
    }

    fail(content: string): TaskProgressChunk {
        this.chunks.length = 0;
        return {command: 'fail', content};
    }
}

interface Sections {
    dynamicSections: DynamicSection[];
    dynamicFooterSections: DynamicSection[];
}

export class SectionChunkStream {
    private readonly sections: Sections = {dynamicSections: [], dynamicFooterSections: []};
    private content: string = '';
    private actionSet: ActionSet = {actions: {}, actionConfigs: {}};

    append(sections?: Partial<Sections>, content?: string, actionSet?: ActionSet): void {
        if (sections?.dynamicSections?.length) {
            this.sections.dynamicSections.push(...sections.dynamicSections);
        }
        if (sections?.dynamicFooterSections?.length) {
            this.sections.dynamicFooterSections.push(...sections.dynamicFooterSections);
        }
        if (content) {
            this.content = content;
        }
        if (actionSet) {
            this.actionSet = actionSet;
        }
    }

    flush(sections?: Partial<Sections>, content?: string, actionSet?: ActionSet): TaskProgressChunk {
        if (sections || content || actionSet) {
            this.append(sections, content, actionSet);
        }

        return {command: 'section', sections: this.sections, content: this.content, actionSet: this.actionSet};
    }

    replaceLast(sections: Partial<Sections>, content?: string, actionSet?: ActionSet): void {
        const dynamicSectionsShouldReplace = this.sections.dynamicSections.length && sections.dynamicSections?.length;
        const dynamicFooterSectionsShouldReplace = this.sections.dynamicFooterSections.length
            && sections.dynamicFooterSections?.length;
        if (dynamicSectionsShouldReplace || dynamicFooterSectionsShouldReplace) {
            if (sections.dynamicSections?.length) {
                this.sections.dynamicSections = sections.dynamicSections;
            }
            if (sections.dynamicFooterSections?.length) {
                this.sections.dynamicFooterSections = sections.dynamicFooterSections;
            }
            this.content = content ?? '';
            if (actionSet) {
                this.actionSet = actionSet;
            }
        }
        else {
            this.append(sections, content, actionSet);
        }
    }

    flushReplaceLast(sections: Partial<Sections>, content?: string, actionSet?: ActionSet): TaskProgressChunk {
        this.replaceLast(sections, content, actionSet);
        return this.flush();
    }

    // 暂时不需要，更新对应section即可
    // fail(content: DynamicSection): TaskProgressChunk {
    //     this.chunks.length = 0;
    //     return {command: 'fail', content};
    // }
}
