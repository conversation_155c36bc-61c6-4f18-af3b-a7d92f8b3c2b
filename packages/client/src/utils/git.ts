import {execa} from 'execa';
import {patchEnvPath} from '@comate/plugin-shared-internals';

patchEnvPath();

export async function isInGit(cwd: string) {
    try {
        const {stdout} = await execa('git', ['rev-parse', '--is-inside-work-tree'], {cwd});
        return stdout.trim() === 'true';
    }
    catch {
        return false;
    }
}

export async function getGitRepositoryUrl(cwd: string): Promise<string | null> {
    try {
        const {stdout} = await execa('git', ['config', '--get', 'remote.origin.url'], {cwd});
        return stdout.trim();
    }
    catch {
        return null;
    }
}

async function getGitBranch(cwd: string): Promise<string> {
    try {
        const {stdout} = await execa('git', ['branch', '--show-current'], {cwd});
        return stdout.trim();
    }
    catch {
        return '';
    }
}

export async function getGitRepositoryInfo(cwd: string) {
    const [repositoryUrl, branch] = await Promise.all([getGitRepositoryUrl(cwd), getGitBranch(cwd)]);
    return {repositoryUrl, branch};
}
