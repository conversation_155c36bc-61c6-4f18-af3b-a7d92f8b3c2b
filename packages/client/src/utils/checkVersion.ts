import {platform} from 'node:process';

export enum PLATFORM {
    INTERNAL = 'baidu-int',
    SAAS = 'baidu-saas',
}

export enum SYSTEM {
    WINDOWS = 'win32',
    LINUX = 'linux',
    FREEBSD = 'freebsd',
    OPENBSD = 'openbsd',
    SUNOS = 'sunos',
    MAC = 'darwin',
}

export const isInternal = process.env.COMATE_ENGINE_PLATFORM === PLATFORM.INTERNAL;

export const isWindows = platform === SYSTEM.WINDOWS;
