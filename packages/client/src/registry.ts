import {JSONSchema7Definition} from 'json-schema';
import {
    CapabilityDescription,
    PluginCapabilityType,
    PluginDescription,
    ProviderCapabilityInfo,
} from '@comate/plugin-shared-internals';
import {ProviderInit} from './providers/base.js';
import {PromptProvider, PromptProviderRegistryDescription} from './providers/prompt.js';
import {SkillProvider, SkillProviderRegistryDescription} from './providers/skill.js';
import {FallbackProvider, FallbackProviderRegistryDescription} from './providers/fallback.js';
import {ScanProvider, ScanProviderRegistryDescription} from './providers/scan.js';
import {BackgroundServiceProvider, BackgroundServiceProviderRegistryDescription} from './providers/background.js';

export interface FunctionParameterDefinition {
    type: 'object';
    properties: Record<string, JSONSchema7Definition>;
    required?: string[] | undefined;
}

export interface FunctionDefinition {
    name: string;
    description: string;
    parameters: FunctionParameterDefinition;
}

interface ProviderConstructorMap {
    Prompt: new(init: ProviderInit) => PromptProvider;
    Skill: new(init: ProviderInit) => SkillProvider;
    Fallback: new(init: ProviderInit) => FallbackProvider;
    Scan: new(init: ProviderInit) => ScanProvider;
    Background: new(init: ProviderInit) => BackgroundServiceProvider;
}

// 纯粹为了下面代码别换行
type CapabilityType = PluginCapabilityType;

interface ProviderCapabilityFactory<T extends CapabilityType = CapabilityType> extends ProviderCapabilityInfo {
    Provider: ProviderConstructorMap[T];
    type: T;
    function?: FunctionDefinition;
    default?: boolean;
}

type GenerateCapabilityType<T extends PluginCapabilityType> = T extends PluginCapabilityType
    ? ProviderCapabilityFactory<T>
    : never;

export type ProviderCapability = GenerateCapabilityType<CapabilityType>;

export interface CapabilityMatch {
    /** 查找的能力的关键字，用于fuzzy检索 */
    capabilityKeyword: string | undefined;
}

export interface PluginSetupContext {
    registry: PluginRegistry;
}

export type PluginSetup = (context: PluginSetupContext) => void;

function diff(x: string[], y: string[]) {
    return x.filter(v => !y.includes(v));
}

// eslint-disable-next-line @typescript-eslint/ban-types
function isClassExtends(target: Function, base: Function) {
    return target.prototype instanceof base || target === base;
}

// 这部分是暴露给插件开发者能看到的
class PluginRegistry {
    readonly capabilities: ProviderCapability[] = [];

    constructor(private readonly owner: PluginDescription) {}

    /**
     * 注册提示词能力
     *
     * @param name 能力名称，使用`kebab-case`形式
     * @param provider 提供者对象，包含名称和描述等属性
     */
    registerPromptProvider(name: string, provider: PromptProviderRegistryDescription) {
        if (!isClassExtends(provider, PromptProvider)) {
            throw new Error('Unexpected provider class registered as PromptProvider');
        }

        const description = this.findCapabilityDescription(name);
        const capability: ProviderCapability = {
            name,
            Provider: provider,
            type: 'Prompt',
            owner: this.owner,
            displayName: description.displayName,
            // 提示词能力对应一个参数固定的函数定义
            function: {
                name: provider.promptName,
                description: provider.description,
                parameters: {
                    type: 'object',
                    properties: {
                        prompt: {
                            type: 'string',
                        },
                    },
                    required: ['prompt'],
                },
            },
        };
        this.capabilities.push(capability);
    }

    /**
     * 注册技能能力
     *
     * @param name 能力名称，使用`kebab-case`形式
     * @param provider 提供者对象，包含名称、描述、参数需要多少等属性
     */
    registerSkillProvider(name: string, provider: SkillProviderRegistryDescription) {
        if (!isClassExtends(provider, SkillProvider)) {
            throw new Error('Unexpected provider class registered as SkillProvider');
        }

        const description = this.findCapabilityDescription(name);
        const capability: ProviderCapability = {
            name,
            Provider: provider,
            type: 'Skill',
            owner: this.owner,
            displayName: description.displayName,
            // 提示词能力对应一个参数固定的函数定义
            function: {
                name: provider.skillName,
                description: provider.description,
                parameters: provider.parameters,
            },
        };
        this.capabilities.push(capability);
    }

    /**
     * 注册后备能力
     *
     * @param name 能力名称，使用`kebab-case`形式
     * @param provider 提供者对象，实现后备能力
     * @param defaultProvider 是否作为默认Provider true的话跳过意图识别
     */
    registerFallbackProvider(name: string, provider: FallbackProviderRegistryDescription, defaultProvider?: boolean) {
        if (!isClassExtends(provider, FallbackProvider)) {
            throw new Error('Unexpected provider class registered as FallbackProvider');
        }

        this.findCapabilityDescription(name);
        const capability: ProviderCapability = {
            name,
            Provider: provider,
            type: 'Fallback',
            owner: this.owner,
            default: defaultProvider,
        };
        this.capabilities.push(capability);
    }

    /**
     * 注册巡航扫描能力
     *
     * @param name 能力名称，使用`kebab-case`形式
     * @param provider 提供者对象，实现巡航扫描能力
     */
    registerScanProvider(name: string, provider: ScanProviderRegistryDescription) {
        if (!isClassExtends(provider, ScanProvider)) {
            throw new Error('Unexpected provider class registered as ScanProvider');
        }

        const capability: ProviderCapability = {
            name,
            Provider: provider,
            type: 'Scan',
            owner: this.owner,
        };
        this.capabilities.push(capability);
    }

    /**
     * 注册后台服务能力
     *
     * @param name 能力名称，使用`kebab-case`形式
     * @param provider 提供者对象，实现巡航扫描能力
     */
    registerBackgroundServiceProvider(name: string, provider: BackgroundServiceProviderRegistryDescription) {
        if (!isClassExtends(provider, BackgroundServiceProvider)) {
            throw new Error('Unexpected provider class registered as BackgroundServiceProvider');
        }

        const capability: ProviderCapability = {
            name,
            Provider: provider,
            type: 'Background',
            owner: this.owner,
        };
        this.capabilities.push(capability);
    }

    private findCapabilityDescription(name: string): CapabilityDescription {
        const capability = this.owner.capabilities.find(v => v.name === name);

        if (!capability) {
            throw new Error(`Registered capability ${name} has no description.`);
        }

        return capability;
    }
}

export class ClientRegistry {
    private readonly registry: PluginRegistry;

    constructor(private readonly owner: PluginDescription) {
        this.registry = new PluginRegistry(owner);
    }

    findCapabilityByName(name: string): ProviderCapability | undefined {
        return this.registry.capabilities.find(v => v.name === name);
    }

    /**
     * 根据用户的输入获取可用的能力列表
     *
     * @param capability 插件能力名称，表现为`/`部分，不提供时会找到插件下的全部能力
     * @returns 符合条件的全部能力
     */
    listCapabilitiesByInput(capability: string | undefined): ProviderCapability[] {
        if (!capability) {
            const fallbackCapability = this.registry.capabilities.find(capability => capability.type === 'Fallback');
            if (fallbackCapability?.default) {
                return [fallbackCapability];
            }
        }
        return capability
            ? this.registry.capabilities.filter(v => v.name === capability)
            : this.registry.capabilities.slice();
    }

    /** @deprecated 使用`listCapabilitiesCan` */
    listCapabilitiesByType(type: PluginCapabilityType): ProviderCapability[] {
        return this.registry.capabilities.filter(v => v.type === type);
    }

    listCapabilitiesCan(can: (capabilityType: PluginCapabilityType) => boolean): ProviderCapability[] {
        return this.registry.capabilities.filter(v => can(v.type));
    }

    /**
     * 将一个插件注册到宿主
     *
     * @param description 插件描述信息，来自插件的`package.json`
     * @param setup 插件实现提供的`setup`函数
     */
    async register(setup: PluginSetup) {
        setup({registry: this.registry});

        // 校验是不是`capabilities`都注册上了
        this.validateMissingProvider();
    }

    /**
     * 验证插件实现的能力与声明的能力是否匹配
     *
     * @param described 通过插件的`package.json`声明的能力列表
     * @param provided 插件实际实现中注册的能力列表
     */
    private validateMissingProvider() {
        const describedNames = this.owner.capabilities.map(v => v.name);
        const providedNames = this.registry.capabilities.map(v => v.name);
        const missing = diff(describedNames, providedNames);

        if (missing.length) {
            throw new Error(`Missing providers for capabilities: ${missing.join(', ')}`);
        }
    }
}
