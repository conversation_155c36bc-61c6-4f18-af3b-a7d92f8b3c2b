import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>ink,
    <PERSON><PERSON>utton,
    ButtonGroup,
    AlertTag,
    Lo<PERSON>,
    <PERSON>lex,
    Card,
} from '@comate/plugin-shared-internals';

type ToProps<T> = Omit<T, 'type'>;

interface Element {
    type: string;
}

interface SimpleElementProps {
    children: string | number | Element | Array<string | number | Element>;
}

type VoidProps = Record<string, never>;

interface ImageProps {
    src: string;
    alt?: string;
    width?: number;
    height?: number;
}

interface LinkProps {
    href: string;
    children: string;
}

interface MarkdownProps {
    children: string;
}

type CodeBlockProps = ToProps<SimpleCodeBlock>;

type FileLinkProps = ToProps<FileLink>;

type CommandButtonProps = ToProps<CommandButton>;

type ButtonGroupProps = ToProps<ButtonGroup>;

type FlexProps = ToProps<Flex>;

type AlertTagProps = ToProps<AlertTag>;
type LoadingProps = ToProps<Loading>;
type CardProps = ToProps<Card>;

declare global {
    // eslint-disable-next-line @typescript-eslint/no-namespace
    namespace JSX {
        interface IntrinsicElements {
            p: SimpleElementProps;
            span: SimpleElementProps;
            ul: SimpleElementProps;
            ol: SimpleElementProps;
            em: SimpleElementProps;
            strong: SimpleElementProps;
            code: SimpleElementProps;
            blockquote: SimpleElementProps;
            h1: SimpleElementProps;
            h2: SimpleElementProps;
            h3: SimpleElementProps;
            h4: SimpleElementProps;
            h5: SimpleElementProps;
            h6: SimpleElementProps;
            li: SimpleElementProps;
            br: VoidProps;
            hr: VoidProps;
            img: ImageProps;
            a: LinkProps;
            table: SimpleElementProps;
            thead: SimpleElementProps;
            tbody: SimpleElementProps;
            tr: SimpleElementProps;
            th: SimpleElementProps;
            td: SimpleElementProps;
            markdown: MarkdownProps;
            'code-block': CodeBlockProps;
            'file-link': FileLinkProps;
            'command-button': CommandButtonProps;
            'button-group': ButtonGroupProps;
            flex: FlexProps;
            'alert-tag': AlertTagProps;
            loading: LoadingProps;
            card: CardProps;
        }

        interface ElementChildrenAttribute {
            // eslint-disable-next-line @typescript-eslint/ban-types
            children: {};
        }
    }
}

export function jsx(type: string, props: Record<string, any>) {
    // 按照React的逻辑，`boolean`、`null`、`undefined`是不会显示出来的
    const children = Array.isArray(props.children)
        ? props.children.filter(v => v != null && typeof v !== 'boolean')
        : props.children;
    return {type, ...props, children};
}

export function Fragment(props: any) {
    return props.children;
}

export {
    jsx as jsxDEV,
    jsx as jsxs,
};
