export {ProviderInit} from './providers/base.js';
export {default as fetch, authorizedFirstPartyFetch} from './api/index.js';
export {FallbackProvider, FallbackProviderRegistryDescription} from './providers/fallback.js';
export {PromptProvider, PromptProviderRegistryDescription} from './providers/prompt.js';
export {SkillProvider, SkillProviderRegistryDescription} from './providers/skill.js';
export {HttpRequest, PipeHttpRequestSkillProvider} from './providers/http.js';
export {ScanProvider, ScanProviderRegistryDescription} from './providers/scan.js';
export {BackgroundServiceProvider, BackgroundServiceProviderRegistryDescription} from './providers/background.js';
export {TextModel, FunctionModel} from '@comate/plugin-shared-internals';
export {getGitRepositoryUrl} from './utils/git.js';
// export {TaskProgressChunkStream} from './utils/output.js';
export {TaskProgressChunkStream, ElementChunkStream, StringChunkStream, SectionChunkStream} from './utils/output.js';
// NOTE: 以下仅给开发者提供类型，不允许开发者自行创建实例
export type {PluginConfig} from './internals/config.js';
export type {ActivationContext} from './internals/context.js';
export type {FileSystem, FileEntryType, FileEntry, FileStat} from './internals/fileSystem.js';
export type {
    InformationRetriever,
    GitRepositoryInfo,
    NoneRepositoryInfo,
    RepositoryInfo,
} from './internals/information.js';
export type {SuppotingLlm} from './internals/llm.js';
export type {MarkdownRender, TableRenderColumn} from './internals/render.js';
export type {User} from './internals/user.js';
export type {
    PluginCapabilityType,
    PluginConfigSection,
    PluginDescription,
    TaskProgressChunk,
    DiagnosticScanTaskProgressChunk,
    DrawChunk,
    FailChunk,
    UserDetail,
    FunctionParameterDefinition,
    ScanHandleGoal,
    Range,
    AcceptMethod,
} from '@comate/plugin-shared-internals';
export type {PluginSetupContext} from './registry.js';
export type {ScanTaskReference} from './handlers/scan.js';
export type {CommandHandler} from './handlers/command.js';
export type {DiagnosticScanHandler} from './handlers/diagnosticScan.js';
export type {Logger} from './internals/logger.js';

// NOTE: 用于检查插件是否把`@comate/plugin-host`和自己的代码打包，这段代码要做到“虽然没被使用，但构建和压缩工具不会删掉它”
(function comateMagic() {
    if (Math.random() === -1) {
        // eslint-disable-next-line no-console
        console.log('THIS IS COMATE');
    }
})();
