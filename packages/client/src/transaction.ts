import {BaseProvider} from './providers/base.js';

export class CapabilityTransaction {
    static current = new CapabilityTransaction();

    private readonly transactions: Map<string, BaseProvider> = new Map();

    hold(key: string, provider: BaseProvider) {
        this.transactions.set(key, provider);
    }

    release(key: string) {
        this.transactions.delete(key);
    }

    get(key: string): BaseProvider | undefined {
        return this.transactions.get(key);
    }
}
