import {TaskProgressChunk, TextModel} from '@comate/plugin-shared-internals';
import {ChatHandler} from '../handlers/chat.js';
import {RunnablePrompt} from '../internals/llm.js';
import {BaseProvider, ProviderInit} from './base.js';

/**
 * 提供自定义大模型提示词的能力
 */
export abstract class PromptProvider extends BaseProvider implements ChatHandler {
    /** 指定简单的提示词模板，可以使用`{{arg}}`占位符 */
    protected promptTemplate = '';

    async *handleChatQuery(): AsyncIterableIterator<TaskProgressChunk> {
        const prompt = await this.buildPrompt();
        const model = this.selectModel();
        const answer = await this.llm.askForText(prompt, {model});
        yield {command: 'draw', content: [answer]};
    }
    /**
     * 取消执行，用于在停止前释放资源
     */
    // eslint-disable-next-line
    handleChatCancel(arg: string): void {}

    /**
     * 自定义构建提示词
     *
     * @returns 最终与大模型交互的提示词
     */
    protected async buildPrompt(): Promise<RunnablePrompt> {
        if (!this.promptTemplate) {
            throw new Error('Prompt provider should either define promptTemplate or implement buildPrompt');
        }

        const context = this.currentContext as unknown as Record<string, string | number>;
        return this.llm.createPrompt(this.promptTemplate, context);
    }

    /**
     * 选择处理用户请求的模型
     *
     * @returns 确定使用的文本模型，默认为`TextModel.Default`
     */
    protected selectModel(): TextModel {
        return TextModel.Default;
    }
}

/**
 * 注册提示词能力的描述信息
 */
export interface PromptProviderRegistryDescription {
    /** 当前能力供运行时认知的名称，使用`camelCase`形式 */
    promptName: string;

    /** 对能力的描述，供大模型理解识别 */
    description: string;

    new(init: ProviderInit): PromptProvider;
}
