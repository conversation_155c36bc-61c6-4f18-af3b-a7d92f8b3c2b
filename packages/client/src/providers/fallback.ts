import {TaskProgressChunk} from '@comate/plugin-shared-internals';
import {<PERSON><PERSON><PERSON><PERSON><PERSON>} from '../handlers/chat.js';
import {BaseProvider, ProviderInit} from './base.js';

/**
 * 提供所有能力未触发时的后备能力
 */
export abstract class FallbackProvider extends BaseProvider implements Chat<PERSON>andler {
    async *handleChatQuery(): AsyncIterableIterator<TaskProgressChunk> {
        for await (const chunk of this.handleQuery()) {
            yield chunk;
        }
    }

    /**
     * 取消执行，用于在停止前释放资源
     */
    // eslint-disable-next-line
    handleChatCancel(arg: string): void {}

    /**
     * 处理用户提交的诉求
     *
     * @returns 处理过程及最终结果
     */
    protected async *handleQuery(): AsyncIterableIterator<TaskProgressChunk> {
        const prompt = this.llm.createPrompt(this.currentContext.query);
        const answer = await this.llm.askForText(prompt);
        yield {command: 'draw', content: answer};
    }
}

/**
 * 注册后备能力的描述信息
 */
export type FallbackProviderRegistryDescription = new(init: ProviderInit) => FallbackProvider;
