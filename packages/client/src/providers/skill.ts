import {TaskProgressChunk, FunctionParameterDefinition} from '@comate/plugin-shared-internals';
import {BaseProvider, ProviderInit} from './base.js';
import {ChatHandler} from '../handlers/chat.js';

type AnyArg = Record<string, any> | void;

/**
 * 提供单向技能的能力
 */
export abstract class SkillProvider<A extends AnyArg = void> extends BaseProvider implements ChatHandler {
    async *handleChatQuery(untrustedArgs: Record<string, any> | undefined): AsyncIterableIterator<TaskProgressChunk> {
        try {
            const args = untrustedArgs ?? this.defaultArgumentValue();

            if (!this.validateArgument(args)) {
                // TODO: 处理执行失败后的展示
                yield {command: 'fail', content: '执行失败'};
                return;
            }

            for await (const chunk of this.execute(args)) {
                yield chunk;
            }
        }
        catch (err) {
            // TODO: 处理执行失败后的展示
            yield {command: 'fail', content: `执行失败 ${err instanceof Error ? `: ${err.message}` : ''}`};
        }
    }

    /**
     * 取消执行，用于在停止前释放资源
     */

    // eslint-disable-next-line
    handleChatCancel(arg: string): void {}

    /**
     * 校验传入的参数是否符合当前技能的要求
     *
     * @param arg 由大模型生成的参数对象
     * @returns 当参数符合要求时，返回`true`
     */
    protected validateArgument(arg: any): arg is A {
        return true;
    }

    /**
     * 当用户未输入任何诉求时，生成默认的参数值
     *
     * @returns 默认参数值
     */
    protected defaultArgumentValue(): A {
        throw new Error('This skill does not provide a default argument');
    }

    /**
     * 使用从用户描述中提取的参数执行技能
     *
     * @returns 最终与大模型交互的提示词
     */
    protected abstract execute(arg: A): AsyncIterableIterator<TaskProgressChunk>;
}

/**
 * 注册提示词能力的描述信息
 */
export interface SkillProviderRegistryDescription {
    /** 供运行时认知的技能名称，使用`camelCase`形式 */
    skillName: string;

    /** 对技能的描述，供大模型理解识别 */
    description: string;

    /** 技能执行时接收的参数类型描述 */
    parameters: FunctionParameterDefinition;

    new(init: ProviderInit): SkillProvider<any>;
}
