import {BaseProvider, ProviderInit} from './base.js';
import {BackgroundHandler} from '../handlers/background.js';

/**
 * 提供后台运行的服务能力
 */
export abstract class BackgroundServiceProvider extends BaseProvider implements BackgroundHandler {
    abstract startBackgroundService(): void;
}

/**
 * 注册后台服务能力的描述信息
 */
export type BackgroundServiceProviderRegistryDescription = new(init: ProviderInit) => BackgroundServiceProvider;
