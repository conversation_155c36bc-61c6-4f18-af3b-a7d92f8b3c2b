import {TaskProgressChunk} from '@comate/plugin-shared-internals';
import {SkillProvider} from './skill.js';

type AnyArg = Record<string, any> | void;

export interface HttpRequest {
    url: string;
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
    headers?: Record<string, string>;
    body?: any;
    timeout?: number;
    responseType?: 'text' | 'json';
}

function getContentType(headers: HttpRequest['headers']) {
    return headers?.['Content-Type'] ?? headers?.['content-type'] ?? null;
}

export abstract class PipeHttpRequestSkillProvider<A extends AnyArg = void> extends SkillProvider<A> {
    protected async *execute(arg: A): AsyncIterableIterator<TaskProgressChunk> {
        const request = await this.buildRequest(arg);
        const isJsonRequest = getContentType(request.headers)?.includes('application/json');
        const signal = request.timeout ? AbortSignal.timeout(request.timeout) : null;

        try {
            const response = await fetch(
                request.url,
                {
                    method: request.method,
                    headers: request.headers,
                    body: request.body ? (isJsonRequest ? JSON.stringify(request.body) : request.body) : null,
                    signal,
                }
            );

            if (response.ok) {
                const data = await (request.responseType === 'json' ? response.json() : response.text());
                yield {command: 'draw', content: this.transformResponseToText(data)};
            }
            else {
                yield {command: 'fail', content: `非常抱歉，发生了未知的网络错误，状态码为${response.status}。`};
            }
        }
        catch {
            if (signal?.aborted) {
                yield {command: 'fail', content: '非常抱歉，网络请求超时。'};
            }
            else {
                yield {command: 'fail', content: '非常抱歉，发生了未知的网络错误。'};
            }
        }
    }

    protected transformResponseToText(responseData: any): string {
        return String(responseData);
    }

    /**
     * 构造一个HTTP请求的信息
     *
     * @param args 用户输入中提取的参数
     * @returns HTTP请求对象
     */
    protected abstract buildRequest(args: A): Promise<HttpRequest>;
}
