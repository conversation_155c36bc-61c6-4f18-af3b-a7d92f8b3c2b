import {BaseProvider, ProviderInit} from './base.js';
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, ScanTaskReference} from '../handlers/scan.js';
import {ScanHandleGoal, TaskProgressChunk} from '@comate/plugin-shared-internals';

const WILL_HANDLE_TIMEOUT = 100;

/**
 * 提供巡航扫描文件能力
 */
export abstract class ScanProvider extends BaseProvider implements ScanHandler {
    async check(): Promise<ScanHandleGoal | false> {
        try {
            const result = await Promise.race([this.willHandle(), this.timeout()]);

            // 这里让`timeout`返回数字表示超时，其它类型都算是正常的`willHandle`结果
            if (typeof result === 'number') {
                this.logger.warn('WillHandleTimeout');
                return false;
            }

            return result;
        }
        catch (ex) {
            this.logger.error(
                'WillHandleFailed',
                {message: ex instanceof Error ? ex.message : `${ex}`}
            );
            return false;
        }
    }

    scan(reference: ScanTaskReference): AsyncIterableIterator<TaskProgressChunk> {
        return this.handleScan(reference);
    }

    private timeout() {
        const executor = (resolve: (value: number) => void) => {
            setTimeout(
                () => resolve(0),
                WILL_HANDLE_TIMEOUT
            );
        };
        return new Promise(executor);
    }

    /**
     * 处理扫描任务并返回对代码的更新补丁
     *
     * @returns 返回的补丁将会被选择性应用到代码中
     */
    protected abstract handleScan(reference: ScanTaskReference): AsyncIterableIterator<TaskProgressChunk>;

    /**
     * 判断是否参与本次扫描任务
     *
     * @returns 选择参与任务则返回目标描述，不参与返回`false`
     */
    protected abstract willHandle(): Promise<ScanHandleGoal | false>;
}

/**
 * 注册后备能力的描述信息
 */
export interface ScanProviderRegistryDescription {
    /** 对扫描能力的描述，供大模型理解识别 */
    description: string;

    new(init: ProviderInit): ScanProvider;
}
