import {
    ChatQueryParsed,
    ChatQueryPayload,
    canHandleChatQuery,
    extractChunkContent,
    isInteractiveContent,
} from '@comate/plugin-shared-internals';
import {SuppotingLlm} from '../internals/llm.js';
import {ProviderCapability} from '../registry.js';
import {EngineSession} from '../channel.js';
import {isChatHandler} from '../handlers/chat.js';
import {InvokeContext, InvokerBase} from './base.js';
import {CapabilityTransaction} from '../transaction.js';
import {PromptProvider} from '../providers/prompt.js';
import {SkillProvider} from '../providers/skill.js';
import {FallbackProvider} from '../providers/fallback.js';

const noLogger = {
    for: () => noLogger,
    verbose: () => {},
    info: () => {},
    warn: () => {},
    error: () => {},
    disable: () => {},
};

interface CapabilityPickResult {
    capability: ProviderCapability;
    args: any;
}
type ChatProvider = PromptProvider | SkillProvider<void> | FallbackProvider;

export class ChatInvoker extends InvokerBase {
    private readonly chatProviders = new Map<string, ChatProvider>();
    attach(messageId: string, provider: ChatProvider) {
        this.chatProviders.set(messageId, provider);
    }
    detach(messageId: string) {
        this.chatProviders.delete(messageId);
    }
    async invoke(payload: ChatQueryPayload, session: EngineSession) {
        try {
            if (payload.input.data && payload.input.data.chatCancleToken) {
                await this.invokeForChatCancle(payload, session, payload.input.data.chatCancleToken);
                return;
            }
            await this.invokeForChat(payload, session);
        }
        catch (ex) {
            // TODO: 优化异常通知
            void session.reportChatProgress(
                payload.input.messageId,
                {command: 'fail', content: '抱歉，插件执行失败' + (ex instanceof Error ? `: ${ex.message}` : '')}
            );
        }
        finally {
            session.finish();
        }
    }

    private async invokeForChatCancle(payload: ChatQueryPayload, session: EngineSession, cancleToken: string) {
        const provider = this.chatProviders.get(payload.input.messageId);
        if (provider) {
            provider.handleChatCancel(cancleToken);
            session.finish(); // 直接停止通信
            this.detach(payload.input.messageId);
        }
    }

    private async invokeForChat(payload: ChatQueryPayload, session: EngineSession) {
        const {capability, args} = await this.resolveInputToCapability(
            payload.input,
            new SuppotingLlm(payload.pluginName, session, noLogger)
        );

        const context: InvokeContext = {
            isRegenerated: payload.isRegenerated,
            session,
            capability,
            pluginName: payload.pluginName,
            systemInfo: payload.systemInfo,
            activationContext: {
                ...payload.context,
                data: payload.input.data,
            },
            informationList: payload.input.informationList,
        };

        const provider = this.createProviderInstance(context);
        if (!isChatHandler(provider)) {
            throw new Error(`Provider ${provider.constructor.name} 必须实现 ChatHandler`);
        }
        this.attach(payload.input.messageId, provider);
        session.log(
            'system',
            this.constructor.name,
            'InvokeChatCapability',
            {capability: capability.name, args}
        );

        for await (const chunk of provider.handleChatQuery(args)) {
            const content = extractChunkContent(chunk);

            if (isInteractiveContent(content)) {
                session.log(
                    'system',
                    this.constructor.name,
                    'HoldProviderForInteractiveContent',
                    {taskId: payload.input.messageId}
                );
                // TODO: 什么时候释放？比如clear
                CapabilityTransaction.current.hold(
                    `${payload.pluginName}/${payload.input.messageId}`,
                    provider
                );
            }

            session.reportChatProgress(payload.input.messageId, chunk, capability.name);
        }
        await this.invokeForChatCancle(payload, session, 'finished');
    }

    private resolveSingleCapabilityUsage(input: ChatQueryParsed, capability: ProviderCapability) {
        const type = capability.type;

        // 如果只有一个能用的，且它是`Fallback`或`Prompt`型的，就不需要再去做意图和参数提取了
        if (type === 'Fallback' || type === 'Prompt') {
            return {capability, args: undefined};
        }
        if (type === 'Skill') {
            // 是`Skill`类型的能力，且这个能力本身就不需要参数，可以直接调用
            // 实际这里`capability.function`不可能不存在
            // NOTE: 这个`if`不能和下面的换顺序，生成的参数值是不一样的，一个`{}`，一个`undefined`
            if (!capability.function || !Object.keys(capability.function.parameters.properties).length) {
                return {capability, args: {}};
            }
            // 用户根本没有输入任何能够做参数提取的内容，那么就直接交给能力自己去使用默认参数
            if (!input.query.trim()) {
                return {capability, args: undefined};
            }
        }
        return null;
    }

    private async resolveInputToCapability(input: ChatQueryParsed, llm: SuppotingLlm): Promise<CapabilityPickResult> {
        const capabilities = this
            .registry
            .listCapabilitiesByInput(input.capability)
            .filter(v => canHandleChatQuery(v.type));

        if (!capabilities.length) {
            throw new Error('未找到适用于当前会话的能力');
        }

        if (capabilities.length === 1) {
            const result = this.resolveSingleCapabilityUsage(input, capabilities[0]);

            if (result) {
                return result;
            }
        }

        // 做意图识别和参数提取
        const functions = capabilities.flatMap(v => (v.function ? v.function : []));
        // TODO: 实际上只是用户的输入很难选择到正确的能力，因为前面的`@`和`/`部分没有，所以需要优化下拼一个逻辑出来
        // TODO 通过参数识别是否是平台调用模型，计费不算插件的，可能会被开发者hack，写个意义不明的key迷惑一下
        const prompt = llm.createPrompt(input.query, {$$temperature: '0.7'});
        const result = await llm.askForFunction(prompt, functions);

        if (result) {
            const capability = capabilities.find(v => v.function?.name === result.name);

            if (capability) {
                return {capability, args: JSON.parse(result.arguments)};
            }
        }

        // 如果没有找到合适的能力，且用户实际上通过`/`指定了能力，那就要直接失败
        if (input.capability) {
            throw new Error('请调整输入，以便为插件生成合适的参数');
        }

        // 识别不出能用的，且用户也没有指定一个能力，此时可以尝试用`Fallback`型的兜底
        const fallback = capabilities.find(v => v.type === 'Fallback');

        if (fallback) {
            return {capability: fallback, args: undefined};
        }

        // 连`Fallback`型的都没有，那么就只能执行失败
        throw new Error('未找到处理当前会话的能力');
    }
}
