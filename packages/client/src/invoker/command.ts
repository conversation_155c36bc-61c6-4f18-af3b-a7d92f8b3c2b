import {CustomCommandInvokePayload, extractChunkContent, isInteractiveContent} from '@comate/plugin-shared-internals';
import {EngineSession} from '../channel.js';
import {InvokerBase} from './base.js';
import {isCommandHandler} from '../handlers/command.js';
import {CapabilityTransaction} from '../transaction.js';

export class CommandInvoker extends InvokerBase {
    async invoke(payload: CustomCommandInvokePayload, session: EngineSession) {
        try {
            await this.invokeCommand(payload, session);
        }
        catch (ex) {
            void session.reportChatProgress(
                payload.taskId,
                {command: 'fail', content: '抱歉，插件执行失败' + (ex instanceof Error ? `: ${ex.message}` : '')},
                undefined,
                payload.messageId
            );
        }
        finally {
            session.finish();
        }
    }

    private async invokeCommand(payload: CustomCommandInvokePayload, session: EngineSession) {
        const {pluginName, taskId, commandName, data, messageId, commandContext} = payload;
        const providerKey = `${pluginName}/${taskId}`;
        const provider = CapabilityTransaction.current.get(providerKey);

        if (!provider) {
            throw new Error('插件响应已过期');
        }

        // TODO: 什么时候释放？
        // CapabilityTransaction.current.release(providerKey);

        if (!isCommandHandler(provider)) {
            session.log(
                'error',
                this.constructor.name,
                'ProviderNotCommandHandler',
                {messageId, commandName}
            );
            throw new Error('插件无法处理当前指示');
        }

        session.log(
            'system',
            this.constructor.name,
            'InvokeCommand',
            {messageId, commandName}
        );

        provider.setCommandContext(commandContext);
        for await (const chunk of provider.handleCommand(commandName, data)) {
            const content = extractChunkContent(chunk);

            if (isInteractiveContent(content)) {
                session.log(
                    'system',
                    this.constructor.name,
                    'HoldProviderForInteractiveContent',
                    {messageId, taskId}
                );
                // TODO: 什么时候释放？
                CapabilityTransaction.current.hold(`${payload.pluginName}/${payload.messageId}`, provider);
            }

            session.reportChatProgress(taskId, chunk, undefined, messageId);
        }
    }
}
