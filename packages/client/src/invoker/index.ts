import {
    ChatQueryPayload,
    ScanTaskPayload,
    StartBackgroundServicePayload,
    CustomCommandInvokePayload,
    WillScanPayload,
    DiagnosticScanInvokePayload,
    SecubotQueryPayload,
} from '@comate/plugin-shared-internals';
import {ClientRegistry} from '../registry.js';
import {EngineSession} from '../channel.js';
import {ChatInvoker} from './chat.js';
import {ScanInvoker} from './scan.js';
import {BackgroundServiceInvoker} from './background.js';
import {CommandInvoker} from './command.js';
import {DiagnosticScanInvoker} from './diagnosticScan.js';
import {SecubotInvokers} from './secubot.js';

export class CapabilityInvoker {
    private readonly chatInvokers = new Map<string, ChatInvoker>();
    private readonly scanInvokers = new Map<string, ScanInvoker>();
    private readonly backgroundServiceInvokers = new Map<string, BackgroundServiceInvoker>();
    private readonly diagnosticScanInvokers = new Map<string, DiagnosticScanInvoker>();
    private readonly commandInvokers = new Map<string, CommandInvoker>();
    private readonly secubotInvokers = new Map<string, SecubotInvokers>();

    attach(pluginName: string, registry: ClientRegistry) {
        this.chatInvokers.set(pluginName, new ChatInvoker(pluginName, registry));
        this.diagnosticScanInvokers.set(pluginName, new DiagnosticScanInvoker(pluginName, registry));
        this.scanInvokers.set(pluginName, new ScanInvoker(pluginName, registry));
        this.backgroundServiceInvokers.set(pluginName, new BackgroundServiceInvoker(pluginName, registry));
        this.commandInvokers.set(pluginName, new CommandInvoker(pluginName, registry));
        this.secubotInvokers.set(pluginName, new SecubotInvokers(pluginName, registry));
    }

    detach(pluginName: string) {
        this.chatInvokers.delete(pluginName);
        this.diagnosticScanInvokers.delete(pluginName);
        this.scanInvokers.delete(pluginName);
        this.backgroundServiceInvokers.delete(pluginName);
        this.commandInvokers.delete(pluginName);
        this.secubotInvokers.delete(pluginName);
    }

    async invokeChat(payload: ChatQueryPayload, session: EngineSession) {
        const invoker = this.chatInvokers.get(payload.pluginName);

        if (!invoker) {
            throw new Error(`No invoker for plugin ${payload.pluginName} found`);
        }

        await invoker.invoke(payload, session);
    }

    async invokeSecubot(payload: SecubotQueryPayload, session: EngineSession) {
        const invoker = this.secubotInvokers.get(payload.pluginName);
        if (!invoker) {
            throw new Error(`No invoker for plugin ${payload.pluginName} found`);
        }

        await invoker.invoke(payload, session);
    }

    async invokeWillScan(payload: WillScanPayload, session: EngineSession) {
        const invoker = this.scanInvokers.get(payload.pluginName);

        if (!invoker) {
            throw new Error(`No invoker for plugin ${payload.pluginName} found`);
        }

        await invoker.invokeWillScan(payload, session);
    }

    async invokeReleaseScan(payload: ScanTaskPayload, session: EngineSession) {
        const invoker = this.scanInvokers.get(payload.pluginName);

        if (!invoker) {
            throw new Error(`No invoker for plugin ${payload.pluginName} found`);
        }

        await invoker.releaseScan(payload, session);
    }

    async invokeScanStreamTask(payload: ScanTaskPayload, session: EngineSession) {
        const invoker = this.scanInvokers.get(payload.pluginName);

        if (!invoker) {
            throw new Error(`No invoker for plugin ${payload.pluginName} found`);
        }

        await invoker.invokeScanStream(payload, session);
    }

    async invokeDiagnosticScan(payload: DiagnosticScanInvokePayload, session: EngineSession) {
        const invoker = this.diagnosticScanInvokers.get(payload.pluginName);

        if (!invoker) {
            throw new Error(`No invoker for plugin ${payload.pluginName} found`);
        }

        await invoker.invoke(payload, session);
    }

    async invokeBackgroundService(payload: StartBackgroundServicePayload, session: EngineSession) {
        const invoker = this.backgroundServiceInvokers.get(payload.pluginName);

        if (!invoker) {
            throw new Error(`No invoker for plugin ${payload.pluginName} found`);
        }

        await invoker.invoke(payload, session);
    }

    async invokeCommand(payload: CustomCommandInvokePayload, session: EngineSession) {
        const invoker = this.commandInvokers.get(payload.pluginName);

        if (!invoker) {
            throw new Error(`No invoker for plugin ${payload.pluginName} found`);
        }

        await invoker.invoke(payload, session);
    }
}
