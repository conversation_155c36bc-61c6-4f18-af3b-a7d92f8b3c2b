import {ReportWillScanPayload, ScanTaskPayload, WillScanPayload} from '@comate/plugin-shared-internals';
import {EngineSession} from '../channel.js';
import {ScanTaskReference, isScanHandler} from '../handlers/scan.js';
import {CapabilityTransaction} from '../transaction.js';
import {InvokeContext, InvokerBase} from './base.js';

export class ScanInvoker extends InvokerBase {
    async releaseScan(payload: ScanTaskPayload, session: EngineSession) {
        session.log(
            'system',
            this.constructor.name,
            'ReleaseScanProvider',
            {scanId: payload.scanId, capabilityName: payload.capabilityName}
        );
        CapabilityTransaction.current.release(`${payload.scanId}/${payload.capabilityName}`);
        session.finish();
    }

    async invokeScanStream(payload: ScanTaskPayload, session: EngineSession) {
        const transactionKey = `${payload.scanId}/${payload.capabilityName}`;
        const provider = CapabilityTransaction.current.get(transactionKey);
        CapabilityTransaction.current.release(transactionKey);

        if (!provider || !isScanHandler(provider)) {
            session.log(
                'warn',
                this.constructor.name,
                'ScanProviderMissing',
                {scanId: payload.scanId, capabilityName: payload.capabilityName}
            );
            return;
        }

        session.log(
            'system',
            this.constructor.name,
            'InvokeScanCapability',
            {scanId: payload.scanId, capabilityName: payload.capabilityName}
        );

        try {
            const reference: ScanTaskReference = {
                description: payload.description,
                ranges: payload.ranges,
            };
            for await (const chunk of provider.scan(reference)) {
                session.reportScanProgress(payload.scanId, payload.capabilityName, chunk);
            }
        }
        catch (ex) {
            // TODO: 优化异常通知
            void session.reportScanProgress(
                payload.scanId,
                payload.capabilityName,
                {command: 'fail', content: '抱歉，插件执行失败' + (ex instanceof Error ? `: ${ex.message}` : '')}
            );
        }
        finally {
            session.finish();
        }
    }

    async invokeWillScan(payload: WillScanPayload, session: EngineSession) {
        const capability = this.registry.findCapabilityByName(payload.capabilityName);

        if (!capability) {
            session.finish();
            return;
        }

        // 对每一个能力，都并行做检查，能处理立刻报告给外部
        const context: InvokeContext = {
            session,
            capability,
            pluginName: payload.pluginName,
            systemInfo: payload.systemInfo,
            activationContext: payload.context,
            // 助理模式下是没有指定挂载知识的
            informationList: [],
        };
        const provider = this.createProviderInstance(context);

        if (!isScanHandler(provider)) {
            session.log(
                'error',
                this.constructor.name,
                'UnsatisfiedProviderType',
                {capability: capability.name, class: provider.constructor.name, required: 'ScanHandler'}
            );
            return;
        }

        try {
            const goal = await provider.check();
            session.log(
                'system',
                this.constructor.name,
                'CheckWillHandle',
                {scanId: payload.scanId, capability: capability.name, goal}
            );
            if (goal && goal.ranges.length) {
                CapabilityTransaction.current.hold(`${payload.scanId}/${capability.name}`, provider);
                const reportPayload: ReportWillScanPayload = {
                    scanId: payload.scanId,
                    pluginName: this.pluginName,
                    capabilityName: capability.name,
                    goal,
                };
                session.reportWillScan(reportPayload);
            }
        }
        finally {
            session.finish();
        }
    }
}
