import {StartBackgroundServicePayload} from '@comate/plugin-shared-internals';
import {EngineSession} from '../channel.js';
import {InvokeContext, InvokerBase} from './base.js';
import {isBackgroundHandler} from '../handlers/background.js';
import {ProviderCapability} from '../registry.js';

const noLogger = {
    for: () => noLogger,
    verbose: () => {},
    info: () => {},
    warn: () => {},
    error: () => {},
};

interface InvokeTarget {
    capability: ProviderCapability;
    payload: StartBackgroundServicePayload;
    session: EngineSession;
}

export class BackgroundServiceInvoker extends InvokerBase {
    async invoke(payload: StartBackgroundServicePayload, session: EngineSession) {
        const capabilities = this.registry.listCapabilitiesCan(type => type === 'Background');
        for (const capability of capabilities) {
            try {
                this.invokeService({capability, payload, session});
            }
            catch (ex) {
                session.log(
                    'error',
                    this.constructor.name,
                    'InvokeBackgroundServiceCapabilityError',
                    {
                        capability: capability.name,
                        reason: ex instanceof Error ? ex.message : `${ex}`,
                    }
                );
            }
        }
    }

    private invokeService({capability, payload, session}: InvokeTarget) {
        const context: InvokeContext = {
            session,
            capability,
            pluginName: payload.pluginName,
            systemInfo: payload.systemInfo,
            activationContext: {
                query: '',
                selectedCode: '',
                activeFileContent: '',
                activeFileLineContent: '',
                activeFilePath: '',
                activeFileName: '',
                activeFileLanguage: '',
                selectedRange: [],
            },
            informationList: [],
        };
        const provider = this.createProviderInstance(context);

        if (!isBackgroundHandler(provider)) {
            // 这里是多个任务一起开的，不能抛异常，只能记日志
            session.log(
                'error',
                this.constructor.name,
                'InvokeBackgroundServiceCapabilityError',
                {
                    capability: capability.name,
                    reason: `Provider ${provider.constructor.name} must implement BackgroundHandler`,
                }
            );
            return;
        }

        session.log(
            'system',
            this.constructor.name,
            'InvokeBackgroundServiceCapability',
            {capability: capability.name}
        );

        provider.startBackgroundService();
    }
}
