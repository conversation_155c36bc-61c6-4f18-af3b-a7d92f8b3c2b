{"name": "@comate/plugin-host", "version": "0.9.2", "type": "module", "engines": {"node": ">=20.10.0"}, "exports": {".": "./dist/index.js", "./bin": "./bin.js", "./jsx-runtime": "./dist/jsx/index.js", "./package.json": "./package.json"}, "files": ["dist", "bin.js"], "contributors": ["zhanglili01 <<EMAIL>>"], "license": "MIT", "publishConfig": {"access": "public"}, "scripts": {"clean": "rm -rf dist", "build": "tsc", "lint": "eslint --max-warnings=0 src --fix", "bundle": "tsc && rollup -c rollup.config.js"}, "devDependencies": {"@rollup/plugin-json": "^6.1.0", "@types/json-schema": "^7.0.15", "@types/node": "18.15.0", "@types/unixify": "^1.0.2", "eslint": "^8.56.0", "execa": "^8.0.1", "typescript": "^5.3.2"}, "dependencies": {"@comate/plugin-shared-internals": "^0.9.2", "markdown-table": "^3.0.3", "unixify": "^1.0.0"}}