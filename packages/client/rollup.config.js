import resolve from '@rollup/plugin-node-resolve';
import dts from 'rollup-plugin-dts';
import commonjs from '@rollup/plugin-commonjs';
import json from '@rollup/plugin-json';
import terser from '@rollup/plugin-terser';

const config = [{
    input: ['dist/main.js', 'dist/index.js'],
    output: {
        dir: 'bundle',
        format: 'es',
    },
    plugins: [
        commonjs(),
        resolve({
            preferBuiltins: true,
        }),
        json(),
        terser()
    ]
}, {
    input: 'dist/main.d.ts',
    output: [{ file: 'bundle/main.d.ts', format: 'es' }],
    plugins: [dts()],
}];

export default config;
