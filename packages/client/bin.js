import {PluginHost} from './dist/main.js';

if (process.env.COMATE_PLUGIN_GROUP_KEY) {
    process.title = 'comate-plugin-' + process.env.COMATE_PLUGIN_GROUP_KEY;
}

const host = new PluginHost();
await host.start();

process.on(
    'message',
    async message => {
        if (!message.comateInternalEvent) {
            return;
        }

        const event = message.comateInternalEvent;

        if (event.type === 'PLUGIN_ATTACH') {
            const description = await host.attach(event.payload.directory);
            if (description) {
                const setupMessage = {
                    comateInternalEvent: {
                        type: 'PLUGIN_SETUP',
                        payload: {
                            pluginName: description.name,
                        },
                    },
                };
                process.send(setupMessage);
            }
        }
        else if (event.type === 'PLUGIN_DETACH') {
            await host.detach(event.payload.pluginName);
        }
    }
);
