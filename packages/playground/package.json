{"name": "@comate/plugin-playground", "version": "0.9.2", "type": "module", "engines": {"node": ">=20.10.0"}, "exports": {".": "./dist/server/index.js"}, "files": ["dist"], "license": "MIT", "publishConfig": {"access": "public"}, "scripts": {"start": "skr dev --env-file=../../.env", "build": "tsc --noEmit && skr build --clean && tsc -p tsconfig.build.json", "analyze": "npm run build -- --analyze --build-target=stable", "lint": "skr lint", "lint-staged": "skr lint --staged --fix --auto-stage", "test": "skr test --target=react"}, "dependencies": {"@comate/plugin-engine": "^0.9.2", "@comate/plugin-shared-internals": "^0.9.2", "@comate/plugin-jsx": "^0.9.2", "conf": "^12.0.0", "eventsource-parser": "^1.1.2", "globby": "13.2.2"}, "devDependencies": {"@codemirror/lang-cpp": "^6.0.2", "@codemirror/lang-html": "^6.4.7", "@codemirror/lang-java": "^6.0.1", "@codemirror/lang-javascript": "^6.2.1", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-less": "^6.0.2", "@codemirror/lang-lezer": "^6.0.1", "@codemirror/lang-markdown": "^6.2.4", "@codemirror/lang-php": "^6.0.1", "@codemirror/lang-python": "^6.1.3", "@codemirror/lang-rust": "^6.0.1", "@codemirror/lang-sass": "^6.0.2", "@codemirror/lang-sql": "^6.5.5", "@codemirror/lang-xml": "^6.0.2", "@comate/plugin-host": "^0.9.2", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@formily/antd-v5": "^1.1.9", "@formily/core": "^2.3.1", "@formily/react": "^2.3.1", "@otakustay/react-json-view": "^0.8.4", "@otakustay/react-source-view": "^0.0.1", "@otakustay/source-tokenizer": "^1.0.0", "@reskript/cli": "6.1.1", "@reskript/cli-build": "6.1.1", "@reskript/cli-dev": "6.1.1", "@reskript/cli-lint": "6.1.1", "@reskript/config-lint": "6.1.1", "@reskript/settings": "6.1.1", "@types/express": "^4.17.21", "@types/hast": "^3.0.3", "@types/node": "18.15.0", "@types/ramda": "^0.29.9", "@types/react": "^18.2.48", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^18.2.18", "@uiw/react-codemirror": "^4.21.21", "antd": "^5.13.1", "core-js": "^3.35.0", "dedent": "^1.5.1", "eslint": "^8.56.0", "huse": "^2.0.4", "husky": "^8.0.3", "jotai": "^2.6.2", "material-file-icons": "^2.4.0", "prism-color-variables": "^1.0.1", "ramda": "^0.29.1", "react": "^18.2.0", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18.2.0", "react-icons": "^5.0.1", "react-markdown": "^9.0.1", "react-suspense-boundary": "^2.3.1", "refractor": "^4.8.1", "remark-gfm": "^4.0.0", "stylelint": "^15.11.0", "typescript": "^5.3.2", "webpack": "^5.89.0"}}