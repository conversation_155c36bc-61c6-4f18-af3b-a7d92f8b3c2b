import {configure} from '@reskript/settings';
import {setupEngineApp} from './server/setup.js';
import {preparePluginLink} from './server/link.js';

export default configure(
    'webpack',
    {
        featureMatrix: {
            stable: {},
            dev: {},
        },
        build: {
            appTitle: 'Comate Playground',
            uses: ['emotion'],
            finalize: config => {
                // eslint-disable-next-line no-param-reassign
                config.optimization.splitChunks = {
                    chunks: 'all',
                    minSize: 30 * 1024,
                    maxSize: 800 * 1024,
                    maxInitialRequests: 5,
                };
                return config;
            },
        },
        devServer: {
            port: 8788,
            proxyRewrite: {
                '/api': 'http://localhost:8788/__skr__',
            },
        },
        portal: {
            setup: async app => {
                const plugins = await preparePluginLink();
                await setupEngineApp(app, {plugins, workspace: process.env.WORKSPACE});
            },
        },
    }
);
