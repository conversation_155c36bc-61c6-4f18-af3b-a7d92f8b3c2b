import {ReactNode} from 'react';
import {FormLayout as BaseLayout, FormButtonGroup, Submit as SubmitButton} from '@formily/antd-v5';
import styled from '@emotion/styled';

const SectionTitle = styled.h3`
    font-size: 20px;
    font-weight: bold;
    margin: 20px 0;
`;

interface SectionProps {
    title: string;
    children: ReactNode;
}

function Section({title, children}: SectionProps) {
    return (
        <BaseLayout layout="vertical">
            <SectionTitle>{title}</SectionTitle>
            {children}
        </BaseLayout>
    );
}

const FieldLabel = styled.span`
    font-size: 14px;
`;

const FieldLayout = styled.label`
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
    gap: 8px;
`;

interface FieldProps {
    label: string;
    children: ReactNode;
}

function Field({label, children}: FieldProps) {
    return (
        <FieldLayout>
            <FieldLabel>{label}</FieldLabel>
            <div>{children}</div>
        </FieldLayout>
    );
}

interface SubmitProps {
    onSubmit: (data: any) => Promise<any>;
}

function Submit({onSubmit}: SubmitProps) {
    return (
        <FormButtonGroup>
            <SubmitButton onSubmit={onSubmit}>提交</SubmitButton>
        </FormButtonGroup>
    );
}

const FormLayout = styled.div`
    padding: 0 8px;
    max-height: 100%;
    overflow-y: auto;
`;

export default Object.assign(
    FormLayout,
    {Section, Field, Submit}
);
