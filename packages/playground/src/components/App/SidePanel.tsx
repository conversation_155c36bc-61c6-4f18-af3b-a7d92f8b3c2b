import {ReactNode, useState} from 'react';
import {IoChatbox, IoList, IoSettings, Io<PERSON>erson} from 'react-icons/io5';
import styled from '@emotion/styled';
import Chat from '@/components/Chat/index.js';
import Log from '@/components/Log/index.js';
import Config from '@/components/Config/index.js';
import User from '@/components/User/index.js';

interface SelectionProps {
    selected: boolean;
}

const NavIcon = styled.i`
    width: 14px;
    height: 14px;
    color: #3478f6;
`;

const NavItem = styled.span<SelectionProps>`
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    width: 80px;
    font-size: 14px;
    background-color: ${props => (props.selected ? '#fafafa' : 'transparent')};
    cursor: pointer;
`;

const Header = styled.div`
    display: flex;
    background-color: #f1f1f1;
`;

const Layout = styled.div`
    display: grid;
    grid-template-rows: var(--side-panel-header-height) 1fr;
    height: 100%;
    background-color: #fafafa;
`;

export default function SidePanel() {
    const [panel, setPanel] = useState('chat');
    const renderTab = (to: string, text: string, icon: ReactNode) => (
        <NavItem
            selected={panel === to}
            onClick={() => setPanel(to)}
        >
            <NavIcon>{icon}</NavIcon>
            {text}
        </NavItem>
    );
    const renderPanel = () => {
        switch (panel) {
            case 'chat':
                return <Chat />;
            case 'log':
                return <Log />;
            case 'config':
                return <Config />;
            case 'user':
                return <User />;
            default:
                return null;
        }
    };

    return (
        <Layout>
            <Header>
                {renderTab('chat', '对话', <IoChatbox />)}
                {renderTab('log', '日志', <IoList />)}
                {renderTab('config', '配置', <IoSettings />)}
                {renderTab('user', '用户', <IoPerson />)}
            </Header>
            {renderPanel()}
        </Layout>
    );
}
