import {ReactElement, useCallback} from 'react';
import styled from '@emotion/styled';
import {useTransitionState} from 'huse';
import {VscReplaceAll, VscCopy, VscCheckAll, VscTerminal, VscGoToFile} from 'react-icons/vsc';
import CopyToClipboard from 'react-copy-to-clipboard';
import {AcceptMethod} from '@comate/plugin-shared-internals';

const ACCEPT_METHOD_TO_ICON: Record<AcceptMethod['method'], ReactElement> = {
    replaceSelection: <VscGoToFile title="替换选中代码" />,
    replaceContent: <VscReplaceAll title="替换指定代码" />,
    toTerminal: <VscTerminal title="采纳到命令行" />,
    copy: <VscCopy title="复制代码" />,
};

const Layout = styled.span`
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
`;

interface Props {
    method: AcceptMethod;
    text: string;
}

export function CopyCode({method, text}: Props) {
    const [noticing, setNoticing] = useTransitionState(false, 2500);
    const copy = useCallback(
        () => setNoticing(true),
        [setNoticing]
    );

    return (
        <CopyToClipboard text={text} onCopy={copy}>
            <Layout>
                {noticing ? <VscCheckAll /> : ACCEPT_METHOD_TO_ICON[method.method]}
            </Layout>
        </CopyToClipboard>
    );
}
