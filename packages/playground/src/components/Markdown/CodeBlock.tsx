import {useMemo} from 'react';
import styled from '@emotion/styled';
import {Source} from '@otakustay/react-source-view';
import {ExtraProps} from 'react-markdown';
import {ElementContent} from 'hast';
import {tokenize, TokenizeOptions} from '@otakustay/source-tokenizer';
import {refractor} from 'refractor';
import {getIcon} from 'material-file-icons';
import {AcceptMethod} from '@comate/plugin-shared-internals';
import '@otakustay/react-source-view/style/index.css';
import 'prism-color-variables/variables.css';
import 'prism-color-variables/themes/duracula.css';
import {CopyCode} from './CopyCode.js';
import {sampleFileNameFromLanguage} from './language.js';

interface LanguageTypeIconProps {
    language: string;
}

function LanguageTypeIcon({language}: LanguageTypeIconProps) {
    const icon = getIcon(sampleFileNameFromLanguage(language));

    /* eslint-disable react/no-danger */
    // bca-disable-line
    return <i style={{width: 14, height: 14}} dangerouslySetInnerHTML={{__html: icon.svg}} />;
    /* eslint-enable react/no-danger */
}

const Header = styled.div`
    display: grid;
    grid-template-columns: auto 1fr;
    grid-auto-flow: column;
    grid-auto-columns: auto;
    gap: 8px;
    align-items: center;
    justify-content: space-between;
    height: 32px;
    padding: 0 12px;
    background-color: #343540;
`;

const Layout = styled.div`
    --source-text-color: #f8f8f3;
    --source-background-color: #000;

    margin: 0;
    font-size: 12px;
    background-color: var(--source-background-color);
    color: var(--source-text-color);
    border-radius: 4px;
    overflow: hidden;
`;

const SourceCode = styled(Source)`
    border: 1em solid transparent;
`;

function extractText(element: ElementContent | undefined) {
    if (!element) {
        return null;
    }

    if (element.type === 'text') {
        return element.value;
    }

    if (element.type === 'element') {
        return extractText(element.children.at(0));
    }

    return null;
}

const DEFAULT_ACCEPT_METHOD: AcceptMethod[] = [{method: 'copy'}];

interface CodeBlockContentProps {
    language?: string;
    code: string;
    closed: boolean;
    acceptMethods?: AcceptMethod[] | undefined;
}

function CodeBlockContent({language, code, closed, acceptMethods = DEFAULT_ACCEPT_METHOD}: CodeBlockContentProps) {
    const syntax = useMemo(
        () => {
            if (code && language && refractor.registered(language)) {
                const options: TokenizeOptions = {
                    highlight: source => refractor.highlight(source, language),
                };
                return tokenize(code, options);
            }

            return undefined;
        },
        [language, code]
    );

    if (!code) {
        return null;
    }

    if (!language) {
        return <pre>{code}</pre>;
    }

    return (
        <Layout>
            <Header>
                <LanguageTypeIcon language={language ?? ''} />
                <span>{language}</span>
                {closed && acceptMethods.map(v => <CopyCode key={v.method} method={v} text={code} />)}
            </Header>
            <SourceCode className="code-block" source={code} syntax={syntax} />
        </Layout>
    );
}

interface Props {
    node: ExtraProps['node'];
}

function CodeBlock({node}: Props) {
    const code = useMemo(
        () => (extractText(node)?.trim() ?? ''),
        [node]
    );
    const language = useMemo(
        () => {
            const child = node?.children.at(0);

            if (!child || child.type !== 'element') {
                return '';
            }

            return /language-(\w+)/.exec(child.properties.className?.toString() || '')?.[1];
        },
        [node]
    );

    return <CodeBlockContent closed code={code} language={language} />;
}

export default Object.assign(CodeBlock, {Content: CodeBlockContent});
