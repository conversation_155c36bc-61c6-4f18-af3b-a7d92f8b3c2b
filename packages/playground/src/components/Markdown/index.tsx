import remarkGfm from 'remark-gfm';
import {ComponentType} from 'react';
import ReactMarkdown from 'react-markdown';
import CodeBlock from './CodeBlock.js';

const remarkPlugins = [remarkGfm];

const components: Record<string, ComponentType<any>> = {
    pre: CodeBlock,
};

interface Props {
    content: string;
}

export default function Markdown({content}: Props) {
    return (
        <ReactMarkdown components={components} remarkPlugins={remarkPlugins}>
            {content}
        </ReactMarkdown>
    );
}
