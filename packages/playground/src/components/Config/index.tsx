import {Suspense, useCallback, useState} from 'react';
import {Button, Select, message} from 'antd';
import styled from '@emotion/styled';
import CodeMirror from '@uiw/react-codemirror';
import {json} from '@codemirror/lang-json';
import {useElementSize} from 'huse';
import {usePluginConfigState, usePluginList} from '@/atoms/info.js';

const languages = [json()];

const SubmitRow = styled.div`
    display: flex;
    justify-content: center;
`;

const SubmitButton = styled(Button)`
    width: 120px;
`;

interface PluginFormProps {
    pluginName: string;
}

function ConfigEditor({pluginName}: PluginFormProps) {
    const [config, setConfig] = usePluginConfigState(pluginName);
    const [code, setCode] = useState(() => JSON.stringify(config, null, 2));
    const [editorContainerRef, editorContainerSize] = useElementSize();
    const [loading, setLoading] = useState(false);
    const submit = useCallback(
        async () => {
            setLoading(true);
            try {
                await setConfig(JSON.parse(code));
                message.success('完成配置更新');
            }
            catch {
                message.error('更新配置失败');
            }
            finally {
                setLoading(false);
            }
        },
        [code, setConfig]
    );

    return (
        <>
            <div ref={editorContainerRef}>
                {/* @ts-expect-error 官方定义不适配ESM */}
                <CodeMirror
                    value={code}
                    extensions={languages}
                    width={`${editorContainerSize?.width ?? 0}px`}
                    height={`${editorContainerSize?.height ?? 0}px`}
                    onChange={setCode}
                />
            </div>
            <SubmitRow>
                <SubmitButton loading={loading} type="primary" onClick={submit}>提交</SubmitButton>
            </SubmitRow>
        </>
    );
}

const Layout = styled.div`
    display: grid;
    grid-template-rows: auto 1fr auto;
    gap: 12px;
    padding: 8px;
`;

export default function Config() {
    const plugins = usePluginList();
    const [plugin, setPlugin] = useState(plugins[0].name);

    return (
        <Layout>
            <Select
                style={{width: '100%'}}
                options={plugins.map(plugin => ({label: plugin.displayName, value: plugin.name}))}
                value={plugins.find(v => v.name === plugin)?.displayName}
                onChange={setPlugin}
            />
            <Suspense fallback={null}>
                <ConfigEditor key={plugin} pluginName={plugin} />
            </Suspense>
        </Layout>
    );
}
