import {Input} from 'antd';
import {useElementSize} from 'huse';
import styled from '@emotion/styled';
import {getIcon} from 'material-file-icons';
import CodeMirror, {Statistics} from '@uiw/react-codemirror';
import {cpp} from '@codemirror/lang-cpp';
import {html} from '@codemirror/lang-html';
import {java} from '@codemirror/lang-java';
import {javascript} from '@codemirror/lang-javascript';
import {json} from '@codemirror/lang-json';
import {lezer} from '@codemirror/lang-lezer';
import {markdown} from '@codemirror/lang-markdown';
import {php} from '@codemirror/lang-php';
import {python} from '@codemirror/lang-python';
import {rust} from '@codemirror/lang-rust';
import {sql} from '@codemirror/lang-sql';
import {xml} from '@codemirror/lang-xml';
import {less} from '@codemirror/lang-less';
import {sass} from '@codemirror/lang-sass';
import {useEditorCodeState, useEditorFileState, useUpdateEditorSelection} from '@/atoms/editor.js';

const languages = {
    cpp: [cpp()],
    html: [html()],
    java: [java()],
    javascript: [javascript({jsx: true, typescript: true})],
    json: [json()],
    lezer: [lezer()],
    markdown: [markdown()],
    php: [php()],
    python: [python()],
    rust: [rust()],
    sql: [sql()],
    xml: [xml()],
    less: [less()],
    sass: [sass()],
};

const Icon = styled.i`
    display: inline-flex;
    width: 16px;
    height: 16px;
`;

interface FileTypeIconProps {
    file: string;
}

function FileTypeIcon({file}: FileTypeIconProps) {
    const icon = getIcon(file);
    const props = {
        // eslint-disable-next-line no-useless-concat
        ['dangerously' + 'SetInnerHTML']: {__html: icon.svg},
    };

    return <Icon {...props} />;
}

const FileNameInput = styled(Input)`
    .ant-input-wrapper {
        .ant-input-group-addon {
            border-radius: 0;
            border: 0;
            padding: 0;
            width: 52px;
            border-right: 1px solid #ddd;
            background-color: #f5f5f5;
        }
    }
`;

const Layout = styled.div`
    display: grid;
    grid-template-rows: auto 1fr;
    height: 100%;
    overflow: hidden;

    .cm-gutters {
        width: 52px;
    }
`;

export default function Editor() {
    const [file, setFile] = useEditorFileState();
    const [code, setCode] = useEditorCodeState();
    const updateSelection = useUpdateEditorSelection();
    const [editorContainerRef, editorContainerSize] = useElementSize();

    return (
        <Layout>
            <FileNameInput
                bordered={false}
                addonBefore={<FileTypeIcon file={file} />}
                value={file}
                // @ts-ignore
                onChange={e => setFile(e.target.value)}
            />
            <div ref={editorContainerRef}>
                {/* @ts-expect-error 官方定义不适配ESM */}
                <CodeMirror
                    value={code}
                    height={`${editorContainerSize?.height ?? 0}px`}
                    extensions={languages.markdown}
                    onChange={setCode}
                    onStatistics={(v: Statistics) => updateSelection(v.selectionCode)}
                />
            </div>
        </Layout>
    );
}
