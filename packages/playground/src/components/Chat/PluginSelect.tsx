import {useCallback} from 'react';
import {PluginDescription} from '@comate/plugin-shared-internals/schema';
import styled from '@emotion/styled';
import {usePluginList} from '@/atoms/info.js';
import {useChatPluginState} from '@/atoms/chat.js';

interface SelectionProps {
    selected: boolean;
}

const PluginHeader = styled.div`
    display: flex;
    align-items: center;
    gap: 4px;
    height: 24px;
    color: #686c8d;
`;

const PluginIcon = styled.img`
    border-radius: 4px;
    overflow: hidden;
    width: 20px;
    height: 20px;
`;

const CapabilityTag = styled.span<SelectionProps>`
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 24px;
    padding: 0 8px;
    font-size: 14px;
    border-radius: 4px;
    background-color: ${props => (props.selected ? '#668ff8' : '#e2effd')};
    color: ${props => (props.selected ? '#fff' : '#000')};
    cursor: pointer;
    flex: none;

    &:hover {
        background-color: #668ff8;
        color: #fff;
    }
`;

const CapabilityList = styled.div`
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
`;

const PluginSection = styled.div`
    display: flex;
    flex-direction: column;
    gap: 8px;
`;

const Layout = styled.div`
    border-bottom: 1px solid #e9ebec;

    &:last-child {
        border-bottom: 0;
    }
`;

interface Props {
    onSelectFinish: () => void;
}

export default function PluginSelect({onSelectFinish}: Props) {
    const plugins = usePluginList();
    const [value, setValue] = useChatPluginState();
    const select = useCallback(
        (pluginName: string, capabilityName: string | null) => {
            setValue({pluginName, capabilityName});
            onSelectFinish();
        },
        [setValue, onSelectFinish]
    );
    const renderPluginSection = (info: PluginDescription) => {
        const renderCapability = (text: string, capability: string | null) => {
            const selected = value.pluginName === info.name && value.capabilityName === capability;

            return (
                <CapabilityTag
                    selected={selected}
                    onClick={() => select(info.name, capability)}
                >
                    {text}
                </CapabilityTag>
            );
        };
        const capabilities = info.capabilities.filter(v => v.type !== 'Fallback');

        return (
            <PluginSection>
                <PluginHeader>
                    <PluginIcon src={`/api/plugins/${info.name}/icon`} />
                    {info.displayName}
                </PluginHeader>
                <CapabilityList>
                    {renderCapability('无能力', null)}
                    {capabilities.map(v => renderCapability(v.displayName, v.name))}
                </CapabilityList>
            </PluginSection>
        );
    };

    return (
        <Layout>
            {plugins.map(renderPluginSection)}
        </Layout>
    );
}
