import {useCallback, useEffect, useRef} from 'react';
import styled from '@emotion/styled';
import {Config<PERSON><PERSON>ider, MESSAGE_PLUGIN_COMMAND, MessageData} from '@comate/plugin-jsx';
import '@comate/plugin-jsx/style.css';
import {MessageInfo, useChatMessageList, useSubmitCommand} from '@/atoms/chat.js';
import Message from './Message.js';

function renderMessage(message: MessageInfo, index: number, dataSource: MessageInfo[]) {
    return (
        <Message
            key={message.messageId}
            id={message.messageId}
            pluginName={message.pluginName}
            role={message.role}
            content={message.content}
            interactive={index === dataSource.length - 1}
        />
    );
}

const Layout = styled.div`
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 0 8px;
    overflow: auto;
`;

export default function MessageList() {
    const messages = useChatMessageList();
    const root = useRef<HTMLDivElement>(null);
    useEffect(
        () => {
            if (root.current) {
                root.current.scrollTo({top: root.current.scrollHeight, behavior: 'smooth'});
            }
        },
        [messages]
    );
    const submitCommand = useSubmitCommand();
    const handleMessage = useCallback(
        (message: MessageData) => {
            if (message.type === MESSAGE_PLUGIN_COMMAND) {
                const data = {
                    pluginName: message.pluginName,
                    taskId: message.taskId,
                    commandName: message.payload.commandName,
                    data: message.payload.data,
                    replyText: message.payload.replyText,
                };
                submitCommand(data);
            }
        },
        [submitCommand]
    );

    return (
        <ConfigProvider theme="light" onMessage={handleMessage}>
            <Layout className="comate-theme-light" ref={root}>
                {messages.map(renderMessage)}
            </Layout>
        </ConfigProvider>
    );
}
