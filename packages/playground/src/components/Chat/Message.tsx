import styled from '@emotion/styled';
import {Article} from '@comate/plugin-jsx';
import {MessageInfo} from '@/atoms/chat.js';

interface SideProps {
    side: 'left' | 'right';
}

const Content = styled(Article)<SideProps>`
    padding: 8px 20px;
    border-radius: 4px;
    max-width: 80%;
    word-wrap: normal;
    word-break: break-all;
    overflow-wrap: break-word;
    font-size: 14px;
    background-color: ${({side}) => (side === 'left' ? '#e6e6e6' : '#0cabf3')};
    color: ${({side}) => (side === 'left' ? '#000' : '#fff')};

    p {
        &:first-child {
            margin-top: 0;
        }

        &:last-child {
            margin-bottom: 0;
        }
    }

    code {
        font-family: monospace;
        padding: 0 .5em;
    }

    .code-block {
        margin: 0;
        border: 1em solid transparent;
        font-size: 12px;
        background-color: #000;
        color: #fff;
        border-radius: 4px;
        overflow-x: auto;
    }
`;

const Layout = styled.div<SideProps>`
    display: flex;
    justify-content: ${({side}) => (side === 'left' ? 'flex-start' : 'flex-end')};
    width: 100%;
    padding: 8px 0;
`;

interface Props {
    id: MessageInfo['messageId'];
    pluginName: MessageInfo['pluginName'];
    role: MessageInfo['role'];
    content: MessageInfo['content'];
    interactive: boolean;
}

export default function Message({id, pluginName, role, content, interactive}: Props) {
    return (
        <Layout side={role === 'user' ? 'right' : 'left'}>
            <Content
                side={role === 'user' ? 'right' : 'left'}
                taskId={id}
                pluginName={pluginName}
                content={content}
                interactive={interactive}
            />
        </Layout>
    );
}
