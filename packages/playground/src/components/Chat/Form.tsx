import {KeyboardEvent, useCallback, useState} from 'react';
import {Input, Popover} from 'antd';
import styled from '@emotion/styled';
import {usePluginList} from '@/atoms/info.js';
import {useChatPluginState, useChatTextState, useSubmitChat} from '@/atoms/chat.js';
import PluginSelect from './PluginSelect.js';

const PluginIcon = styled.img`
    width: 16px;
    height: 16px;
    overflow: hidden;
    border-radius: 4px;
`;

const SelectTrigger = styled.span`
    display: flex;
    align-items: center;
    gap: 4px;
    cursor: pointer;
`;

const Layout = styled.div`
    margin: 20px 8px 12px;
`;

export default function Form() {
    const plugins = usePluginList();
    const [open, setOpen] = useState(false);
    const [pending, setPending] = useState(false);
    const [plugin] = useChatPluginState();
    const [text, setText] = useChatTextState();
    const submitChat = useSubmitChat();
    const submit = useCallback(
        async (e: KeyboardEvent<HTMLInputElement>) => {
            if (e.code === 'Enter') {
                e.preventDefault();
                setPending(true);
                await submitChat();
                setPending(false);
            }
        },
        [submitChat]
    );
    const renderPluginInfo = () => {
        if (!plugin.pluginName) {
            return '选择插件';
        }

        const capability = plugin.capabilityName
            ? plugins.flatMap(v => v.capabilities).find(v => v.name === plugin.capabilityName)
            : null;
        return (
            <>
                {plugin.pluginName && <PluginIcon src={`/api/plugins/${plugin.pluginName}/icon`} />}
                {capability?.displayName}
            </>
        );
    };

    return (
        <Layout>
            <Input
                disabled={pending || !plugin.pluginName}
                value={text}
                addonBefore={
                    <Popover
                        open={open}
                        trigger="click"
                        placement="topLeft"
                        content={<PluginSelect onSelectFinish={() => setOpen(false)} />}
                        overlayStyle={{width: 400}}
                        onOpenChange={setOpen}
                    >
                        <SelectTrigger>
                            {renderPluginInfo()}
                        </SelectTrigger>
                    </Popover>
                }
                // @ts-ignore
                onChange={e => setText(e.target.value)}
                onKeyUp={submit}
            />
        </Layout>
    );
}
