import styled from '@emotion/styled';
import {Boundary} from 'react-suspense-boundary';
import Form from './Form.js';
import MessageList from './MessageList.js';

const Layout = styled.div`
    height: calc(100vh - var(--side-panel-header-height));
    display: grid;
    grid-auto-flow: row;
    grid-template-rows: 1fr auto;
`;

export default function Chat() {
    return (
        <Layout>
            <MessageList />
            <Boundary>
                <Form />
            </Boundary>
        </Layout>
    );
}
