import {Suspense} from 'react';
import {createForm} from '@formily/core';
import {FormProvider, Field} from '@formily/react';
import {FormItem, Input} from '@formily/antd-v5';
import {useUserState} from '@/atoms/user.js';
import FormLayout from '@/components/FormLayout/index.js';

const form = createForm();

function Content() {
    const [user, setUser] = useUserState();

    return (
        <FormProvider form={form}>
            <FormLayout>
                <FormLayout.Section title="用户信息">
                    <Field
                        required
                        name="name"
                        title="用户名"
                        initialValue={user.name}
                        decorator={[FormItem]}
                        component={[Input]}
                    />
                    <Field
                        required
                        name="displayName"
                        title="姓名"
                        initialValue={user.displayName}
                        decorator={[FormItem]}
                        component={[Input]}
                    />
                    <Field
                        required
                        name="email"
                        title="电子邮箱"
                        initialValue={user.email}
                        decorator={[FormItem]}
                        component={[Input]}
                    />
                </FormLayout.Section>
                <FormLayout.Submit onSubmit={setUser} />
            </FormLayout>
        </FormProvider>
    );
}

export default function User() {
    return (
        <Suspense fallback={null}>
            <Content />
        </Suspense>
    );
}
