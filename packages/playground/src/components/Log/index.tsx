import styled from '@emotion/styled';
import {IoCaretUp, IoCaretDown} from 'react-icons/io5';
import {LogPayload} from '@comate/plugin-shared-internals/schema';
import {JsonView} from '@otakustay/react-json-view';
import '@otakustay/react-json-view/style';
import {useSwitch} from 'huse';
import {useChatLogList} from '@/atoms/chat.js';

const COLOR_MAP: Record<LogPayload['level'], string> = {
    system: '#aaa',
    verbose: '#333',
    info: '#0072c6',
    warn: '#ff9800',
    error: '#f4362b',
};

const LineTitle = styled.div<{level: LogPayload['level']}>`
    display: flex;
    align-items: center;
    gap: 8px;
    height: 24px;
    font-size: 14px;
    color: ${props => COLOR_MAP[props.level]};
`;

const ToggleIcon = styled.i`
    display: flex;
    align-items: center;
    font-size: 16px;
    cursor: pointer;

    &:hover {
        color: #4676ee;
    }
`;

interface ToggleProps {
    expanded: boolean;
    onClick: () => void;
}

function Toggle({expanded, onClick}: ToggleProps) {
    return (
        <ToggleIcon>
            {/* @ts-ignore */}
            {expanded ? <IoCaretUp onClick={onClick} /> : <IoCaretDown onClick={onClick} />}
        </ToggleIcon>
    );
}

interface LineProps {
    data: LogPayload;
}

function Line({data}: LineProps) {
    const [expanded, expand, collapse] = useSwitch(false);
    const hasDetail = Object.keys(data.detail).length > 0;

    return (
        <>
            <LineTitle level={data.level}>
                [{data.source}] {data.action}
                {hasDetail && <Toggle expanded={expanded} onClick={expanded ? collapse : expand} />}
            </LineTitle>
            {expanded && <JsonView source={data.detail} />}
        </>
    );
}

const Layout = styled.div`
    padding: 0 8px;
    height: calc(100vh - var(--side-panel-header-height));
    overflow-y: auto;
`;

export default function Log() {
    const logs = useChatLogList();

    return (
        <Layout>
            {/* eslint-disable-next-line react/no-array-index-key */}
            {logs.map((v, i) => <Line key={i} data={v} />)}
        </Layout>
    );
}
