import {UserDetail, PluginDescription} from '@comate/plugin-shared-internals/schema';
import {request} from './request.js';

type PluginCapabilityType = 'Prompt' | 'Skill' | 'Fallback' | 'Scan';

export interface CapabilityDescription {
    name: string;
    type: PluginCapabilityType;
    displayName: string;
    description: string;
}

export default {
    async list() {
        const plugins: PluginDescription[] = await request('GET', '/plugins').then(r => r.json());
        return plugins;
    },
    async getConfig(name: string) {
        const config = await request('GET', `/plugins/${name}/config`).then(r => r.json());
        return config;
    },
    async updateConfig(name: string, config: any) {
        const updated = await request('PUT', `/plugins/${name}/config`, config).then(r => r.json());
        return updated;
    },
    async updateUser(user: UserDetail) {
        const updated = await request('PUT', '/user', user);
        return updated;
    },
};
