export async function request(method: string, url: string, body?: unknown) {
    const response = await fetch(
        '/api' + url,
        {
            method,
            headers: {
                'Content-Type': 'application/json',
            },
            body: body == null ? undefined : JSON.stringify(body),
        }
    );

    if (!response.ok) {
        const message = await response.text();
        throw new Error(`${response.status} ${message}`);
    }

    return response;
}
