import {
    ACTION_LOG,
    ACTION_CHAT_TASK_PROGRESS,
    LogPayload,
    TaskProgressPayload,
    CustomCommandPayload,
} from '@comate/plugin-shared-internals/schema';
import {request} from './request.js';

export interface LogData {
    action: typeof ACTION_LOG;
    payload: LogPayload;
}

export interface ProgressData {
    action: typeof ACTION_CHAT_TASK_PROGRESS;
    payload: TaskProgressPayload;
}

export type MessageData = LogData | ProgressData;

export interface ChatMessageRequest {
    messageId: string;
    pluginName: string;
    capabilityName: string | null;
    content: string;
    file: string;
    code: string;
    selectedCode: string;
}

// eslint-disable-next-line complexity
function languageFromFile(file: string) {
    if (file === 'docker-compose.yml') {
        return 'dockercompose';
    }

    const extension = file.split('.').pop()?.toLowerCase();
    switch (extension) {
        case 'abap':
            return 'abap';
        case 'bat':
            return 'bat';
        case 'bibtex':
            return 'bibtex';
        case 'clojure':
            return 'clojure';
        case 'coffeescript':
            return 'coffeescript';
        case 'c':
        case 'h':
            return 'c';
        case 'cpp':
            return 'cpp';
        case 'cs':
            return 'csharp';
        case 'css':
            return 'css';
        case 'cu':
            return 'cuda-cpp';
        case 'diff':
            return 'diff';
        case 'dockerfile':
            return 'dockerfile';
        case 'fs':
            return 'fsharp';
        case 'go':
            return 'go';
        case 'groovy':
            return 'groovy';
        case 'hbs':
            return 'handlebars';
        case 'haml':
            return 'haml';
        case 'html':
            return 'html';
        case 'ini':
            return 'ini';
        case 'java':
            return 'java';
        case 'js':
            return 'javascript';
        case 'jsx':
            return 'javascriptreact';
        case 'json':
            return 'json';
        case 'jsonc':
            return 'jsonc';
        case 'julia':
            return 'julia';
        case 'tex':
            return 'latex';
        case 'less':
            return 'less';
        case 'lua':
            return 'lua';
        case 'makefile':
            return 'makefile';
        case 'md':
            return 'markdown';
        case 'm':
            return 'objective-c';
        case 'mm':
            return 'objective-cpp';
        case 'pl':
            return 'perl';
        case 'php':
            return 'php';
        case 'ps1':
            return 'powershell';
        case 'pug':
            return 'pug';
        case 'py':
            return 'python';
        case 'r':
            return 'r';
        case 'cshtml':
            return 'razor';
        case 'rb':
            return 'ruby';
        case 'rs':
            return 'rust';
        case 'scss':
            return 'scss';
        case 'sass':
            return 'sass';
        case 'shader':
            return 'shaderlab';
        case 'sh':
            return 'shellscript';
        case 'slim':
            return 'slim';
        case 'sql':
            return 'sql';
        case 'styl':
            return 'stylus';
        case 'swift':
            return 'swift';
        case 'ts':
            return 'typescript';
        case 'tsx':
            return 'typescriptreact';
        case 'vb':
            return 'vb';
        case 'vue':
            return 'vue';
        case 'xml':
            return 'xml';
        case 'xsl':
            return 'xsl';
        case 'yaml':
        case 'yml':
            return 'yaml';
        default:
            return 'plaintext';
    }
}

async function consumeStream(response: Response, onData: (data: MessageData) => void) {
    if (!response.body) {
        throw new Error('No response body');
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder('utf-8');
    // eslint-disable-next-line no-constant-condition
    while (true) {
        const {value, done} = await reader.read();
        if (done) {
            break;
        }
        const lines = decoder.decode(value).trim().split('\n\n');
        for (const line of lines) {
            onData(JSON.parse(line.slice('data: '.length)));
        }
    }
}

export default {
    sendChat: async (payload: ChatMessageRequest, onData: (data: MessageData) => void) => {
        const body = {
            input: {
                messageId: payload.messageId,
                pluginName: payload.pluginName,
                capability: payload.capabilityName,
                query: payload.content,
            },
            context: {
                query: payload.content,
                selectedCode: payload.selectedCode,
                activeFileContent: payload.code,
                activeFilePath: payload.file,
                activeFileName: payload.file.split('/').pop(),
                activeFileLanguage: languageFromFile(payload.file),
            },
        };
        const response = await request('POST', '/chat', body);
        await consumeStream(response, onData);
    },
    sendCommand: async (payload: CustomCommandPayload, onData: (data: MessageData) => void) => {
        const body = {
            pluginName: payload.pluginName,
            taskId: payload.taskId,
            commandName: payload.commandName,
            data: payload.data,
        };
        const response = await request('POST', '/command', body);
        await consumeStream(response, onData);
    },
};
