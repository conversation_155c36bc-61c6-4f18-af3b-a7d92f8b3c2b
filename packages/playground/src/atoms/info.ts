import {atom, useAtom, useAtomValue} from 'jotai';
import {atomFamily} from 'jotai/utils';
import infoApi from '@/api/info.js';

const pluginList = atom(infoApi.list);

export function usePluginList() {
    return useAtomValue(pluginList);
}

function createConfigAtom(name: string) {
    return atom(
        () => infoApi.getConfig(name),
        (get, set, newConfig) => infoApi.updateConfig(name, newConfig)
    );
}

const configFamily = atomFamily(createConfigAtom);

export function usePluginConfigState(name: string) {
    return useAtom(configFamily(name));
}
