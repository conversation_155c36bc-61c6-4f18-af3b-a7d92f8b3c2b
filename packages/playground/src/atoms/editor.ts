import dedent from 'dedent';
import {atom, useAtom, useAtomValue, useSetAtom} from 'jotai';

const initialText = dedent`
    # 代码区

    你可以在上方文本框中输入一个文件名， 并在此处编写代码，在编辑器中的内容和选中区域都会提交至插件。

    以下是可以使用的文件后缀：

    - .abap
    - .bat
    - .bibtex
    - .clojure
    - .coffeescript
    - .c
    - .h
    - .cpp
    - .cs
    - .css
    - .cu
    - .diff
    - .dockerfile
    - .fs
    - .go
    - .groovy
    - .hbs
    - .haml
    - .html
    - .ini
    - .java
    - .js
    - .jsx
    - .json
    - .jsonc
    - .julia
    - .tex
    - .less
    - .lua
    - .makefile
    - .md
    - .m
    - .mm
    - .pl
    - .php
    - .ps1
    - .pug
    - .py
    - .r
    - .cshtml
    - .rb
    - .rs
    - .scss
    - .sass
    - .shader
    - .sh
    - .slim
    - .sql
    - .styl
    - .swift
    - .ts
    - .tsx
    - .vb
    - .vue
    - .xml
    - .xsl
    - .yaml
    - .yml
`;

const file = atom('README.md');

const code = atom(initialText);

const selection = atom('');

export function useEditorFileState() {
    return useAtom(file);
}

export function useEditorCodeState() {
    return useAtom(code);
}

export function useEditorSelection() {
    return useAtomValue(selection);
}

export function useUpdateEditorSelection() {
    return useSetAtom(selection);
}
