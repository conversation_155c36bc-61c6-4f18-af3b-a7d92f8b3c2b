import {useCallback} from 'react';
import {atom, useAtom, useAtomValue, useSet<PERSON>tom} from 'jotai';
import {
    ACTION_CHAT_TASK_PROGRESS,
    ChunkContent,
    CustomCommandPayload,
    LogPayload,
} from '@comate/plugin-shared-internals/schema';
import chatApi, {ChatMessageRequest} from '@/api/chat.js';
import {useEditorCodeState, useEditorFileState, useEditorSelection} from './editor.js';

export interface PluginSelectResult {
    pluginName: string | null;
    capabilityName: string | null;
}

export interface MessageInfo {
    messageId: string;
    pluginName: string;
    role: 'user' | 'assistant';
    content: ChunkContent;
}

const plugin = atom<PluginSelectResult>({pluginName: null, capabilityName: null});

const text = atom('');

const messages = atom<MessageInfo[]>([]);

const logs = atom<LogPayload[]>([]);

export function useChatPluginState() {
    return useAtom(plugin);
}

export function useChatTextState() {
    return useAtom(text);
}

export function useChatMessageList() {
    return useAtomValue(messages);
}

export function useChatLogList() {
    return useAtomValue(logs);
}

const PLACEHOLDER_TEXT = '正在调用插件处理您的请求...';

export function useSubmitChat() {
    const [file] = useEditorFileState();
    const [code] = useEditorCodeState();
    const selectedCode = useEditorSelection();
    const [plugin] = useChatPluginState();
    const [text, setText] = useChatTextState();
    const setMessages = useSetAtom(messages);
    const setLogs = useSetAtom(logs);
    const submit = useCallback(
        async () => {
            if (!plugin.pluginName) {
                return;
            }

            const messageContent = text.trim();
            const messageId = crypto.randomUUID();
            const conversation: MessageInfo[] = [
                {messageId: crypto.randomUUID(), role: 'user', content: [messageContent], pluginName: ''},
                {messageId, role: 'assistant', content: [PLACEHOLDER_TEXT], pluginName: plugin.pluginName},
            ];
            setMessages(v => [...v, ...conversation]);
            setLogs([]);
            setText('');

            const payload: ChatMessageRequest = {
                messageId,
                file,
                code,
                selectedCode,
                pluginName: plugin.pluginName,
                capabilityName: plugin.capabilityName,
                content: messageContent,
            };
            await chatApi.sendChat(
                payload,
                data => {
                    if (data.action === ACTION_CHAT_TASK_PROGRESS) {
                        const chunk = data.payload.chunk;
                        const content = typeof chunk === 'object' && 'content' in chunk ? chunk.content : chunk;
                        const updateAssistantMessage = (message: MessageInfo): MessageInfo => {
                            if (message.messageId === messageId) {
                                return {...message, content};
                            }
                            return message;
                        };
                        setMessages(v => v.map(updateAssistantMessage));
                    }
                    else if (data.action === 'LOG') {
                        setLogs(v => [...v, data.payload]);
                    }
                }
            );
        },
        [file, code, selectedCode, plugin, text, setText, setMessages, setLogs]
    );
    return submit;
}

export function useSubmitCommand() {
    const setMessages = useSetAtom(messages);
    const setLogs = useSetAtom(logs);
    const submit = useCallback(
        async (payload: CustomCommandPayload) => {
            const messageId = crypto.randomUUID();
            const conversation: MessageInfo[] = [
                {messageId: crypto.randomUUID(), role: 'user', content: [payload.replyText], pluginName: ''},
                {messageId, role: 'assistant', content: [PLACEHOLDER_TEXT], pluginName: payload.pluginName},
            ];
            setMessages(v => [...v, ...conversation]);
            setLogs([]);

            // TODO: 复用一下
            await chatApi.sendCommand(
                payload,
                data => {
                    if (data.action === ACTION_CHAT_TASK_PROGRESS) {
                        const chunk = data.payload.chunk;
                        const content = typeof chunk === 'object' && 'content' in chunk ? chunk.content : chunk;
                        const updateAssistantMessage = (message: MessageInfo): MessageInfo => {
                            if (message.messageId === messageId) {
                                return {...message, content};
                            }
                            return message;
                        };
                        setMessages(v => v.map(updateAssistantMessage));
                    }
                    else if (data.action === 'LOG') {
                        setLogs(v => [...v, data.payload]);
                    }
                }
            );
        },
        [setMessages, setLogs]
    );
    return submit;
}
