import {atom, useAtom} from 'jotai';
import {UserDetail} from '@comate/plugin-shared-internals/schema';
import infoApi from '@/api/info.js';

const user = atom(
    async (): Promise<UserDetail> => ({name: 'test', email: '<EMAIL>', displayName: 'test'}),
    async (get, set, payload: UserDetail) => {
        const updated = await infoApi.updateUser(payload);
        return updated;
    }
);

export function useUserState() {
    return useAtom(user);
}
