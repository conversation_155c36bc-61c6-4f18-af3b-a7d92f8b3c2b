[class*='comate-layout-flex'] {
    display: flex;
    align-items: stretch;
}

[class*='comate-layout-inline-flex'] {
    display: inline-flex;
    align-items: stretch;
}

.comate-layout-flex-vertical {
    flex-direction: column;

    > [class*='comate-layout-flex'],
    > [class*='comate-layout-inline-flex'] {
        width: 100%;
    }
}

.comate-layout-flex-vertical-start {
    flex-direction: column;
    align-items: flex-start;
}

.comate-layout-flex-no-wrap {
    flex-wrap: wrap;
}

.comate-layout-flex,
.comate-layout-inline-flex {
    &.gap-sm {
        gap: 2px;
    }

    &.gap-s {
        gap: 4px;
    }

    &.gap-m {
        gap: 8px;
    }

    &.gap-l {
        gap: 16px;
    }

    &.gap-xl {
        gap: 20px;
    }
}

.comate-layout-flex-center,
.comate-layout-inline-flex-center {
    align-items: center;
}

.comate-layout-flex-center-horizontal,
.comate-layout-inline-flex-center-horizontal {
    align-items: center;
    justify-content: center;
}

.comate-layout-flex-center-vertical,
.comate-layout-inline-flex-center-vertical {
    align-items: center;
    justify-content: center;
}

.comate-layout-flex-center-between,
.comate-layout-inline-flex-center-between {
    align-items: center;
    justify-content: space-between;
}

.comate-layout-flex-center-around,
.comate-layout-inline-flex-center-around {
    align-items: center;
    justify-content: space-around;
}

.comate-layout-flex-center-end,
.comate-layout-inline-flex-center-end {
    align-items: center;
    justify-content: flex-end;
}
