import crypto from 'node:crypto';
import {EventEmitter} from 'node:events';
import {
    ACTION_REQUEST_PERMISSION,
    ACTION_ASK_LLM,
    ACTION_CHAT_TASK_PROGRESS,
    ACTION_SESSION_FINISH,
    TaskProgressPayload,
    ACTION_CHAT_QUERY,
    ACTION_LOG,
    LogPayload,
    PluginDescription,
    ACTION_GET_PLUGIN_CONFIG,
    GetPluginConfigPayload,
    IdeSideConfigPayload,
    IdeSidePermissionPayload,
    IdeSideLlmPayload,
    IdeSideInformationPayload,
    InformationPayload,
    InformationQueryType,
    ACTION_INFORMATION_QUERY,
    getKnowledgeQueryResult,
    ScanQueryPayload,
    ACTION_SCAN_QUERY,
    ACTION_SCAN_TASK_PROGRESS,
    StartBackgroundServicePayload,
    ACTION_START_BACKGROUND_SERVICE,
    Execution,
    ACTION_ASK_LLM_STREAMING,
    CustomCommandPayload,
    ACTION_CUSTOM_COMMAND,
} from '@comate/plugin-shared-internals';
import {
    LlmType,
    ChatQueryPayload,
    LlmPayload,
    RequestPermissionPayload,
    UserDetail,
    ChannelImplement,
} from '@comate/plugin-shared-internals';
import {Response} from 'express';
import {ServerLlm} from './llm.js';
import {PluginConfigStore, SystemConfigStore} from './store.js';

interface MessageOf<A, P> {
    sessionId: string;
    execution: Execution;
    // `CHAT_QUERY`的`pluginName`在`payload.input`里面，但暂时不影响使用
    data: {action: A, payload: {pluginName: string} & P};
}

type RequestPermissionMessage = MessageOf<typeof ACTION_REQUEST_PERMISSION, RequestPermissionPayload>;

type GetPluginConfigMessage = MessageOf<typeof ACTION_GET_PLUGIN_CONFIG, GetPluginConfigPayload>;

type LlmMessage = MessageOf<typeof ACTION_ASK_LLM, LlmPayload>;

type LlmStreamingMessage = MessageOf<typeof ACTION_ASK_LLM_STREAMING, LlmPayload>;

type InformationQueryMessage = MessageOf<typeof ACTION_INFORMATION_QUERY, InformationPayload>;

type DrawChatMessage = MessageOf<typeof ACTION_CHAT_TASK_PROGRESS, TaskProgressPayload>;

type LogMessage = MessageOf<typeof ACTION_LOG, LogPayload>;

type ScanProgressMessage = MessageOf<typeof ACTION_SCAN_TASK_PROGRESS, TaskProgressPayload>;

type SessionFinishMessage = MessageOf<typeof ACTION_SESSION_FINISH, never>;

type ServerMessage =
    | RequestPermissionMessage
    | LlmMessage
    | LlmStreamingMessage
    | GetPluginConfigMessage
    | DrawChatMessage
    | ScanProgressMessage
    | LogMessage
    | SessionFinishMessage
    | InformationQueryMessage;

const llm = new ServerLlm();

interface Session {
    id: string;
    response: Response;
}

export class ServerImplement implements ChannelImplement {
    private readonly events = new EventEmitter();
    private readonly sessions: Session[] = [];
    private readonly systemConfigStore = new SystemConfigStore();
    private readonly pluginConfigStore;

    constructor(private readonly workspace: string, plugins: PluginDescription[]) {
        this.pluginConfigStore = new PluginConfigStore(plugins);
    }

    setUserDetail(userDetail: UserDetail) {
        this.systemConfigStore.set('userDetail', userDetail);
    }

    getPluginConfig(pluginName: string) {
        return this.pluginConfigStore.get(pluginName);
    }

    setPluginConfig(pluginName: string, config: Record<string, unknown>) {
        this.pluginConfigStore.set(pluginName, config);
    }

    startSession(response: Response) {
        const id = crypto.randomUUID();
        this.sessions.push({id, response});
    }

    endSession(response: Response) {
        const index = this.sessions.findIndex(v => v.response === response);
        if (index === -1) {
            this.sessions.splice(index, 1);
        }
    }

    findSessionId(response: Response) {
        const session = this.sessions.find(v => v.response === response);
        return session?.id;
    }

    chat(sessionId: string, payload: Omit<ChatQueryPayload, 'systemInfo' | 'pluginName'>) {
        const messagePayload: ChatQueryPayload = {
            ...payload,
            pluginName: payload.input.pluginName,
            systemInfo: {
                cwd: this.workspace,
                userId: this.systemConfigStore.get('userId'),
                userDetail: this.systemConfigStore.get('userDetail'),
            },
        };
        this.events.emit(
            'message',
            {
                sessionId: sessionId,
                data: {
                    action: ACTION_CHAT_QUERY,
                    payload: messagePayload,
                },
            }
        );
    }

    command(taskId: string, payload: CustomCommandPayload) {
        this.events.emit(
            'message',
            {
                sessionId: taskId,
                data: {
                    action: ACTION_CUSTOM_COMMAND,
                    payload,
                },
            }
        );
    }

    scan(scanId: string, payload: Omit<ScanQueryPayload, 'systemInfo'>) {
        const scanPayload: ScanQueryPayload = {
            ...payload,
            systemInfo: {
                cwd: this.workspace,
                userId: this.systemConfigStore.get('userId'),
                userDetail: this.systemConfigStore.get('userDetail'),
            },
        };
        this.events.emit(
            'message',
            {
                sessionId: scanId,
                data: {
                    action: ACTION_SCAN_QUERY,
                    payload: scanPayload,
                },
            }
        );
    }

    startBackgroundService(pluginName: string) {
        const sessionId = crypto.randomUUID();
        const backgroundServicePayload: StartBackgroundServicePayload = {
            pluginName,
            systemInfo: {
                cwd: this.workspace,
                userId: this.systemConfigStore.get('userId'),
                userDetail: this.systemConfigStore.get('userDetail'),
            },
        };
        this.events.emit(
            'message',
            {
                sessionId,
                data: {
                    action: ACTION_START_BACKGROUND_SERVICE,
                    payload: backgroundServicePayload,
                },
            }
        );
    }

    on(eventName: 'message', listener: NodeJS.MessageListener) {
        this.events.on(eventName, listener);
    }

    /**
     * 虽然名字叫`send`，但其实是处理插件发来的消息的
     *
     * @param message 插件发过来的消息
     */
    async send(message: ServerMessage) {
        switch (message.data.action) {
            case ACTION_REQUEST_PERMISSION:
                this.grantPermission(message, message.data);
                break;
            case ACTION_ASK_LLM:
                await this.callLlm(message, message.data);
                break;
            case ACTION_ASK_LLM_STREAMING:
                await this.callLlmStreaming(message, message.data);
                break;
            case ACTION_GET_PLUGIN_CONFIG:
                this.returnPluginConfig(message, message.data);
                break;
            case ACTION_CHAT_TASK_PROGRESS:
            case ACTION_SCAN_TASK_PROGRESS:
            case ACTION_LOG:
                this.forwardToResponse(message, message.data);
                break;
            case ACTION_SESSION_FINISH:
                this.finishResponse(message);
                break;
            case ACTION_INFORMATION_QUERY:
                await this.getKnowledgeQueryResult(message, message.data);
                break;
        }
    }

    private async callLlm(message: ServerMessage, data: LlmMessage['data']) {
        if (data.payload.type === LlmType.Text) {
            const result = await llm.askForText(data.payload);
            this.respond<IdeSideLlmPayload>(
                message,
                ACTION_ASK_LLM,
                {
                    pluginName: data.payload.pluginName,
                    type: LlmType.Text,
                    result,
                }
            );
        }
        else if (data.payload.type === LlmType.Function) {
            const result = await llm.askForFunction(data.payload);
            this.respond<IdeSideLlmPayload>(
                message,
                ACTION_ASK_LLM,
                {
                    pluginName: data.payload.pluginName,
                    type: LlmType.Function,
                    result,
                }
            );
        }
        else {
            // TODO: 其它几种LLM调用没实现
            this.respond(
                message,
                ACTION_ASK_LLM,
                {
                    type: data.payload.type,
                    result: 'Not implemented',
                }
            );
        }
    }

    private async callLlmStreaming(message: ServerMessage, data: LlmStreamingMessage['data']) {
        if (data.payload.type !== LlmType.Text) {
            throw new Error('Streaming only support text model.');
        }

        const stream = await llm.askForTextStreaming(data.payload);
        const reader = stream.getReader();
        const responseMessage: ServerMessage = {
            ...message,
            execution: {
                ...message.execution,
                segmentOrder: -1,
                done: false,
            },
        };
        // eslint-disable-next-line no-constant-condition
        while (true) {
            const {value, done} = await reader.read();
            responseMessage.execution.segmentOrder++;
            responseMessage.execution.done = done;
            const text: string | undefined = value?.type === 'event' ? JSON.parse(value.data).result : undefined;
            this.respond(
                responseMessage,
                ACTION_ASK_LLM_STREAMING,
                {
                    pluginName: data.payload.pluginName,
                    type: LlmType.Text,
                    result: text,
                }
            );

            if (done) {
                break;
            }
        }
    }

    private async getKnowledgeQueryResult(message: ServerMessage, data: InformationQueryMessage['data']) {
        const res = await getKnowledgeQueryResult(
            process.env.COMATE_LICENSE || process.env.COMATE_USERNAME || 'tianyanbo',
            data.payload.query,
            InformationQueryType.Text,
            [],
            data.payload?.options
        );

        this.respond<IdeSideInformationPayload>(
            message,
            ACTION_INFORMATION_QUERY,
            {
                pluginName: data.payload.pluginName,
                result: res.chunks.map(v => v.content),
            }
        );
    }

    private grantPermission(message: ServerMessage, data: RequestPermissionMessage['data']) {
        this.respond<IdeSidePermissionPayload>(
            message,
            ACTION_REQUEST_PERMISSION,
            {
                pluginName: data.payload.pluginName,
                type: data.payload.type,
                granted: true,
            }
        );
    }

    private returnPluginConfig(message: ServerMessage, data: GetPluginConfigMessage['data']) {
        const config = this.getPluginConfig(data.payload.pluginName);
        const value = config[data.payload.key];
        this.respond<IdeSideConfigPayload>(
            message,
            ACTION_GET_PLUGIN_CONFIG,
            {
                pluginName: data.payload.pluginName,
                key: data.payload.key,
                value,
            }
        );
    }

    private forwardToResponse(message: ServerMessage, data: any) {
        const session = this.sessions.find(v => v.id === message.sessionId);

        if (!session) {
            return;
        }

        session.response.write(`data: ${JSON.stringify(data)}\n\n`);
    }

    private finishResponse(message: ServerMessage) {
        const session = this.sessions.find(v => v.id === message.sessionId);
        session?.response.end();
    }

    private respond<T>(input: ServerMessage, action: string, payload: T) {
        this.events.emit(
            'message',
            {
                sessionId: input.sessionId,
                execution: input.execution,
                data: {
                    action,
                    payload: {
                        pluginName: input.data.payload.pluginName,
                        ...payload,
                    },
                },
            }
        );
    }
}
