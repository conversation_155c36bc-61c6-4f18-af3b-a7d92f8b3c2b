import path from 'node:path';
import {Application} from 'express';
import {EngineInit, PluginEngine} from '@comate/plugin-engine';
import {readPluginDescription} from '@comate/plugin-shared-internals';
import {ServerImplement} from './implement.js';
import {SingleProcessChannelGroupdManager} from './group.js';

export interface PluginLocation {
    name: string;
    directory: string;
}

interface Options {
    plugins: PluginLocation[];
    workspace?: string;
    base?: string;
}

export async function setupEngineApp(app: Application, {plugins, workspace = process.cwd(), base = ''}: Options) {
    process.env['COMATE_ENGINE_APP_NAME'] = 'Playground';
    process.env['COMATE_ENGINE_DEBUG'] = 'true';
    process.env['COMATE_ENGINE_PLATFORM'] = 'baidu-int';
    process.env['COMATE_VERSION'] = '1.0.0-beta';

    const descriptions = await Promise.all(plugins.map(v => readPluginDescription(v.directory)));
    const implement = new ServerImplement(workspace, descriptions);
    const init: EngineInit = {
        channelGroupManager: new SingleProcessChannelGroupdManager(),
        pluginDirectories: plugins.map(v => v.directory),
        channel: implement,
        // onPluginSetup: pluginName => implement.startBackgroundService(pluginName),
    };
    const engine = new PluginEngine(init);
    await engine.start();

    app.use(
        '*',
        (req, res, next) => {
            implement.startSession(res);
            res.on('close', () => implement.endSession(res));
            next();
        }
    );
    app.get(
        `${base}/plugins`,
        async (req, res) => {
            const info = await Promise.all(plugins.map(v => readPluginDescription(v.directory)));
            res.json(info);
        }
    );
    app.get(
        `${base}/plugins/:name/icon`,
        async (req, res) => {
            const info = plugins.find(v => v.name === req.params.name);
            if (info) {
                const description = await readPluginDescription(info.directory);
                const extension = path.extname(description.icon);
                res.contentType(extension).sendFile(path.resolve(info.directory, description.icon));
            }
            else {
                res.status(404).end('Not Found');
            }
        }
    );
    app.post(
        `${base}/chat`,
        (req, res) => {
            if (!req.body) {
                res.status(400).end('Bad Request');
                return;
            }

            const sessionId = implement.findSessionId(res);

            if (!sessionId) {
                res.status(403).end('Forbidden');
                return;
            }

            res.setHeader('Content-Type', 'text/event-stream');
            res.setHeader('Cache-Control', 'no-cache, no-transform');
            res.setHeader('Connection', 'keep-alive');
            res.writeHead(200);
            implement.chat(sessionId, {pluginName: req.body.input.pluginName, ...req.body});
        }
    );
    app.post(
        `${base}/command`,
        (req, res) => {
            if (!req.body) {
                res.status(400).end('Bad Request');
                return;
            }

            const sessionId = implement.findSessionId(res);

            if (!sessionId) {
                res.status(403).end('Forbidden');
                return;
            }

            res.setHeader('Content-Type', 'text/event-stream');
            res.setHeader('Cache-Control', 'no-cache, no-transform');
            res.setHeader('Connection', 'keep-alive');
            res.writeHead(200);
            implement.command(sessionId, req.body);
        }
    );
    app.post(
        `${base}/scan`,
        (req, res) => {
            if (!req.body) {
                res.status(400).end('Bad Request');
                return;
            }

            const sessionId = implement.findSessionId(res);

            if (!sessionId) {
                res.status(403).end('Forbidden');
                return;
            }

            res.setHeader('Content-Type', 'text/event-stream');
            res.setHeader('Cache-Control', 'no-cache, no-transform');
            res.setHeader('Connection', 'keep-alive');
            res.writeHead(200);
            implement.scan(sessionId, req.body);
        }
    );
    app.put(
        `${base}/user`,
        (req, res) => {
            implement.setUserDetail(req.body);
            res.json(req.body);
        }
    );
    app.get(
        `${base}/plugins/:name/config`,
        (req, res) => {
            const value = implement.getPluginConfig(req.params.name);
            if (value) {
                res.json(value);
            }
            else {
                res.status(404).end('Not Found');
            }
        }
    );
    app.put(
        `${base}/plugins/:name/config`,
        (req, res) => {
            implement.setPluginConfig(req.params.name, req.body);
            res.json(req.body);
        }
    );

    return engine;
}
