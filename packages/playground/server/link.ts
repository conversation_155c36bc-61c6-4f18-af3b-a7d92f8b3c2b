import path from 'node:path';
import {globby} from 'globby';
import {readPluginDescription} from '@comate/plugin-shared-internals';

/**
 * 服务启动时把当前项目中的插件都链接到`plugins`目录下，前端可以直接调用到
 */
export async function preparePluginLink() {
    const files = await globby('../../plugins/*/package.json', {absolute: true});
    const directories = files.map(v => path.dirname(v));

    const result: Array<{name: string, directory: string}> = [];
    for (const directory of directories) {
        const description = await readPluginDescription(directory);
        result.push({directory, name: description.name});
    }

    return result;
}
