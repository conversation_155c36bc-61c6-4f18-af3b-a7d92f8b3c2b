import path from 'node:path';
import crypto from 'node:crypto';
import os from 'node:os';
import {PluginConfig, PluginDescription, UserDetail} from '@comate/plugin-shared-internals';
import Conf, {Schema, Options} from 'conf';

// 因为 iapi 之前类型写错，跑不起来的话尝试删除一次tmp文件
const storeLocation = path.join(os.tmpdir(), 'comate-engine', 'playground');

interface SystemConfig {
    userId: string;
    userDetail: UserDetail;
}

function serializeConfig(value: any) {
    return JSON.stringify(value, null, 2);
}

function configSchemaToDefault(schema: PluginConfig) {
    const config: Record<string, any> = {};
    for (const section of schema.sections) {
        for (const [name, schema] of Object.entries(section.properties)) {
            const defaultValue = schema.type === 'array' ? [] : schema.default;
            config[name] = defaultValue;
        }
    }
    return config;
}

function configSchemaToStoreSchema(schema: PluginConfig) {
    const properties: Record<string, any> = {};
    const required: string[] = [];
    for (const section of schema.sections) {
        Object.assign(properties, section.properties);
        required.push(...section.required ?? []);
    }

    return {type: 'object', properties, required};
}

export class SystemConfigStore extends Conf<SystemConfig> {
    constructor() {
        const options: Options<SystemConfig> = {
            schema: {
                userId: {type: 'string'},
                userDetail: {
                    type: 'object',
                    properties: {
                        name: {type: 'string'},
                        displayName: {type: 'string'},
                        email: {type: 'string'},
                    },
                    required: ['name', 'displayName', 'email'],
                },
            },
            defaults: {
                userId: crypto.randomUUID(),
                userDetail: {
                    name: 'test',
                    displayName: '测试用户',
                    email: '<EMAIL>',
                },
            },
            cwd: storeLocation,
            configName: 'system',
            serialize: serializeConfig,
        };
        super(options);
    }
}

export class PluginConfigStore extends Conf<Record<string, Record<string, unknown>>> {
    constructor(plugins: PluginDescription[]) {
        const defaults: Record<string, Record<string, unknown>> = {};
        const schema: Schema<Record<string, Record<string, unknown>>> = {};
        for (const description of plugins) {
            defaults[description.name] = configSchemaToDefault(description.configSchema);
            schema[description.name] = configSchemaToStoreSchema(description.configSchema);
        }

        const options: Options<Record<string, Record<string, unknown>>> = {
            schema,
            defaults,
            cwd: storeLocation,
            configName: 'plugin',
            serialize: serializeConfig,
        };

        super(options);
    }
}
