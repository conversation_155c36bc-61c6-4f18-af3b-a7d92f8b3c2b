import {EventSourceParserStream} from 'eventsource-parser/stream';
import {FunctionDefinition, FunctionModel, LlmPayloadTypes, LlmType, TextModel} from '@comate/plugin-shared-internals';
import {ParsedEvent} from 'eventsource-parser';

interface ChatMessage {
    role: 'user' | 'assistant';
    content: string;
}

interface FunctionCall {
    name: string;
    arguments: string;
    thoughts?: string;
}

interface ErnieBotResponse {
    id: string;
    result: string;
    function_call?: FunctionCall;
}

async function fetchErnieBotAccessToken() {
    const params = new URLSearchParams();
    params.set('grant_type', 'client_credentials');
    params.set('client_id', process.env.ERNIE_AK ?? '');
    params.set('client_secret', process.env.ERNIE_SK ?? '');

    const response = await fetch(`https://aip.baidubce.com/oauth/2.0/token?${params}`);
    const data = await response.json();
    return data.access_token;
}

interface ErnieBotRequest {
    messages: ChatMessage[];
    functions?: FunctionDefinition[];
    model?: TextModel | FunctionModel;
}

const MODEL_URLS: Record<TextModel | FunctionModel, string> = {
    [TextModel.Default]: 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions',
    [TextModel.ErnieBot]: 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions',
    [TextModel.ErnieBot128]: 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/ernie-3.5-128k',
    [TextModel.ErnieBot4]: 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions_pro',
    [TextModel.ErnieBot4Turbo]: 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/ernie-4.0-turbo-8k',
    [TextModel.ErnieBot4Turbo128]:
        'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/ernie-4.0-turbo-128k',
};

async function callErnieBot({messages, functions, model}: ErnieBotRequest): Promise<ErnieBotResponse> {
    const accessToken = await fetchErnieBotAccessToken();
    const body = {
        messages,
        functions,
    };
    const response = await fetch(
        `${MODEL_URLS[model ?? TextModel.ErnieBot]}?access_token=${accessToken}`,
        {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(body),
        }
    );
    return response.json();
}

async function callErnieBotStreaming(request: ErnieBotRequest): Promise<ReadableStream<ParsedEvent>> {
    const {messages, functions, model} = request;
    const accessToken = await fetchErnieBotAccessToken();
    const body = {
        stream: true,
        messages,
        functions,
    };
    const response = await fetch(
        `${MODEL_URLS[model ?? TextModel.ErnieBot]}?access_token=${accessToken}`,
        {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(body),
        }
    );

    if (!response.body) {
        throw new Error('Unable to read response body streaming ErnieBot');
    }

    return response.body.pipeThrough(new TextDecoderStream()).pipeThrough(new EventSourceParserStream());
}

function formatPrompt(promptTemplate: string, args: Record<string, any> | undefined) {
    if (args) {
        return promptTemplate.replaceAll(
            /\{\{(\w+)\}\}/g,
            (match, key) => {
                const replacement = args[key];
                return replacement === undefined ? match : String(replacement);
            }
        );
    }

    return promptTemplate;
}

export class ServerLlm {
    async askForText(payload: LlmPayloadTypes[LlmType.Text]) {
        const prompt = formatPrompt(payload.promptTemplate, payload.args);
        // TODO: 修改为调用Comate后端服务
        const messages: ChatMessage[] = [
            {role: 'user', content: prompt},
        ];
        const response = await callErnieBot({messages, model: payload.model});
        return response.result;
    }

    async askForFunction(payload: LlmPayloadTypes[LlmType.Function]) {
        // TODO: 需要Comate后端来提供能力
        const prompt = formatPrompt(payload.promptTemplate, payload.args);
        const messages: ChatMessage[] = [
            {role: 'user', content: prompt},
        ];
        const response = await callErnieBot({messages, functions: payload.functions, model: payload.model});
        return response.function_call ?? null;
    }

    async askForTextStreaming(payload: LlmPayloadTypes[LlmType.Text]) {
        const prompt = formatPrompt(payload.promptTemplate, payload.args);
        // TODO: 修改为调用Comate后端服务
        const messages: ChatMessage[] = [
            {role: 'user', content: prompt},
        ];
        const stream = await callErnieBotStreaming({messages, model: payload.model});
        return stream;
    }
}
