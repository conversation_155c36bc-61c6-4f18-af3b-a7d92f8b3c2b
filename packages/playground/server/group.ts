import {ChildProcess} from 'node:child_process';
import {
    ChannelGroupManager,
    PluginSetupContext,
    PluginChannel,
    PluginGroup,
    forkPluginProcess,
    invokePluginSetupToProcess,
} from '@comate/plugin-engine';

export class SingleProcessChannelGroupdManager extends ChannelGroupManager {
    private process: ChildProcess | null = null;

    protected getGroupKey() {
        return 'playground';
    }

    protected createGroupValue(groupKey: string) {
        const child = forkPluginProcess(
            groupKey,
            {
                onPluginSetup: pluginName => {
                    const group = this.groups.get(groupKey);

                    if (!group) {
                        return;
                    }

                    const plugin = group.plugins.get(pluginName);
                    plugin?.onSetup?.(group.value.channel);
                },
            }
        );
        const channel = new PluginChannel(child);
        child.stdout?.pipe(process.stdout);
        child.stderr?.pipe(process.stderr);
        this.process = child;
        channel.start();
        return {channel};
    }

    protected destroyGroup() {
    }

    protected async onPluginJoinGroup(group: PluginGroup, setupContext: PluginSetupContext) {
        if (this.process) {
            await invokePluginSetupToProcess(
                this.process,
                {name: setupContext.description.name, directory: setupContext.directory}
            );
        }
    }

    protected async onPluginExitGroup() {
        return Promise.resolve();
    }
}
