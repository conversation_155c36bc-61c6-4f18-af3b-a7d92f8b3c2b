import resolve from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import terser from '@rollup/plugin-terser';

const config = [{
    input: 'dist/fallbackServer.js', // 这里替换为你程序的入口文件
    output: {
        file: 'dist/fallbackServer.js',
    },
    plugins: [
        commonjs(),
        resolve({
            preferBuiltins: true, // 告诉 Rollup 使用 Node 的内置模块
        }),
        terser(),
    ],
}];

export default config;
