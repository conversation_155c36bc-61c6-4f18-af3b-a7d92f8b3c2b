import {createAxiosInstance} from '@comate/plugin-shared-internals';


export interface AxiosResponse<T = any> {
    data: T;
    status: number;
    statusText: string;
    request?: any;
}

export interface ResponseDataType<T> {
    data: T;
    code: number;
    msg: string;
}
interface ParamsInferCardScoreByCommittedFiles {
    titles: string[];
    diff: string;
}

interface ResultsInferCardScoreByCommittedFiles {
    result: {
        distance: number[];
        rank: number[];
    };
    // eslint-disable-next-line camelcase
    ret_code: number;
}
const axiosInstance = createAxiosInstance();

export const apiInferCardScoreByCommittedDiff = async (params: ParamsInferCardScoreByCommittedFiles) => {
    try {
        const res = await axiosInstance.post<typeof params, AxiosResponse<ResultsInferCardScoreByCommittedFiles>>(
            'http://10.27.142.62:8264/wenxin/inference/rank',
            params,
            {timeout: 2 * 1000}
        );
        if (res.data.ret_code !== 200) {
            throw new Error(`status code: ${res.data.ret_code}`);
        }
        return res.data;
    }
    catch (e) {
        throw e;
    }
};

//  右下角弹框=1，sourceControl=2
type Source = '1' | '2';

interface SpaceParams {
    task_engine_userId: string;
    hisMsg?: string;
}

export type SpaceDetail = Array<Record<string, string[]>>;

interface SpaceResponseData {
    code: string;
    spaceDetails: SpaceDetail;
    hisMsg?: string;
}

export const getSpaceDetailByHistory = async (params: SpaceParams): Promise<SpaceResponseData> => {
    try {
        const res: AxiosResponse<SpaceResponseData> = await axiosInstance.post(
            ' http://uflow.baidu-int.com/workflow/api/flow/v1/trigger-webhook/5cbfb9c0e58e4fc58b9abf0e066131d6',
            params,
            {
                headers: {
                    'Content-Type': 'application/json',
                },
                timeout: 20 * 1000,
            }
        );

        if (res.status !== 200) {
            throw new Error(`status code: ${res.status}`);
        }
        return res.data;
    }
    catch (e) {
        throw e;
    }
};

interface Params {
    query: string;
    task_engine_userId: string;
    codeModule?: string;
    spaceDetails?: SpaceDetail;
    hisMsg?: string;
    codePath?: string;
    source?: Source;
    cmdType?: string;
    from?: 'vscode' | 'jetbrains';
}
interface Card {
    cardIdAndTile: string;
    cardDetailUrl: string;
    cardType: string;
    cardTypeStyle: string;
    cardStatusStyle: string;
    cardStatus: string;
    cardPlan: string;
}

interface ResponseData {
    content: Card[];
    contentByHisMsg: Card[];
}


export const getIssuesByQuery = async (params: Params): Promise<ResponseData> => {
    try {
        const res: AxiosResponse<ResponseDataType<ResponseData>> = await axiosInstance.post(
            'http://uflow.baidu-int.com/workflow/api/flow/v1/trigger-webhook/8891cb6b6345413f90ae96b85149d386',
            params,
            {
                headers: {
                    'Content-Type': 'application/json',
                },
                timeout: 20 * 1000,
            }
        );

        if (res.status !== 200 || res.data.code !== 200) {
            throw new Error(`status code: ${res.status}`);
        }
        return res.data.data;
    }
    catch (e) {
        throw e;
    }
};
