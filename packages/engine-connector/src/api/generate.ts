import qs from 'qs';
import {axiosInstance} from '@comate/plugin-shared-internals';
import {MultiAcceptData} from '../service/Track/api.js';

export type Model = 'COMPLETION_AGENT' | 'UNIT_TEST_AGENT' | 'DEBUG_AGENT' | 'SECURITY_AGENT' | 'FULL_STACK_AGENT';

export interface CreateUUIDParams {
    ide: string;
    username: string;
    repo: string;
    branch?: string;
    path: string;
    uuid?: string;
    content: string;
    function?: string;
    model?: Model;
    /** license */
    key?: string;
    /** 设备的数字签名 */
    device?: string;
    /** 用于多文件，支持采纳放弃的场景（智能体） */
    multiSuggestions?: {fileContent: MultiAcceptData[]};
}

interface CreateUUIDResponse {
    status: string;
    message?: string;
    data: {
        uuid: string;
    };
}

// 通过推荐内容接口获取 uuid
export function generate({
    ide,
    username,
    repo,
    branch,
    path,
    content,
    function: modelFunction,
    key,
    device,
    model,
    uuid,
    multiSuggestions,
}: CreateUUIDParams) {
    const data = {
        ide,
        username,
        repo,
        branch,
        path,
        content,
        generatedContent: content,
        row: '1',
        col: '1',
        pluginVersion: '1.0.0',
        model,
        function: modelFunction,
        key,
        device,
        uuid,
        multiSuggestions,
    } as any;
    if (data.multiSuggestions) {
        data.multiSuggestions = JSON.stringify(data.multiSuggestions);
    }
    const query = qs.stringify(data);
    return axiosInstance<CreateUUIDResponse>('/api/generate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        data: query,
    })
        .then(res => res.data.data?.uuid)
        .catch(() => {
            throw new Error('generate uuid error');
        });
}
