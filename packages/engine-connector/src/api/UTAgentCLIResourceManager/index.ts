import {basename, join} from 'node:path';
import * as fs from 'node:fs';
import https from 'node:https';
import {access, unlink} from 'node:fs/promises';
import {
    type TypeFSE,
    readJson,
    writeJSON,
    remove,
    ensureDirectoryExist,
} from '@comate/plugin-shared-internals';
import {compare, satisfies, compareVersions} from 'compare-versions';
import {createAxiosInstance} from '@comate/plugin-shared-internals';
import {isFileExist} from './utils.js';
import {RESOURCES_MAPPING, VersionConfig} from './constants.js';
import {getResourceConfigPath, getResourceStorageFolder} from './storage.js';
import {extractTarGz, getOsPlatformAndArch, verifyFileHash} from './utils.js';
import {LoggerRoleInstance} from '../../service/Logger.js';

interface LocalConfig {
    path: string;
    version?: string;
    sha256?: string;
    obsoleteVersions?: string[];
}

type Sha256Mapping = Record<string, string | Record<string, string>>;

interface VersionItem {
    version: string;
    releaseDate: string;
    sha256: Sha256Mapping;
    compatibility?: {
        vscode: string;
    };
    deprecated?: boolean;
}

interface RemoteVersionInfo {
    name: string;
    description?: string;
    availability?: string;
    versions: VersionItem[];
}

const axiosInstance = createAxiosInstance();

function download(url: string, destinationFolder: string, filename?: string): Promise<string> {
    return new Promise(async (resolve, reject) => {
        try {
            await ensureDirectoryExist(destinationFolder);
        }
        catch (e) {
            reject(e);
            return;
        }
        const resolvedFilename = filename || url.split('/').pop() || `download_${Date.now()}`;
        const destination = join(destinationFolder, resolvedFilename);
        const file = fs.createWriteStream(destination);

        const request = https.get(url, response => {
            if (response.statusCode !== 200) {
                reject(new Error('Server responded with status code ' + response.statusCode));
            }
            response.pipe(file);
        });

        file.on('finish', () => {
            file.close();
            resolve(destination);
        });

        file.on('error', err => {
            fs.unlink(destination, () => reject(err));
        });

        request.on('error', err => {
            fs.unlink(destination, () => reject(err));
        });
    });
}

async function readLocalConfigJson(
    name: string,
    storagePath: string,
    consumer: ResourceConsumer
): Promise<Partial<LocalConfig> | undefined> {
    const consumerName = consumer.name;
    const resourceRoot = join(storagePath, name);
    try {
        const resourceConfigPath = getResourceConfigPath(resourceRoot);
        const resourceConfig = await readJson(resourceConfigPath);
        return resourceConfig[consumerName] as Partial<LocalConfig>;
    }
    catch (e) {
        return undefined;
    }
}

async function updateLocalConfig(
    storageFolder: string,
    consumer: ResourceConsumer,
    localConfig: LocalConfig,
    logger: LoggerRoleInstance
) {
    const consumerName = consumer.name;
    const resourceConfigPath = getResourceConfigPath(storageFolder);
    try {
        const isExist = await isFileExist(resourceConfigPath);
        if (isExist) {
            const data = await readJson(resourceConfigPath);
            data[consumerName] = localConfig;
            await writeJSON(resourceConfigPath, data);
        }
        else {
            await writeJSON(resourceConfigPath, {[consumerName]: localConfig});
        }
    }
    catch (e) {
        logger.error(`update local config json at ${resourceConfigPath} failed: ${(e as Error).message}`);
    }
}

function ideVersionSatisfies(item: any, consumerName?: string, consumerVersion?: string) {
    if (!consumerName || !consumerVersion) {
        return true;
    }
    if (item.compatibility && item.compatibility[consumerName]) {
        return satisfies(consumerVersion, item.compatibility[consumerName]);
    }
    return true;
}

interface RemoteAvailableVersionItem {
    version: string;
    sha256: string;
    platformAndArch: string;
}

function sortByVersion(a: {version?: string}, b: {version?: string}) {
    return compareVersions(b.version || '1.0.0', a.version || '1.0.0');
}

function getAvailableRemoteVersions(
    name: string,
    remoteVersionInfo: RemoteVersionInfo,
    consumer: ResourceConsumer,
    logger: LoggerRoleInstance
) {
    const allVersions = remoteVersionInfo.versions;
    if (!allVersions) {
        logger.error(`Failed to get ${name} versions from versions.json!`);
        return [];
    }
    const {name: consumerName, version: consumerVersion} = consumer;
    const availableVersions: RemoteAvailableVersionItem[] = [];
    for (const version of allVersions) {
        if (version.deprecated === true) {
            continue;
        }
        if (!ideVersionSatisfies(version, consumerName, consumerVersion)) {
            continue;
        }

        if (version.sha256 && typeof version.sha256.any === 'string') {
            availableVersions.push({
                version: version.version,
                sha256: version.sha256.any,
                platformAndArch: 'any',
            });
        }
        else {
            const {platform, arch} = getOsPlatformAndArch();
            if (!version.sha256) {
                return [];
            }
            const versionVerifiedOnPlatform = version.sha256[platform];
            if (
                version.sha256
                && typeof versionVerifiedOnPlatform === 'object'
                && versionVerifiedOnPlatform[arch]
            ) {
                availableVersions.push({
                    version: version.version,
                    sha256: versionVerifiedOnPlatform[arch],
                    platformAndArch: `${platform}-${arch}`,
                });
            }
        }
    }

    return availableVersions;
}

function getExpectLatestVersion(resource: ResourceItem, remoteAvailableVersions: RemoteAvailableVersionItem[]) {
    const {versionConfig} = resource;
    const availableVersions = remoteAvailableVersions.filter(v => satisfies(v.version, versionConfig.version));
    const sortedVersions = availableVersions.sort(sortByVersion);
    return sortedVersions[0];
}

function getLocalLatestVersion(localConfigs: LocalConfig[]) {
    const latestConfig = localConfigs.sort(sortByVersion)[0];
    return latestConfig ? {path: latestConfig.path, version: latestConfig.version} : undefined;
}

async function shouldUpdateResource(
    resource: ResourceItem,
    expectVersion: RemoteAvailableVersionItem,
    logger?: LoggerRoleInstance
) {
    if (!resource.localLatestVersion || !resource.localLatestVersion.version) {
        return true;
    }
    const localVersion = resource.localLatestVersion.version;
    const needUpgrade = compare(localVersion, expectVersion.version, '<');
    if (needUpgrade) {
        return true;
    }
    const isExist = await isFileExist(resource.localLatestVersion.path);
    if (!isExist) {
        return true;
    }
    return false;
}

/**
 * 保留一个最新的tar文件，其余的都删了
 */
async function deleteObsoleteTarFiles(folder: string, files: TypeFSE.Dirent[]) {
    const tarFiles = files.filter(file => file.name.endsWith('.tar.gz'));
    const sortedTarFiles = tarFiles.sort(
        (a, b) => {
            const aVersion = a.name.split('-').pop()?.replace('.tar.gz', '');
            const bVersion = b.name.split('-').pop()?.replace('.tar.gz', '');
            return sortByVersion({version: aVersion}, {version: bVersion});
        }
    );
    sortedTarFiles.shift();
    for (const file of sortedTarFiles) {
        await unlink(join(folder, file.name));
    }
}

/**
 * 删除旧版本的解压文件，现在使用那个版本完全是按 .latest 中的path来决定的，如果拿不到，证明用不了，直接删掉吧
 */
async function deleteObsoleteUnpackedFiles(
    folder: string,
    files: TypeFSE.Dirent[],
    prefix: string,
    localLatestVersion?: LocalAvailableVersion
) {
    const unpackedFiles = files.filter(file => file.name.startsWith(prefix));
    const unlinkedFiles = localLatestVersion
        ? unpackedFiles.filter(file => file.name !== basename(localLatestVersion.path))
        : unpackedFiles;
    for (const file of unlinkedFiles) {
        await remove(join(folder, file.name));
    }
}

async function clearObsoleteFiles(
    resource: ResourceItem,
    folder: string,
    consumer: ResourceConsumer,
    localLatestVersion?: LocalAvailableVersion,
    logger?: LoggerRoleInstance
) {
    try {
        const folderExist = await isFileExist(folder);
        if (!folderExist) {
            return;
        }
        const items = fs.readdirSync(folder, {withFileTypes: true});
        await deleteObsoleteTarFiles(folder, items.filter(item => item.isFile()));
        const unpackedFilePrefix = `${consumer.name}-${resource.name}`;
        await deleteObsoleteUnpackedFiles(
            folder,
            items.filter(item => item.isDirectory()),
            unpackedFilePrefix,
            localLatestVersion
        );
    }
    catch (e) {
        logger?.error(`Failed to clear obsolete files for ${resource.name} resource: ${(e as Error).message}`);
    }
}

interface LocalAvailableVersion {
    path: string;
    version?: string;
}

interface ResourceItem {
    name: string;
    versionConfig: VersionConfig;
    localLatestVersion?: LocalAvailableVersion;
}

interface ResourceConsumer {
    name: string;
    version?: string;
}

interface ResourceManagerOptions {
    consumer: ResourceConsumer;
}

export class UTAgentCLIResourceManager {
    private readonly resources = new Map<string, ResourceItem>();
    private globalStoragePath: string[] = [];

    constructor(readonly options: ResourceManagerOptions, readonly logger: LoggerRoleInstance) {
        Object.entries(RESOURCES_MAPPING).forEach(([name, versionConfig]) => {
            this.resources.set(
                name,
                {
                    name,
                    versionConfig,
                }
            );
        });
        this.logger = logger;
    }

    resolveResource(name: string) {
        const path = this.resources.get(name)?.localLatestVersion?.path;

        // TODO: 是否在 windows 下有问题？
        if (getOsPlatformAndArch().platform === 'win32' && path) {
            return path;
        }
        return path?.replace(' ', '\\ ');
    }

    async loadResources() {
        try {
            await Promise.allSettled(
                Array.from(this.resources.values()).map(resource => {
                    return this.doLoadResource(resource);
                })
            );
        }
        catch (e) {
            this.logger.error(`Failed to load resource: ${(e as Error).message}`);
        }
    }

    async checkResourceExist() {
        try {
            await Promise.all(
                Array.from(this.resources.values()).map(async resource => {
                    const path = this.resolveResource(resource.name);
                    if (!path || !isFileExist(path)) {
                        throw new Error(`Failed to get resource storage path from: ${path}`);
                    }
                })
            );
            return true;
        }
        catch {
            return false;
        }
    }

    async initResource() {
        try {
            const storagePaths = await getResourceStorageFolder();
            if (storagePaths.length === 0) {
                this.logger.error('Failed to get comate resource storage path!');
                return;
            }
            this.globalStoragePath = storagePaths;
            this.logger.info('Global resource storage path: ', this.globalStoragePath);
            await Promise.all(
                Array.from(this.resources.values()).map(async resource => {
                    // 初始化时候没有值
                    resource.localLatestVersion = await this.getLocalLatestVersion(resource);
                })
            );
        }
        catch (e) {
            this.logger.error(`Failed to init resource: ${(e as Error).message}`);
        }
    }

    async doLoadResource(resource: ResourceItem) {
        try {
            const [localLatestVersion, remoteVersionInfo] = await Promise.all([
                this.getLocalLatestVersion(resource),
                this.getRemoteVersionInfo(resource),
            ]);
            // 初始化时候没有值
            resource.localLatestVersion = localLatestVersion;
            if (!localLatestVersion) {
                this.logger.warn(`Failed to get local latest version for ${resource.name} resource!`);
            }
            this.clearOldVersion(resource, localLatestVersion);
            if (!remoteVersionInfo) {
                this.logger.error(`Failed to get ${resource.name} versions info!`);
                return;
            }
            await this.checkAndUpdate(resource, remoteVersionInfo);
        }
        catch (e) {
            this.logger.error(`Failed to download ${resource.name}: ${(e as Error).message}`);
        }
    }

    async checkAndUpdate(resource: ResourceItem, remoteVersionInfo: RemoteVersionInfo) {
        const remoteAvailableVersions = getAvailableRemoteVersions(
            resource.name,
            remoteVersionInfo,
            this.options.consumer,
            this.logger
        );
        const expectVersion = getExpectLatestVersion(resource, remoteAvailableVersions);
        if (!expectVersion) {
            this.logger.error(
                `No available version for ${resource.name} with version ${resource.versionConfig.version}!`
            );
            return;
        }
        const shouldUpdate = await shouldUpdateResource(resource, expectVersion, this.logger);
        if (!shouldUpdate) {
            this.logger.info(
                `Resource ${resource.name} is up to date, expect version: ${expectVersion.version}`
                    + `, local latest version: ${resource.localLatestVersion?.version}`
            );
            return;
        }
        this.downloadResource(resource, expectVersion);
    }

    async getResourceStorageFolder(name: string): Promise<string | undefined> {
        for (const storagePath of this.globalStoragePath) {
            const folder = join(storagePath, name);
            try {
                await ensureDirectoryExist(folder);
                await access(folder, fs.constants.W_OK);
                return folder;
            }
            catch (e) {
                this.logger.warn(`Failed to access ${folder}: ${(e as Error).message}`);
            }
        }
        return undefined;
    }

    async downloadResource(resource: ResourceItem, expectVersion: RemoteAvailableVersionItem) {
        // TODO: 检查下本地是否有下载好的 tar 包
        const destinationFolder = await this.getResourceStorageFolder(resource.name);
        if (!destinationFolder) {
            this.logger.error(`Failed to get storage folder for ${resource.name}`);
            return;
        }

        const filename = `${resource.name}-${expectVersion.platformAndArch}-${expectVersion.version}.tar.gz`;
        const downloadUrl = `${resource.versionConfig.path}/${filename}`;

        try {
            const destination = join(destinationFolder, filename);
            const downloadFile = async () => {
                await download(downloadUrl, destinationFolder, filename);
                const hashValid = await verifyFileHash(destination, expectVersion.sha256);
                if (!hashValid) {
                    throw new Error('hash not match for downloaded file');
                }
            };
            const exist = await isFileExist(destination);
            if (exist) {
                const hashValid = await verifyFileHash(destination, expectVersion.sha256);
                if (!hashValid) {
                    await unlink(destination);
                    await downloadFile();
                }
            }
            else {
                await downloadFile();
            }
            const consumerName = this.options.consumer.name;
            const unpackedFileName = `${consumerName}-${resource.name}-${expectVersion.version}`;
            const unpackFilePath = join(destinationFolder, unpackedFileName);
            await extractTarGz(destination, unpackFilePath);
            const latestVersion = {path: unpackFilePath, version: expectVersion.version, sha256: expectVersion.sha256};
            await updateLocalConfig(
                destinationFolder,
                this.options.consumer,
                {path: unpackFilePath, version: expectVersion.version, sha256: expectVersion.sha256},
                this.logger
            );
            resource.localLatestVersion = latestVersion;
            this.logger.info(`Successfully upgrade ${resource.name} to version: ${expectVersion.version}`);
        }
        catch (e) {
            this.logger.error(
                `Failed to upgrade ${resource.name} to ${expectVersion.version}: ${(e as Error).message}`
            );
        }
    }

    private clearOldVersion(resource: ResourceItem, localLatestVersion?: LocalAvailableVersion) {
        for (const storagePath of this.globalStoragePath) {
            const folder = join(storagePath, resource.name);
            clearObsoleteFiles(resource, folder, this.options.consumer, localLatestVersion, this.logger);
        }
    }

    private async getLocalLatestVersion(resource: ResourceItem) {
        const localConfigs = await Promise.all(this.globalStoragePath.map(async path => {
            return readLocalConfigJson(resource.name, path, this.options.consumer);
        }));
        const validConfigs = localConfigs.filter((config): config is LocalConfig => !!(config && config.path));
        return getLocalLatestVersion(validConfigs);
    }

    private async getRemoteVersionInfo(resource: ResourceItem): Promise<RemoteVersionInfo | undefined> {
        const versionsConfigUrl = join(resource.versionConfig.path, 'versions.json');
        try {
            const versionInfo = await axiosInstance.get<RemoteVersionInfo>(versionsConfigUrl);
            return versionInfo.data;
        }
        catch (e) {
            this.logger.error(
                `Failed to get ${resource.name} versions from ${versionsConfigUrl}: ${(e as Error).message}`
            );
            return undefined;
        }
    }
}
