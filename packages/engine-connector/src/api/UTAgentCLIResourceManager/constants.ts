export interface VersionConfig {
    id: string;
    version: string;
    path: string;
}

export const RESOURCES_MAPPING: Record<string, VersionConfig> = {
    UTAgent: {
        id: 'UTAgent',
        version: '^2.0.0',
        path: process.env.ENVIRONMENT === 'test'
            ? 'https://baidu-coverage.bj.bcebos.com/comate/bin/UTAgent/'
            : 'https://baidu-ide.bj.bcebos.com/comate/bin/UTAgent/',
    },
};
