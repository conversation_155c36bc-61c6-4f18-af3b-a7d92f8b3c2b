import {createReadStream} from 'node:fs';
import {access} from 'node:fs/promises';
import crypto from 'crypto-js';
import decompress from 'decompress';
import {emptyDir} from '@comate/plugin-shared-internals';

export async function isFileExist(path: string): Promise<boolean> {
    try {
        await access(path);
        return true;
    }
    catch (e) {
        return false;
    }
}

export function getOsPlatformAndArch(): {platform: NodeJS.Platform, arch: NodeJS.Architecture | 'x86'} {
    const platform = process.platform;
    const arch = (() => {
        if (process.arch === 'x64') {
            return 'x86';
        }
        else if (process.arch === 'arm64' || process.arch === 'arm') {
            return 'arm64';
        }
        return process.arch;
    })();
    return {platform, arch};
}

export async function extractTarGz(sourcePath: string, destPath: string) {
    try {
        await emptyDir(destPath);
        await decompress(sourcePath, destPath);
    }
    catch {
        throw new Error('Failed to extract the file!');
    }
}

export async function verifyFileHash(filePath: string, sha256: string) {
    return new Promise((resolve, reject) => {
        const hash = crypto.algo.SHA256.create();
        const fileStream = createReadStream(filePath);

        fileStream.on('data', (chunk: Buffer) => {
            hash.update(crypto.lib.WordArray.create(chunk));
        });
        fileStream.on('end', () => {
            const calculatedHash = hash.finalize().toString(crypto.enc.Hex);
            resolve(calculatedHash === sha256);
        });
        fileStream.on('error', err => {
            reject(err);
        });
    });
}
