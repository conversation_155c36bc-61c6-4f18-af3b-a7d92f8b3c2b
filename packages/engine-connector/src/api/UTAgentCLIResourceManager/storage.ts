import {join} from 'node:path';
import {homedir, tmpdir} from 'node:os';
import {getOsAppDataPath, ensureDirectoryExist} from '@comate/plugin-shared-internals';

const COMATE_HOME_FOLDER = join(homedir(), '.comate');

export const getResourceConfigPath = (resourceRoot: string) => join(resourceRoot, '.latest');

export async function getComateOsFolder() {
    const osPath = getOsAppDataPath();
    const comateOsFolder = join(osPath, 'comate');
    const exist = await ensureDirectoryExist(comateOsFolder);
    return exist ? comateOsFolder : '';
}

export async function getComateHomeFolder() {
    const exist = await ensureDirectoryExist(COMATE_HOME_FOLDER);
    return exist ? COMATE_HOME_FOLDER : '';
}

export async function getComateTmpFolder() {
    const comateTmpFolder = join(tmpdir(), 'comate');
    const exist = await ensureDirectoryExist(comateTmpFolder);
    return exist ? comateTmpFolder : '';
}

/**
 * 获取所有资源可能存储的路径
 * 因为不确定上次存储的路径是哪个，所以需要逐一判断是否存在
 *
 * @returns 资源存储文件夹路径的数组
 */
export async function getResourceStorageFolder() {
    if (process.env.RESOURCE_STORAGE_PATH) {
        return [process.env.RESOURCE_STORAGE_PATH];
    }
    const folders = await Promise.all([
        getComateOsFolder(),
        getComateHomeFolder(),
        getComateTmpFolder(),
    ]);
    return folders.filter(folder => !!folder);
}
