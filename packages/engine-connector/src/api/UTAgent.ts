import {createAxiosInstance} from '@comate/plugin-shared-internals';

const BASE_URL_MAPPING: Record<string, string> = {
    internal: 'https://comate.baidu.com',
    'internal-test': 'http://smart-ut.test.cov.appspace.baidu.com',
    saas: 'https://comate.baidu.com',
    'saas-test': 'http://smart-ut.test.cov.appspace.baidu.com',
};

export interface UserGuideResponse {
    result: 'SUCCESS' | 'FAIL';
    msg: string;
    isEnd: boolean;
}

interface UserGuideParams {
    query: string;
}

const baseAxiosInstance = createAxiosInstance({
    baseURL: BASE_URL_MAPPING[process.env.PLATFORM + (process.env.ENVIRONMENT === 'test' ? '-test' : '')]
        ?? BASE_URL_MAPPING['internal-test'],
});

// 获得用户引导文档
export function getUserGuide({
    query,
}: UserGuideParams) {
    return baseAxiosInstance('/api/plugin/ut/userGuide', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        responseType: 'stream',
        data: {query},
    });
}
