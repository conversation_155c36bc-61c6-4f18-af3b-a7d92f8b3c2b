import {IncomingMessage} from 'http';
import {axiosInstance} from '@comate/plugin-shared-internals';

interface UserDetail {
    username: string;
    version: string;
}

function headerWithUsername(userDetail: UserDetail) {
    return {
        'X-Source': 'COMATE',
        'X-Client-Version': userDetail.version,
        'Content-Type': 'application/json',
        'login-name': userDetail.username,
        'Uuap-login-name': userDetail.username,
    };
}

interface HistoryMessageItem {
    role: 'user' | 'assistant';
    content: string;
}

interface AnalyzeParams {
    userDetail: UserDetail;
    query: string;
    contexts?: string;
    platform?: string;
    repo?: string;
    conversationId: string;
    history?: HistoryMessageItem[];
    cwd?: string;
}


export interface ClosestFunctionContext {
    filePath: string;
    lineNum: number;
    maxNum?: number;
    forwardRangeNum: number;
    backRangeNum: number;
}

export interface FunctionDefinitionContext {
    filePath: string;
    lineNum: number;
    symbolName: string;
    symbolType: string;
}

export interface FileContext {
    filePath: string;
    lineNum: number;
    retrieval: boolean;
    forwardRangeNum: number;
    backRangeNum: 10, // 指定行号下面的行数
}

export type AnalyzeContext = Record<string, ClosestFunctionContext | FunctionDefinitionContext | FileContext>;

export interface AnalyzeData {
    recordId: string;
    rewriteQuery: string;
    queryType?: string;
    errorReason: string;
    context: AnalyzeContext;
}

export interface AnalyzeResponse {
    requestId: string;
    code: number;
    message?: string;
    data: AnalyzeData;
}

export async function analyze({userDetail, ...params}: AnalyzeParams) {
    const result = await axiosInstance.post<AnalyzeResponse>(
        '/api/autodebug/api/v1/errorfix/agentAnalysis',
        params,
        {
            headers: headerWithUsername(userDetail),
        }
    );

    if (result.data.code !== 200) {
        throw new Error(result.data.message);
    }

    return result.data.data;
}

export interface CodeSnippetItem {
    filePath: string;
    startLineNum: number;
    endLineNum: number;
    content: string;
    symbolName?: string;
    type?: string;
}

export type CodeContext = Record<string, CodeSnippetItem[]>;

interface AutoDebugFixParams {
    userDetail: UserDetail;
    recordId?: string;
    device: string;
    conversationId: string;
    query: string;
    context?: CodeContext;
    queryType?: string;
    history?: HistoryMessageItem[];
}

export interface AutoDebugFixResult {
    isEnd: boolean;
    result: string;
    uuid?: string;
}

export interface AutoDebugFixResponse {
    requestId: string;
    code: number;
    message?: string;
    data?: AutoDebugFixResult
}

export async function autoDebugFix({userDetail, ...params}: AutoDebugFixParams) {
    const result = await axiosInstance.post<IncomingMessage>(
        '/api/autodebug/api/v1/errorfix/agentSolution',
        params,
        {
            headers: headerWithUsername(userDetail),
            responseType: 'stream',
        }
    );
    return result.data;
}

interface ResponseBase<T> {
    requestId: string;
    code: number;
    message?: string;
    data: T;
}

interface CodeAdoptParams {
    userDetail: UserDetail;
    uuid: string;
    adoptedCode: string;
}

export async function codeAdopt({userDetail, uuid, adoptedCode}: CodeAdoptParams) {
    const result = await axiosInstance.post<ResponseBase<any>>(
        `/api/autodebug/api/v1/errorfix/codeAdopt?uuid=${uuid}`,
        {adoptedCode, type: 'AGENT'},
        {
            headers: headerWithUsername(userDetail),
        }
    );
    return result.data;
}

interface CodeGenerateParams {
    userDetail: UserDetail;
    uuid: string;
    recordId: string;
    generatedCode: string;
}

export async function codeGenerate({userDetail, ...params}: CodeGenerateParams) {
    const result = await axiosInstance.post<ResponseBase<any>>(
        '/api/autodebug/api/v1/errorfix/codeGenerate',
        {...params, type: 'AGENT'},
        {
            headers: headerWithUsername(userDetail),
        }
    );
    return result.data;
}

export async function runStatus(
    {userDetail, ...params}: {recordId: string; status: string, userDetail: UserDetail}
) {
    const result = await axiosInstance.post<ResponseBase<any>>(
        '/api/autodebug/api/v1/errorfix/runStatus',
        params,
        {
            headers: headerWithUsername(userDetail),
        }
    );
    return result.data;
}

export async function taskStatus(
    {userDetail, ...params}: {conversationId: string; status: string, userDetail: UserDetail}
) {
    const result = await axiosInstance.post<ResponseBase<any>>(
        '/api/autodebug/api/v1/errorfix/taskStatus',
        params,
        {
            headers: headerWithUsername(userDetail),
        }
    );
    return result.data;
}

