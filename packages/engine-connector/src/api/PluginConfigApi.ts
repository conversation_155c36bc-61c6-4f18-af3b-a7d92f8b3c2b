// HEAD
import {
    InformationQueryType,
    KnowledgeOptions,
    KnowledgeQueryWorkspace,
    getCurrentUserKnowledgeSet,
    getCurrentUserPluginConfigSet,
    getKnowledgeQueryResult,
} from '@comate/plugin-shared-internals';
import {LoggerRoleInstance} from '../service/Logger.js';
import {Mediator} from '../service/Mediator.js';
import {UserDetailWithId} from '../service/User.js';
import {isInternal, isSaaS} from '../utils/isSaaS.js';

interface Chunk {
    id: number;
    workspaceType: string;
    workspaceUUID: string;
    knowledgeType: string;
    knowledgeUUID: string;
    content: string;
    source: string;
    status: string;
    keys: any[];
    score?: number | null;
    hits?: number | null;
    creator?: string | null;
}

export class PluginConfigApi {
    mediator: Mediator;
    logger: LoggerRoleInstance;
    user!: UserDetailWithId;

    constructor(mediator: Mediator, logger: LoggerRoleInstance) {
        this.mediator = mediator;
        this.logger = logger;
    }

    init(user: UserDetailWithId) {
        this.user = user;
    }

    getCurrentUserPluginConfigSet = () => getCurrentUserPluginConfigSet(isSaaS() ? this.user.license : this.user.name);

    getCurrentUserKnowledgeSet = () =>
        getCurrentUserKnowledgeSet(
            isInternal() ? this.user.name : this.user.license,
            isInternal() ? undefined : this.user.customizeService,
            this.user.name
        );

    getKnowledgeQueryResult = async (
        query: string,
        retrievalType: InformationQueryType,
        workspaces: KnowledgeQueryWorkspace[],
        options?: KnowledgeOptions
    ): Promise<{chunks: Chunk[]}> =>
        getKnowledgeQueryResult(
            isInternal() ? this.user.name : this.user.license,
            query,
            retrievalType,
            workspaces,
            options
        );
}
