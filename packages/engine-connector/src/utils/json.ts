const jsonParse = (json: string | null): Record<string, any> | null => {
    if (!json) {
        return null;
    }
    return JSON.parse(json);
};

// https://wiki.geekdream.com/Specification/json-rpc_2.0.html
export class JsonRPC {
    private static source: string | null;
    private static result: Record<string, any> | null;

    static findJsonRPC = (json: unknown | string | null): typeof JsonRPC => {
        if (json && typeof json === 'string' && json.includes('{') && json.includes('}')) {
            // 长文本，且内容带有 {} 的会存在正则匹配不准确的情况。因此通过第一个{ 和 最后一个 } 手动截取字符串。
            const start = json.indexOf('{');
            const end = json.lastIndexOf('}');
            this.source = json.substring(start, end + 1);
        }
        return JsonRPC;
    };

    static parse = (json?: string | null): typeof JsonRPC => {
        if (json === undefined && this.source && typeof this.source === 'string') {
            this.result = jsonParse(this.source);
        }
        else if (typeof json === 'string') {
            this.result = jsonParse(json);
        }
        return JsonRPC;
    };

    static getParams = () => {
        try {
            if (JsonRPC.isRPCResult(this.result)) {
                return {...this.result.params};
            }
            return null;
        }
        finally {
            this.clean();
        }
    };

    static getFindedJsonString = () => {
        return this.source;
    };

    // 多个 Content-Length 返回
    static isMoreContent(json: string): boolean {
        return (json.match(/Content-Length: \d+\n{2}/)?.length ?? 0) > 1;
    }

    static *getMoreContentChunk(json: string): Generator<string, void> {
        if (this.isMoreContent(json)) {
            return;
        }
        let result: RegExpExecArray | null = null;
        // 处理后："{}\n{}"
        let pureString = json.replace(/Content-Length: \d+\n{2}/, '');
        while (result = /Content-Length: (\d+)\n{2}/.exec(json)) {
            const contentLength = Number(result[1]);
            const currentJson = pureString.substring(0, contentLength);
            pureString = pureString.substring(contentLength + 1);
            yield currentJson;
        }
    }

    static isRealJson = (json: string) => {
        try {
            return !!JSON.parse(json);
        }
        catch {
            return false;
        }
    };

    private static isRPCResult = (result: any): result is Record<string, any> => {
        return result && typeof result === 'object' && result.jsonrpc === '2.0' && result.id != null;
    };

    private static clean = () => {
        this.source = null;
        this.result = null;
    };
}
