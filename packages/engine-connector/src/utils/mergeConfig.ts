export function mergeConfig(objectA: Record<string, any>, objectB: Record<string, any>, depth = 1) {
    for (const key in objectB) {
        if (objectB.hasOwnProperty(key)) {
            if (
                depth < 3 && typeof objectB[key] === 'object' && objectB[key] != null
                && objectA.hasOwnProperty(key) && typeof objectA[key] === 'object' && objectA[key] != null
            ) {
                objectA[key] = mergeConfig(objectA[key], objectB[key], depth + 1);
            }
            else {
                objectA[key] = objectB[key];
            }
        }
    }
    return objectA;
}
