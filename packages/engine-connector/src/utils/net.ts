import {createServer} from 'node:net';

export function isPortAvailable(port: number) {
    return new Promise(resolve => {
        const server = createServer().listen(port, () => {
            server.close();
            resolve(true); // 端口可用
        });

        server.on('error', err => {
            // @ts-ignore
            if (err.code === 'EADDRINUSE') {
                resolve(false); // 端口被占用
            }
        });
    });
}
