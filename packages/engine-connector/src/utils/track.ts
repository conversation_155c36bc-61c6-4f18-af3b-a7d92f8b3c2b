import path from 'node:path';
import {getGitRepositoryUrl} from '@comate/plugin-host';
import {VirtualEditor} from '@comate/plugin-shared-internals';

interface RemoteInfo {
    remote: string;
    protocol: string;
    host: string;
    repository: string;
    owner: string | null;
    project: string;
}

export function parseRemoteUri(inputUri: string): RemoteInfo {
    // Ignore any trailing modifiers, such as the ones supported
    // on npm uris (ex: git+ssh://**************:npm/npm.git#v1.0.27)
    const uri = inputUri.replace(/#.+$/, '');

    let matches: RegExpMatchArray | null = null;

    // Check if it's a URL with the following known format:
    // http://site.com/owner/project(.git)
    // Any protocols are accepted, to support complex protocols
    // (ex. git+https://<EMAIL>/npm/npm.git)
    const generalFormat = /^([^:]+):\/\/([^@]+@)?([^/:]+)\/([^/]+)\/([^/]+)$/;

    if ((matches = generalFormat.exec(uri))) {
        const owner = matches[4];
        const project = matches[5].replace(/\.git$/, '');

        return {
            remote: inputUri,
            protocol: matches[1],
            host: matches[3],
            repository: `${owner}/${project}`,
            owner,
            project,
        };
    }

    // Check if it's a URL with the following known format:
    // (ssh://)************:owner/project(.git)
    const gitKnownRemote = /^(([^:]+):\/\/)?git@([^:]+):([^/]+)\/([^/]+)$/;

    if ((matches = gitKnownRemote.exec(uri))) {
        const owner = matches[4];
        const project = matches[5].replace(/\.git$/, '');

        return {
            remote: inputUri,
            protocol: matches[2] || 'ssh',
            host: matches[3],
            repository: `${owner}/${project}`,
            owner,
            project,
        };
    }

    // Check if it's a URI with any protocol, host, and path.
    // ex: ssh://<EMAIL>/~/repos/R.git
    const looseUriFormat = /^([^:]+):\/\/([^@]+@)?([^/]+)\/(.+)$/;

    if ((matches = looseUriFormat.exec(uri))) {
        const repoPath = matches[4];
        const project = path.basename(repoPath).replace(/\.git$/, '');

        return {
            remote: inputUri,
            protocol: matches[1],
            host: matches[3],
            repository: repoPath,
            owner: null,
            project,
        };
    }

    // The remote is a local path or an unknown URL, just
    // return its repository name without the owner's info

    const dirName = path.basename(uri);
    const repoName = dirName.replace(/\.git$/, '');

    return {
        remote: inputUri,
        protocol: 'file',
        host: 'localhost',
        owner: null,
        repository: repoName,
        project: repoName,
    };
}

const getTraceRepo = async (cwd: string) => {
    try {
        const repositoryUrl = await getGitRepositoryUrl(cwd);
        if (repositoryUrl) {
            const {repository} = await parseRemoteUri(repositoryUrl);
            if (repository) {
                return repository;
            }
        }
    }
    catch (ex) {
        //
    }

    // 取前两级
    return path.relative(path.resolve(cwd, path.join('..', '..')), cwd);
};

/**
 * 统计用于代码生成的代码库信息
 * 1. 获取执行目录：当前文件存在，取当前文件的cwd, 不存在取workspace
 * 2. 在执行目录获取 gitRemoteUrl, 只要能够按规则解析，取 host:port/ 后面的地址
 * 3. 如果2解析不出来，假设1的执行目录为 /home/<USER>/xxx/yyy ，往上取两级吧，示例为 xxx/yyy
 */
export const getTraceRepoInfo = async (virtualEditor: VirtualEditor, workspaceRoot: string) => {
    const activeEditor = await virtualEditor.getActiveDocument();
    if (activeEditor.existed && activeEditor.scheme === 'file') {
        const cwd = activeEditor.absolutePath;
        const repoName = await getTraceRepo(path.dirname(cwd));
        return {repoName, repoId: '', currentFilePath: activeEditor.absolutePath};
    }

    if (workspaceRoot) {
        const repoName = await getTraceRepo(workspaceRoot);
        return {repoName: repoName, repoId: '', currentFilePath: ''};
    }

    return {repoName: '', repoId: '', currentFilePath: ''};
};
