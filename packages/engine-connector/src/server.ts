import {ComateService} from './service/ComateService.js';
import {ENVIRONMENT, PLATFORM, setPlatformAndEnvironment} from '@comate/plugin-shared-internals';

setPlatformAndEnvironment(
    process.env.PLATFORM as PLATFORM || PLATFORM.INTERNAL,
    process.env.ENVIRONMENT as ENVIRONMENT || ENVIRONMENT.PRODUCTION
);

process.title = 'comate-plugin-kernel';

export async function run() {
    const comateService = new ComateService();
    await comateService.init();
}
