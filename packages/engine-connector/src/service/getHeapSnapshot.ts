import fs from 'fs';
import os from 'os';
import * as path from 'path';
import inspector from 'inspector';
export async function getHeapSnapshot(snapshotName: string) {
    const session = new inspector.Session();
    session.connect();

    const snapshotDir = path.join(os.homedir(), '.comate-engine', 'heap-snapshot');
    if (!fs.existsSync(snapshotDir)) {
        fs.mkdirSync(snapshotDir, {recursive: true});
    }

    const snapshotPath = path.join(snapshotDir, `${snapshotName}.heapsnapshot`);
    try {
        let chunks = '';
        const getSnapShotChunks = (result: {params: {chunk: string}}) => {
            chunks += result.params.chunk;
        };

        await new Promise((resolve, reject) => {
            session.on('HeapProfiler.addHeapSnapshotChunk', getSnapShotChunks);
            session.post('HeapProfiler.takeHeapSnapshot', (err: Error | null, r: any) => {
                if (err) {
                    reject(err);
                    return;
                }
                session.off('HeapProfiler.addHeapSnapshotChunk', getSnapShotChunks);
                resolve(r);
            });
        });

        // 使用 await 等待文件写入操作的完成
        await fs.promises.writeFile(snapshotPath, chunks);
    }
    catch (error) {
        throw error;
    }
    finally {
        session.disconnect();
    }
}
