import {PluginConfigApi} from '../api/PluginConfigApi.js';
import {URI} from 'vscode-uri';
import {LLMApi} from '../api/LLMApi.js';
import {Logger} from './Logger.js';
import {Mediator} from './Mediator.js';
import {PluginChannel} from './PluginChannel/index.js';
import {PluginConfig} from './PluginConfig/index.js';
import {User} from './User.js';
import {IdeChannel} from './IdeChannel/index.js';
import {EngineDiagnosticCache} from './DiagnosticCache.js';
import {MessageGenerate} from './MessageGenerate/index.js';
import {
    ACTION_ENGINE_PROCESS_START_SUCCESS,
    VirtualEditor,
    getApiHost,
    patchEnvPath,
} from '@comate/plugin-shared-internals';
import {ConversationBufferMemory} from '@comate/plugin-engine';
import {ChatSessionManager} from './ChatSessionManager/index.js';
import {ConversationManager} from './AgentConversation/index.js';
import {UnitTestAgent} from './AgentConversation/TestBotConversation/UnitTestAgent/index.js';
import {CommonCache} from './CommonCache.js';
import {isSaaS} from '../utils/isSaaS.js';
import {active} from '@comate/kernel';
import {kernel} from '@comate/kernel-shared';
import {PromptTemplateService} from './PromptTemplateService/index.js';
import AppLaunchTracker from './AppLaunchTracker.js';
import {getGitRepositoryInfo} from '../../../client/dist/utils/git.js';

export class ComateService {
    version = '0.0.1';

    private conversationBufferMemory?: ConversationBufferMemory;

    /**
     * 清理资源
     */
    dispose() {
        if (this.conversationBufferMemory) {
            this.conversationBufferMemory.dispose();
            this.conversationBufferMemory = undefined;
        }
    }

    /**
     * @description
     * 初始化 Engine，包括 IDE Channel、User、Plugin Config、Plugin Channel、Plugin Config API、LLM API、Diagnostic Cache、Message Generate、Chat History Manager、Conversation Buffer Memory、Mediator、Unit Test Agent。
     * 完成后通知 IDE Channel 和发送初始化成功日志。
     *
     * @async
     * @returns {Promise<void>} 无返回值
     */
    async init() {
        // 用于补充常见环境变量
        patchEnvPath();

        const logger = new Logger();
        const mediator = new Mediator();
        const virtualEditor = new VirtualEditor({
            send: mediator.sendToIde.bind(mediator),
            logger: logger.getLoggerAsRole('VirtualEditor'),
        });

        const engineLogger = logger.getLoggerAsRole('Engine');
        const ideChannel = new IdeChannel(
            mediator,
            virtualEditor,
            engineLogger
        );
        await ideChannel.init();
        await ideChannel.kernelService.LSPInitialized();

        ideChannel.kernelService.onInitialized(async () => {
            engineLogger.info('kernel Initialized');
            active();
            const user = new User(ideChannel, logger.getLoggerAsRole('USER'));
            let isUserInitError = false;
            try {
                await user.init();
            }
            catch (error: any) {
                isUserInitError = true;
                // TODO 用户信息获取失败，影响主要逻辑问题，需要上报
                ideChannel.sendNotification('USER_DETAIL_ERROR', {error: error.message});
            }
            logger.addLogUploader(user.getUserDetail());
            // 这之前getLoggerAsRole的logUploader都是undefined，需要重新写一下
            engineLogger.logUploader = logger.logUploader;

            // Engine 初始化成功
            try {
                new AppLaunchTracker(logger);
            }
            catch (error: any) {}

            const pluginConfig = new PluginConfig(mediator, logger.getLoggerAsRole('PluginConfig'));
            const pluginChannel = new PluginChannel(mediator, logger.getLoggerAsRole('pluginChannel'));
            const pluginConfigApi = new PluginConfigApi(mediator, logger.getLoggerAsRole('pluginConfigApi'));
            const llmApi = new LLMApi(mediator, logger.getLoggerAsRole('LLMApi'));
            const commonCache = new CommonCache(logger.getLoggerAsRole('CommonCache'));

            const diagnosticCache = new EngineDiagnosticCache(mediator, logger.getLoggerAsRole('DiagnosticCache'));

            const rootUri = kernel.env.workspaceInfo.workspaceFolders?.at(0)?.uri;
            // 如果不存在工作区，会parse到 / 目录去
            const rootPath = URI.parse(rootUri ?? '').fsPath;
            const gitinfo = await getGitRepositoryInfo(rootPath);
            const agentConversationManager = new ConversationManager(
                // virtualEditor 如果不在工作区内，全部输出专为纯文本
                {
                    repoId: '',
                    username: isSaaS() ? user.userDetail.license : user.userDetail.name,
                    rootPath: rootUri ? rootPath : '',
                    branch: gitinfo.branch,
                    repoUrl: gitinfo.repositoryUrl || '',
                },
                mediator,
                virtualEditor,
                logger.getLoggerAsRole('ConversationManager')
            );

            const messageGenerate = new MessageGenerate(
                mediator,
                logger.getLoggerAsRole('MessageGenerate'),
                rootUri ? rootPath : ''
            );
            const chatSessionManager = new ChatSessionManager(mediator, logger.getLoggerAsRole('ChatSessionManager'));
            const conversationBufferMemory = new ConversationBufferMemory();

            const promptTemplateService = new PromptTemplateService(
                mediator,
                logger.getLoggerAsRole('PromptTemplateService')
            );

            mediator.init({
                ideChannel,
                pluginChannel,
                user,
                pluginConfig,
                pluginConfigApi,
                llmApi,
                diagnosticCache,
                messageGenerate,
                conversationBufferMemory,
                chatSessionManager,
                agentConversationManager,
                commonCache,
                promptTemplateService,
            });

            pluginConfigApi.init(user.getUserDetail());

            await pluginChannel.init();
            await promptTemplateService.init();

            llmApi.init(user.getUserDetail());
            messageGenerate.init();

            const log = logger.getLoggerAsRole('CS');

            log.info('IDE:', process.env.IDE);
            log.info('HTTP Proxy URL:', process.env.HTTP_PROXY_URL ?? 'None');
            log.info('HTTP HOST:', process.env.HTTP_HOST ?? 'None');
            log.info('Environment:', process.env.ENVIRONMENT);
            log.info('Platform:', process.env.PLATFORM);
            log.info('Api Base Url:', getApiHost());
            log.info('User Info:', user.getUserDetail());
            log.info('Engine Initialized');
            ideChannel.initialized();

            const utAgent = new UnitTestAgent(logger.getLoggerAsRole('UTAgent'), {
                ...user.getUserDetail(),
                ide: process.env.IDE,
            }, rootPath);
            await utAgent.initResourceManager();
            await utAgent.downloadResources();
        });
        await ideChannel.sendNotification(ACTION_ENGINE_PROCESS_START_SUCCESS, {});
    }
}
