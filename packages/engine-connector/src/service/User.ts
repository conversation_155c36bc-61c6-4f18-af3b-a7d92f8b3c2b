import {UserDetail, axiosInstance} from '@comate/plugin-shared-internals';
import {LoggerRoleInstance} from './Logger.js';
import {IdeChannel} from './IdeChannel/index.js';
import {kernel} from '@comate/kernel-shared';

export interface UserDetailForServer {
    uid: string;
    username: string;
    name: string;
    email: string;
    /**
     * 企业配置
     * enterprise 是否企业账号
     * enableCodeSecurity 是否开启代码安全
     */
    enterpriseConfig?: {
        enterprise: boolean; // true 是企业账号，非true 不是企业账号
        enableCodeSecurity: boolean; // true 代码安全开启，非true 代码安全没有开启
    };
}

export async function getUserDetail(token: string): Promise<UserDetailForServer> {
    return axiosInstance('/api/v2/api/comateplus/users', {
        headers: {
            'X-Username': token,
        },
    })
        .then(res => res.data.data);
}

export interface UserDetailWithId extends UserDetail {
    uid: string;
    license: string;
    ideVersion: string;
    version: string;
    platform: string;
    customizeService?: string;
    language?: string;
    device: string;
}

export class User {
    ideChannel: IdeChannel;
    userDetail!: UserDetailWithId & UserDetailForServer;
    logger: LoggerRoleInstance;

    constructor(ideChannel: IdeChannel, logger: LoggerRoleInstance) {
        this.ideChannel = ideChannel;
        this.logger = logger;
    }

    async init() {
        const name = kernel.config.username || '';
        const license = kernel.config.license || '';
        const res = await getUserDetail(kernel.env.isSaaS ? license : name);
        const {uid, email, name: displayName, enterpriseConfig} = res;
        const user = {
            platform: kernel.env.edition,
            ideVersion: kernel.env.ideVersion,
            version: kernel.env.extensionVersion,
            license,
            name,
            uid,
            device: kernel.env.deviceId,
            displayName,
            email,
            username: name,
            enterpriseConfig,
        };

        this.userDetail = user;
        return {username: name, license};
    }

    getUserDetail() {
        return this.userDetail;
    }
}
