import {TEST_BOT_BLOCK_SIGN} from './contans.js';
import type {CommandAndEnv, UtFileResult} from './types.js';
import {getRelativePath} from './utils.js';

// 注意，这里是单例，一定要处理好多任务
const utFileResultCacheMap = new WeakMap<CommandAndEnv, UtFileResult[]>();

function appendUtFileResult(key: CommandAndEnv, value: UtFileResult[]) {
    if (utFileResultCacheMap.has(key)) {
        const oldValue = utFileResultCacheMap.get(key);
        oldValue && utFileResultCacheMap.set(key, oldValue.concat(value));
    }
    else {
        utFileResultCacheMap.set(key, value);
    }
}

export function deleteComposeUtFileResult(key: CommandAndEnv | null) {
    key && utFileResultCacheMap.delete(key);
    key = null;
}

function uniqueAndUpdateByPath(utFileResultArray: UtFileResult[]) {
    const groupByMap = new Map<string, UtFileResult>();
    utFileResultArray.forEach(item => {
        groupByMap.set(item.testFilePath, item);
    });
    return groupByMap;
}

// key: [{ut1}, {ut2}]
export function getComposeUtFileResult(key: CommandAndEnv) {
    return Array.from(uniqueAndUpdateByPath(utFileResultCacheMap.get(key) ?? []).values());
}

// 某些特殊命令，代码是增量输出，需要拼接
function codeCompose(utFile: UtFileResult[] | null, commandAndEnv: CommandAndEnv) {
    if (utFile) {
        appendUtFileResult(commandAndEnv, utFile);
    }
    return getComposeUtFileResult(commandAndEnv);
}

function appendCodeTplString(utFileResultArray: UtFileResult[] | null, commandAndEnv?: CommandAndEnv) {
    if (!commandAndEnv) {
        return [];
    }
    return codeCompose(utFileResultArray, commandAndEnv);
}

export function createUtCodeSummary(utFile: UtFileResult, commandAndEnv: CommandAndEnv) {
    const {meta} = utFile;
    const codeSummary = `已经生成${meta?.generatedTestMethodCount ?? 0}个单测用例${
        meta?.fileCoverage
            ? commandAndEnv.command === 'diff' || commandAndEnv.command === 'gen'
                ? `，被测文件变更行覆盖率${meta?.fileCoverage}。`
                : `，被测文件行覆盖率${meta?.fileCoverage}。`
            : '。'
    }`;
    return codeSummary;
}

function createCodeMdWithSubViewContent(
    rootPath: string,
    utFileResultArray: UtFileResult[],
    commandAndEnv: CommandAndEnv,
    isPushUtFileResultArray?: boolean
) {
    let content = '';

    (isPushUtFileResultArray ? appendCodeTplString(utFileResultArray, commandAndEnv) : utFileResultArray)?.forEach(
        (utFile: UtFileResult) => {
            const {testFilePath} = utFile;
            const codeSummary = createUtCodeSummary(utFile, commandAndEnv);

            content += `\`\`\`${TEST_BOT_BLOCK_SIGN}:${testFilePath}\n${
                JSON.stringify({content: codeSummary, type: 'testbot', filePath: testFilePath})
            }\n\`\`\`\n`;
        }
    );

    return content;
}

export default function createUtFileContent(
    rootPath: string,
    commandAndEnv: CommandAndEnv,
    utFileResultArray?: UtFileResult[],
    isPushUtFileResultArray?: boolean
): string {
    if (!utFileResultArray) {
        return '';
    }

    return createCodeMdWithSubViewContent(rootPath, utFileResultArray, commandAndEnv, isPushUtFileResultArray);
}
