import {LoggerRoleInstance} from '../../../Logger.js';
import {FILE_NAME_SIGN_$} from './contans.js';

export function getFileExtension(filePath: string) {
    if (!filePath) {
        return '';
    }
    const matches = /\.([^.]+)$/.exec(filePath);
    return matches ? matches[1] : '';
}

export function getFileName(filePath: string) {
    if (!filePath) {
        return '';
    }
    const matches = /([^\\\/]+)$/.exec(filePath);
    return matches ? matches[1] : '';
}

export function tryToWithDefaultNull<T>(fn: () => T, logger?: LoggerRoleInstance) {
    try {
        return fn();
    }
    catch (e: any) {
        logger?.error(e.message);
        return null;
    }
}

// 获得相对路径
export function getRelativePath(rootPath: string, path: string) {
    return path.substring(rootPath.length);
}

export function escapeFilePathSign(filePath: string) {
    return filePath.startsWith(FILE_NAME_SIGN_$) ? filePath.substring(FILE_NAME_SIGN_$.length) : filePath;
}
