import {execSync} from 'node:child_process';

// 检查是否存在合适 java 运行环境
export function isOsExitJava() {
    // 是否有 java 命令
    try {
        const result = execSync('java -version');
        if (result.toString().includes('command not found')) {
            return false;
        }
        return true;
    }
    catch {
        return false;
    }
}

// 是否有 maven 环境
export function isOsExitMaven() {
    try {
        const result = execSync('mvn -v');
        if (result.toString().includes('command not found')) {
            return false;
        }
        return true;
    }
    catch {
        return false;
    }
}

// 是否有 gradle 环境
export function isOsExitGradle() {
    try {
        const result = execSync('gradle -v');
        if (result.toString().includes('command not found')) {
            return false;
        }
        return true;
    }
    catch {
        return false;
    }
}

export function isOsAvaliable() {
    return isOsExitJava() && (isOsExitMaven() || isOsExitGradle());
}
