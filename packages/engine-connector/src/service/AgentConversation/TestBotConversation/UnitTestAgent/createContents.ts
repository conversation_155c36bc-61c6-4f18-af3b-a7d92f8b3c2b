import {FILE_NAME_SIGN_$, TEST_BOT_BLOCK_SIGN} from './contans.js';
import type {AgentResult, GenerateProcess} from './types.js';
import {getRelativePath} from './utils.js';

export function createFirstGenerateContent(process?: GenerateProcess): string {
    const {firstGenerate} = process ?? {};

    if (!firstGenerate) {
        return '';
    }

    let content = '';

    content += firstGenerate.caseName?.length
        ? `\`\`\`${TEST_BOT_BLOCK_SIGN}:${FILE_NAME_SIGN_$ + firstGenerate.testFilePath}\n${
            JSON.stringify({
                content: firstGenerate
                    .caseName
                    ?.join('\n'),
                type: 'testbot',
                filePath: FILE_NAME_SIGN_$ + firstGenerate.testFilePath,
            })
        }\n\`\`\`\n`
        : '';

    if (firstGenerate.state === 'SUCCEED') {
        content += firstGenerate.summary;
    }

    return content;
}

export function createAnalysisPanel(process?: GenerateProcess) {
    if (process?.srcFileScan) {
        const statusTransformer: Record<string, 'inProgress' | 'success'> = {
            RUNNING: 'inProgress',
            SUCCEED: 'success',
        };

        return {
            status: statusTransformer[process.srcFileScan.state],
            content: process.srcFileScan.srcFilesName,
        };
    }
}

export function createSrcFileScanContent(process?: GenerateProcess): string {
    if (process?.srcFileScan && process.srcFileScan.state === 'SUCCEED') {
        return process.srcFileScan.summary;
    }
    return '';
}

export function detectErrorMsg(result: AgentResult): string {
    const {process} = result;

    // 状态导致的错误
    if (['FAILED', 'ABORT', 'SKIPPED'].includes(result.status)) {
        return (result.message ?? 'UTAgent未知错误') + '\n';
    }

    // 环境检测，只有 UNUSABLE时，显示异常信息
    if (process?.envCheck && process.envCheck.envState === 'UNUSABLE') {
        return process.envCheck.errorMsg.msg + '\n\n' + process.envCheck.errorMsg.repairSuggest + '\n';
    }

    return '';
}

export function getLoadingMsg(result: AgentResult, cmd: string) {
    const {process} = result;

    if (process?.srcFileScan?.state === 'RUNNING' && !process.firstGenerate) {
        return '正在为您分析文件';
    }

    if (process?.firstGenerate?.state === 'RUNNING') {
        return '正在为您生成单测方法';
    }

    if (process?.srcFileScan?.state === 'SUCCEED' && result.status === 'RUNNING') {
        return cmd === 'diff' || cmd === 'gen' ? '正在为您执行并验证用例' : '';
    }
}
