import _ from 'lodash';
import {AgentConversationType} from '@comate/plugin-shared-internals';
import {diffLines} from 'diff';
import {UnitTestAgent} from './UnitTestAgent/index.js';
import {ConversationBase, UserDetail} from '../ConversationBase.js';
import type {Reply, ExecutePayload} from './UnitTestAgent/types.js';
import {type Repo} from '../../Composer/types.js';
import type {VirtualEditor} from '@comate/plugin-shared-internals';
import type {LoggerRoleInstance} from '../../Logger.js';
import type {Task} from '../../Composer/tasks/Task.js';
import {FILE_NAME_SIGN_$, PREVIEW_FILE_CONTENT_TAG, UESABLE_FILE_CONTENT_TAG} from './UnitTestAgent/contans.js';
import {sleep} from '../../Composer/utils/index.js';
import {PreviewComposerTask} from '../../Composer/tasks/PreviewComposerTask.js';
import {escapeFilePathSign} from './UnitTestAgent/utils.js';
import type {MultiAcceptData} from '../../Track/api.js';

function equalFilePath(filePath?: string) {
    return (task: {absolutePath: string}) => {
        return filePath ? task.absolutePath === filePath : true;
    };
}

function isPreviewTask(task?: Task): task is PreviewComposerTask {
    return task instanceof PreviewComposerTask;
}

interface AcceptedFilePathContent {
    originalContent: string;
    accepted: boolean;
}

type MessageFromViewView =
    | {action: 'file-view' | 'file-reject' | 'file-accept' | 'file-accept-usedable-all', id: string, filePath: string}
    | {action: 'file-accept-all-ut' | 'file-reject-all', id: string}
    | {action: 'file-reject-all', shell: 'string', id: string};

export class TestBotConversation extends ConversationBase<string, string, MessageFromViewView> {
    readonly type: AgentConversationType = AgentConversationType.TestBotConversation;

    private filePathAndContentMapping: Record<string, string> = {};
    private acceptedFilePathContentMap = new Map<string, AcceptedFilePathContent>();
    private reply: Reply | null = null;
    private readonly utAgent: UnitTestAgent;
    private openDocumentHistoryIndexedByMessageId: Map<string, string[]> = new Map();
    private steamingDiff: {
        currentDiffContentKey: string;
        currentDiffContent: string;
        steaming?: boolean;
    } = {
        currentDiffContentKey: '', // key
        currentDiffContent: '', // 内容
        steaming: false, // 是否正在流式输出
    };

    constructor(
        protected readonly repo: Repo,
        protected readonly virtualEditor: VirtualEditor,
        onStatusChange: any,
        onMessageChange: any,
        logger: LoggerRoleInstance,
        userDetail: UserDetail
    ) {
        super(repo, virtualEditor, onStatusChange, onMessageChange, logger, 'UNIT_TEST_AGENT', userDetail);
        const userInfo = {
            name: this.userDetail.name,
            ide: process.env.IDE,
            ideVersion: this.userDetail.ideVersion,
        };

        this.utAgent = new UnitTestAgent(this.logger, userInfo, this.repo.rootPath);
    }

    async init() {
        // 因为流程上 resource 初始化 和 执行是异步的，这里有增加判断
        if (!await this.utAgent.checkExecutable()) {
            this.logger.error('Not found ut executable binary file, redownload it');
            await this.utAgent.downloadResources();
        }
        // 重置缓存数据
        this.resetOriginalContent();
        this.openDocumentHistoryIndexedByMessageId.set(this.runningMessage!.id, []);
    }

    async *startWork(payload: ExecutePayload) {
        if (!this.runningMessage) {
            return {
                content: '',
            };
        }

        await this.init();

        const {license} = this.userDetail;
        const reply = this.utAgent.run({...payload, extra: {...payload.extra, license}}, this.runningMessage.id);

        for await (const chunk of reply) {
            const {status, content, filePathAndContentMapping, ...rest} = chunk;

            this.filePathAndContentMapping = filePathAndContentMapping ?? {};
            this.runningMessage.status = status;
            this.reply = {status, content: await this.runningMessage.composer.update(content), ...rest};

            await this.rewriteOriginalContent();

            yield JSON.stringify(this.reply);

            this.autoSteamingOutputDiff();
        }

        this.endReplyContent();
        yield JSON.stringify(this.reply);
    }

    async handleNewMessage(payload: MessageFromViewView) {
        // 初始化原文件缓存，防止进行中采纳，原文件内容没有更新，并且要放到采纳动作前，防止将新的内容更新后再计算diff
        await this.initOriginalContent();
        switch (payload.action) {
            case 'file-view': {
                // 兼容特殊场景的文件名
                if (payload.filePath.startsWith(FILE_NAME_SIGN_$)) {
                    const realAbsolutePath = payload.filePath.substring(FILE_NAME_SIGN_$.length);
                    const newTask = this.createTask(realAbsolutePath);
                    await newTask.update(this.filePathAndContentMapping[payload.filePath], true);
                    await newTask.openDiff();
                }
                else {
                    const task = this.findTaskByFilePath(payload.filePath);
                    await this.rewrite(task);
                    await task?.openDiff();
                }
                break;
            }
            case 'file-accept': {
                // 兼容特殊场景的文件名
                if (payload.filePath.startsWith(FILE_NAME_SIGN_$)) {
                    const realAbsolutePath = payload.filePath.substring(FILE_NAME_SIGN_$.length);
                    const newTask = this.createTask(realAbsolutePath);
                    newTask.setForeground(this.runningMessage?.composer.foreground ?? false);
                    await newTask.update(this.filePathAndContentMapping[payload.filePath], true);
                    await newTask.saveAndOpen();
                }
                else {
                    const task = this.findTaskByFilePath(payload.filePath);
                    await this.rewrite(task);
                    await (isPreviewTask(task) ? task?.saveAndOpen() : task?.save());
                }
                break;
            }
            case 'file-reject': {
                // 兼容特殊场景的文件名
                if (payload.filePath.startsWith(FILE_NAME_SIGN_$)) {
                    const realAbsolutePath = payload.filePath.substring(FILE_NAME_SIGN_$.length);
                    const newTask = this.createTask(realAbsolutePath);
                    newTask.setForeground(this.runningMessage?.composer.foreground ?? false);
                    const oldTask = this.findTaskByFilePath(payload.filePath);
                    await newTask.update(this.filePathAndContentMapping[payload.filePath], true);
                    isPreviewTask(oldTask) && await newTask.rewriteOriginalContentFromTask(oldTask);
                    await newTask?.revert();
                }
                else {
                    const task = this.findTaskByFilePath(payload.filePath);
                    await task?.revert();
                }
                break;
            }
            case 'file-accept-all-ut': {
                const tasks = this.findAllRealTasks();
                for (const task of tasks) {
                    await this.rewrite(task);
                    await task.save();
                }
                break;
            }
            case 'file-reject-all': {
                const tasks = this.findAllRealTasks();
                for (const task of tasks) {
                    await task.revert();
                }
                break;
            }
            case 'file-accept-usedable-all': {
                const tasks = this.findAllRealTasks();
                for (const task of tasks) {
                    const usedableContent = this.getUsableContent(task.absolutePath);
                    if (usedableContent) {
                        await this.rewrite(task, usedableContent);
                        await task.save();
                    }
                }
                break;
            }
        }

        await this.updateTaskContent();
        this.acceptLogOnActionEnd(payload);
    }

    setConversationChunk(chunk: string) {
        this.runningMessage!.content = chunk;
    }

    async stopGenerating() {
        // 如果有正在流，终止
        this.steamingDiff.steaming = false;
        if (this.runningMessage) {
            await this.utAgent.stop(this.runningMessage.id);
            this.runningMessage.composer.tasks.forEach(task => task.cancel());
            this.endReplyContent();
            await super.stopGenerating();
        }
    }

    private getUsableContent(absolutePath: string) {
        return this.filePathAndContentMapping[UESABLE_FILE_CONTENT_TAG + absolutePath];
    }

    private findAllRealTasks() {
        return this.runningMessage?.composer.tasks.filter(task => !task.absolutePath.startsWith(FILE_NAME_SIGN_$))
            ?? [];
    }

    private async rewriteOriginalContent() {
        if (this.filePathAndContentMapping) {
            for (const key of Object.keys(this.filePathAndContentMapping)) {
                const task = this.findTaskByFilePath(key);
                isPreviewTask(task) && await task.rewriteOriginalContentIfEmpty(escapeFilePathSign(task.absolutePath));
            }
        }
    }

    private findTaskByFilePath(filePath: string) {
        return this.runningMessage?.composer.tasks.find(equalFilePath(filePath));
    }

    private createTask(filePath: string) {
        return new PreviewComposerTask(this.virtualEditor, this.repo, 'preview', filePath);
    }

    private endReplyContent() {
        const {status, loadingMsg, testbotMapping} = this.utAgent.end();
        if (this.reply) {
            this.reply.loadingMsg = loadingMsg;
            this.reply.status = status;
            this.reply.testbotMapping = testbotMapping;
            this.runningMessage!.status = status;
            this.runningMessage!.content = JSON.stringify(this.reply);
        }
    }

    private async updateTaskContent() {
        // 这里的 markdown 会因为序列化多次，在 preview block ```{}``` 的 {} 中补充多个转义字符
        if (this.reply) {
            const markdown = await this.runningMessage!.composer.update(this.reply!.content);
            this.reply.content = markdown;
            this.runningMessage!.content = JSON.stringify(this.reply);
        }
    }

    // 当有 PREVIEW_FILE_CONTENT_TAG 的 key 存在时，自动打开 diff 窗口，当切换新文件的时候，自动切换。
    private async autoSteamingOutputDiff() {
        const keys = Object.keys(this.filePathAndContentMapping).filter(key =>
            key.startsWith(PREVIEW_FILE_CONTENT_TAG)
        );
        if (!keys.length) {
            return;
        }

        // 展示最后一条
        const lastKey = keys[keys.length - 1];
        const previousKey = this.steamingDiff.currentDiffContentKey;
        const previousContent = this.steamingDiff.currentDiffContent;

        // 更新 lastKey 和 lastContent
        this.steamingDiff.currentDiffContentKey = lastKey;
        this.steamingDiff.currentDiffContent = this.filePathAndContentMapping[lastKey];

        if (previousContent === this.steamingDiff.currentDiffContent) {
            return;
        }

        if (previousKey !== this.steamingDiff.currentDiffContentKey && this.steamingDiff.steaming === true) {
            // 切换时，停止现有的 diff 流
            this.steamingDiff.steaming = false;
            // 一个间隔流的时间，让下一次流被终止
            await sleep(100);
        }

        if (this.steamingDiff.steaming === false) {
            this.steamingDiff.steaming = true;
            await this.streamOutputDiff(previousContent);
            this.steamingDiff.steaming = false;
        }
    }

    private async openVirtualDiffDocumentNessary(absolutePath: string, leftContent: string, rightContent: string) {
        // 每条消息均是一个 id，ut 只进行一轮对话
        const assistantMsgId = this.runningMessage!.id;
        const previousOpenDocumentPaths = this.openDocumentHistoryIndexedByMessageId.get(assistantMsgId)!;
        const lastOpenedDocumentPath = _.last(previousOpenDocumentPaths);
        const activeDocument = await this.virtualEditor.getActiveDocument();
        const stayAtPreviousDocument = activeDocument.existed
            && activeDocument?.scheme === 'diff'
            && activeDocument.absolutePath === lastOpenedDocumentPath;
        if (!lastOpenedDocumentPath || stayAtPreviousDocument) {
            await this.virtualEditor.openVirtualDiffDocument({
                absolutePath,
                content: leftContent,
                modified: rightContent,
            });
            this.openDocumentHistoryIndexedByMessageId.set(assistantMsgId, [
                ...previousOpenDocumentPaths,
                absolutePath,
            ]);
        }
    }

    private async streamOutputDiff(previousContent: string, streaming: boolean = true) {
        let absolutePath = this.steamingDiff.currentDiffContentKey.substring(PREVIEW_FILE_CONTENT_TAG.length);
        // 兼容特殊场景的文件名
        absolutePath = escapeFilePathSign(absolutePath);
        const rightContent = this.steamingDiff.currentDiffContent;
        const leftContent = (await this.virtualEditor.getDocument({absolutePath})).content ?? '';
        if (rightContent.trim()) {
            await this.openVirtualDiffDocumentNessary(absolutePath, leftContent, rightContent);
            if (streaming) {
                // 先将原来的内容刷新到 diff 窗口
                const needReplacePreviousContent = previousContent && previousContent.length < rightContent.length;
                const steamingContent = needReplacePreviousContent
                    ? rightContent.slice(previousContent.length).split('\n')
                    : rightContent.split('\n');
                let content = needReplacePreviousContent ? previousContent + '\n' : '';
                for (const line of steamingContent) {
                    // 如果停止流，跳出
                    if (this.steamingDiff.steaming === false) {
                        break;
                    }
                    await sleep(100);
                    content += line + '\n';
                    await this.virtualEditor.replaceVirtualDiffModifiedDocument({absolutePath, content});
                }
                // 如果有跳出的情况，确保最后一行显示完整
                await this.virtualEditor.replaceVirtualDiffModifiedDocument({absolutePath, content: rightContent});
            }
            else {
                await this.virtualEditor.replaceVirtualDiffModifiedDocument({absolutePath, content: rightContent});
            }
        }
    }

    private async rewrite(task?: Task, content?: string) {
        if (!task || !(task instanceof PreviewComposerTask)) {
            return;
        }
        const realContent = content ?? this.filePathAndContentMapping[task.absolutePath];
        await task.update(realContent, true);
    }

    private getAllFilePath() {
        return Object
            .entries(this.filePathAndContentMapping)
            .filter(([filePath]) => {
                // 特殊场景的兼容处理：中断时，只有$开头内容
                if (
                    filePath.startsWith(FILE_NAME_SIGN_$)
                    && !this.filePathAndContentMapping![escapeFilePathSign(filePath)]
                ) {
                    return true;
                }
                return filePath.startsWith(this.repo.rootPath);
            });
    }

    private getUploadCreatedContent(): [
        string, // 文件路径
        string, // 生成的代码
        boolean, // 是否接受
    ][] {
        return this
            .getAllFilePath()
            .map(([filePath]) => [
                escapeFilePathSign(filePath),
                // 只有中断时，这里的内容才是 $ 开头的文件内容
                this.filePathAndContentMapping[filePath],
                this.acceptedFilePathContentMap.get(escapeFilePathSign(filePath))?.accepted ?? false,
            ]);
    }

    private updateAcceptedFilePathContentMap(absolutePath: string, acceptedContent: Partial<AcceptedFilePathContent>) {
        const oldContent = this.acceptedFilePathContentMap.get(absolutePath) ?? {accepted: false, originalContent: ''};
        this.acceptedFilePathContentMap.set(absolutePath, {
            accepted: acceptedContent.accepted ?? oldContent.accepted,
            originalContent: acceptedContent.originalContent ?? oldContent.originalContent,
        });
    }

    private getUploadActionContent(payload?: MessageFromViewView): [
        string, // 文件路径
        string, // 生成的代码
        boolean, // 是否接受
    ][] {
        switch (payload?.action) {
            case 'file-accept': {
                return this.getUploadCreatedContent().map(([filePath, content, accepted]) => {
                    if (filePath === payload.filePath) {
                        this.updateAcceptedFilePathContentMap(filePath, {accepted: true});
                        return [filePath, content, true];
                    }
                    else if (
                        payload.filePath.startsWith(FILE_NAME_SIGN_$)
                        && filePath === escapeFilePathSign(payload.filePath)
                    ) {
                        this.updateAcceptedFilePathContentMap(filePath, {accepted: true});
                        return [filePath, this.filePathAndContentMapping[payload.filePath], true];
                    }
                    return [filePath, content, accepted];
                });
            }
            case 'file-reject': {
                return this.getUploadCreatedContent().map(([filePath, content, accepted]) => {
                    if (filePath === payload.filePath) {
                        this.updateAcceptedFilePathContentMap(filePath, {accepted: false});
                        return [filePath, content, false];
                    }
                    else if (
                        payload.filePath.startsWith(FILE_NAME_SIGN_$)
                        && filePath === escapeFilePathSign(payload.filePath)
                    ) {
                        this.updateAcceptedFilePathContentMap(filePath, {accepted: false});
                        return [filePath, this.filePathAndContentMapping[payload.filePath], false];
                    }
                    return [filePath, content, accepted];
                });
            }
            case 'file-accept-all-ut': {
                return this.getUploadCreatedContent().map(([filePath, content]) => [filePath, content, true]);
            }
            case 'file-reject-all': {
                return this.getUploadCreatedContent().map(([filePath, content]) => [filePath, content, false]);
            }
            case 'file-accept-usedable-all': {
                return this.getUploadCreatedContent().map(([filePath]) => [
                    // 如果采纳已验证，没有验证代码的为空，并将采纳结果变更为 false
                    filePath,
                    this.getUsableContent(filePath) ?? '',
                    true,
                ]);
            }
            case 'file-view': {
                return [];
            }
            default: {
                return this.getUploadCreatedContent();
            }
        }
    }

    private async acceptLogOnActionEnd(payload?: MessageFromViewView) {
        const uuid = this.runningMessage?.id ?? '';
        const multiAcceptData: MultiAcceptData[] = this
            .getUploadActionContent(payload)
            .map(([filePath, content, accepted], index) => {
                return ({
                    id: `${index}`,
                    accepted,
                    path: filePath,
                    row: '1',
                    col: '1',
                    generatedContent: this.getDiffContent(filePath, content).join('\n'),
                });
            });

        multiAcceptData.length && this.acceptLog(uuid, multiAcceptData);
    }

    async acceptLogOnEnd() {
        await this.initOriginalContent();
        this.acceptLogOnActionEnd();
    }

    private async getDocumentText(absolutePath: string) {
        const {content, existed} = await this.virtualEditor.getDocument({absolutePath});
        return existed ? content : '';
    }

    private async initOriginalContent() {
        const absolutePathList = this
            .getAllFilePath()
            .map(([absolutePath]) => escapeFilePathSign(absolutePath));
        for (const absolutePath of absolutePathList) {
            if (!this.acceptedFilePathContentMap.has(absolutePath)) {
                const fileContent = await this.getDocumentText(escapeFilePathSign(absolutePath));
                this.acceptedFilePathContentMap.set(absolutePath, {
                    originalContent: fileContent,
                    accepted: false,
                });
            }
        }
    }

    private resetOriginalContent() {
        this.acceptedFilePathContentMap = new Map();
    }

    private getDiffContent(absolutePath: string, content?: string) {
        const realAbsolutePath = escapeFilePathSign(absolutePath);
        const originalContent = this.acceptedFilePathContentMap.get(realAbsolutePath)?.originalContent ?? '';
        const newContent = content ?? this.filePathAndContentMapping[absolutePath];
        const diff = diffLines(originalContent, newContent);
        return diff.filter(part => part.added).map(part => part.value);
    }
}
