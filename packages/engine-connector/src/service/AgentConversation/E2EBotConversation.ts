import _ from 'lodash';
import {isNotJunk} from 'junk';
import {statSync, readdirSync, readFileSync} from 'node:fs';
import {join, relative} from 'node:path';
import {Tokens, lexer} from 'marked';
import {AcceptState} from '../Composer/types.js';
import {ChatResponse, analyze, chat, illegalTextFile} from '../Composer/api.js';
import {createCodeChunk, createTerminalCodeChunk} from '../Composer/utils/codeChunk.js';
import {ConversationBase} from './ConversationBase.js';
import {AgentConversationStatus, AgentConversationType} from '@comate/plugin-shared-internals';
import {extractLocalhostAddressFromOutput} from '../../utils/terminal.js';
import {isPortAvailable} from '../../utils/net.js';
import {MultiAcceptData} from '../Track/api.js';
import {getComposerCodeMeta} from '../Composer/Composer.js';
import {PreviewComposerTask} from '../Composer/tasks/PreviewComposerTask.js';
import {DeleteComposerTask} from '../Composer/tasks/DeleteComposerTask.js';

function equalFilePathOrUniqKey(uniqKey?: string) {
    return (task: {key: string}) => {
        return uniqKey ? task.key === uniqKey : true;
    };
}

const excludeFolderNames = ['node_modules', 'dist', 'site-packages'];
function getFilesFromDir(dirPath: string, excludeFolderNames: string[]): string[] {
    const files: string[] = [];
    const entries = readdirSync(dirPath, {withFileTypes: true});
    for (const entry of entries) {
        const filePath = join(dirPath, entry.name);
        if (entry.isDirectory()) {
            files.push(...getFilesFromDir(join(dirPath, entry.name), excludeFolderNames));
        }
        else {
            files.push(filePath);
        }
    }
    return files;
}

const {uniqBy} = _;

interface ComposerFileAcceptRecord {
    fileOperate: {
        acceptedFiles: string[];
        rejectedFiles: string[];
    };
}

type RelativePath = string;
interface Knowledge {
    id: RelativePath;
    name: RelativePath;
    type: 'FILE' | 'FOLDER' | 'CURRENT_FILE' | 'TERMINAL';
}

interface Payload {
    query: string;
    knowledgeList: Knowledge[];
    conversationId?: number;
    extend: ComposerFileAcceptRecord;
}

type MessageFromViewView =
    | {action: 'file-view' | 'file-reject' | 'file-accept' | 'copy', id: string, filePath: string}
    | {action: 'file-accept-all' | 'file-reject-all' | 'regenerate-chat', id: string}
    | {action: 'execute-shell' | 'insert-shell', shell: 'string', id: string};

export class E2EBotConversation extends ConversationBase<string, string, MessageFromViewView> {
    type: AgentConversationType = AgentConversationType.E2EBotConversation;
    status = AgentConversationStatus.Ready;

    async *startWork(payload: Omit<Payload, 'extend'>) {
        const params = await this.injectExtendParamsIfPreviousMessageExisted(payload);
        const chunks = this.createConversation(params);
        for await (const chunk of chunks) {
            yield chunk;
        }
    }

    private async injectExtendParamsIfPreviousMessageExisted(payload: Omit<Payload, 'extend'>): Promise<Payload> {
        const extend: ComposerFileAcceptRecord = {
            fileOperate: {
                acceptedFiles: [],
                rejectedFiles: [],
            },
        };
        if (this.previousMessage) {
            const message = this.previousMessage;
            // 多轮步骤1：把状态塞到参数里
            for (const task of message.composer.tasks) {
                if (task.accepted === AcceptState.UNTOUCHED) {
                    await task.revert();
                }
                if (task.accepted === AcceptState.ACCEPT) {
                    extend.fileOperate.acceptedFiles.push(`${task.action}:${task.filePath}`);
                }
                else {
                    extend.fileOperate.rejectedFiles.push(`${task.action}:${task.filePath}`);
                }
            }
            message.content = await this.previousMessage.composer.toMarkdown();
            // 多轮步骤2：把这一轮对话所有相关的文件都丢到knowledgeList里
            for (const msg of this.assistantMessages) {
                const abstractedContextFiles: Knowledge[] = msg
                    .composer
                    .tasks
                    .map(task => ({
                        type: 'FILE' as const,
                        name: task.filePath,
                        id: task.filePath,
                    }))
                    .filter(task => task.id);
                payload.knowledgeList = uniqBy([...payload.knowledgeList, ...abstractedContextFiles], 'id');
            }
            return {
                query: payload.query,
                conversationId: message.conversationId,
                knowledgeList: payload.knowledgeList,
                extend,
            };
        }
        return {
            query: payload.query,
            knowledgeList: payload.knowledgeList,
            extend,
        };
    }

    setConversationChunk(chunk: string): void {
        this.runningMessage!.content = chunk;
    }

    private async *createConversation(params: Payload) {
        // 第一步：创建一条user消息+一条loading中的消息先发出去
        const message = this.runningMessage!;
        try {
            // 第一步：通过anaylze接口创建对话，获取conversationId用于后端处理多轮
            const analyzeResult = await analyze({
                username: this.repo.username,
                query: params.query,
                conversationId: params?.conversationId,
                slash: 'Composer',
                contexts: [],
            });
            const {conversationId, intentStrategies, extend} = analyzeResult.data.data;
            const contextsSerializedFromIntent = intentStrategies?.reduce<Knowledge[]>(
                (result, intent) => {
                    if (intent.rule.startsWith('FOLDER')) {
                        result.push({
                            type: 'FILE',
                            id: intent.context.id,
                            name: intent.context.id,
                        });
                    }
                    else if (intent.rule.startsWith('FILE')) {
                        result.push({
                            type: 'FILE',
                            id: intent.context.id,
                            name: intent.context.id,
                        });
                    }
                    else if (intent.rule === 'CURRENT_FILE_READ') {
                        result.push({
                            type: 'FILE',
                            id: 'currentFile',
                            name: '当前文件',
                        });
                    }
                    return result;
                },
                []
            );
            const {existed, absolutePath} = await this.virtualEditor.getActiveDocument();
            const activeDocumentRelativePath = existed
                ? relative(
                    this.repo.rootPath.replace(/\\/g, '/'),
                    absolutePath.replace(/\\/g, '/')
                )
                : undefined;
            params.knowledgeList = (contextsSerializedFromIntent || params.knowledgeList).map(
                (knowledge: Knowledge) => {
                    if (knowledge.type === 'FILE' && knowledge.id === activeDocumentRelativePath) {
                        return {...knowledge, type: 'CURRENT_FILE', id: 'currentFile'};
                    }
                    return knowledge;
                }
            );
            message.conversationId = conversationId;
            // 第二步：如果要实现外挂知识集，或者检索代码向量在这里处理
            // 第三步：将knowledgeList转换成codeChunks
            const {codeChunks, contexts} = await this.getCodeChunksAndContexts(params);
            const chatStreamResponse = await chat(this.virtualEditor, this.repo.rootPath, {
                username: this.repo.username,
                query: params.query,
                contexts,
                conversationId,
                codeChunks,
                extend: {...params.extend, ...extend},
            });

            // 第四步：等生成流式输出
            let generatorChunk: ChatResponse | null = null;
            for await (const chunk of chatStreamResponse) {
                generatorChunk = chunk;
                // 第五步每一个流式输出要对外更新消息
                const content = message.composer.update(generatorChunk.content.detail.summary);
                // 第六步：如果消息结束要生成用于上报采纳的uuid
                if (chunk.content.detail.end) {
                    const adoptUuid = chunk.content.detail.adoptionUuid;
                    if (adoptUuid) {
                        message.composer.tasks.forEach(task => task.setAdoptUuid(adoptUuid));
                    }
                    // TODO：临时记录日志，用于测试定位问题用，后续删除
                    // writeFile(
                    //     join(homedir(), '.comate-engine', `chat-${conversationId}`),
                    //     JSON.stringify({contexts, codeChunks, content: generatorChunk.content.detail.summary}),
                    //     () => {}
                    // );
                }
                yield content;
            }
            message.status = 'success';
        }
        catch (ex) {
            const errorMsg = ex instanceof Error ? ex.message : '生成失败';
            const content = await message.composer.update(errorMsg);
            message.status = 'failed';
            yield content;
        }
    }

    async beforeStop() {
        if (this.runningMessage) {
            this.runningMessage.composer.tasks.forEach(task => task.cancel());
            this.runningMessage.content = await this.runningMessage.composer.toMarkdown();
        }
    }

    private async getCodeChunksAndContexts(params: Payload) {
        const codeChunks = [];
        const contexts = [];
        let totalSize = 0;
        for await (const knowledge of params.knowledgeList) {
            if (knowledge.type === 'TERMINAL') {
                contexts.push({type: 'TERMINAL', id: knowledge.id, name: knowledge.name});
                const {output} = await this.virtualEditor.getLatestOutputFromActiveTerminal();
                codeChunks.push(createTerminalCodeChunk(this.repo.rootPath, output));
            }
            else if (knowledge.type === 'FILE' || knowledge.type === 'FOLDER') {
                if (knowledge.id === 'currentFile') {
                    const {absolutePath, content, existed} = await this.virtualEditor.getActiveDocument();
                    if (existed) {
                        const relativePath = relative(
                            this.repo.rootPath.replace(/\\/g, '/'),
                            absolutePath.replace(/\\/g, '/')
                        );
                        contexts.push({
                            type: 'CURRENT_FILE',
                            path: relativePath,
                            id: knowledge.id,
                            name: knowledge.name,
                        });
                        codeChunks.push(
                            createCodeChunk(this.repo.rootPath, relativePath, 'FILE', content)
                        );
                    }
                }
                else {
                    const filePath = join(this.repo.rootPath, knowledge.id);
                    try {
                        const stats = statSync(filePath);
                        if (stats.isDirectory()) {
                            contexts.push({type: 'FOLDER', id: knowledge.id, name: knowledge.id});

                            const directoryDir = join(this.repo.rootPath, knowledge.id);
                            const filePathsInDirs = getFilesFromDir(directoryDir, excludeFolderNames);
                            for (const absolutePath of filePathsInDirs) {
                                if (totalSize > 100 * 1000) {
                                    break;
                                }
                                const relativePath = relative(
                                    this.repo.rootPath.replace(/\\/g, '/'),
                                    absolutePath.replace(/\\/g, '/')
                                );
                                const {illegal, type} = await illegalTextFile(this.repo.username, absolutePath);
                                if (isNotJunk(absolutePath)) {
                                    if (illegal) {
                                        codeChunks.push(
                                            createCodeChunk(this.repo.rootPath, relativePath, type, '')
                                        );
                                    }
                                    else {
                                        const content = readFileSync(absolutePath, 'utf-8');
                                        if (content.length < 5 * 1000) {
                                            totalSize += content.length;
                                            codeChunks.push(
                                                createCodeChunk(this.repo.rootPath, relativePath, type, content)
                                            );
                                        }
                                    }
                                }
                            }
                        }
                        else if (isNotJunk(filePath)) {
                            const {content, existed} = await this.virtualEditor.getDocument({absolutePath: filePath});
                            if (existed) {
                                contexts.push({type: 'FILE', id: knowledge.id, name: knowledge.id});
                                codeChunks.push(createCodeChunk(this.repo.rootPath, knowledge.id, 'FILE', content));
                            }
                        }
                    }
                    catch (ex) {
                        //
                    }
                }
            }
            else if (knowledge.type === 'CURRENT_FILE') {
                const {existed, absolutePath, content} = await this.virtualEditor.getActiveDocument();
                if (existed) {
                    const relativePath = relative(
                        this.repo.rootPath.replace(/\\/g, '/'),
                        absolutePath.replace(/\\/g, '/')
                    );
                    contexts.push({
                        type: 'CURRENT_FILE',
                        path: relativePath,
                        id: knowledge.id,
                        name: knowledge.name,
                    });
                    codeChunks.push(
                        createCodeChunk(this.repo.rootPath, relativePath, 'FILE', content)
                    );
                }
            }
        }

        return {contexts, codeChunks: uniqBy(codeChunks, chunk => chunk.path)};
    }

    sanitizeDiffString(input: string) {
        const removeDiffLineAnnotation = (inputs: string[]) => inputs.filter(line => !/@@(.*)@@/.test(line));
        const lines = input.split(/\r?\n/);
        if (
            lines[0].startsWith('---')
            && lines[1].startsWith('+++')
        ) {
            return removeDiffLineAnnotation(lines.slice(2)).join('\n');
        }

        return removeDiffLineAnnotation(lines).join('\n');
    }

    async acceptWithLog() {
        const markdown = await this.runningMessage!.composer.toMarkdown();
        const message = this.runningMessage!;
        const block = lexer(markdown);
        const composerCodeBlock = block.filter(block => !!getComposerCodeMeta(block)) as Array<Tokens.Code>;
        const multiSuggestions = composerCodeBlock.reduce<MultiAcceptData[]>(
            (result, block, i) => {
                if (block.type === 'code') {
                    const task = message.composer.tasks[i];
                    if (!task || task instanceof PreviewComposerTask || task instanceof DeleteComposerTask) {
                        return result;
                    }

                    result.push({
                        id: i.toString(),
                        path: task.filePath,
                        row: '1',
                        col: '1',
                        generatedContent: this.sanitizeDiffString(block.text),
                        accepted: task.accepted === AcceptState.ACCEPT,
                    });
                }
                return result;
            },
            []
        );
        this.acceptLog(message.id, multiSuggestions);
    }

    async handleNewMessage(payload: MessageFromViewView) {
        switch (payload.action) {
            case 'execute-shell': {
                const {output} = await this.virtualEditor.executeTerminalShell({
                    cmd: payload.shell,
                    cwd: this.repo.rootPath,
                    duration: 5000,
                    run: true,
                });
                if (payload.id !== this.runningMessage?.id) {
                    return;
                }
                // 检查output字符串中是否存在http(s?)://localhost:任意端口，或者ip+端口之类的地址
                const localhostAddress = extractLocalhostAddressFromOutput(output);
                if (localhostAddress) {
                    const canParse = URL.canParse(localhostAddress);
                    if (canParse) {
                        const {port} = new URL(localhostAddress);
                        const portUsed = await isPortAvailable(Number(port));
                        if (portUsed) {
                            this.runningMessage.composer.updatePreview(localhostAddress);
                        }
                    }
                }
                break;
            }
            case 'insert-shell': {
                await this.virtualEditor.executeTerminalShell({
                    cmd: payload.shell,
                    cwd: this.repo.rootPath,
                    duration: 5000,
                    run: false,
                });
                break;
            }
            case 'file-view': {
                const task = this.runningMessage?.composer.tasks.find(equalFilePathOrUniqKey(payload.filePath));
                await task?.openDiff();
                break;
            }
            case 'file-accept': {
                const message = this.assistantMessages.find(({id}) => id === payload.id);
                const task = message?.composer.tasks.find(equalFilePathOrUniqKey(payload.filePath));
                await task?.save();
                break;
            }
            case 'file-reject': {
                const task = this.runningMessage?.composer.tasks.find(equalFilePathOrUniqKey(payload.filePath));
                await task?.revert();
                break;
            }
            case 'file-accept-all': {
                const tasks = this.runningMessage?.composer.tasks || [];
                for (const task of tasks) {
                    await task.save();
                }
                break;
            }
            case 'file-reject-all': {
                const tasks = this.runningMessage?.composer.tasks || [];
                for (const task of tasks) {
                    await task.revert();
                }
                break;
            }
            case 'regenerate-chat': {
                const previous = _.last(this.userMessages);
                if (previous) {
                    super.startNewWork({
                        conversationId: this.id,
                        conversationType: this.type,
                        messageType: 'add-message',
                        payload: previous.payload,
                    });
                }
            }
        }

        const markdown = await this.runningMessage!.composer.toMarkdown();
        const message = this.runningMessage!;
        if (payload.action !== 'regenerate-chat' && payload.action !== 'file-view') {
            this.acceptWithLog();
        }
        message.content = markdown;
    }

    async acceptLogOnEnd() {
        this.acceptWithLog();
    }
}
