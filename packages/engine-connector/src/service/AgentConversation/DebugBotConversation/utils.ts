import {marked} from 'marked';
import {DebugAgentCodeContextItem, BlockItem} from '@comate/plugin-shared-internals';
import {CodeContext, CodeSnippetItem} from '../../../api/debug.js';

export function equalFilePath(filePath?: string) {
    return (task: {absolutePath: string}) => {
        return filePath ? task.absolutePath === filePath : true;
    };
}

// 检索的代码不会都展示，目前只展示 relatedCode 和 errorCode 类型的
export function buildDisplayCodeItem(context: CodeContext) {
    const items: DebugAgentCodeContextItem[] = [];
    const resolve = (chunk: CodeSnippetItem) => {
        if (chunk.type === 'relatedCode' || chunk.type === 'errorCode') {
            items.push({
                type: chunk.type,
                filePath: chunk.filePath,
                startLineNum: chunk.startLineNum,
                endLineNum: chunk.endLineNum,
            });
        }
    };
    const resolveMybatis = (chunk: Record<string, any>) => {
        for (const item of Object.values(chunk)) {
            resolve(item);
            if (typeof item.retrievalFile === 'object') {
                resolve(item.retrievalFile);
            }
        }
    };
    for (const [key, codeChunks] of Object.entries(context)) {
        for (const chunk of codeChunks) {
            if (key === 'mybatis') {
                resolveMybatis(chunk);
                continue;
            }
            resolve(chunk);
        }
    }
    return items;
}

export const getQuery = (code = '') => `请帮我分析终端的报错日志，并提供解决方案。只解决第一个报错\n${code}`;

const isReplaceTask = (lang: string) => lang.startsWith('replaceFrom:') || lang.startsWith('replaceTo:');

export function parseMarkdownToBlocks(markdown: string) {
    const blocks = marked.lexer(markdown);
    const res: BlockItem[] = [];
    let lastCodeBlock: BlockItem | null = null;
    for (const item of blocks) {
        if (item.type !== 'code' || typeof item.lang !== 'string' || !isReplaceTask(item.lang)) {
            res.push({type: 'text', content: item.raw});
            continue;
        }
        const result = /^(replaceFrom|replaceTo):(.*)$/.exec(item.lang);
        if (!result) {
            continue;
        }
        const action = result[1];
        const filePath = result[2].trim();
        if (action === 'replaceFrom') {
            lastCodeBlock = {
                type: 'code',
                content: item.raw,
                replaceToFileData: {filePath, from: item.text, to: ''},
            };
            res.push(lastCodeBlock);
        }
        else if (lastCodeBlock && lastCodeBlock.replaceToFileData) {
            lastCodeBlock.replaceToFileData.to = item.text;
            lastCodeBlock = null;
        }
    }
    return res;
}
