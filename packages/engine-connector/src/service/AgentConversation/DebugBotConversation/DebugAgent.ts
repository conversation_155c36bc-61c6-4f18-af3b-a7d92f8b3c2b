import _ from 'lodash';
import {
    AGENT_DEBUG_CUSTOM_ACTION,
    DebugAgentPayload,
    DebugAgentResponse,
    SSEProcessor,
} from '@comate/plugin-shared-internals';
import {
    analyze,
    AnalyzeData,
    autoDebugFix,
    AutoDebugFixResponse,
    CodeContext,
    runStatus,
    taskStatus,
} from '../../../api/debug.js';
import {Composer} from '../../Composer/Composer.js';
import {UserDetailWithId} from '../../User.js';
import {DataManager} from './DataManager.js';
import {IdeEventAction, MessageData, RunTaskResult} from './types.js';
import {buildDisplayCodeItem, getQuery, parseMarkdownToBlocks} from './utils.js';

const {isEmpty} = _;

export class DebugAgent {
    private readonly dataManager = new DataManager({status: 'loading', processLog: []});
    // 接口分析结果
    analyzeResult: AnalyzeData | undefined = undefined;
    // 本地代码检索结果
    private codeContext: CodeContext | undefined = undefined;
    private sendIdeAction: (name: IdeEventAction, data?: any) => Promise<any>;
    // 运行命令名称
    private commandName = '';
    private composer?: Composer | undefined = undefined;
    private runTaskResult: RunTaskResult | undefined = undefined;

    constructor(
        private readonly conversationId: string,
        private readonly username: string,
        private readonly payload: DebugAgentPayload | undefined,
        private readonly userDetail: UserDetailWithId,
        onMessageChange: (name: string, data: any) => Promise<any>
    ) {
        this.sendIdeAction = (action: IdeEventAction, data?: any) => {
            return onMessageChange(AGENT_DEBUG_CUSTOM_ACTION, {action, data});
        };
    }

    get query() {
        const {code, customPrompt} = this.payload ?? {};
        return customPrompt ? customPrompt : getQuery(code);
    }

    async *generate(composer: Composer) {
        this.composer = composer;
        try {
            // 1. 更新初始状态
            yield this.dataManager.data;
            // 2. 分析错误原因
            yield await this.analyzePayload();
            // 3. 请求 IDE 做代码检索
            yield await this.searchCode();
            // 提前获取执行命令名称
            this.getCommandName();
            // 4. 请求接口生成修复代码
            for await (const content of this.generateContent()) {
                const composedMarkdown = await this.composer.update(content);
                yield this.dataManager.updateContent(composedMarkdown);
            }
            // 5. 更新到可执行状态
            yield await this.readyToRun();
        }
        catch (ex) {
            const errorMsg = ex instanceof Error ? ex.message : '生成失败';
            yield this.dataManager.stop('error', errorMsg);
        }
    }

    /**
     * 根据 payload 进行分析并生成代码，没有状态也不会对结果进行处理
     * @yields 返回生成的代码对象，包含：错误原因、检索到的代码片段、模型返回的 markdown 内容
     */
    async *generateCode() {
        const result: DebugAgentResponse = {};
        try {
            await this.analyzePayload();
            result.errorReason = this.analyzeResult?.errorReason;
            yield {result, end: false};

            await this.searchCode();
            result.contexts = this.codeContext ? buildDisplayCodeItem(this.codeContext) : [];
            yield {result, end: false};

            for await (const content of this.generateContent()) {
                result.content = content;
                result.blocks = parseMarkdownToBlocks(content);
                yield {result, end: false};
            }
            yield {result, end: true};
        }
        catch (ex) {
            const errorMsg = ex instanceof Error ? ex.message : '生成失败';
            result.content = errorMsg;
            yield {result, end: true};
        }
    }

    /** 取消生成，任何时候都可以调用，状态切换到 cancelled 状态 */
    async cancel() {
        const statusBeforeCancel = this.dataManager.data.status;
        const data = this.dataManager.stop(
            'cancelled',
            '任务已停止。',
            () => this.reportConversationStatusChange('ABORT')
        );
        if (this.composer && statusBeforeCancel === 'fixing') {
            this.composer.tasks.forEach(task => task.finish !== true && task.cancel());
            data.content = await this.composer.toMarkdown();
        }
        return data;
    }

    async run(beforeRunning: (data: MessageData) => void) {
        this.dataManager.running();
        beforeRunning(this.dataManager.data);
        try {
            const res = await this.sendIdeAction('run', this.payload?.command) as RunTaskResult;
            this.runTaskResult = res;
            this.reportRunCommandDone(res.runResult);
            return this.dataManager.updateRunResult(res.status, res.description ?? '获取运行结果失败');
        }
        catch (e) {
            return this.dataManager.stop('error', e instanceof Error ? e.message : '运行失败，未知错误');
        }
    }

    /** 主动结束任务，和取消的区别是，取消是生成中，结束是运行完成后用户选择 */
    abortTask() {
        this.composer?.tasks.forEach(task => task.closeVirtualDocument());
        return this.dataManager.stop(
            'aborted',
            '您已结束任务，修复任务结束。',
            () => this.reportConversationStatusChange('ABORT')
        );
    }

    getRestartPayload() {
        if (this.runTaskResult?.restartPayload) {
            const payload = {
                ...this.payload,
                ...this.runTaskResult.restartPayload,
            }
            return Promise.resolve(payload);
        }
        return this.sendIdeAction('restart', this.payload);
    }

    getRegeneratePayload() {
        return this.payload;
    }

    /**
     * 请求远端分析问题，并更新状态到 analyzing
     */
    private async analyzePayload() {
        const {platform, cwd, contexts} = this.payload ?? {};
        const analyzeResult = await analyze({
            userDetail: {...this.userDetail, username: this.username},
            conversationId: this.conversationId,
            query: this.query,
            cwd,
            platform,
            contexts: typeof contexts === 'object' ? JSON.stringify(contexts) : contexts,
        });
        this.analyzeResult = analyzeResult;
        return this.dataManager.analyzing(analyzeResult.errorReason);
    }

    /**
     * 根据分析结果请求 IDE 进行代码检索，并更新状态到 fixing
     */
    private async searchCode() {
        const codeContext = this.analyzeResult && !isEmpty(this.analyzeResult.context)
            ? await this.sendIdeAction('search', this.analyzeResult.context) as CodeContext
            : {};
        this.codeContext = codeContext;
        return this.dataManager.fixing(buildDisplayCodeItem(codeContext));
    }

    private async getCommandName() {
        if (this.payload?.command) {
            this.commandName = this.payload?.command.commandLine;
            return;
        }
        this.commandName = await this.sendIdeAction('commandName');
    }

    /** 根据分析结果和检索的代码生成结果 */
    private async *generateContent() {
        const chatStreamResponse = await autoDebugFix({
            userDetail: {...this.userDetail, username: this.username},
            query: this.query,
            context: this.codeContext,
            device: this.userDetail.device,
            recordId: this.analyzeResult?.recordId,
            conversationId: this.conversationId,
            queryType: this.analyzeResult?.queryType,
        });
        const processor = new SSEProcessor<AutoDebugFixResponse>(chatStreamResponse);
        let fullText = '';
        for await (const chunk of processor.processSSE()) {
            const {data, code, message} = chunk;
            if (code !== 200) {
                throw new Error(message);
            }
            if (!data) {
                continue;
            }
            if (data.isEnd) {
                return fullText;
            }
            fullText += data.result;
            yield fullText;
        }
    }

    /** 更新状态到 operating 等待用户操作 */
    private async readyToRun() {
        if (this.composer) {
            // 到这里后面不会再更新视图了，如果有没完成的 task 肯定就一直转圈了，所以需要强制结束
            const content = await this.composer.safeToMarkdown();
            this.dataManager.updateContent(content);
        }
        if (this.payload?.needsValidation === false) {
            return this.dataManager.exit();
        }

        const commandName = typeof this.commandName === 'string' && this.commandName
            ? ` \`${this.commandName}\``
            : '';

        if (!commandName && this.payload?.command) {
            return this.dataManager.stop('error', '获取运行命令失败，您可以「采纳」后手动重新运行命令并进行验证。');
        }
        return this.dataManager.operating(`我将使用您「采纳」的文件，尝试重新运行${commandName}`);
    }

    private reportRunCommandDone(status: string) {
        if (this.analyzeResult && this.analyzeResult.recordId) {
            const recordId = this.analyzeResult.recordId;
            runStatus({userDetail: {...this.userDetail, username: this.username}, recordId, status});
            this.reportConversationStatusChange(status);
        }
    }

    private reportConversationStatusChange(status: string) {
        if (this.conversationId) {
            taskStatus({
                userDetail: {...this.userDetail, username: this.username},
                conversationId: this.conversationId,
                status
            });
        }
    }
}
