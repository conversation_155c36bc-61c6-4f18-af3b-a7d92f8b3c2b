import {DebugAgentCodeContextItem} from '@comate/plugin-shared-internals';
import {MessageData} from './types.js';

const progressStatus = ['loading', 'analyzing', 'fixing', 'operating', 'running', 'failed'];

/** 管理状态和数据，主要限制状态的流转 */
export class DataManager {
    constructor(private _data: MessageData) {}

    static create() {
        return new DataManager({status: 'loading', processLog: []});
    }

    static from(data: MessageData) {
        return new DataManager(data);
    }

    get data(): MessageData {
        return this._data;
    }

    analyzing(errorReason: string) {
        if (this._data.status === 'loading') {
            this._data.status = 'analyzing';
            this._data.errorReason = errorReason;
        }
        return this._data;
    }

    fixing(codeContextItems: DebugAgentCodeContextItem[]) {
        if (this._data.status === 'analyzing') {
            this._data.status = 'fixing';
            this._data.contexts = codeContextItems;
        }
        return this._data;
    }

    operating(description: string) {
        if (this._data.status === 'fixing') {
            this._data.status = 'operating';
            this._data.processLog.push(description);
        }
        return this._data;
    }

    running() {
        if (this._data.status === 'operating' || this._data.status === 'failed') {
            this._data.status = 'running';
        }
        return this._data;
    }

    updateContent(content: string) {
        if (this._data.status === 'fixing') {
            this._data.content = content;
        }
        return this._data;
    }

    updateRunResult(status: 'error' | 'success' | 'failed', description: string) {
        if (this._data.status === 'running') {
            this._data.status = status;
            this._data.processLog.push(description);
        }
        return this._data;
    }

    stop(status: 'error' | 'aborted' | 'cancelled', description: string, callback?: () => void) {
        if (progressStatus.includes(this._data.status)) {
            this._data.status = status;
            this._data.processLog.push(description);
            callback && callback();
        }
        return this._data;
    }

    exit() {
        if (this._data.status === 'fixing') {
            this._data.status = 'exited';
        }
        return this._data;
    }
}
