import {DebugAgentPayload, DebugAgentResponse} from '@comate/plugin-shared-internals';

export type DebugStatus =
    | 'loading'
    | 'analyzing'
    | 'fixing'
    // 等待用户采纳/拒绝
    | 'operating'
    // 运行中
    | 'running'
    // 运行失败但能继续
    | 'failed'
    // 运行成功
    | 'success'
    // 运行失败且不能继续，可能是报错了
    | 'error'
    // 用户主动结束任务
    | 'aborted'
    // 生成过程中取消
    | 'cancelled'
    // 不需要验证时，修复后提前退出
    | 'exited';

export interface MessageData extends DebugAgentResponse {
    status: DebugStatus;
    processLog: string[];
}

export type MessageAction =
    | 'debug-run'
    | 'file-accept-all'
    | 'file-reject-all'
    | 'debug-abort'
    | 'debug-restart'
    | 'regenerate-chat';

export type MessageFromViewView =
    | {action: 'file-view' | 'file-reject' | 'file-accept', id: string, filePath: string}
    | {action: MessageAction, id: string}
    | {action: 'insert-shell', id: string, shell: string};

export interface RunTaskResult {
    status: 'success' | 'error' | 'failed';
    description: string;
    runResult: string;
    restartPayload?: DebugAgentPayload;
}

export type IdeEventAction = 'run' | 'search' | 'commandName' | 'restart';

export interface IdeEventData {
    action: IdeEventAction;
    data?: any;
}
