import {
    AgentConversationType,
    AgentConversationStatus,
    DebugAgentPayload,
} from '@comate/plugin-shared-internals';
import {codeAdopt, codeGenerate} from '../../../api/debug.js';
import {MultiAcceptData} from '../../Track/api.js';
import {AcceptState, ComposerCodeStatus, Repo} from '../../Composer/types.js';
import {PreviewComposerTask} from '../../Composer/tasks/PreviewComposerTask.js';
import {
    AssistantMessage,
    ConversationBase,
    omitUselessMessageProperties,
    UserDetail,
} from '../ConversationBase.js';
import {
    MessageData,
    IdeEventData,
    MessageFromViewView,
} from './types.js';
import {equalFilePath} from './utils.js';
import {DebugAgent} from './DebugAgent.js';

const acceptOrRejectActions = [
    'file-reject',
    'file-accept',
    'file-accept-all',
    'file-reject-all',
    'insert-shell',
];

export class DebugBotConversation extends ConversationBase<MessageData, any> {
    type: AgentConversationType = AgentConversationType.DebugBotConversation;
    // 主动更新视图
    private updateMessageView: () => void;
    private debugAgent: DebugAgent | null = null;

    constructor(
        repo: Repo,
        virtualEditor: any,
        onStatusChange: any,
        onMessageChange: any,
        logger: any,
        private readonly sendCustomEventToIde: (name: string, data: IdeEventData) => Promise<any>,
        userDetail: UserDetail
    ) {
        super(repo, virtualEditor, onStatusChange, onMessageChange, logger, 'DEBUG_AGENT', userDetail);
        this.updateMessageView = () =>
            onMessageChange(
                this.id,
                this.messages.map(omitUselessMessageProperties(['composer']))
            );
    }

    async *startWork(payload: DebugAgentPayload) {
        const message = this.runningMessage!;
        this.debugAgent = new DebugAgent(
            this.id,
            this.repo.username,
            payload,
            this.userDetail,
            this.sendCustomEventToIde
        );
        for await (const data of this.debugAgent.generate(message.composer)) {
            yield data;
        }
    }

    setConversationChunk(chunk: MessageData) {
        const lastMessage = this.runningMessage!;
        if (lastMessage && lastMessage.role === 'assistant' && lastMessage.status === 'inProgress') {
            lastMessage.content = chunk;
        }
    }

    async handleNewMessage(payload: MessageFromViewView) {
        switch (payload.action) {
            case 'file-view': {
                const task = this.runningMessage?.composer.tasks.find(equalFilePath(payload.filePath));
                await task?.openDiff();
                break;
            }
            case 'file-accept': {
                const task = this.runningMessage?.composer.tasks.find(equalFilePath(payload.filePath));
                await task?.save();
                break;
            }
            case 'file-reject': {
                const task = this.runningMessage?.composer.tasks.find(equalFilePath(payload.filePath));
                await task?.revert();
                break;
            }
            case 'file-accept-all': {
                const tasks = this.runningMessage?.composer.tasks || [];
                for (const task of tasks) {
                    await task.save();
                }
                break;
            }
            case 'file-reject-all': {
                const tasks = this.runningMessage?.composer.tasks || [];
                for (const task of tasks) {
                    await task.revert();
                }
                break;
            }
            case 'debug-run': {
                this.debugRunTask();
                break;
            }
            case 'debug-abort': {
                this.abortTask();
                break;
            }
            case 'debug-restart': {
                this.restartTask();
                break;
            }
            case 'regenerate-chat': {
                this.regenerate();
                break;
            }
            case 'insert-shell': {
                await this.virtualEditor.executeTerminalShell({
                    cmd: payload.shell,
                    cwd: this.repo.rootPath,
                    duration: 5000,
                    run: false,
                });
                break;
            }
        }
        const message = this.runningMessage! as AssistantMessage<MessageData>;
        const markdown = await message.composer.toMarkdown();
        message.content.content = markdown;
        if (acceptOrRejectActions.includes(payload.action)) {
            this.telemetryGeneration(true);
        }
    }

    async acceptLogOnEnd() {
        this.telemetryGeneration(false);
    }

    protected async beforeStop() {
        if (!this.debugAgent || !this.runningMessage) {
            return;
        }
        const message = this.runningMessage as AssistantMessage<MessageData>;
        message.content = await this.debugAgent.cancel();
    }

    /** 点击运行时触发，将任务重新转为 RUNNING 状态，消息和状态都自定义管理 */
    private async debugRunTask() {
        if (!this.debugAgent) {
            return;
        }
        this.updateStatus(AgentConversationStatus.Running);
        const message = this.runningMessage! as AssistantMessage<MessageData>;
        message.content = await this.debugAgent.run(
            data => {
                // 异步执行前更新一下状态
                message.content = data;
                this.updateMessageView();
            }
        );
        if (this.status === AgentConversationStatus.Running) {
            this.updateStatus(AgentConversationStatus.Completed);
        }
        this.updateMessageView();
    }

    private abortTask() {
        if (!this.debugAgent) {
            return;
        }
        const message = this.runningMessage! as AssistantMessage<MessageData>;
        message.content = this.debugAgent.abortTask();
        this.stopGenerating();
    }

    private async restartTask() {
        if (!this.debugAgent) {
            return;
        }
        const payload = await this.debugAgent.getRestartPayload();
        this.onConversationMessage({
            conversationId: this.id,
            messageType: 'add-message',
            payload,
        });
    }

    private async regenerate() {
        if (!this.debugAgent) {
            return;
        }
        this.startNewWork({
            payload: this.debugAgent.getRegeneratePayload(),
            conversationId: this.id,
            messageType: 'add-message',
            conversationType: AgentConversationType.DebugBotConversation,
        });
    }

    private async telemetryGeneration(isAcceptOrReject: boolean = false) {
        const message = this.runningMessage! as AssistantMessage<MessageData>;
        const multiSuggestions: MultiAcceptData[] = [];
        const tasks = message.composer?.tasks || [];
        for (let i = 0; i < tasks.length; i++) {
            const task = tasks[i];
            if (task instanceof PreviewComposerTask
                || (task.status !== ComposerCodeStatus.DONE)
            ) {
                continue;
            }
            multiSuggestions.push({
                id: i.toString(),
                path: task.filePath,
                row: '1',
                col: '1',
                generatedContent: task.calculatedDiff,
                accepted: task.accepted === AcceptState.ACCEPT,
            });
        }
        this.acceptLog(message.id, multiSuggestions);
        if (isAcceptOrReject) {
            this.onCodeAccepted(message.id, multiSuggestions);
        }
        else {
            this.onCodeGenerated(message.id, multiSuggestions);
        }
    }

    // AutoDebug 的采纳上报
    private onCodeAccepted(uuid: string, multiSuggestions: MultiAcceptData[]) {
        const adoptedCode = multiSuggestions
            .filter(v => v.accepted === true)
            .map(v => v.generatedContent)
            .join('\n');
        codeAdopt({
            userDetail: {...this.userDetail, username: this.repo.username},
            uuid,
            adoptedCode,
        });
    }

    private onCodeGenerated(uuid: string, multiSuggestions: MultiAcceptData[]) {
        if (!this.debugAgent || !this.debugAgent.analyzeResult) {
            return;
        }
        const generatedCode = multiSuggestions.map(v => v.generatedContent).join('\n');
        codeGenerate({
            userDetail: {...this.userDetail, username: this.repo.username},
            uuid,
            generatedCode,
            recordId: this.debugAgent.analyzeResult.recordId,
        });
    }
}
