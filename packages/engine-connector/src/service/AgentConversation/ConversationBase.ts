import crypto from 'node:crypto';
import _ from 'lodash';
import {
    AgentConversationStatus,
    AgentConversationType,
    AgentPayload,
    VirtualEditor,
} from '@comate/plugin-shared-internals';
import {generate, Model} from '../../api/generate.js';
import {isJetbrains} from '../../utils/checkIDE.js';
import {LoggerRoleInstance} from '../Logger.js';
import {Composer} from '../Composer/Composer.js';
import {Repo} from '../Composer/types.js';
import {MultiAcceptData, acceptCode} from '../Track/api.js';
import {UserDetailForServer, UserDetailWithId} from '../User.js';
import {parseRemoteUri} from '../../utils/track.js';

const {findLast, omit} = _;

export interface Message {
    id: string;
    status: 'inProgress' | 'success' | 'failed';
}

export interface UserMessage extends Message {
    role: 'user';
    content: string;
    // 提问时引用的代码块，渲染时用的是 markdown 组件，可以自定义其他内容
    code?: string;
    /** 原始的payload，用于重新生成对话 */
    payload: any;
}

export interface AssistantMessage<T = any> extends Message {
    role: 'assistant';
    composer: Composer;
    content: T;
    conversationId?: number;
}

export type UserDetail = UserDetailWithId & UserDetailForServer;

export const omitUselessMessageProperties = (keys: string[]) => (message: UserMessage | AssistantMessage) => {
    if (message.role === 'user') {
        return message;
    }
    else {
        return omit(message, keys);
    }
};

export abstract class ConversationBase<YieldChunk = string, WebviewContent = any, MessageOperationPayload = any> {
    readonly logger: LoggerRoleInstance;
    readonly id: string;
    readonly model: Model;
    readonly userDetail: UserDetail;
    abstract type: AgentConversationType;
    status: AgentConversationStatus = AgentConversationStatus.Ready;
    foreground: boolean = false;
    messages: Array<UserMessage | AssistantMessage<WebviewContent>> = [];
    cancellationToken: boolean = false;

    constructor(
        protected readonly repo: Repo,
        protected readonly virtualEditor: VirtualEditor,
        private onStatusChange: any,
        private onMessageChange: any,
        logger: LoggerRoleInstance,
        model: Model,
        userDetail: UserDetailWithId & UserDetailForServer
    ) {
        this.id = crypto.randomUUID(); // 使用 UUID 生成唯一的任务 ID
        this.model = model;
        this.logger = logger;
        this.userDetail = userDetail;
    }

    get runningMessage() {
        return findLast(
            this.messages,
            (message): message is AssistantMessage<WebviewContent> => message.role === 'assistant'
        );
    }

    get assistantMessages() {
        return this.messages.filter((message): message is AssistantMessage<WebviewContent> =>
            message.role === 'assistant'
        );
    }

    get userMessages() {
        return this.messages.filter((message): message is UserMessage => message.role === 'user');
    }

    get previousMessage() {
        const messages = this.assistantMessages;
        return messages[Math.max(0, messages.length - 2)];
    }

    async onConversationMessage(agentPayload: AgentPayload<MessageOperationPayload>) {
        this.logger.info('onConversationMessage', agentPayload);
        switch (agentPayload.messageType) {
            case 'add-message':
                // 智能体内部实现也可以自己发新的一轮
                this.startNewWork(agentPayload);
                break;
            case 'stop-generating':
                await this.stopGenerating();
                break;
            case 'message-operation': {
                // 处理需要转发到message的消息
                await this.handleNewMessage(agentPayload?.payload);
                this.onMessageChange(this.id, this.messages);
                break;
            }
        }
    }

    private getGenerateParams(uuid?: string, multiSuggestions?: {fileContent: MultiAcceptData[]}) {
        return {
            uuid,
            // 暂时先区分一下 后续等ide初始化engine带上ide参数
            ide: isJetbrains ? 'jetbrains' : 'vscode',
            username: this.userDetail.username,
            key: this.userDetail.license,
            repo: parseRemoteUri(this.repo.repoUrl || '').repository,
            branch: this.repo.branch,
            path: multiSuggestions?.fileContent?.[0]?.path ?? 'agent.java',
            content: 'agent',
            model: this.model,
            device: this.userDetail.device,
            multiSuggestions,
        };
    }

    async generateMessageId() {
        try {
            const params = this.getGenerateParams();
            const uuid = await generate(params);
            return uuid;
        }
        catch (ex) {
            this.logger.error('generate message id error', (ex as Error).message);
            return crypto.randomUUID();
        }
    }

    // 追加一轮，一轮表现为一个用户消息，同时启动智能体的逻辑开始处理
    // 第一轮默认会被调用，参数为用户的query或智能体自己写死
    async startNewWork(agentPayload: AgentPayload<MessageOperationPayload>) {
        this.logger.info('startNewWork', agentPayload);

        this.cancellationToken = false;
        this.updateStatus(AgentConversationStatus.Running);

        const userMessage: UserMessage = {
            id: crypto.randomUUID(),
            role: 'user',
            status: 'success',
            // TODO：还是把message消息和操作消息分开来比较好
            // @ts-expect-error
            content: agentPayload.payload?.query ?? '',
            // @ts-expect-error
            code: agentPayload.payload?.code,
            payload: agentPayload.payload,
        };
        this.messages.push(userMessage);

        const composer = new Composer(
            this.virtualEditor,
            this.repo,
            this.logger
        );
        composer.setForeground(this.foreground);
        const assistantMessageId = await this.generateMessageId();
        const assistantMessage: AssistantMessage = {
            id: assistantMessageId,
            role: 'assistant',
            status: 'inProgress',
            content: '',
            composer,
        };
        this.messages.push(assistantMessage);
        // 返回第一次对话保证问题消息展示
        this.onMessageChange(this.id, this.messages);
        const iterator = this.startWork(agentPayload.payload);
        while (true) {
            if (this.cancellationToken) {
                break;
            }

            try {
                let checkToken: NodeJS.Timer | null = null;
                const result = await Promise.race([
                    iterator.next(),
                    new Promise<{done: boolean, value: any}>(resolve => {
                        checkToken = setInterval(() => {
                            if (this.cancellationToken) {
                                checkToken && clearInterval(checkToken);
                                resolve({done: true, value: 'stop-generating'});
                            }
                        }, 500);
                    }),
                ]);
                checkToken && clearInterval(checkToken);
                const {value: chunk, done} = result;
                // 用户点击停止生成，任务可能还在执行，但是肯定不会再发新的消息
                if ((done && chunk === 'stop-generating') || this.cancellationToken) {
                    break;
                }
                // 正常结束，done为true，最后一次chunk为空
                if (done) {
                    this.updateStatus(AgentConversationStatus.Completed);
                    this.acceptLogOnEnd();
                    break;
                }
                this.setConversationChunk(chunk);
                this.onMessageChange(this.id, this.messages);
            }
            catch (ex) {
                this.updateStatus(AgentConversationStatus.Failed);
                break;
            }
        }
    }

    // 业务实现自己的setConversatoin
    abstract setConversationChunk(chunk: YieldChunk): void;

    updateStatus(status: AgentConversationStatus): void {
        this.status = status;
        this.onStatusChange(this.id);
    }

    async stopGenerating() {
        this.beforeStop && await this.beforeStop();
        this.logger.info('stopGenerating');
        this.cancellationToken = true;
        this.afterStop && await this.afterStop();
        this.onMessageChange(this.id, this.messages);
        this.updateStatus(AgentConversationStatus.Cancelled);
        this.acceptLogOnEnd();
    }

    getConversationInfo() {
        return {
            id: this.id,
            status: this.status,
            type: this.type,
        };
    }

    setForeground(foreground: boolean) {
        this.foreground = foreground;
        this.assistantMessages.forEach(message => message.composer?.setForeground(foreground));
    }

    async acceptLog(messageId: string, data: MultiAcceptData[]) {
        const multiSuggestions = {fileContent: data};
        const params = this.getGenerateParams(messageId, multiSuggestions);
        await generate(params);
        await acceptCode({
            uuid: messageId,
            accepted: true,
            content: '',
            multiSuggestions,
        });
    }

    // 结束前清理一些内容，如果需要就覆盖
    protected beforeStop(): Promise<void> {
        return Promise.resolve();
    }
    protected afterStop(): Promise<void> {
        return Promise.resolve();
    }

    // 可以取到this.messages来调度怎么执行
    protected abstract startWork(userMessageContent: any): AsyncIterableIterator<YieldChunk>;
    protected abstract handleNewMessage(messageOperationPayload: MessageOperationPayload): void;
    protected abstract acceptLogOnEnd(): void;
}
