import {
    AgentConversationInfo,
    AgentConversationType,
    AgentMessage,
    AgentPayload,
    VirtualEditor,
} from '@comate/plugin-shared-internals';
import {LoggerRoleInstance} from '../Logger.js';
import {Mediator} from '../Mediator.js';
import {ConversationBase, omitUselessMessageProperties} from './ConversationBase.js';
import {SecuBotConversation} from './SecuBotConversation.js';
import {E2EBotConversation} from './E2EBotConversation.js';
import {DebugBotConversation} from './DebugBotConversation/index.js';
import {Repo} from '../Composer/types.js';
import _ from 'lodash';
import {TestBotConversation} from './TestBotConversation/index.js';

// 定义 ConversationManager 来管理多个任务
export class ConversationManager {
    private conversations: Map<string, ConversationBase> = new Map();
    private foregroundConversationId: string = '';

    constructor(
        private readonly repo: Repo,
        private readonly mediator: Mediator,
        private readonly virtualEditor: VirtualEditor,
        private readonly logger: LoggerRoleInstance
    ) {
    }

    addConversation(agentPayload: AgentPayload): ConversationBase | undefined {
        try {
            let conversation: any;
            switch (agentPayload.conversationType) {
                case AgentConversationType.SecuBotConversation:
                    conversation = new SecuBotConversation(
                        this.mediator,
                        this.repo,
                        this.virtualEditor,
                        this.onStatusChange.bind(this),
                        this.onMessageChange.bind(this),
                        this.logger,
                        this.mediator.userDetail()
                    );
                    break;
                case AgentConversationType.TestBotConversation:
                    conversation = new TestBotConversation(
                        this.repo,
                        this.virtualEditor,
                        this.onStatusChange.bind(this),
                        this.onMessageChange.bind(this),
                        this.logger,
                        this.mediator.userDetail()
                    );
                    break;
                case AgentConversationType.E2EBotConversation:
                    conversation = new E2EBotConversation(
                        this.repo,
                        this.virtualEditor,
                        this.onStatusChange.bind(this),
                        this.onMessageChange.bind(this),
                        this.logger,
                        'FULL_STACK_AGENT',
                        this.mediator.userDetail()
                    );
                    break;
                case AgentConversationType.DebugBotConversation:
                    conversation = new DebugBotConversation(
                        this.repo,
                        this.virtualEditor,
                        this.onStatusChange.bind(this),
                        this.onMessageChange.bind(this),
                        this.logger,
                        this.sendCustomEventToIde.bind(this),
                        this.mediator.userDetail()
                    );
                    break;
                default:
                    return;
            }

            this.conversations.set(conversation.id, conversation);
            this.setForegroundConversation(conversation.id);
            return conversation;
        }
        catch (e) {
            this.logger.error('addConversation', e);
            return;
        }
    }

    // 通知函数（例如更新Tab上的红点）
    onStatusChange(conversationId: string): void {
        const conversationInfo = this.conversations.get(conversationId)!.getConversationInfo();
        this.mediator.sendToIde('COMATE_AGENT_UPDATE_MESSAGE', {
            data: {
                type: 'conversation-status',
                conversationInfo,
            },
        });
    }

    // 如果是前台任务，则更新前台任务的内容
    onMessageChange(conversationId: string, messages: any): void {
        if (conversationId === this.foregroundConversationId) {
            this.logger.info('onMessageChange', conversationId, messages);
            const conversationInfo = this.conversations.get(conversationId)!.getConversationInfo();
            this.mediator.sendToIde('COMATE_AGENT_UPDATE_MESSAGE', {
                data: {
                    type: 'conversation-messages',
                    conversationInfo,
                    messages: messages.map(omitUselessMessageProperties(['composer'])),
                },
            });
        }
    }

    // 切换前台任务
    setForegroundConversation(conversationId: string): void {
        // todo 触发时机
        const conversation = this.conversations.get(conversationId);
        if (!conversation) {
            return;
        }

        Array.from(this.conversations.values()).forEach(
            conversation => conversation.setForeground(false)
        );
        // 设置新任务为前台任务
        conversation.setForeground(true);
        this.foregroundConversationId = conversationId;
        this.logger.info('setForegroundConversation', conversationId);

        const messages = conversation.messages.map(omitUselessMessageProperties(['composer']));
        this.onMessageChange(conversationId, messages);
    }

    // 列出所有任务
    listAllConversations(): AgentConversationInfo[] {
        const allConversations = Array
            .from(this.conversations)
            .filter(([, conversation]) => conversation.messages.length)
            .map(([, conversation]) => {
                const lastUserMessage = (conversation as E2EBotConversation).messages.slice().reverse().find(msg =>
                    msg.role === 'user'
                );
                return {
                    ...conversation.getConversationInfo(),
                    lastQuery: lastUserMessage?.content,
                };
            });
        return allConversations.reverse();
    }

    // 添加一个新对话，可能带消息，可能不带，带消息直接触发一轮work
    onNewMessage(agentPayload: AgentPayload) {
        const {conversationId} = agentPayload;
        // 已存在的conversation发送了新消息
        let conversation: ConversationBase | undefined;
        if (conversationId) {
            conversation = this.conversations.get(conversationId);
            conversation?.onConversationMessage(agentPayload);
        }
        else {
            conversation = this.addConversation(agentPayload);
        }
        if (conversation) {
            return {
                id: conversation.id,
                status: conversation.status,
                type: conversation.type,
            };
        }
    }

    /**
     * 智能体向IDE发送自定义事件
     */
    private async sendCustomEventToIde(event: string, data: any) {
        return this.mediator.sendToIde(event, data);
    }
}
