import {Logger} from './Logger.js';

export default class AppLaunchTracker {
    logger: Logger;
    lastExecutionDate: string;

    constructor(logger: Logger) {
        this.logger = logger;
        this.lastExecutionDate = '';
        this.initializeScheduler();
    }

    private initializeScheduler() {
        // 每小时检查一次
        setInterval(() => {
            this.checkAndTrackLaunch();
        }, 60 * 60 * 1000); // 1小时 = 60 * 60 * 1000毫秒

        this.checkAndTrackLaunch();
    }

    private checkAndTrackLaunch() {
        const currentDate = new Date().toDateString();

        if (this.lastExecutionDate !== currentDate) {
            this.trackLaunch();
            this.lastExecutionDate = currentDate;
        }
    }

    private trackLaunch() {
        this.logger.logUploader?.logUserAction({
            category: 'engineInnerInit',
            content: 'IDE channel initialized',
        });
    }
}
