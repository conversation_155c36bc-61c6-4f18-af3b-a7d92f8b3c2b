import {LoggerRoleInstance} from './Logger.js';

export class CommonCache {
    private readonly logger: LoggerRoleInstance;
    private cache: Map<string, any> = new Map();

    constructor(logger: LoggerRoleInstance) {
        this.logger = logger;
    }

    async setCache(key: string, value: any) {
        try {
            this.cache.set(key, value);
        }
        catch (ex) {
            this.logger.error('set cache error: ', (ex as Error).message);
        }
    }

    async getCache(key: string) {
        try {
            const cache = this.cache.get(key);
            return cache;
        }
        catch (ex) {
            this.logger.error('get cache error: ', (ex as Error).message);
            return undefined;
        }
    }


}
