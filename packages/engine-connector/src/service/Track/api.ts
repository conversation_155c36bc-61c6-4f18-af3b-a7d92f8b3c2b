import {axiosInstance} from '@comate/plugin-shared-internals';
import qs from 'qs';

export interface MultiAcceptData {
    id: string;
    path: string;
    row: string;
    col: string;
    generatedContent: string;
    accepted: boolean;
}

interface AcceptCodeOptions {
    uuid: string;
    accepted: boolean;
    /** 没采纳时用户的实际输入，目前默认都传一个空字符串 */
    content: string;
    /** 用于部分采纳时回传采纳部分代码内容 */
    generatedContent?: string;
    persistent15s?: boolean;
    persistent30s?: boolean;
    persistent120s?: boolean;
    persistent300s?: boolean;
    persistent600s?: boolean;
    /** 主要用于Comate+追踪采纳了的代码 */
    acceptedBlocks?: string;
    acceptanceInfo?: AcceptanceInfo;
    /** 用于多文件，支持采纳放弃的场景（智能体） */
    multiSuggestions?: {fileContent: MultiAcceptData[]};
}

interface AcceptanceInfo {
    row: number; // 插入的行号
    col: number; // 插入的列号
    originContent: string; // 原始代码
    acceptanceContent: string; // 被采纳的代码块
    acceptanceType: ACCEPTANCE_TYPE; // 采纳类型
}

export enum ACCEPTANCE_TYPE {
    COPY = 'COPY',
    NEWFILE = 'NEWFILE',
    INSERTINTOTERMINAL = 'INSERTINTOTERMINAL',
    REPLACETOFILE = 'REPLACETOFILE',
    INSERTTOFILE = 'INSERTTOFILE',
    INSERT = 'INSERT', // accept和其他默认情况
    REPLACE = 'REPLACE',
}

interface CommonEmptyResponse {
    status: string;
    message?: string;
}

export function replaceMiddleWithAsterisk(str: string) {
    if (str.length <= 2) {
        return str;
    }

    const firstChar = str.charAt(0);
    const lastChar = str.charAt(str.length - 1);
    let middleString = '';
    for (let i = 1; i < str.length - 1; i++) {
        middleString += '*';
    }
    return firstChar + middleString + lastChar;
}

// eslint-disable-next-line max-len, no-useless-escape
const accessAndSecretKeyRegex =
    '(?:access[-_. ]?key[-_. ]?id|access[-_. ]?key[-_. ]?secret|(?:aws|ibm|gcp|oracle|jd|alibaba|baidu|tencent|api|app|client)[-_. ]?secret|(?:api|app|access|secret|security|private)[-_. ]?key|secret[-_. ]?id|securityJsCode|(?:access|private|secret|api|security|classic|fine-grained)[-_. ]?token|ak|[-_. ]sk|password|passwd|pwd)[ \t\'"]*?(?:[:=]|=>)[ \t"\']*?(?:cli_|JDC_|ghp_|github_pat_\w{22}_)?([a-z0-9\/+=]{16,})(?:["\']?;?$)';

export function hideSensitiveContent(str: string) {
    const regex = new RegExp(accessAndSecretKeyRegex, 'gim');
    return str.replace(regex, (fullMatch, subString) => {
        return fullMatch.replace(subString, replaceMiddleWithAsterisk(subString));
    });
}

export function hideSensitive(params: Record<string, any>, fields: string[]) {
    for (const field of fields) {
        if (params[field] && typeof params[field] === 'string') {
            params[field] = hideSensitiveContent(params[field]);
        }
    }
    return params;
}

export async function acceptCode(params: AcceptCodeOptions) {
    const deductedParams = hideSensitive(params, ['content', 'generatedContent']);
    if (deductedParams.acceptanceInfo) {
        deductedParams.acceptanceInfo = JSON.stringify(deductedParams.acceptanceInfo);
    }
    if (deductedParams.multiSuggestions) {
        deductedParams.multiSuggestions = JSON.stringify(deductedParams.multiSuggestions);
    }
    const query = qs.stringify(deductedParams);
    const res = await axiosInstance.put<CommonEmptyResponse>(
        '/api/generate/acceptance',
        query,
        {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
        }
    );
    return res;
}
