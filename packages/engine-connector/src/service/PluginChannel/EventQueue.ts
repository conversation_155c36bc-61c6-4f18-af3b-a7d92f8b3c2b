// 把event emitter 的功能封装成一个类，返回async iterator
export class EventQueue<T> {
    private readonly tasks: T[] = [];
    private resolves: Array<(value: IteratorResult<T>) => void> = [];
    private isStopped: boolean = false;

    extraData: any;

    push(task: T) {
        if (this.isStopped) {
            return;
        }
        if (this.resolves.length > 0) {
            const resolve = this.resolves.shift();
            resolve?.({value: task, done: false});
        }
        else {
            this.tasks.push(task);
        }
    }

    stop() {
        this.isStopped = true;
        for (const resolve of this.resolves) {
            resolve({
                done: true,
                value: undefined,
            });
        }
        this.resolves = [];
    }

    [Symbol.asyncIterator]() {
        return {
            next: (): Promise<IteratorResult<T | undefined>> => {
                if (this.tasks.length > 0) {
                    const value = this.tasks.shift();
                    return Promise.resolve({value, done: false});
                }
                else if (this.isStopped) {
                    return Promise.resolve({value: undefined, done: true});
                }
                else {
                    return new Promise(resolve => this.resolves.push(resolve));
                }
            },
        };
    }

    setExtraData(data: any) {
        this.extraData = data;
    }

    getExtraData() {
        return this.extraData;
    }
}