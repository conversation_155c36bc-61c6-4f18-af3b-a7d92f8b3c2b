import path from 'node:path';
import fs from 'node:fs';
import fsp from 'node:fs/promises';
import os from 'node:os';
import {
    RemoteConsole,
} from 'vscode-languageserver/node.js';
import dayjs from 'dayjs';
import {isObject} from '../utils/isObject.js';
import {LogUploaderProvider} from '@comate/plugin-shared-internals';
import {UserDetailWithId} from './User.js';
import {
    ACTION_QUERY_SELECTOR,
    ACTION_COMATE_PLUS_QUERY_SELECTOR,
    ACTION_COMATE_PLUS_DIAGNOSTIC_SCAN,
} from '@comate/plugin-shared-internals';
import {isDevMod} from '../utils/checkIDE.js';

const getCurrentTime = () => {
    return dayjs().format('YYYY-MM-DD HH:mm:ss');
};

const getDateString = (past?: number) => {
    return dayjs().subtract(past || 0, 'day').format('YYYY-MM-DD');
};

type LogItems = Array<object | any>;

/**
 * 最大日志行长度，超过此长度的日志将被过滤，以防日志文件过大
 * @param {number} maxLogLineLength 10KB
 */
const maxLogLineLength = 10000;

/**
 * 写入日志中需要过滤的字段
 * @param {string[]} loggerFilter
 */
const loggerFilter = [
    ACTION_QUERY_SELECTOR,
    ACTION_COMATE_PLUS_QUERY_SELECTOR,
    ACTION_COMATE_PLUS_DIAGNOSTIC_SCAN,
];

/**
 * 日志留存策略
 * @param {number} MAX_LOG_SIZE_MB - 留存大小，单位为MB
 * @param {number} MAX_LOG_DAYS - 留存天数
 */
const MAX_LOG_SIZE_MB = 10;
const MAX_LOG_DAYS = 14;

const MAX_LOG_SIZE_BYTES = MAX_LOG_SIZE_MB * 1024 * 1024; // 自动计算 不用改

/**
 * 获取可写的日志文件路径
 * @param baseLogPath 基础日志路径
 * @param maxRetries 最大重试次数
 * @returns 可写的日志文件路径
 */
function getWritableLogPath(baseLogPath: string, maxRetries: number = 10): string {
    const dir = path.dirname(baseLogPath);
    const ext = path.extname(baseLogPath);
    const baseName = path.basename(baseLogPath, ext);

    // 首先检查基础路径是否可用
    try {
        // 检查是否存在且可写
        if (fs.existsSync(baseLogPath)) {
            fs.accessSync(baseLogPath, fs.constants.W_OK);
            return baseLogPath;
        }
        // 不存在则尝试创建
        fs.writeFileSync(baseLogPath, '');
        return baseLogPath;
    }
    catch (e) {
        // 遍历目录查找已存在的日志文件或尝试创建新的
        for (let i = 1; i <= maxRetries; i++) {
            const newPath = path.join(dir, `${baseName}-${i}${ext}`);
            try {
                if (fs.existsSync(newPath)) {
                    fs.accessSync(newPath, fs.constants.W_OK);
                    return newPath;
                }
                fs.writeFileSync(newPath, '');
                return newPath;
            }
            catch (e) {
                if (i === maxRetries) {
                    throw new Error(`Failed to create writable log file after ${maxRetries} attempts`);
                }
            }
        }
    }
    throw new Error('Unable to create writable log file');
}

export interface LoggerRoleInstance {
    error: (...arr: LogItems) => void;
    warn: (...arr: LogItems) => void;
    info: (...arr: LogItems) => void;
    verbose: (...arr: LogItems) => void;
    setLogInstance: (console: RemoteConsole) => void;
    logUploader: LogUploaderProvider | undefined;
}

export class Logger {
    private logFile: string = '';
    private stream: fs.WriteStream | null = null;
    private writeQueue: Array<string> = [];
    private readonly MAX_QUEUE_SIZE = 1000;
    private writeTimer: NodeJS.Timeout | null = null;
    private isWriting: boolean = false;
    private drainPromise: Promise<void> | null = null;
    logUploader?: LogUploaderProvider;
    clientConsole?: RemoteConsole;

    constructor() {
        const name = getDateString();
        this.init(name);
        // 启动定时写入
        this.startWriteTimer();
    }

    private startWriteTimer() {
        // 每秒尝试写入队列中的日志
        this.writeTimer = setInterval(() => {
            if (!this.isWriting) {
                this.processWriteQueue().catch(e => {
                    this.logUploader?.logError('LogWriteError', `Failed to process write queue: ${e}`);
                });
            }
        }, 1000);
    }

    private async waitForDrain(): Promise<void> {
        if (!this.stream?.writable) {
            return;
        }

        if (!this.drainPromise) {
            this.drainPromise = new Promise(resolve => {
                this.stream!.once('drain', () => {
                    this.drainPromise = null;
                    resolve();
                });
            });
        }

        return this.drainPromise;
    }

    private async processWriteQueue() {
        if (!this.stream?.writable || this.writeQueue.length === 0 || this.isWriting) {
            return;
        }

        this.isWriting = true;
        try {
            while (this.writeQueue.length > 0) {
                const content = this.writeQueue[0]; // 看一下队列头，但不要立即移除
                if (!content) {
                    break;
                }

                // 检查是否需要处理背压
                if (!this.stream.write(content)) {
                    // 需要等待drain事件
                    await this.waitForDrain();
                }

                // 写入成功，现在可以安全地移除队列头
                this.writeQueue.shift();
            }
        }
        catch (e) {
            this.logUploader?.logError('LogWriteError', `Failed to write logs: ${e}`);
        }
        finally {
            this.isWriting = false;
        }
    }

    async dispose() {
        // 清理定时器
        if (this.writeTimer) {
            clearInterval(this.writeTimer);
            this.writeTimer = null;
        }

        // 尝试写入剩余的日志
        if (this.writeQueue.length > 0) {
            try {
                await this.processWriteQueue();
            }
            catch (e) {
                this.logUploader?.logError('LogWriteError', `Failed to write remaining logs during disposal: ${e}`);
            }
        }

        // 清空队列
        this.writeQueue = [];
        this.drainPromise = null;
    }

    private init(name: string) {
        const folder = path.join(os.homedir(), '.comate-engine', 'log');
        const folderExist = fs.existsSync(folder);
        if (!folderExist) {
            fs.mkdirSync(folder, {recursive: true});
        }

        try {
            const baseLogPath = path.join(os.homedir(), '.comate-engine', 'log', `${name}.log`);
            this.logFile = getWritableLogPath(baseLogPath);
        }
        catch (error) {
            this.logFile = '';
            return;
        }

        this.stream = fs.createWriteStream(this.logFile, {flags: 'a'});

        this.deleteRedundantLog(folder);
    }

    /**
     * 删除过期日志
     * @param {string} folder - 日志目录
     * 当前的策略是 保留最近MAX_LOG_DAYS天的日志 如果日志文件大小超过MAX_LOG_SIZE_BYTES 删除更早的日志文件
     */
    private deleteRedundantLog(folder: string) {
        const oldestAllowedDate = getDateString(MAX_LOG_DAYS - 1);

        fsp
            .readdir(folder)
            .then(files => {
                const logSizes: {[filename: string]: number} = {};
                let totalSize = 0;
                const filesToDelete: string[] = [];
                const statPromises: Array<Promise<void>> = [];

                // 收集日志文件
                files.forEach(file => {
                    if (file.endsWith('.log')) {
                        const filePath = path.join(folder, file);
                        const fileDate = file.split('.')[0];

                        if (dayjs(fileDate).isBefore(oldestAllowedDate)) {
                            filesToDelete.push(filePath);
                        }

                        const statPromise = fsp.stat(filePath).then(stats => {
                            logSizes[file] = stats.size;
                            totalSize += stats.size;
                        });
                        statPromises.push(statPromise);
                    }
                });

                // 与engine启动逻辑无关 不阻塞
                Promise.all(statPromises).then(() => {
                    if (totalSize > MAX_LOG_SIZE_BYTES) {
                        const sortedFiles = Object
                            .entries(logSizes)
                            .sort(([aDate], [bDate]) => bDate.localeCompare(aDate));

                        let accumulatedSize = 0;
                        for (const [file, size] of sortedFiles) {
                            accumulatedSize += size;
                            if (accumulatedSize > MAX_LOG_SIZE_BYTES) {
                                filesToDelete.push(path.join(folder, file));
                            }
                        }
                    }

                    // 删除日志文件
                    Promise
                        .all(filesToDelete.map(file => fsp.unlink(file)))
                        .then(() => {
                            this.logUploader?.logError(
                                'LogFileDelete',
                                `Deleted log files: ${filesToDelete.join(', ')}`
                            );
                            // 暂不上报剩余日志文件大小
                            // this.logUploader?.logError('LogFileSize', `Remaining log files and sizes: ${JSON.stringify(logSizes)}`);
                        })
                        .catch(error => {
                            this.logUploader?.logError( // 上报删除日志异常
                                'DeleteLogError',
                                `Error while deleting previous logs: ${error}`
                            );
                        });
                });
            })
            .catch(error => {
                this.logUploader?.logError('DeleteLogError', `Error while reading log directory: ${error}`); // 上报读取日志目录异常
            });
    }

    private concatString(...texts: LogItems) {
        return texts.reduce((acc, cur) => {
            if (isObject(cur)) {
                acc += ' ' + JSON.stringify(cur);
            }
            else {
                acc += ' ' + cur;
            }
            return acc;
        }, '');
    }

    private writeLog(level: string, role: string) {
        return (...texts: LogItems) => {
            if (!this.logFile) {
                return;
            }
            try {
                const content = `[${process.pid}] [${getCurrentTime()}] [${level}] [${role.toUpperCase()}] ${
                    this.concatString(...texts)
                }\n`;

                // 过滤指定字段
                if (level !== 'ERROR' && texts.length > 1) {
                    if (loggerFilter.some(e => texts[1] === e)) {
                        return;
                    }

                    // 过滤超长行
                    if (content.length > maxLogLineLength) {
                        this.logUploader?.logError(
                            'FilteredLog',
                            this.concatString(['__filtered_length__', texts[0], texts[1], content.length])
                        );
                        return;
                    }
                }

                // 添加到写入队列
                if (this.writeQueue.length < this.MAX_QUEUE_SIZE) {
                    this.writeQueue.push(content);
                    // 如果当前没有写入操作在进行，立即开始处理队列
                    if (!this.isWriting) {
                        this.processWriteQueue().catch(e => {
                            this.logUploader?.logError('LogWriteError', `Failed to process write queue: ${e}`);
                        });
                    }
                }
                else {
                    // 队列已满，丢弃最旧的日志
                    this.writeQueue.shift();
                    this.writeQueue.push(content);
                    this.logUploader?.logError('LogQueueFull', 'Log queue is full, dropping oldest log');
                }

                // 根据日志级别处理
                switch (level) {
                    case ('ERROR'):
                        this.logUploader?.logError(role, content);
                        this.clientConsole?.error(content);
                        break;
                    case ('WARN'):
                        this.logUploader?.logError(role, content);
                        this.clientConsole?.warn(content);
                        break;
                    case ('INFO'):
                        isPaddleErrorLog(role, texts)
                            && this.logUploader?.logError('CodeConvertSkillProvider', content);
                        isDevMod && this.clientConsole?.info(content);
                        break;
                    case ('VERBOSE'):
                        isDevMod && this.clientConsole?.log(content);
                        break;
                    default:
                        isDevMod && this.clientConsole?.log(content);
                }
            }
            catch (e) {
                if (e instanceof Error) {
                    this.logUploader?.logError('LogWriteError', `Failed to write ${level} log: ${e.message}`);
                }
            }
        };
    }

    addLogUploader(userDetail: UserDetailWithId) {
        this.logUploader = new LogUploaderProvider({
            platform: userDetail.platform,
            ideVersion: userDetail.ideVersion,
            version: userDetail.version,
            license: userDetail.license,
            username: userDetail.name,
        });
    }

    getLoggerAsRole(role: string): LoggerRoleInstance {
        return {
            logUploader: this.logUploader,
            // asyncInfo = this.writeLog('INFO');
            error: this.writeLog('ERROR', role),
            warn: this.writeLog('WARN', role),
            info: this.writeLog('INFO', role),
            verbose: this.writeLog('VERBOSE', role),
            setLogInstance: (console: RemoteConsole) => {
                this.clientConsole = console;
            },
        };
    }
}

function isPaddleErrorLog(role: string, texts: LogItems) {
    if (role === 'pluginChannel' && texts.length > 0 && isObject(texts[0])) {
        const log = texts[0] as {
            source?: string;
            level?: string;
        };
        const providers = ['CodeConvertSkillProvider'];
        if (providers.includes(log.source ?? '') && log.level === 'error') {
            return true;
        }
    }
    return false;
}
