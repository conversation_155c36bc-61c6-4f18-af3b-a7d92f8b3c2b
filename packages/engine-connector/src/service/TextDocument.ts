import {TextDocument} from 'vscode-languageserver-textdocument';
import {TextDocuments, _Connection} from 'vscode-languageserver/node.js';

export class TextDocumentService {
    documents: TextDocuments<TextDocument>;
    activeDocument: TextDocument | null = null;
    constructor() {
        this.documents = new TextDocuments(TextDocument);
    }
    // TODO textDocument 生命周期注册
    async init(ideConnect: _Connection) {
        // this.documents.onDidChangeContent(change => {
        //     let document = change.document;
        //     this.activeDocument = document;
        //     let text = document.getText();
        //     // this.logger.info('onDidChangeContent:', text)
        // })
        // this.documents.onDidClose(e => {
        //     let document = e.document;
        //     if (this.activeDocument?.uri === e.document.uri) {     // 如果关闭的文件是activeDocument，清空activeDocument
        //         this.activeDocument = null;
        //     }
        //     // this.logger.info('onDidClose:', document.uri)
        // })
        // this.documents.onDidOpen(e => {
        //     let document = e.document;
        //     this.activeDocument = document;    // 更新activeDocument
        //     // URI 的 `path` 属性包括了完整的文件路径
        //     let filePath = document.uri;

        //     // 分割文件路径为路径部分和文件名称
        //     let filename = filePath.split('/').pop();
        //     // this.logger.info('Opened document: ' + document.uri);
        //     // this.logger.info('Filename: ' + filename);
        // })
        this.documents.listen(ideConnect);
    }
}
