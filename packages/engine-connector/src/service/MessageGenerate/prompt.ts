export const saasMessagePrompt = `
当前开发的项目名称为“{{projectName}}”，项目描述如下：

\`\`\`
{{projectDescription}}
\`\`\`
当前项目修改过的文件以及修改内容如下：

{{modifications}}

以下是一些历史的提交记录，请参考它们的格式来生成提交信息：

{{history}}

现在需要使用\`git commit\`提交本次修改，请为我生成提交的信息，满足以下要求：

- 优先使用符合历史提交的语言，如果无法确定，请使用{{language}}
- 小于20个字
- 不包含换行
- 符合历史提交信息的格式和语言
- 如果历史提交信息中有无法确定的内容元素，不要生成该元素

按照以下JSON格式给出响应，不要输出任何非JSON格式的内容

\`\`\`json
{
    "message": "小于20字的提交信息"
}
\`\`\`
`;

export const internalMessagePrompt = `
当前提交需要绑定的issue id如下：

\`\`\`
{{id}}
\`\`\`

issue类型如下：

\`\`\`
{{type}}
\`\`\`

issue 标题如下：

\`\`\`
{{title}}
\`\`\`

当前项目修改过的文件以及修改内容如下：

{{modifications}}

以下是一些历史的提交记录，请参考它们的格式来生成提交信息：

{{history}}

现在需要使用\`git commit\`提交本次修改，请为我生成提交的信息，满足以下要求：

- 尽量包含id、type、title信息
- 小于20个字
- 不包含换行
- 符合历史提交信息的格式和语言
- 如果历史提交信息中有无法确定的内容元素，不要生成该元素

按照以下JSON格式给出响应，不要输出任何非JSON格式的内容

\`\`\`json
{
    "message": "小于20字的提交信息"
}
\`\`\`
`;

export const descriptionPrompt = `
有一个GitHub项目，名称是{{projectName}}，以下是项目中的\`README\`的内容：

\`\`\`markdown
{{readme}}
\`\`\`

请根据以上信息，用小于100个字的内容描述这个项目的作用。
`;
