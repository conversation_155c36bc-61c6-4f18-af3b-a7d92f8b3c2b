import fs from 'node:fs/promises';
import {simpleGit, SimpleGit} from 'simple-git';
import {execa} from 'execa';
import * as path from 'path';
import {isVscode} from '../../utils/checkIDE.js';

export function formatPrompt(promptTemplate: string, args?: Record<string, unknown>) {
    if (args) {
        return promptTemplate.replace(
            /\{\{(\w+)\}\}/g,
            (match, key) => {
                const replacement = args[key];
                return replacement === undefined ? match : String(replacement);
            }
        );
    }

    return promptTemplate;
}

export const getReadmeContent = async (rootPath: string): Promise<string> => {
    try {
        const readmePath = path.join(rootPath, 'README.md');
        await fs.access(readmePath, fs.constants.F_OK);
        const stats = await fs.stat(readmePath);
        // 处理文件大小超过限制的情况
        const maxFileSize = 1024 * 1024; // 设置读取文件大小限制为1MB
        if (stats.size > maxFileSize) {
            throw new Error(`File size of ${readmePath} (${stats.size} bytes) exceeds maximum size of ${maxFileSize} bytes. Skipping...`);
        }
        const data = await fs.readFile(readmePath, 'utf8');
        return data;
    }
    catch (error) {
        throw new Error(`Error accessing file or directory: ${error}`);
    }
};

const formatDiffBlock = (text: string) => {
    return text
        .split('\n')
        .filter(line => line.startsWith('+') || line.startsWith('-'))
        .map(line => line.replace(/^(---|\+\+\+) /, '').replace(/^[+-] */, ''));
};

const ignoreFiles = ['yarn.lock'];

export const getUntrackedFiles = async (cwd: string, paths: string[] = []) => {
    try {
        const {stdout: untrackedFiles} = await execa('git', ['ls-files', '--others', '--exclude-standard'], {cwd});
        const newFiles = paths.filter(path => untrackedFiles.split('\n').includes(path));
        return newFiles;
    }
    catch (e) {
        return [];
    }
};

export const getUntrackedFileContent = async (cwd: string, paths: string[] = []) => {
    const fileContents = await Promise.all(paths.map(async path => {
        const content = await fs.readFile(`${cwd}/${path}`, 'utf-8');
        return `${path}\n${content}`;
      }));

      return fileContents.join('\n');
}

export const getStagedDiffContent = async (cwd: string, paths: string[] = []): Promise<string> => {
    const git = simpleGit(cwd);
    const untrackedFiles = await getUntrackedFiles(cwd, paths);
    const modifiedFiles = paths.filter(path => !untrackedFiles.includes(path));
    const diffOptions = isVscode ? ['--cached'] : ['HEAD', '--', ...modifiedFiles];
    const diffResult = await git.diff(diffOptions);
    const createFileContent = await getUntrackedFileContent(cwd, untrackedFiles);
    return isVscode ? diffResult : `${diffResult}\n${createFileContent}`;
};

const getDefaultLog = async (git: SimpleGit): Promise<string> => {
    const history = await git.log({maxCount: 10, format: {message: '%s'}});
    return history.all.map(v => v.message).join('\n');
};

export const getLog = async (cwd: string): Promise<string> => {
    const git = simpleGit(cwd);
    try {
        const username = (await git.raw(['config', '--get', 'user.name'])).trim();
        const email = (await git.raw(['config', '--get', 'user.email'])).trim();
        const author = `${username} <${email}>`;
        // simple-git 提供的log方法无法过滤author
        const currentUserHistory = await git.raw([
            'log',
            '--max-count=10',
            `--author=${author}`,
            '--pretty=format:%s',
        ]);
        if (!currentUserHistory || currentUserHistory.length === 0) {
            const history = await getDefaultLog(git);
            return history;
        }
        return currentUserHistory;
    }
    catch (e) {
        const history = await getDefaultLog(git);
        return history;
    }
};

export const getCommittedDiff = async (cwd: string, paths?: string[]) => {
    try {
        const stagedDiff = await getStagedDiffContent(cwd, paths);
        if (!stagedDiff) {
            return {diffStatus: 'success', diff: ''};
        }
        const list = stagedDiff
            .split(/^diff --git /)
            .map(formatDiffBlock)
        // 过滤掉无用的文件，减少diff的长度，暂时只有 yarn.lock ，
            .filter(v => v.length && ignoreFiles.every(file => !v[0].endsWith(file)))
            .map(v => v.join(' '));
        const diffText = list.join(' ').slice(0, 10000);
        return {diffStatus: 'success', diff: diffText};
    }
    catch (ex) {
        return {diffStatus: 'fail', diff: '', errorMessage: (ex as Error).message};
    }
};

export const extractJsonFromString = (input: string): string | null => {
    const jsonBlockRegex = /```json\n([\s\S]*?)\n```/;
    const match = jsonBlockRegex.exec(input);
    return match ? match[1] : input;
};

export const formatIssueMessage = (result: string) => {
    const jsonString = extractJsonFromString(result);
    if (jsonString) {
        try {
            const value = JSON.parse(jsonString);
            return value?.message;
        }
        catch {
            throw new Error('format issue message error.');
        }
    }
    return null;
};

export function extractIdAndTitle(text: string) {
    const regex = /\[(.*?)\](.*)/;
    const match = regex.exec(text);
    if (match) {
        const id = match[1].trim();
        const title = match[2].trim();
        return {id, title};
    }

    return {id: '', title: text};
}
