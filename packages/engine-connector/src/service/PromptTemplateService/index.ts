import {LoggerRoleInstance} from '../Logger.js';
import {Mediator} from '../Mediator.js';
import {mkdir, readdir, readFile, stat, unlink} from 'node:fs/promises';
import path from 'node:path';
import {URI} from 'vscode-uri';
import dayjs from 'dayjs';
import {ChatMessage} from '@comate/plugin-engine';
import {
    ACTION_CHAT_TASK_PROGRESS,
    InformationQueryType,
    KnowledgeSetBase,
    TaskProgressChunk,
    TextModel,
    getPromptTemplateRootPath,
} from '@comate/plugin-shared-internals';
import {StringChunkStream} from '@comate/plugin-host';
import {MessageEvent} from '../PluginChannel/index.js';
import {formatPrompt, generatePromptTemplate, normalizeFilePath, parseFilePathInPromptTemplate, parsePromptFile} from './utils.js';
import {ComatePlusChatQueryPayload, PromptTemplateCreatePayload, PromptTemplateList} from '../../types/ideListener.js';
import {isFileExist} from '../../utils/fs.js';
import {modelMapMaxInputString} from '../../api/LLMApi.js';

// 留点余量，防止输入过长
const MAX_INPUT_STRING = modelMapMaxInputString[TextModel.ErnieBot4Turbo128] - 2000;
interface FileKnowledge extends KnowledgeSetBase {
    content: string;
}
export class PromptTemplateService {
    mediator: Mediator;
    logger: LoggerRoleInstance;

    constructor(mediator: Mediator, logger: LoggerRoleInstance) {
        this.mediator = mediator;
        this.logger = logger;
    }

    async init() {
        this.logger.info('PromptTemplateService init');
        this.getAll();
    }

    private async getPromptTemplateDirectoryPath() {
        try {
            const workspaceFolders = await this.mediator.getWorkspaceFolders();

            if (workspaceFolders && workspaceFolders.length > 0) {
                const uri = URI.parse(workspaceFolders[0].uri).fsPath;
                return getPromptTemplateRootPath(uri);
            }
        }
        catch (e) {
            this.logger.error(e);
        }
    }

    private async getDirectoryPromptTemplates(directoryPath: string) {
        try {
            const dints = await readdir(directoryPath, {withFileTypes: true});
            const results: PromptTemplateList[] = [];

            for (const dint of dints) {
                if (dint.isFile() && dint.name.endsWith('.prompt')) {
                    const filePath = path.join(directoryPath, '/', dint.name);
                    const {success, data} = await this.validatePromptTemplate(filePath);
                    if (success) {
                        const promptTemplateFileContent = await readFile(filePath, {encoding: 'utf8'});
                        const fileName = dint.name.replace(/\.prompt$/, '');
                        const {name, description} = parsePromptFile(fileName, promptTemplateFileContent);

                        results.unshift({uuid: fileName, name, description, ts: data?.ts || 0});
                    }
                }
            }

            return results.sort((a, b) => b.ts - a.ts);
        }
        catch (e) {
            this.logger.error(e);
            return [];
        }
    }

    async getAll() {
        try {
            const directoryPath = await this.getPromptTemplateDirectoryPath();
            if (directoryPath) {
                const isPromptTemplateFolderExist = await isFileExist(directoryPath);
                if (isPromptTemplateFolderExist) {
                    const promptTemplateList = await this.getDirectoryPromptTemplates(directoryPath);
                    return promptTemplateList;
                }
                return [];
            }
            return [];
        }
        catch (e) {
            this.logger.error(e);
            return [];
        }
    }

    async create(params?: PromptTemplateCreatePayload) {
        try {
            const directoryPath = await this.getPromptTemplateDirectoryPath();
            if (directoryPath) {
                const isPromptTemplateFolderExist = await isFileExist(directoryPath);

                if (!isPromptTemplateFolderExist) {
                    await mkdir(directoryPath, {recursive: true});
                }

                const filePath = path.join(directoryPath, `prompt_${dayjs().format('YYYY-MM-DD_HH_mm_ss')}.prompt`);
                const fileContent = generatePromptTemplate(params)

                return {
                    uri: filePath,
                    content: fileContent,
                };
            }
            return '未打开工作区，无法创建自定义指令';
        }
        catch (e) {
            this.logger.error(e);
            return '创建自定义指令失败';
        }
    }

    async save(fileName: string) {
        try {
            const promptTemplates = await this.getAll();
            const directoryPath = await this.getPromptTemplateDirectoryPath();
            if (directoryPath) {
                const filePath = path.join(directoryPath, '/', fileName + '.prompt');
                // const {success, message: validateMsg} = await this.validatePromptTemplate(filePath);

                // if (!success) {
                //     return validateMsg;
                // }

                const promptTemplateFileContent = await readFile(filePath, {encoding: 'utf8'});
                const {name} = parsePromptFile(filePath, promptTemplateFileContent);

                if (promptTemplates.find(item => item.name === name && item.uuid !== fileName) !== undefined) {
                    return '该自定义指令已存在，请重新命名';
                }
                return;
            }
            return '未打开工作区';
        }
        catch (e) {
            this.logger.error(e);
            return '保存自定义指令失败';
        }
    }

    async delete(fileName: string) {
        try {
            const directoryPath = await this.getPromptTemplateDirectoryPath();
            if (directoryPath) {
                const filePath = path.join(directoryPath, fileName + '.prompt');
                if (await isFileExist(filePath)) {
                    await unlink(filePath);
                    return;
                }
                else {
                    return '该自定义指令不存在';
                }
            }
            return '未打开工作区';
        } catch (e) {
            this.logger.error(e);
            return '删除自定义指令失败';
        }
    }

    private validateLength(str: string, msg: string) {
        if (str.length > MAX_INPUT_STRING) {
            throw new Error(`${msg}，不能超过${MAX_INPUT_STRING}个字符，请简化或移除该部分内容。`);
        }
    }

    private async validatePromptTemplate(filePath: string) {
        try {
            const stats = await stat(filePath);
            const fileSize = stats.size;

            // 判断文件大小是否超过限制
            if (fileSize > MAX_INPUT_STRING) {
                return {
                    success: false,
                    message: '自定义指令文件过大，当前大小: ' + fileSize + ' 字节',
                };
            }
            return {
                success: true,
                data: {
                    ts: stats.birthtimeMs || stats.ctimeMs,
                }
            };
        } catch (err) {
            this.logger.error('获取文件状态时出错:', err);

            return {
                success: false,
                message: '自定义指令文件获取失败，请先检查该文件是否存在',
            };;
        }
    }

    private generateMessageEvent(promptTemplateName: string, messageId: number, chunk: TaskProgressChunk): MessageEvent {
        return {
            messageId: messageId,
            sessionId: '',
            data: {
                action: ACTION_CHAT_TASK_PROGRESS,
                payload: {
                    taskId: String(messageId),
                    messageId,
                    chunk,
                    informationList: [],
                    capabilityName: promptTemplateName,
                }
            }
        };
    }

    private async processNormalKnowledgeList({
        query,
        inputKnowledgeList,
        templateKnowledgeList,
        mergeUserKnowledge
    }: {
        query: string;
        inputKnowledgeList?: KnowledgeSetBase[];
        templateKnowledgeList?: string[];
        mergeUserKnowledge?: boolean;
    }) {
        let normalKnowledgeList: string = '';

        if (templateKnowledgeList?.length || (
            mergeUserKnowledge && inputKnowledgeList?.length
        )) {
            this.logger.info('Prompt 生成结果验证 [知识集检索(配置)]：', templateKnowledgeList?.join('/'));
            this.logger.info('Prompt 生成结果验证 [知识集检索(输入)]：', inputKnowledgeList!.map((item) => item.uuid)?.join('/'));

            const knowledgeQueryResult =  await this.mediator.pluginConfigApi.getKnowledgeQueryResult(
                query,
                InformationQueryType.Text,
                inputKnowledgeList!.map((item) => ({uuid: item.uuid ?? item.id, type: 'NORMAL'})),
                {
                    mergeUserKnowledge,
                    knowledgeSets: templateKnowledgeList?.map(uuid => ({uuid, type: 'NORMAL'})),
                },
            );
            for (const chunk of knowledgeQueryResult.chunks) {
                normalKnowledgeList += chunk.content?.trim();
            }
            this.logger.info('Prompt 生成结果验证 [知识集检索(结果)]：', normalKnowledgeList);

            this.validateLength(normalKnowledgeList, '知识集检索结果过长');
        }
        return normalKnowledgeList;
    }

    private async processInputFileKnowledgeList(inputKnowledgeList?: FileKnowledge[]) {
        let fileKnowledgeList: string = '';

        if (inputKnowledgeList?.length) {
            for (const fileKnowledge of inputKnowledgeList) {
                fileKnowledgeList += fileKnowledge.content?.trim();
            }
        }

        this.validateLength(fileKnowledgeList, '输入文件的文件内容过长');

        return fileKnowledgeList;
    }

    private async processTemplateFileKnowledgeList(filePathMap?: Record<string, string>) {
        const result: Record<string, string> = {};
        const workspaceFolders = await this.mediator.getWorkspaceFolders();
        const errors: string[] = [];

        if (workspaceFolders?.[0]?.uri) {
            const workspaceFolderRootPath = URI.parse(workspaceFolders[0].uri).fsPath;
            if (filePathMap) {
                for (const [uuid, relativeFilePath] of Object.entries(filePathMap)) {
                    const filePath = normalizeFilePath(workspaceFolderRootPath, relativeFilePath);
                    try {
                        const stats = await stat(filePath);

                        if (stats.isFile()) {
                            if (stats.size > MAX_INPUT_STRING) {
                                errors.push(`文件${relativeFilePath}内容过长`);
                                continue;
                            }
                            const fileContent = await readFile(filePath, {encoding: 'utf8'});
                            result[uuid] = fileContent?.trim();
                        }
                    }
                    catch (e) {
                        errors.push(`未找到文件${relativeFilePath}`);
                        break;
                    }
                }
                if (errors.length > 0) {
                    throw new Error(errors.join('，'));
                }
            }
            return result;
        }
        return result;
    }

    async *chat(promptTemplateName: string, data: ComatePlusChatQueryPayload) {
        const directoryPath = await this.getPromptTemplateDirectoryPath();
        const stream = new StringChunkStream();

        if (directoryPath) {
            const filePath = path.join(directoryPath, '/', data.input.capability + '.prompt');
            const {success, message: validateMsg} = await this.validatePromptTemplate(filePath);

            if (!success) {
                yield this.generateMessageEvent(promptTemplateName, data.input.messageId, stream.fail(validateMsg || ''));
                return;
            }

            try {
                const promptTemplateFileContent = await readFile(filePath, {encoding: 'utf8'});
                const {system, promptTemplate} = parsePromptFile(filePath, promptTemplateFileContent);

                // promptTemplate的插槽
                const args = {
                    query: data.input.query,
                    selectedCode: data.context?.selectedCode,
                    activeFileContent: data.context?.activeFileContent,
                    activeFileLineContent: data.context?.activeFileLineContent,
                    activeFileLanguage: data.context?.activeFileLanguage,
                    knowledgeList: '',
                };

                const inputKnowledgeList = data.input.informationList?.filter(({type}) => ['SYSTEM', 'NORMAL'].includes(type));
                const normalKnowledgeList = await this.processNormalKnowledgeList({
                    query: data.input.query,
                    inputKnowledgeList,
                    templateKnowledgeList: system.knowledgeList,
                    mergeUserKnowledge: system.mergeUserKnowledge
                });

                const templateFileKnowledgePathMap = parseFilePathInPromptTemplate(promptTemplate);
                this.logger.info('Prompt 生成结果验证 [文件路径映射]：', JSON.stringify(templateFileKnowledgePathMap));
                const templateFileKnowledgeMap = await this.processTemplateFileKnowledgeList(templateFileKnowledgePathMap);
                this.logger.info('Prompt 生成结果验证 [文件内容映射]：', JSON.stringify(templateFileKnowledgeMap));

                const fileKnowledgeList = await this.processInputFileKnowledgeList(
                    data.input.informationList?.filter(({type}) => ['FILE', 'CURRENT_FILE'].includes(type)) as FileKnowledge[],
                );

                args.knowledgeList = normalKnowledgeList + '\n' + fileKnowledgeList + '\n';

                const prompt = formatPrompt(promptTemplate, {...args, ...templateFileKnowledgeMap})?.trim();

                if (!prompt) {
                    throw new Error('Prompt 为空，请确认自定义指令Prompt配置是否正确');
                }

                this.logger.info('Prompt 生成结果验证：', prompt);

                this.validateLength(prompt, '您输入的内容和选中的内容总长度过长');

                const model = prompt.length > modelMapMaxInputString[TextModel.ErnieBot4] ? TextModel.ErnieBot4Turbo128 : TextModel.ErnieBot4;

                const messages: ChatMessage[] = [
                    {role: 'user', content: prompt},
                ];

                if (normalKnowledgeList?.length > 0) {
                    this.logger.logUploader?.logUserAction({
                        category: 'promptTemplate',
                        action: 'chatWithKnowledge',
                        content: `inputKnowledgeList: ${inputKnowledgeList?.length},templateKnowledgeList: {${system?.knowledgeList?.length}},mergeUserKnowledge: ${system?.mergeUserKnowledge}`,
                    });
                }

                const chunks = await this.mediator.llmApi.callErnieBotStreaming({
                    messages,
                    pluginName: 'PromptTemplate',
                    model,
                    modelOptions: {
                        temperature: system?.temperature,
                        topP: system?.topP,
                        penaltyScore: system?.penaltyScore,
                    },
                    device: this.mediator.userDetail()?.device,
                });

                let result: string = '';

                for await (const chunk of chunks) {
                    result += chunk.result;
                    yield this.generateMessageEvent(promptTemplateName, data.input.messageId, stream.flushReplaceLast(result));
                }
            }
            catch (e) {
                yield this.generateMessageEvent(promptTemplateName, data.input.messageId, stream.fail((e as Error)?.message));
            }
        }
    }
}

