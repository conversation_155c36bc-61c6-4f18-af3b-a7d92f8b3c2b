import jsyaml from 'js-yaml';
import path from 'node:path';

// const PREAMBLE_SEPERATOR = '\n---\n';
const PREAMBLE_SEPERATOR = process.platform === 'win32' ? '\r\n---Prompt---\r\n' : '\n---Prompt---\n';
const ANNOTATION_SEPERATOR = process.platform === 'win32' ? '\r\n############\r\n' :  '\n############\n';

export function formatPrompt(promptTemplate: string, args?: Record<string, unknown>) {
    if (args) {
        return promptTemplate.replace(
            /\{\{\s*((\w+)|(\[FILE\]\s*[\\\/\-\.\w]+))\s*\}\}/g,
            (match, key) => {
                const replacement = args[key];
                // eslint-disable-next-line no-negated-condition
                return replacement !== undefined ? String(replacement) : match;
            }
        );
    }
    return promptTemplate;
}

export function getPreambleAndBody(content: string): [string, string] {
    let [preamble, body] = content.split(PREAMBLE_SEPERATOR);
    if (body === undefined) {
        body = preamble;
        preamble = "";
    }
    return [preamble, body.split(ANNOTATION_SEPERATOR)?.[0] ?? ''];
}

// 自定义Prompt文件中配置信息
interface PreambleRaw {
    name?: string;
    description?: string;
    temperature?: number | string;
    topP?: number | string;
    penaltyScore?: number | string;
    /* Prompt需要的知识信息，值为知识库uuids，多个知识用逗号分隔 */
    knowledgeList?: string[];
    /* 是否允许Input输入的知识参与检索 */
    mergeUserKnowledge?: boolean;
}

function formatNumber(value: string | number | undefined, config?: {isInteger?: boolean, min?: number, max?: number}) {
    // 判断是否为undefined
    if (typeof value === 'undefined') {
        return;
    }
    let result: number = 0;

    if (typeof value === 'number') {
        result = value;
    }

    if (typeof value === 'string') {
        const trimedValue = value.trim();
        const isNumeric = /^\d+$/.test(trimedValue);
        if (!isNumeric) {
            return;
        }
        result = parseInt(trimedValue, 10);
    }

    // 判断是否为整数
    if (config?.isInteger && !Number.isInteger(result)) {
        return;
    }
    // 判断最小值
    if (typeof config?.min !== 'undefined' && result < config.min) {
        return;
    }
    // 判断最大值
    if (typeof config?.max !== 'undefined'  && result > config.max) {
        return;
    }
    return result;
}

function formatBoolean(value: string | number | boolean | undefined) {
    if (typeof value === 'undefined') {
        return;
    }
    if (typeof value === 'boolean') {
        return value;
    }
    const trimedValue = String(value).trim().toLowerCase();
    return ['true', '1'].includes(trimedValue);
}

export function parsePreamble(preambleRaw: Partial<PreambleRaw>) {

    return {
        temperature: formatNumber(preambleRaw.temperature, {min: 0, max: 1}),
        topP: formatNumber(preambleRaw.topP, {min: 0, max: 1}),
        penaltyScore: formatNumber(preambleRaw.penaltyScore, {min: 1, max: 2}),
        knowledgeList: preambleRaw.knowledgeList?.filter((item) => item?.trim().length > 0),
        mergeUserKnowledge: formatBoolean(preambleRaw.mergeUserKnowledge),
    };
}

export function parsePromptFile(fileName: string, content: string) {
    let [preambleText, promptTemplate] = getPreambleAndBody(content);

    const preambleRaw: Partial<PreambleRaw> = jsyaml.load(preambleText, {json: true}) ?? {};

    const {name: preambleName, description: preambledDescription, ...restPreamble} = preambleRaw;
    const name = preambleName ? String(preambleName) : fileName;
    const description = preambledDescription ?? name;

    return {name, description, system: parsePreamble(restPreamble), promptTemplate};
}

interface ParamsGeneratePromptTemplate {
    query?: string;
}

const promptTemplatePreamble = `
# 自定义指令名称，必填
name: 自定义指令名称
# 自定义指令描述，可选
description: 自定义指令的描述
`;

const defaultPromptTemplate = `
该部分内容为自定义Prompt具体内容，请根据需求直接修改此段话。比如：帮我生成一个随机数。
`;

const promptTemplateAnnotation = `
# =================高级Prompt编写说明==========================
# Prompt支持以下多种变量配合使用：
    # {{ query }} 为用户输入框输入的内容
    # {{ knowledgeList }} 为knowledgelist配置和用户输入的知识，详细说明见最下方知识手册
    # {{ selectedCode }} 为用户当前选中代码
    # {{ activeFileContent}} 为IDE光标所在文件的内容
    # {{ activeFilelineContent}} 为IDE光标所在行的内容
    # {{ [FILE] path }} 为本地文件内容，将path替换为相对当前项目根目录的相对路径即可
# 详细说明请见使用手册：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/Hb6OQz5Jc7/PvZ5uWD8MdM0w_
`;

export const parseFilePathInPromptTemplate = (promptTemplate: string) => {
    try {
        const regex = /\{\{\s*(\[FILE\]\s*([\\\/\-\.\w]+))\s*\}\}/g;
        const matches: Record<string, string> = {};
        let match;

        while ((match = regex.exec(promptTemplate)) !== null) {
            matches[match[1]] = match[2];
        }
        return matches;
    }
    catch (error) {
        return undefined;
    }
}

export const normalizeFilePath = (rootPath: string, filePath: string) => {
    if (filePath?.includes('/')) {
        if (process.platform === 'win32') {
            return path.join(rootPath, filePath.replace(/\//g, path.sep));
        }

        return path.join(rootPath, filePath);
    }
    if (filePath?.includes('\\')) {
        if (process.platform === 'win32') {
            return path.join(rootPath, filePath);
        }
        return path.join(rootPath, filePath.replace(/\\/g, path.sep));
    }
    return path.join(rootPath, filePath);
}

export function generatePromptTemplate(params?: ParamsGeneratePromptTemplate) {
    let content: string = promptTemplatePreamble + PREAMBLE_SEPERATOR;

    if (!params) {
        content += defaultPromptTemplate;
    }

    if (params?.query) {
        content += `请回答我的问题：${params.query}`;
    }

    return content + ANNOTATION_SEPERATOR + promptTemplateAnnotation;
}