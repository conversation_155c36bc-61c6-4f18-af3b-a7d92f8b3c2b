import {LoggerRoleInstance} from '../Logger.js';
import {Mediator} from '../Mediator.js';
import {localPluginConfig, mergePluginConfig, pluginConfigDetail} from '@comate/plugin-shared-internals';
import {PluginMetas} from './pluginMeta.js';

let PluginCache: Record<string, any> | undefined;

// TODO 监听本地配置变化
export class PluginConfig {
    mediator: Mediator;
    logger: LoggerRoleInstance;

    constructor(mediator: Mediator, logger: LoggerRoleInstance) {
        this.mediator = mediator;
        this.logger = logger;
    }

    private async syncGlobalLocalConfig() {
        const localConfig = await this.localConfig();
        const configSet = await this.mediator.pluginConfigApi.getCurrentUserPluginConfigSet() || [];
        const cloudConfig = Object.fromEntries(
            configSet.map((v: any) => [
                v.pluginName,
                {
                    enabled: v.configValues.enabled ?? true,
                    config: v.configValues.values,
                    meta: PluginMetas[v.pluginName],
                },
            ])
        );
        this.logger.verbose(`Write global plugin config success to ${JSON.stringify(cloudConfig)}`);
        const mergedConfig = mergePluginConfig(cloudConfig, localConfig);
        PluginCache = mergedConfig;
        this.logger.verbose(`Write global plugin config success to mergedConfig ${JSON.stringify(cloudConfig)}`);
        return mergedConfig;
    }

    async all() {
        const config = PluginCache;
        if (config) {
            this.syncGlobalLocalConfig();
            return config;
        }

        return this.syncGlobalLocalConfig();
    }

    async detail(pluginName: string) {
        const configSet = await this.mediator.pluginConfigApi.getCurrentUserPluginConfigSet();
        const localConfig = await this.localConfig();
        return pluginConfigDetail(pluginName, configSet, localConfig);
    }

    async localConfig() {
        const folders = await this.mediator.getWorkspaceFolders();
        return localPluginConfig(folders);
    }
}
