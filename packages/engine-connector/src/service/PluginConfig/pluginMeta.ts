import {PluginMeta} from '@comate/plugin-shared-internals';

export const PluginMetas: Record<string, PluginMeta> = {
    security: {
        independentProcess: true,
        enableBackgroundService: true,
        defaultPermissions: [],
        // 需要内建到一方
        behaveAsBuiltIn: true,
        // 只有企业才能使用
        onlyBusinesses: true,
        enableDiagnostic: true,
        diagnosticCapabilityName: 'diagnosticScan',
    },
};
