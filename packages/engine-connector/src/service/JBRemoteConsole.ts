import {ACTION_COMATE_LSP_REMOTE_CONSOLE} from '@comate/plugin-shared-internals';
import {
    ClientCapabilities,
    Connection,
    RemoteConsole,
    ServerCapabilities,
} from 'vscode-languageserver/node.js';

export class JBRemoteConsole implements RemoteConsole {
    connection: Connection;

    constructor(connection: Connection) {
        this.connection = connection;
    }

    error(message: string): void {
        this.connection.sendRequest(ACTION_COMATE_LSP_REMOTE_CONSOLE, {
            type: 'error',
            message,
        });
    }

    warn(message: string): void {
        this.connection.sendRequest(ACTION_COMATE_LSP_REMOTE_CONSOLE, {
            type: 'warn',
            message,
        });
    }

    info(message: string): void {
        this.connection.sendRequest(ACTION_COMATE_LSP_REMOTE_CONSOLE, {
            type: 'info',
            message,
        });
    }

    log(message: string): void {
        this.connection.sendRequest(ACTION_COMATE_LSP_REMOTE_CONSOLE, {
            type: 'log',
            message,
        });
    }

    initialize(capabilities: ClientCapabilities): void {
        throw new Error('Method not implemented.');
    }

    fillServerCapabilities(capabilities: ServerCapabilities<any>): void {
        throw new Error('Method not implemented.');
    }
}
