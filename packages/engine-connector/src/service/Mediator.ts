import {LLMApi} from '../../src/api/LLMApi.js';
import {PluginConfigApi} from '../api/PluginConfigApi.js';
import {PluginConfig} from './PluginConfig/index.js';
import {IdeChannel} from './IdeChannel/index.js';
import {PluginChannel} from './PluginChannel/index.js';
import {User} from './User.js';
import {EngineDiagnosticCache} from './DiagnosticCache.js';
import {MessageGenerate} from './MessageGenerate/index.js';
import {Execution, QuerySelectorPayload} from '@comate/plugin-shared-internals';
import {ConversationBufferMemory} from '@comate/plugin-engine';
import {ChatSessionManager} from './ChatSessionManager/index.js';
import {ConversationManager} from './AgentConversation/index.js';
import {CommonCache} from './CommonCache.js';
import {PromptTemplateService} from './PromptTemplateService/index.js';
import {ComatePlusChatQueryPayload} from '../types/ideListener.js';

interface MediatorParams {
    ideChannel: IdeChannel;
    pluginChannel: PluginChannel;
    user: User;
    pluginConfig: PluginConfig;
    pluginConfigApi: PluginConfigApi;
    llmApi: LLMApi;
    diagnosticCache: EngineDiagnosticCache;
    messageGenerate: MessageGenerate;
    conversationBufferMemory: ConversationBufferMemory;
    chatSessionManager: ChatSessionManager;
    agentConversationManager: ConversationManager;
    commonCache: CommonCache;
    promptTemplateService: PromptTemplateService;
}

export class Mediator {
    pluginConfigApi!: PluginConfigApi;
    llmApi!: LLMApi;
    diagnosticCache!: EngineDiagnosticCache;
    agentConversationManager!: ConversationManager;
    messageGenerate!: MessageGenerate;
    conversationBufferMemory!: ConversationBufferMemory;
    chatSessionManager!: ChatSessionManager;
    commonCache!: CommonCache;
    promptTemplateService!: PromptTemplateService;

    private ideChannel!: IdeChannel;
    private pluginChannel!: PluginChannel;
    private user!: User;
    private pluginConfig!: PluginConfig;

    /**
     * @function
     * @param {MediatorParams} params - 参数对象，包含以下属性：
     * - ideChannel {IdeChannel} IDE通道实例
     * - pluginChannel {PluginChannel} 插件通道实例
     * - user {User} 用户信息对象
     * - pluginConfig {PluginConfig} 插件配置对象
     * - pluginConfigApi {PluginConfigApi} 插件配置API对象
     * - llmApi {LlmApi} LLM API对象
     * - diagnosticCache {DiagnosticCache} 诊断缓存对象
     * - messageGenerate {MessageGenerate} 消息生成器对象
     * - conversationBufferMemory {ConversationBufferMemory} 会话缓存内存对象
     * - chatSessionManager {ChatHistoryManager} 聊天记录管理器对象
     * @description 初始化中介者类的实例，并设置相关属性和方法。
     */
    init(
        {
            ideChannel,
            pluginChannel,
            user,
            pluginConfig,
            pluginConfigApi,
            llmApi,
            diagnosticCache,
            messageGenerate,
            conversationBufferMemory,
            agentConversationManager,
            chatSessionManager,
            commonCache,
            promptTemplateService
        }: MediatorParams
    ) {
        this.conversationBufferMemory = conversationBufferMemory;
        this.ideChannel = ideChannel;
        this.agentConversationManager = agentConversationManager;
        this.pluginChannel = pluginChannel;
        this.user = user;
        this.pluginConfig = pluginConfig;
        this.pluginConfigApi = pluginConfigApi;
        this.llmApi = llmApi;
        this.diagnosticCache = diagnosticCache;
        this.messageGenerate = messageGenerate;
        this.chatSessionManager = chatSessionManager;
        this.commonCache = commonCache;
        this.promptTemplateService = promptTemplateService;
    }
    // 发送给IDE
    sendToIde(type: string, params: any) {
        return this.ideChannel.send(type, params);
    }
    // 发送给IDE通知
    sendToIdeNotification(type: string, params: any) {
        return this.ideChannel.sendNotification(type, params);
    }
    // 发送给插件
    sendToPlugin(sessionId: string, data: any, execution?: Execution) {
        return this.pluginChannel.send(sessionId, data, execution);
    }
    // 发送给插件，不需要等待返回
    launchSendToPlugin(sessionId: string, data: any, execution?: Execution) {
        return this.pluginChannel.launchSend(sessionId, data, execution);
    }

    getPluginCapabilityInfo() {
        return this.pluginChannel.getPluginCapabilityInfo();
    }

    getSuggestCapabilitiesByMatch(payload: QuerySelectorPayload) {
        return this.pluginChannel.getSuggestCapabilitiesByMatch(payload);
    }

    async getWorkspaceFolders() {
        return this.ideChannel.getWorkspaceFolders();
    }

    async getLocalConfig() {
        return this.pluginConfig.localConfig();
    }

    userDetail() {
        return this.user.getUserDetail();
    }
    // 获取指定插件的配置
    async getPluginConfig(pluginName: string) {
        return this.pluginConfig.detail(pluginName);
    }

    // 获取所有插件的配置
    async getPluginConfigAll() {
        return this.pluginConfig.all();
    }

    promptTemplateChat(promptTemplateName: string, data: ComatePlusChatQueryPayload) {
        return this.promptTemplateService.chat(promptTemplateName, data);
    }
}
