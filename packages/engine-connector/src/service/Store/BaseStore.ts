import {Low} from 'lowdb';
import {JSONFile} from 'lowdb/node';
import os from 'node:os';
import path from 'node:path';
import fs from 'node:fs';
import {AsyncQueue} from './AsyncQueue.js';

const getPath = (filename: string, defaultData: any) => {
    const dirPath = path.join(os.homedir(), '.comate-engine', 'store');
    if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
    }
    const filePath = path.join(dirPath, filename);
    if (!fs.existsSync(filePath)) {
        fs.writeFileSync(filePath, JSON.stringify(defaultData));
    }
    return filePath;
};

export class BaseStore<T> {
    protected db: Low<T>;
    protected adapter: JSONFile<T>;
    protected filename: string;
    private queue = new AsyncQueue();
    private writeQueue = new AsyncQueue();

    constructor(filename: string, defaultData: T) {
        const file = getPath(filename, defaultData);
        this.adapter = new JSONFile<T>(file);
        this.db = new Low(this.adapter, defaultData);
        this.filename = filename;
        this.db.read();
    }

    async read() {
        return this.db.read();
    }

    async write() {
        try {
            await this.writeQueue.enqueue(async () => {
                this.db.write();
            });
        } catch (e) {
            // do nothing
        }
    }

    async _update(fn: (data: T) => T) {
        await this.queue.enqueue(async () => {
            try {
                await this.read();
                this.db.data = fn(this.db.data);
                await this.write();
                return this.db.data;
            } catch (e) {
                // do nothing
            }
        });
    }

    async drop() {
        // 删除文件
        const file = getPath(this.filename, '');
        if (fs.existsSync(file)) {
            fs.unlinkSync(file);
        }
    }
}