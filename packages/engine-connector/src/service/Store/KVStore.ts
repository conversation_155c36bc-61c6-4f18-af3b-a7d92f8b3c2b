import {BaseStore} from './BaseStore.js';

export class KVStore<T extends Record<string, any> = Record<string, any>> extends BaseStore<T> {
    constructor(dbName: string, defaultData: T = {} as T) {
        super(dbName, defaultData);
    }

    async set(key: keyof T, value: any) {
        return this._update(data => {
            data[key] = value;
            return data;
        });
    }

    async get<K extends keyof T>(key: K): Promise<T[K]> {
        return this.db.data[key];
    }
}