import {BaseStore} from './BaseStore.js';

export class ArrayStore<T> extends BaseStore<T[]> {
    constructor(dbName: string, defaultData: T[] = []) {
        super(dbName, defaultData);
    }

    async push(item: T) {
        await this._update(data => {
            return data.concat([item]);
        });
        return this.read();
    }

    async filter(fn: (item: T) => boolean) {
        await this.read();
        return this.db.data.filter(fn);
    }

    async find(fn: (item: T) => boolean) {
        await this.read();
        return this.db.data.find(fn);
    }

    async map<R>(fn: (value: T, index: number, array: T[]) => R): Promise<R[]> {
        await this.read();
        return this.db.data.map<R>(fn);
    }

    async reduce(fn: (acc: any, item: T) => any, initialValue: any) {
        await this.read();
        return this.db.data.reduce(fn, initialValue);
    }

    async sort(fn: (a: T, b: T) => number) {
        await this.read();
        return [...this.db.data].sort(fn);
    }

    async slice(start: number, end?: number) {
        await this.read();
        return this.db.data.slice(start, end);
    }

    async at(index: number) {
        await this.read();
        return this.db.data.at(index);
    }

    async get() {
        await this.read();
        return this.db.data;
    }

    async delete(fn: (item: T) => boolean) {
        return this._update(
            data => {
                const newData = data.filter(item => !fn(item));
                return newData;
            }
        );
    }

    async update(updateFn: (item: T) => T) {
        return this._update(data => {
            return data.map(updateFn);
        });
    }
}
