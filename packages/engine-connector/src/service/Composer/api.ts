/* eslint-disable complexity */
import {extname} from 'node:path';
import {IncomingMessage} from 'http';
import _ from 'lodash';
import {SSEProcessor, VirtualEditor, axiosInstance} from '@comate/plugin-shared-internals';
import {IllegalTextExtDefinition, IntentStrategy} from './types.js';
import {getTraceRepoInfo} from '../../utils/track.js';

const {memoize} = _;
interface AnalyzeParams {
    username: string;
    query: string;
    conversationId?: number;
    slash: string;
    intent?: boolean;
    contexts: any[];
}

const baseUrl = '/api/aidevops/autocomate';
// const proxy = {
//     protocol: 'http',
//     port: 9090,
//     host: '127.0.0.1',
// };
const proxy = undefined;
// const baseUrl = 'https://comate-test.now.baidu.com/api/aidevops/autocomate';
// const baseUrl = 'http://localhost:8080';

// 一个资源使用超过限制的Error
export class ResourceUsageOverlimitError extends Error {}
export class InternalServerError extends Error {}
export async function analyze({username, ...params}: AnalyzeParams) {
    const result = await axiosInstance.post<
        {
            code: number;
            message: string;
            data: {messageId: number, conversationId: number, intentStrategies: IntentStrategy[], extend: any};
        }
    >(
        baseUrl + '/rest/autowork/v1/analyze',
        params,
        {
            headers: {
                'X-Source': 'COMATE',
                'Content-Type': 'application/json',
                'login-name': username,
                'Uuap-login-name': username,
            },
            proxy,
        }
    );

    if (result.data.code === 429) {
        throw new ResourceUsageOverlimitError(result.data.message);
    }
    else if (result.data.code !== 200) {
        throw new InternalServerError(result.data.message);
    }

    return result;
}

export interface ChatResponse {
    content: {
        type: 'EXCEPTION' | string;
        detail: {
            end: boolean;
            summary: string;
            exceptionMsg: string;
            adoptionUuid: string;
        };
    };
}

interface ChatParams {
    username: string;
    query: string;
    contexts: any[];
    codeChunks: any[];
    conversationId?: number;
    extend?: any;
}

export async function* chat(virtualEditor: VirtualEditor, workspaceRoot: string, {username, ...params}: ChatParams) {
    const trackRepo = await getTraceRepoInfo(virtualEditor, workspaceRoot);
    const res = await axiosInstance.post<IncomingMessage>(
        baseUrl + '/rest/autowork/v1/chat/stream',
        {...params, ...trackRepo, slash: 'Composer'},
        {
            headers: {
                'X-Source': 'COMATE',
                'Content-Type': 'application/json',
                'login-name': username,
                'Uuap-login-name': username,
                'plugin-version': 'VsCode-3.0.0',
            },
            responseType: 'stream',
            proxy,
        }
    );
    const processor = new SSEProcessor<ChatResponse>(res.data);
    for await (const chunk of processor.processSSE()) {
        if (chunk.content.type === 'EXCEPTION') {
            throw new InternalServerError(chunk.content.detail.exceptionMsg);
        }
        yield chunk;
    }
}

const getIllegalTextExtname = memoize(async (username: string) => {
    try {
        const data = await axiosInstance.get<{data: IllegalTextExtDefinition[]}>(
            '/api/aidevops/autocomate/rest/autowork/v1/reference-file-extension-whitelist',
            {
                headers: {
                    'X-Source': 'COMATE',
                    'Content-Type': 'application/json',
                    'login-name': username,
                    'Uuap-login-name': username,
                },
            }
        );
        return data.data.data;
    }
    catch (ex) {
        return [];
    }
});

// 根据文件路径判断文件是否是音频、视频、图标、文档等
export async function illegalTextFile(username: string, filePath: string) {
    const defintions = await getIllegalTextExtname(username);
    const ext = extname(filePath);
    const def = defintions.find(def => {
        return def.extensions.includes(ext.replace(/^./, ''));
    });
    return def ? {illegal: true, type: def.type} as const : {illegal: false, type: ''} as const;
}
