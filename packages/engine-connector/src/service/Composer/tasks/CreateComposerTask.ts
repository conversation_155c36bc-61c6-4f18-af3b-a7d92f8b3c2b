import {ComposerCodeStatus} from '../types.js';
import {Task, AbstractTask} from './Task.js';
import {sleep} from '../utils/index.js';

export class CreateComposerTask extends Task implements AbstractTask {
    actionName = '创建';

    private async createCode(content: string, forcePreview: boolean) {
        this.openVirtualDocumentNecessary(forcePreview, '', content);
        await this.update(content);
    }

    private streamOutputFinish = false;
    protected readonly generatedContentLines: {lines: string[], next: number} = {lines: [], next: 0};

    async update(content: string, closed: boolean = false) {
        if (this.streamOutputFinish || this.cancelled) {
            return;
        }

        this.generatedContentLines.lines = content.split(/\r?\n/);
        if (closed) {
            this.streamOutputFinish = true;
            this.content = content;
        }
    }

    get nextStreamLine() {
        return this.generatedContentLines.lines[this.generatedContentLines.next];
    }

    async streamOutputOnce() {
        while (!this.streamOutputFinish || typeof this.nextStreamLine !== 'undefined') {
            if (this.cancelled) {
                await this.revert();
                return;
            }

            // eslint-disable-next-line no-negated-condition
            if (typeof this.nextStreamLine !== 'undefined') {
                const lines = this.generatedContentLines.lines.slice(0, this.generatedContentLines.next + 1);
                const content = lines.join('\n');
                await this.replaceVirtualDocument(content);
                this.generatedContentLines.next = this.generatedContentLines.next + 1;
            }

            await sleep(17);
        }

        if (!this.cancelled) {
            await this.replaceVirtualDocument(this.content);
            this.status = ComposerCodeStatus.DONE;
        }
    }

    /**
     * @param 是否强制预览，一次composer任务中的第一个输出文件会强制预览，其余会根据编辑器的情况来处理打开
     */
    async start(content: string, forcePreview?: boolean) {
        this.status = ComposerCodeStatus.PROCESSING;

        await this.createCode(content, !!forcePreview);
        this.streamOutputOnce();
    }

    async revert() {
        this.reject();
        // 关闭 Diff -> 保存原文件Tab -> 关闭原文件 Tab -> 删除
        await this.closeVirtualDocument();
        await this.saveDocument();
        await this.closeDocument();
        await this.removeDocument();
    }

    async end(content: string, forcePreview?: boolean) {
        this.streamOutputFinish = true;
        this.content = content;

        await this.createCode(content, !!forcePreview);
        this.status = ComposerCodeStatus.DONE;
    }

    async save() {
        this.accept();
        // 用 Diff 内容覆盖 -> 关闭 Diff -> 打开文件Tab -> 保存
        const content = await this.getVirtualDocumentText();
        await this.saveDocumentWithReplaceContentAndOpen(content);
        await this.closeVirtualDocument();
        await this.saveDocument();
    }
}
