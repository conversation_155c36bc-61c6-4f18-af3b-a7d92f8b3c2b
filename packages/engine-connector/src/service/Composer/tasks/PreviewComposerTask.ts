import {ComposerCodeStatus} from '../types.js';
import {Task, AbstractTask} from './Task.js';

export class PreviewComposerTask extends Task implements AbstractTask {
    actionName = '自定义展示';

    update(content: string, closed: boolean) {
        this.content = content;
        if (closed) {
            this.status = ComposerCodeStatus.DONE;
        }
    }

    get updatedConetnt() {
        return JSON.stringify({
            type: 'previewUrl',
            content: this.content,
        });
    }

    start() {
        this.status = ComposerCodeStatus.PROCESSING;
    }

    async rewriteOriginalContent(absolutePath: string) {
        const {content} = await this.virtualEditor.getDocument({absolutePath});
        this.originalContent = content.toString();
    }

    async rewriteOriginalContentIfEmpty(absolutePath: string) {
        if (!this.originalContent) {
            await this.rewriteOriginalContent(absolutePath);
        }
    }

    async rewriteOriginalContentFromTask(task: PreviewComposerTask) {
        if (!this.originalContent) {
            this.originalContent = task.getOriginalContent();
        }
    }

    getOriginalContent() {
        return this.originalContent;
    }

    async revert() {
        this.reject();
        // 如果原文件存在，保留文件，把内容写回去
        if (this.originalContent && this.originalContent?.length !== 0) {
            await this.saveDocumentWithReplaceContent(this.originalContent);
            // return;
        }
        // 关闭 Diff -> 保存原文件Tab -> 关闭原文件 Tab -> 删除
        await this.closeVirtualDocument();
        await this.saveDocument();
        await this.closeDocument();
        // 如果有源文件，不删除文件
        (!this.originalContent || this.originalContent.length === 0) && await this.removeDocument();
    }

    async save() {
        this.accept();
        // 用 Diff 内容覆盖 -> 关闭 Diff -> 打开文件Tab -> 保存
        await this.saveDocumentWithReplaceContentAndOpen(this.content);
        await this.closeVirtualDocument();
        await this.saveDocument();
    }

    async saveAndOpen() {
        await this.save();
    }
    end() {}
}
