import _ from 'lodash';
import {applyDiff} from '@comate/plugin-shared-internals';
import {Task, AbstractTask} from './Task.js';
import {sleep} from '../utils/index.js';
import {ComposerCodeStatus} from '../types.js';

const {once} = _;

export class EditComposerTask extends Task implements AbstractTask {
    actionName = '修改';
    protected applyStreamFinished = false;
    protected readonly generatedContentLines: {lines: string[], next: number} = {lines: [], next: 0};

    // diff 匹配删除行时是否忽略前面的缩进
    skipIndentWhenMatchDiffLine = false;

    get nextStreamLine() {
        return this.generatedContentLines.lines[this.generatedContentLines.next];
    }

    get newContent() {
        return this.generatedContentLines.lines.join(this.eol);
    }

    /** 最后一次更新内容时，要自动跳转到哪一行，因为内容是异步加载到 DIFF 文档的，暂时不支持流式更新时跳转 */
    calculateAutoScrollLine(): number | undefined {
        return undefined;
    }

    protected async *unshiftLineGenerator() {
        while (!this.applyStreamFinished || typeof this.nextStreamLine !== 'undefined') {
            if (this.status === ComposerCodeStatus.CANCELLED) {
                await this.revert();
                return;
            }

            // eslint-disable-next-line no-negated-condition
            if (typeof this.nextStreamLine !== 'undefined') {
                yield this.nextStreamLine;
                this.generatedContentLines.next = this.generatedContentLines.next + 1;
            }

            if (!this.applyStreamFinished) {
                // 如果消息没结束，加sleep，去掉后这个while会把线程阻塞掉，17ms是为了匹配60帧率
                await sleep(17);
            }
        }
    }

    /**
     * 仅执行一次 streamOutput，结束后把任务状态设置成 DONE
     */
    protected async streamOutputDiff() {
        const generator = await this.unshiftLineGenerator();
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        while (!(await generator.next()).done) {
            await this.replaceVirtualDocument(this.newContent);
        }
        await this.replaceVirtualDocument(this.newContent, this.calculateAutoScrollLine());
        this.status = ComposerCodeStatus.DONE;
    }

    private streamOutputDiffOnce = once(this.streamOutputDiff);

    private diffText = '';
    async update(content: string, closed: boolean = false) {
        if (this.status === ComposerCodeStatus.CANCELLED) {
            this.applyStreamFinished = true;
            return;
        }

        const diff = content.replace(/^```diff(.*)\n/, '').replace(/```(\n?)$/, '');
        const diffLines = diff.split('\n');
        // 保证每一行的diff都是完整的一行，再闭合前排除最后一行
        const safeDiff = closed ? diff : diffLines.slice(0, diffLines.length - 1).join('\n');
        if (diff && safeDiff.length && !this.applyStreamFinished) {
            // 最后一帧不能按diff返回要全文返回
            const newContent = applyDiff(
                this.originalContent || '',
                safeDiff,
                {
                    returnContentWhenDiffLineLoopFinish: !closed,
                    eol: this.eol,
                    skipIndentWhenMatchDiffLine: this.skipIndentWhenMatchDiffLine,
                }
            );
            if (newContent) {
                this.generatedContentLines.lines = newContent.split(/\r?\n/);
                this.applyStreamFinished = closed;

                if (closed) {
                    this.content = newContent;
                    // eslint-disable-next-line max-depth
                    while (!this.finish) {
                        await sleep(100);
                    }
                    this.diffText = diff;
                }
            }
        }
    }

    async start(content: string, forcePreview?: boolean) {
        this.originalContent = await this.getDocumentText();
        this.status = ComposerCodeStatus.PROCESSING;
        await this.openVirtualDocumentNecessary(forcePreview, this.originalContent);
        this.streamOutputDiffOnce();
        await this.update(content);
    }

    /** 任务结束后，如果想重新触发 start、update 的流程，可以调这个方法 */
    async restart(content: string, forcePreview?: boolean) {
        this.applyStreamFinished = false;
        this.streamOutputDiffOnce = once(this.streamOutputDiff);
        await this.start(content, forcePreview);
    }

    async end(content: string, forcePreview?: boolean) {
        this.applyStreamFinished = true;
        this.content = content;
        this.originalContent = await this.getDocumentText();
        await this.openVirtualDocumentNecessary(forcePreview, this.originalContent, this.content);
        this.status = ComposerCodeStatus.DONE;
    }

    protected async saveDocumentAfterAccept(accept: boolean) {
        const acceptContent = await this.getVirtualDocumentText();
        const originalContent = this.originalContent || '';
        await this.saveDocumentWithReplaceContentAndOpen(accept ? acceptContent : originalContent);
        await this.closeVirtualDocument();
    }

    async revert() {
        this.reject();
        await this.saveDocumentAfterAccept(false);
    }

    async save() {
        this.accept();
        await this.saveDocumentAfterAccept(true);
    }
}
