import {join, isAbsolute, relative} from 'node:path';
import {writeFile} from 'fs/promises';
import {AcceptState, ComposerCodeStatus} from '../types.js';
import {VirtualEditor} from '@comate/plugin-shared-internals';
import {LoggerRoleInstance} from '../../Logger.js';
import {Repo} from '../types.js';
import {formatWin32PathSep, isFilePathEqual} from '../utils/index.js';
import {isJetbrains} from '../../../utils/checkIDE.js';
import {convertChangesToDMP, diffLines} from 'diff';

export abstract class AbstractTask {
    abstract actionName: string;
    abstract status: ComposerCodeStatus;
    abstract absolutePath: string;
    abstract start(content?: string): void;
    abstract update(content: string, closed?: boolean): any;
    abstract end(content: string, forcePreview?: boolean): any;
    abstract save(): any;
    abstract revert(): any;
}

export interface Opts {
    /** 用于替换掉绝对路径检索的匹配标识，解决一次composer里有多个同路径任务的场景 */
    uniqKeyPrefix?: string;
}

const getRelativePath = (filePath: string, repo: Repo) => {
    if (filePath.startsWith(repo.rootPath)) {
        return relative(repo.rootPath, filePath);
    }
    return filePath;
};

export class Task {
    actionName = '';
    foreground: boolean = false;
    accepted: AcceptState = AcceptState.UNTOUCHED;
    status: ComposerCodeStatus = ComposerCodeStatus.UNREADY;
    protected content: string = '';
    protected originalContent: string | null = null;
    constructor(
        protected virtualEditor: VirtualEditor,
        readonly repo: Repo,
        readonly action: string,
        readonly filePath: string,
        protected readonly opts?: Opts,
        protected readonly logger?: LoggerRoleInstance
    ) {
        // NOTE: UTAgent 这里的 filePath 存在 /$ 开头的情况，具体可以定位到 FILE_NAME_SIGN_$
        this.filePath = getRelativePath(filePath, repo);
    }

    get absolutePath() {
        // 如果是绝对路径直接返回
        // 这里的 startsWith('/') 还不能去掉，单测智能体可能是虚拟路径已 / 开头
        if (this.filePath.startsWith('/') || isAbsolute(this.filePath)) {
            return this.filePath;
        }
        return join(this.repo.rootPath, this.filePath);
    }

    get key() {
        if (this.opts?.uniqKeyPrefix) {
            return `${this.opts.uniqKeyPrefix}/${this.absolutePath}`;
        }
        return this.absolutePath;
    }

    get finish() {
        return this.status === ComposerCodeStatus.DONE || this.status === ComposerCodeStatus.CANCELLED;
    }

    get cancelled() {
        return this.status === ComposerCodeStatus.CANCELLED;
    }

    get untouched() {
        return this.accepted === AcceptState.UNTOUCHED;
    }

    get calculatedDiff() {
        const temp = convertChangesToDMP(diffLines(this.originalContent || '', this.content || '')) || [];
        const res = temp.filter(item => item[0] !== 0).map(item => {
            if (item[0] === 1) {
                return ('+  ' + item[1]);
            }
            return ('-  ' + item[1]);
        });
        return res.join('');
    }

    protected dependTask: Task | undefined;

    protected uuid: string = '';
    setAdoptUuid(uuid: string) {
        this.uuid = uuid;
    }

    dependOn(task: Task) {
        this.dependTask = task;
    }

    accept() {
        this.accepted = AcceptState.ACCEPT;
    }

    reject() {
        this.accepted = AcceptState.REJECT;
    }

    cancel() {
        this.status = ComposerCodeStatus.CANCELLED;
    }

    protected async openDocument() {
        if (!this.foreground) {
            return;
        }
        return this.virtualEditor.openDocument({absolutePath: this.absolutePath});
    }

    protected async getActiveDocument() {
        return this.virtualEditor.getActiveDocument();
    }

    protected async openVirtualDocument(leftContent?: string | null, rightContent?: string | null) {
        if (!this.foreground) {
            return;
        }
        await this.virtualEditor.openVirtualDiffDocument({
            absolutePath: this.absolutePath,
            content: leftContent || '',
            modified: rightContent || '',
            stream: true,
        });
    }

    protected async openVirtualDocumentNecessary(
        forcePreview?: boolean,
        leftContent?: string | null,
        rightContent?: string | null
    ) {
        if (!this.foreground) {
            return;
        }
        if (forcePreview) {
            await this.openVirtualDocument(leftContent, rightContent);
        }
        else {
            const document = await this.getActiveDocument();
            if (
                document.scheme === 'diff'
                && isFilePathEqual(document.absolutePath, this.dependTask?.absolutePath || '')
            ) {
                await this.openVirtualDocument(leftContent, rightContent);
            }
        }
    }
    protected async replaceVirtualDocument(content: string | null, scrollToLine?: number) {
        await this.virtualEditor.replaceVirtualDiffModifiedDocument({
            absolutePath: this.absolutePath,
            content: content || '',
            scrollToLine,
        });
    }

    protected async getDocumentText() {
        const {content} = await this.virtualEditor.getDocument({absolutePath: this.absolutePath});
        return content;
    }

    protected async saveDocument() {
    }

    protected async refreshProjectTree() {
        await this.virtualEditor.refreshProjectTree();
    }

    protected async removeDocument() {
        await this.virtualEditor.deleteDocument({absolutePath: this.absolutePath});
    }

    protected eol: string = '\n';
    protected async getDocumentEndOfLine() {
        // const document = await vscode.workspace.openTextDocument(vscode.Uri.file(this.absolutePath));
        // this.eol = document.eol === vscode.EndOfLine.LF ? '\n' : '\r\n';
    }

    async closeVirtualDocument() {
        if (!this.foreground) {
            return;
        }
        await this.virtualEditor.closeVirtualDiffDocument({absolutePath: this.absolutePath});
    }

    protected async closeDocument() {
        if (!this.foreground) {
            return;
        }
        await this.virtualEditor.closeDocument({absolutePath: this.absolutePath});
    }

    protected async createDocument(content: string | null) {
        await writeFile(this.absolutePath, content || '', 'utf-8');
    }

    /**
     * 总共有4种格式的路径，idea在windows上无法处理第一个种，统一转成第三种
     * 1. c:\Users\<USER>\IdeaProjects\untitled\a.js
     * 2. c:\Users\<USER>\IdeaProjects\untitled/a.js
     * 3. C:/Users/<USER>/IdeaProjects/untitled/a.js
     * 4. /Users/<USER>/IdeaProjects/untitled/a.js
     */
    get normalizedAbsolutedPath() {
        return isJetbrains ? formatWin32PathSep(this.absolutePath) : this.absolutePath;
    }

    protected async saveDocumentWithReplaceContent(content: string) {
        await this.virtualEditor.saveDocumentWithReplaceContent({
            absolutePath: this.normalizedAbsolutedPath,
            content,
        });
        await this.refreshProjectTree();
    }

    protected async saveDocumentWithReplaceContentAndOpen(content: string) {
        await this.virtualEditor.saveDocumentWithReplaceContentAndOpen({
            absolutePath: this.normalizedAbsolutedPath,
            content,
        });
        await this.refreshProjectTree();
    }

    protected async getVirtualDocumentText() {
        const {content, existed} = await this.virtualEditor.getVirtualDiffDocument({
            absolutePath: this.absolutePath,
        });
        if (existed) {
            return content;
        }
        else {
            return this.content;
        }
    }

    setForeground(foreground: boolean) {
        this.foreground = foreground;
    }

    async openDiff() {
        const originalContent = await this.getDocumentText();
        await this.virtualEditor.openVirtualDiffDocument({
            absolutePath: this.absolutePath,
            content: originalContent,
            modified: this.content,
            stream: false,
        });
    }

    toAcceptedLabel() {
        return `${this.action}:${this.filePath}`;
    }
}
