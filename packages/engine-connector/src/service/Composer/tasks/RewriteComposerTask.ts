import {sleep} from '../utils/index.js';
import {ComposerCodeStatus} from '../types.js';
import {EditComposerTask} from './EditComposerTask.js';

export class RewriteComposerTask extends EditComposerTask {
    async update(content: string, closed: boolean = false) {
        if (this.status === ComposerCodeStatus.CANCELLED) {
            this.applyStreamFinished = true;
            return;
        }

        this.generatedContentLines.lines = content.split(/\r?\n/);
        this.applyStreamFinished = closed;
        if (closed) {
            this.content = content;
        }

        if (closed) {
            // eslint-disable-next-line max-depth
            while (!this.finish) {
                await sleep(100);
            }
        }
    }

    toAcceptedLabel() {
        return `create:${this.filePath}`;
    }
}
