import {Task, AbstractTask} from './Task.js';
import {isFileExist} from '../../../utils/fs.js';
import {ComposerCodeStatus} from '../types.js';

export class DeleteComposerTask extends Task implements AbstractTask {
    actionName = '删除';
    update() {}
    async start() {
        const file = await this.getDocumentText();
        this.originalContent = file.toString();
        this.content = '';
        this.status = ComposerCodeStatus.DONE;
    }

    async revert() {
        this.reject();
        // 只有文件不存在的时候需要还原
        if (!await isFileExist(this.absolutePath)) {
            await this.createDocument(this.originalContent);
            await this.openDocument();
        }
    }

    async save() {
        this.accept();
        // 关闭tab, 并删除文件
        await this.closeVirtualDocument();
        await this.closeDocument();
        await this.removeDocument();
    }
    end() {}
}
