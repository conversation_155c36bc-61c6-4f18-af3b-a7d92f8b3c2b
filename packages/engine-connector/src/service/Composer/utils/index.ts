import _ from 'lodash';
export const sleep = (ms: number) => new Promise(r => setTimeout(r, ms));

const removeIllegalWindowsPathDiskPrefix = (path: string) => path.replace(/^[a-zA-Z]:/, '');
export const formatWin32PathSep = (unknownPath: string) => {
    return unknownPath.replace(/\\/g, '/');
};

export const parseUnknownFilePath = _.flow([
    removeIllegalWindowsPathDiskPrefix,
    formatWin32PathSep,
]);

export const isFilePathEqual = (leftPath: string, rightPath: string) => {
    if (!leftPath || !rightPath) {
        return false;
    }
    return parseUnknownFilePath(leftPath) === parseUnknownFilePath(rightPath);
};
