export const createCodeChunk = (repo: string, path: string, type = 'fullContent', content: string) => {
    return {
        repo,
        type,
        path,
        content,
        contentStart: {
            line: 0,
            column: 0,
        },
        contentEnd: {
            line: 0,
            column: 0,
        },
    };
};

export const createTerminalCodeChunk = (repo: string, content: string) => {
    return {
        repo,
        type: 'terminal',
        path: '',
        content,
        contentStart: {
            line: 0,
            column: 0,
        },
        contentEnd: {
            line: 0,
            column: 0,
        },
    };
};
