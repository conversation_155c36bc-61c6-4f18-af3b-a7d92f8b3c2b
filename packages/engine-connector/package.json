{"private": true, "name": "@comate/engine-connector", "version": "0.9.2", "type": "module", "exports": {".": {"import": "./dist/server.js", "require": "./dist/server.js"}}, "files": ["dist"], "publishConfig": {"registry": "http://registry.npm.baidu-int.com"}, "repository": {"type": "git", "url": "ssh://<EMAIL>:8235/baidu/ide/comate-plugin-host"}, "scripts": {"build": "rm -rf dist && tsc -p ./ && rollup -c fallbackServer.rollup.config.js", "dev": "node ./scripts/watch.js", "bundle": "rollup -c rollup.config.js"}, "dependencies": {"@comate/kernel": "0.9.2", "@comate/kernel-shared": "0.9.2", "@comate/plugin-engine": "^0.9.2", "@types/crypto-js": "^4.2.2", "@types/decompress": "^4.2.7", "@types/lodash": "^4.14.202", "@types/qs": "^6.9.11", "compare-versions": "^6.1.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "decompress": "^4.2.1", "diff": "^7.0.0", "fastest-levenshtein": "^1.0.16", "form-data": "^4.0.0", "js-yaml": "^4.1.0", "junk": "^4.0.1", "lodash": "^4.17.21", "lowdb": "^7.0.1", "marked": "^11.2.0", "qs": "^6.11.2", "simple-git": "^3.25.0", "systeminformation": "^5.23.19", "vscode-languageserver": "8.0.2", "vscode-languageserver-textdocument": "^1.0.11", "vscode-uri": "^3.0.8"}, "devDependencies": {"@comate/plugin-host": "^0.9.2", "@comate/plugin-shared-internals": "^0.9.2", "@reskript/config-lint": "^6.1.1", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "^5.0.5", "@types/diff": "^5.0.9", "@types/js-yaml": "^4.0.9", "rollup": "^4.9.2", "rollup-plugin-dts": "^6.1.0", "vscode-languageserver-types": "^3.17.5"}}