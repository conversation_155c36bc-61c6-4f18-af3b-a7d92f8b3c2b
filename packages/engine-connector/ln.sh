#!/bin/bash
## 判断环境变量 pipe 直接跳过
if [ "$AGILE_MODULE_NAME" = "baidu/ide/comate-plugin-host" ]; then
    echo "流水线跳过建立软链"
    exit 0  # 退出脚本
fi

## 建立链接到本地plugins的软连
current_dir=$(pwd)

if [[ "$current_dir" == *"packages/engine-connector"* ]]; then
    cd ./dist
    rm plugins
    ln -s ../../../plugins ./plugins
else
    cd packages/engine-connector/dist
    rm plugins
    ln -s ../../../plugins ./plugins
fi
