import childProcess from 'node:child_process';
import fs from 'node:fs/promises';
import readline from 'node:readline';
import path from 'node:path';
import url from 'node:url';

const __filename = url.fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function exec(command) {
    return new Promise((resolve, reject) => {
        childProcess.exec(command, (error, stdout) => {
            if (error) {
                reject(error);
                return;
            }
            resolve(stdout);
        });
    });
}

async function pathExists(path) {
    try {
        await fs.access(path);
        return true;
    } catch (error) {
        if (error.code === 'ENOENT') {
            return false;
        }
        throw error;
    }
}


async function main() {
    // 检查是否存在目录
    const exists = await pathExists('../bundle');
    if (exists) {
        await exec('rm -rf ../bundle');
    }
    await exec('npm run bundle:engine');
    await exec('cp package.json bundle');
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout,
    });
    let repoPath = path.resolve(__dirname, '../../../../comate-plugin-host');
    rl.question('请输入 comate-plugin-host 的绝对路径，默认认为在相邻项目', answer => {
        if (answer.trim()) {
            repoPath = answer;
        }
        rl.close();
    });
    const pluginHostBundlePaths = ['@comate', '@comate-plugins'].map(v => path.resolve(repoPath, v));
    rl.on('close', async () => {
        if ((await Promise.all(pluginHostBundlePaths.map(v => pathExists(v)))).findIndex(v => !v) !== -1) {
            console.error('comate-plugin-host 依赖未找到，请检查是否打包完成');
            return;
        }
        await fs.mkdir('./bundle/node_modules', {recursive: true});
        await exec(`cp -r ${pluginHostBundlePaths[0]} ./bundle/node_modules/@comate`);
        await fs.mkdir('./bundle/plugins', {recursive: true});
        await exec(`cp -r ${pluginHostBundlePaths[1]}/* ./bundle/plugins`);
        process.exit(0);
    });
}
main();
