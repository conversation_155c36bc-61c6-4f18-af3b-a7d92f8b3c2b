import fs from 'fs';
import path, { dirname } from 'path';
import { exec } from 'child_process';
import { fileURLToPath } from 'url';

const __dirname = dirname(fileURLToPath(import.meta.url));
const directoryToWatch = path.resolve(__dirname, '../src');
runCommand();
fs.watch(directoryToWatch, { recursive: true }, async (eventType, filename) => {
    if (!filename) {
        return;
    }
    runCommand();
});

function runCommand() {
    exec('npm run build', {
        cwd: path.resolve(__dirname, '../'),
    }, (error, stdout, stderr) => {
        if (error) {
            console.error(`执行 shell 命令时出错：${error, stderr, stdout}`);
            return;
        }
        console.log('命令执行成功:' + new Date());
        console.log(stdout, stderr);
        const plugins = fs.readdirSync(path.resolve(__dirname, '../../../plugins'));
        console.log('plugins: ' + plugins);
        for (const pluginPath of plugins) {
            if (pluginPath === '.DS_Store') {
                continue;
            }
            // 判断 dist assets package.json 是否存在，不存在就return
            if (
                !fs.existsSync(path.resolve(__dirname, '../../../plugins', pluginPath, 'dist')) ||
                !fs.existsSync(path.resolve(__dirname, '../../../plugins', pluginPath, 'assets')) ||
                !fs.existsSync(path.resolve(__dirname, '../../../plugins', pluginPath, 'package.json'))
            ) {
                continue;
            }

            copyFolder(
                path.resolve(__dirname, '../../../plugins', pluginPath, 'dist'),
                path.resolve(__dirname, '../dist/plugins', pluginPath, 'dist')
            );
            copyFolder(
                path.resolve(__dirname, '../../../plugins', pluginPath, 'assets'),
                path.resolve(__dirname, '../dist/plugins', pluginPath, 'assets')
            );
            fs.copyFileSync(
                path.resolve(__dirname, '../../../plugins', pluginPath, 'package.json'),
                path.resolve(__dirname, '../dist/plugins', pluginPath, 'package.json')
            );
        }
    });
}

function copyFolder(sourceDir, targetDir) {
    // 创建目标文件夹
    fs.mkdirSync(targetDir, { recursive: true });

    // 读取源文件夹中的文件/子文件夹
    const files = fs.readdirSync(sourceDir);

    files.forEach(file => {
        const sourcePath = path.join(sourceDir, file);
        const targetPath = path.join(targetDir, file);

        // 获取文件/文件夹的详细信息
        const stats = fs.statSync(sourcePath);

        if (stats.isFile()) {
            // 如果是文件，直接拷贝
            fs.copyFileSync(sourcePath, targetPath);
        } else if (stats.isDirectory()) {
            // 如果是文件夹，递归拷贝子文件夹
            copyFolder(sourcePath, targetPath);
        }
    });
}
