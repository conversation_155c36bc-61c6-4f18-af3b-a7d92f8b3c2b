interface PendingTask {
    promise: Promise<any>;
    resolve: (param: any) => void;
}

function isTaskObjectInitialized(value: Partial<PendingTask>): value is PendingTask {
    return typeof value.promise === 'object' && typeof value.resolve === 'function';
}

function createTask(): PendingTask {
    const task: Partial<PendingTask> = {};
    task.promise = new Promise<void>(resolve => (task.resolve = resolve));

    // 类型处理，不可能进这个分支
    if (!isTaskObjectInitialized(task)) {
        throw new Error('Failed to create a pending task due to unexpected system error');
    }

    return task;
}

export class TaskManager<K> {
    private readonly tasks = new Map<K, PendingTask>();

    start(key: K): Promise<any> {
        const task = this.tasks.get(key) ?? createTask();
        this.tasks.set(key, task);
        return task.promise;
    }

    resolve(key: K, data?: any) {
        const task = this.tasks.get(key);

        if (task) {
            task.resolve(data);
            this.tasks.delete(key);
        }

        return !!task;
    }

    /**
     * Remove a task without resolving it
     * @param key The task key to remove
     * @returns true if a task was removed, false otherwise
     */
    remove(key: K): boolean {
        return this.tasks.delete(key);
    }
}

interface QueueItemResolved<T> {
    state: 'resolved';
    value: T;
}

interface YieldResult<T> {
    value: T;
    done: false;
}

interface QueueItemPending<T> {
    state: 'pending';
    promise: Promise<YieldResult<T>>;
    resolve: (data: YieldResult<T>) => void;
}

export class StreamingTask<T = any> implements AsyncIterable<T> {
    private chunksCount = -1;

    private readonly queue: Array<QueueItemResolved<T> | QueueItemPending<T>> = [];

    put(index: number, value: T, done: boolean) {
        if (this.chunksCount >= 0) {
            return;
        }

        const item = this.queue.at(index);

        if (item && item.state === 'pending') {
            item.resolve({value, done: false});
        }
        else {
            const item: QueueItemResolved<T> = {
                state: 'resolved',
                value,
            };
            this.queue[index] = item;
        }

        if (done) {
            this.chunksCount = this.queue.length;
        }
    }

    [Symbol.asyncIterator](): AsyncIterator<T> {
        const state = {
            cursor: 0,
        };
        const pull = (): Promise<IteratorResult<T>> => {
            const index = state.cursor;
            if (this.chunksCount >= 0 && index >= this.chunksCount) {
                return Promise.resolve({value: undefined, done: true});
            }

            const item = this.queue.at(index);
            state.cursor++;

            if (item) {
                return item.state === 'resolved' ? Promise.resolve({value: item.value, done: false}) : item.promise;
            }

            const pendingItem: Partial<QueueItemPending<T>> = {state: 'pending'};
            pendingItem.promise = new Promise<YieldResult<T>>(resolve => (pendingItem.resolve = resolve));
            this.queue[index] = pendingItem as QueueItemPending<T>;
            return pendingItem.promise;
        };
        return {
            next: pull,
        };
    }
}

export class StreamingTaskManager<K> {
    private readonly tasks = new Map<K, StreamingTask>();

    start(key: K): StreamingTask {
        const task = this.tasks.get(key) ?? new StreamingTask();
        this.tasks.set(key, task);
        return task;
    }

    put(key: K, index: number, data: any, done: boolean) {
        const task = this.tasks.get(key);

        if (task) {
            task.put(index, data, done);

            if (done) {
                this.tasks.delete(key);
            }
        }

        return !!task;
    }
}
