======== original
import React, { useState } from 'react';
import TodoItem from './TodoItem';

const TodoList = () => {
    const [todos, setTodos] = useState([]);
    const [newTodo, setNewTodo] = useState('');

    const handleAddTodo = () => {
        if (newTodo.trim() !== '') {
            setTodos([...todos, newTodo]);
            setNewTodo('');
        }
    };

    const handleDeleteTodo = (index) => {
        const newTodos = todos.filter((_, todoIndex) => todoIndex !== index);
        setTodos(newTodos);
    };

    return (
        <div>
            <h1>Todo List</h1>
            <input
                type=\"text\"
                value={newTodo}
                onChange={(e) => setNewTodo(e.target.value)}
                placeholder=\"Enter a new todo\"
            />
            <button onClick={handleAddTodo}>Add</button>
            <ul>
                {todos.map((todo, index) => (
                    <TodoItem
                        key={index}
                        index={index}
                        todo={todo}
                        onDelete={handleDeleteTodo}
                    />
                ))}
            </ul>
        </div>
    );
};

export default TodoList;
======== diff
--- src/components/TodoList.js
+++ src/components/TodoList.js
@@ ... @@
-import React, { useState } from 'react';
+import React, { useState, ChangeEvent } from 'react';
@@ ... @@
const TodoList = () => {
-    const [todos, setTodos] = useState([]);
-    const [newTodo, setNewTodo] = useState('');
+interface TodoItemProps {
+    index: number;
+    todo: string;
+    onDelete: (index: number) => void;
+}
+
+const TodoList: React.FC = () => {
+    const [todos, setTodos] = useState<string[]>([]);
+    const [newTodo, setNewTodo] = useState<string>('');

-    const handleAddTodo = () => {
+    const handleAddTodo = (): void => {
         if (newTodo.trim() !== '') {
             setTodos([...todos, newTodo]);
             setNewTodo('');
         }
     };

-    const handleDeleteTodo = (index) => {
+    const handleDeleteTodo = (index: number): void => {
         const newTodos = todos.filter((_, todoIndex) => todoIndex !== index);
         setTodos(newTodos);
     };

+    const handleInputChange = (e: ChangeEvent<HTMLInputElement>): void => {
+        setNewTodo(e.target.value);
+    };
+
     return (
         <div>
             <h1>Todo List</h1>
             <input
                 type=\"text\"
                 value={newTodo}
-                onChange={(e) => setNewTodo(e.target.value)}
+                onChange={handleInputChange}
                 placeholder=\"Enter a new todo\"
             />
             <button onClick={handleAddTodo}>Add</button>
             <ul>
======== applied
import React, { useState, ChangeEvent } from 'react';
import TodoItem from './TodoItem';

const TodoList = () => {
interface TodoItemProps {
    index: number;
    todo: string;
    onDelete: (index: number) => void;
}

const TodoList: React.FC = () => {
    const [todos, setTodos] = useState<string[]>([]);
    const [newTodo, setNewTodo] = useState<string>('');

    const handleAddTodo = (): void => {
        if (newTodo.trim() !== '') {
            setTodos([...todos, newTodo]);
            setNewTodo('');
        }
    };

    const handleDeleteTodo = (index: number): void => {
        const newTodos = todos.filter((_, todoIndex) => todoIndex !== index);
        setTodos(newTodos);
    };

    const handleInputChange = (e: ChangeEvent<HTMLInputElement>): void => {
        setNewTodo(e.target.value);
    };

    return (
        <div>
            <h1>Todo List</h1>
            <input
                type=\"text\"
                value={newTodo}
                onChange={handleInputChange}
                placeholder=\"Enter a new todo\"
            />
            <button onClick={handleAddTodo}>Add</button>
            <ul>
                {todos.map((todo, index) => (
                    <TodoItem
                        key={index}
                        index={index}
                        todo={todo}
                        onDelete={handleDeleteTodo}
                    />
                ))}
            </ul>
        </div>
    );
};

export default TodoList;