======== original
import os
import json
from datetime import datetime, timedelta

class TodoList:
    def __init__(self, filename="tasks.json"):
        self.filename = filename
        self.tasks = self.load_tasks()

    def load_tasks(self):
        if os.path.exists(self.filename):
            with open(self.filename, "r") as f:
                return json.load(f)
        return []

    def save_tasks(self):
        with open(self.filename, "w") as f:
            json.dump(self.tasks, f, indent=2, default=str)

    def add_task(self, description, days_due=None):
        due_date = (datetime.now() + timedelta(days=days_due)).date() if days_due else None
        task = {"description": description, "completed": False, "due_date": str(due_date) if due_date else None}
        self.tasks.append(task)
        self.save_tasks()
        print(f"任务 '{description}' 已添加。")

    def view_tasks(self):
        if not self.tasks:
            print("待办事项列表为空。")
        else:
            print("待办事项列表：")
            for index, task in enumerate(self.tasks, 1):
                status = "✓" if task["completed"] else " "
                due_date = f" (截止日期: {task['due_date']})" if task['due_date'] else ""
                print(f"{index}. [{status}] {task['description']}{due_date}")

    def complete_task(self, index):
        if 1 <= index <= len(self.tasks):
            self.tasks[index-1]["completed"] = True
            self.save_tasks()
            print(f"任务 '{self.tasks[index-1]['description']}' 已标记为完成。")
        else:
            print("无效的任务索引。")

    def delete_task(self, index):
        if 1 <= index <= len(self.tasks):
            deleted_task = self.tasks.pop(index-1)
            self.save_tasks()
            print(f"任务 '{deleted_task['description']}' 已删除。")
        else:
            print("无效的任务索引。")

def clear_screen():
    os.system('cls' if os.name == 'nt' else 'clear')

def main():
    todo_list = TodoList()

    while True:
        clear_screen()
        print("\n===== 待办事项列表 =====")
        todo_list.view_tasks()
        print("\n选择操作：")
        print("1. 添加任务")
        print("2. 完成任务")
        print("3. 删除任务")
        print("4. 退出")

        choice = input("请输入选项（1-4）：")

        if choice == '1':
            description = input("请输入新任务：")
            days_due = input("请输入截止天数（可选，直接回车跳过）：")
            days_due = int(days_due) if days_due.isdigit() else None
            todo_list.add_task(description, days_due)
        elif choice == '2':
            index = input("请输入要完成的任务编号：")
            if index.isdigit():
                todo_list.complete_task(int(index))
            else:
                print("请输入有效的数字。")
        elif choice == '3':
            index = input("请输入要删除的任务编号：")
            if index.isdigit():
                todo_list.delete_task(int(index))
            else:
                print("请输入有效的数字。")
        elif choice == '4':
            print("感谢使用，再见！")
            break
        else:
            print("无效选项，请重试。")

        input("\n按回车键继续...")

if __name__ == "__main__":
    main()
======== diff
--- todolist.py
+++ todolist.py
@@ -1,68 +1,76 @@
 import os
-import json
 from datetime import datetime, timedelta
+from pymongo import MongoClient
+from bson import ObjectId

 class TodoList:
-    def __init__(self, filename=\"tasks.json\"):
-        self.filename = filename
-        self.tasks = self.load_tasks()
-
-    def load_tasks(self):
-        if os.path.exists(self.filename):
-            with open(self.filename, \"r\") as f:
-                return json.load(f)
-        return []
-
-    def save_tasks(self):
-        with open(self.filename, \"w\") as f:
-            json.dump(self.tasks, f, indent=2, default=str)
+    def __init__(self):
+        self.client = MongoClient('mongodb://localhost:27017/')
+        self.db = self.client['todolist']
+        self.tasks = self.db.tasks

     def add_task(self, description, days_due=None):
         due_date = (datetime.now() + timedelta(days=days_due)).date() if days_due else None
-        task = {\"description\": description, \"completed\": False, \"due_date\": str(due_date) if due_date else None}
-        self.tasks.append(task)
-        self.save_tasks()
+        task = {
+            \"description\": description,
+            \"completed\": False,
+            \"due_date\": due_date
+        }
+        self.tasks.insert_one(task)
         print(f\"任务 '{description}' 已添加。\")

     def view_tasks(self):
-        if not self.tasks:
+        tasks = list(self.tasks.find())
+        if not tasks:
             print(\"待办事项列表为空。\")
         else:
             print(\"待办事项列表：\")
-            for index, task in enumerate(self.tasks, 1):
+            for index, task in enumerate(tasks, 1):
                 status = \"✓\" if task[\"completed\"] else \" \"
-                due_date = f\" (截止日期: {task['due_date']})\" if task['due_date'] else \"\"
+                due_date = f\" (截止日期: {task['due_date'].strftime('%Y-%m-%d')})\" if task['due_date'] else \"\"
                 print(f\"{index}. [{status}] {task['description']}{due_date}\")

     def complete_task(self, index):
-        if 1 <= index <= len(self.tasks):
-            self.tasks[index-1][\"completed\"] = True
-            self.save_tasks()
-            print(f\"任务 '{self.tasks[index-1]['description']}' 已标记为完成。\")
+        tasks = list(self.tasks.find())
+        if 1 <= index <= len(tasks):
+            task = tasks[index-1]
+            self.tasks.update_one({\"_id\": task[\"_id\"]}, {\"$set\": {\"completed\": True}})
+            print(f\"任务 '{task['description']}' 已标记为完成。\")
         else:
             print(\"无效的任务索引。\")

     def delete_task(self, index):
-        if 1 <= index <= len(self.tasks):
-            deleted_task = self.tasks.pop(index-1)
-            self.save_tasks()
+        tasks = list(self.tasks.find())
+        if 1 <= index <= len(tasks):
+            deleted_task = tasks[index-1]
+            self.tasks.delete_one({\"_id\": deleted_task[\"_id\"]})
             print(f\"任务 '{deleted_task['description']}' 已删除。\")
         else:
             print(\"无效的任务索引。\")

+    def close(self):
+        self.client.close()
+
 def clear_screen():
     os.system('cls' if os.name == 'nt' else 'clear')

 def main():
     todo_list = TodoList()

-    while True:
-        clear_screen()
-        print(\"\\n===== 待办事项列表 =====\")
-        todo_list.view_tasks()
-        print(\"\\n选择操作：\")
-        print(\"1. 添加任务\")
-        print(\"2. 完成任务\")
-        print(\"3. 删除任务\")
-        print(\"4. 退出\")
+    try:
+        while True:
+            clear_screen()
+            print(\"\\n===== 待办事项列表 =====\")
+            todo_list.view_tasks()
+            print(\"\\n选择操作：\")
+            print(\"1. 添加任务\")
+            print(\"2. 完成任务\")
+            print(\"3. 删除任务\")
+            print(\"4. 退出\")

-        choice = input(\"请输入选项（1-4）：\")
+            choice = input(\"请输入选项（1-4）：\")

-        if choice == '1':
-            description = input(\"请输入新任务：\")
-            days_due = input(\"请输入截止天数（可选，直接回车跳过）：\")
-            days_due = int(days_due) if days_due.isdigit() else None
-            todo_list.add_task(description, days_due)
-        elif choice == '2':
-            index = input(\"请输入要完成的任务编号：\")
-            if index.isdigit():
-                todo_list.complete_task(int(index))
-            else:
-                print(\"请输入有效的数字。\")
-        elif choice == '3':
-            index = input(\"请输入要删除的任务编号：\")
-            if index.isdigit():
-                todo_list.delete_task(int(index))
+            if choice == '1':
+                description = input(\"请输入新任务：\")
+                days_due = input(\"请输入截止天数（可选，直接回车跳过）：\")
+                days_due = int(days_due) if days_due.isdigit() else None
+                todo_list.add_task(description, days_due)
+            elif choice == '2':
+                index = input(\"请输入要完成的任务编号：\")
+                if index.isdigit():
+                    todo_list.complete_task(int(index))
+                else:
+                    print(\"请输入有效的数字。\")
+            elif choice == '3':
+                index = input(\"请输入要删除的任务编号：\")
+                if index.isdigit():
+                    todo_list.delete_task(int(index))
+                else:
+                    print(\"请输入有效的数字。\")
+            elif choice == '4':
+                print(\"感谢使用，再见！\")
+                break
             else:
-                print(\"请输入有效的数字。\")
-        elif choice == '4':
-            print(\"感谢使用，再见！\")
-            break
-        else:
-            print(\"无效选项，请重试。\")
+                print(\"无效选项，请重试。\")

-        input(\"\\n按回车键继续...\")
+            input(\"\\n按回车键继续...\")
+    finally:
+        todo_list.close()

 if __name__ == \"__main__\":
     main()
======== applied
import os
from datetime import datetime, timedelta
from pymongo import MongoClient
from bson import ObjectId

class TodoList:
    def __init__(self):
        self.client = MongoClient('mongodb://localhost:27017/')
        self.db = self.client['todolist']
        self.tasks = self.db.tasks

    def add_task(self, description, days_due=None):
        due_date = (datetime.now() + timedelta(days=days_due)).date() if days_due else None
        task = {
            "description": description,
            "completed": False,
            "due_date": due_date
        }
        self.tasks.insert_one(task)
        print(f"任务 '{description}' 已添加。")

    def view_tasks(self):
        tasks = list(self.tasks.find())
        if not tasks:
            print("待办事项列表为空。")
        else:
            print("待办事项列表：")
            for index, task in enumerate(tasks, 1):
                status = "✓" if task["completed"] else " "
                due_date = f" (截止日期: {task['due_date'].strftime('%Y-%m-%d')})" if task['due_date'] else ""
                print(f"{index}. [{status}] {task['description']}{due_date}")

    def complete_task(self, index):
        tasks = list(self.tasks.find())
        if 1 <= index <= len(tasks):
            task = tasks[index-1]
            self.tasks.update_one({"_id": task["_id"]}, {"$set": {"completed": True}})
            print(f"任务 '{task['description']}' 已标记为完成。")
        else:
            print("无效的任务索引。")

    def delete_task(self, index):
        tasks = list(self.tasks.find())
        if 1 <= index <= len(tasks):
            deleted_task = tasks[index-1]
            self.tasks.delete_one({"_id": deleted_task["_id"]})
            print(f"任务 '{deleted_task['description']}' 已删除。")
        else:
            print("无效的任务索引。")

    def close(self):
        self.client.close()

def clear_screen():
    os.system('cls' if os.name == 'nt' else 'clear')

def main():
    todo_list = TodoList()

    try:
        while True:
            clear_screen()
            print("\n===== 待办事项列表 =====")
            todo_list.view_tasks()
            print("\n选择操作：")
            print("1. 添加任务")
            print("2. 完成任务")
            print("3. 删除任务")
            print("4. 退出")

            choice = input("请输入选项（1-4）：")

            if choice == '1':
                description = input("请输入新任务：")
                days_due = input("请输入截止天数（可选，直接回车跳过）：")
                days_due = int(days_due) if days_due.isdigit() else None
                todo_list.add_task(description, days_due)
            elif choice == '2':
                index = input("请输入要完成的任务编号：")
                if index.isdigit():
                    todo_list.complete_task(int(index))
                else:
                    print("请输入有效的数字。")
            elif choice == '3':
                index = input("请输入要删除的任务编号：")
                if index.isdigit():
                    todo_list.delete_task(int(index))
                else:
                    print("请输入有效的数字。")
            elif choice == '4':
                print("感谢使用，再见！")
                break
            else:
                print("无效选项，请重试。")

            input("\n按回车键继续...")
    finally:
        todo_list.close()

if __name__ == "__main__":
    main()