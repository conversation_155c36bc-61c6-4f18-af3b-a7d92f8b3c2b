======== original
package com.baidu.autocomate.infrastructure.agent.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Agent持久化对象（PO）类
 * 该类表示用于在数据库中存储agent信息的数据结构
 * 使用MyBatis-Plus注解将其映射到"auto_agent"表
 */
@Data
@TableName("auto_agent")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgentPO {

    /**
     * agent的唯一标识符
     * 该字段由数据库自动生成
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * agent的名称
     * 该字段映射到数据库中的"name"列
     */
    @TableField("name")
    private String name;

    /**
     * agent的描述
     * 该字段映射到数据库中的"description"列
     */
    @TableField("description")
    private String description;

}
======== diff
--- infrastructure/src/main/java/com/baidu/autocomate/infrastructure/agent/po/AgentPO.java
+++ infrastructure/src/main/java/com/baidu/autocomate/infrastructure/agent/po/AgentPO.java
@@ -11,9 +11,9 @@ import lombok.Data;
 import lombok.NoArgsConstructor;

 /**
- * Agent持久化对象（PO）类
- * 该类表示用于在数据库中存储agent信息的数据结构
- * 使用MyBatis-Plus注解将其映射到\"auto_agent\"表
+ * Agent Persistence Object (PO) class
+ * This class represents the data structure used to store agent information in the database
+ * It is mapped to the \"auto_agent\" table using MyBatis-Plus annotations
  */
 @Data
 @TableName(\"auto_agent\")
@@ -22,21 +22,21 @@ import lombok.NoArgsConstructor;
 public class AgentPO {

     /**
-     * agent的唯一标识符
-     * 该字段由数据库自动生成
+     * Unique identifier for the agent
+     * This field is automatically generated by the database
      */
     @TableId(value = \"id\", type = IdType.AUTO)
     private Long id;

     /**
-     * agent的名称
-     * 该字段映射到数据库中的\"name\"列
+     * Name of the agent
+     * This field is mapped to the \"name\" column in the database
      */
     @TableField(\"name\")
     private String name;

     /**
-     * agent的描述
-     * 该字段映射到数据库中的\"description\"列
+     * Description of the agent
+     * This field is mapped to the \"description\" column in the database
      */
     @TableField(\"description\")
     private String description;
======== applied
package com.baidu.autocomate.infrastructure.agent.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Agent Persistence Object (PO) class
 * This class represents the data structure used to store agent information in the database
 * It is mapped to the "auto_agent" table using MyBatis-Plus annotations
 */
@Data
@TableName("auto_agent")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgentPO {

    /**
     * Unique identifier for the agent
     * This field is automatically generated by the database
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * Name of the agent
     * This field is mapped to the "name" column in the database
     */
    @TableField("name")
    private String name;

    /**
     * Description of the agent
     * This field is mapped to the "description" column in the database
     */
    @TableField("description")
    private String description;

}