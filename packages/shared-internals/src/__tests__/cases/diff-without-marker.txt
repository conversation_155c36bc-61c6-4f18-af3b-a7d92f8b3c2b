======== original
const XLSX = require('xlsx');
const {applyDiff} = require('./diff.js');
const fs = require('fs');

// 读取 Excel 文件
function readExcelFile() {
    try {
        const workbook = XLSX.readFile('AutoApply-Diff-v1.xlsx');

        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];

        const data = XLSX.utils.sheet_to_json(worksheet);

        const dataAppendApplied = data.map(row => {
            const answer = row['DeepSeek-API(V1.0.1)-答案'];
            const change = row['change'];
            const original_code = row['original_code'];
            const applied = applyDiff(original_code, change, {eol: '\n'});
            return {...row, applied};
        });

        const newWorksheet = XLSX.utils.json_to_sheet(dataAppendApplied);

        const newWorkbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(newWorkbook, newWorksheet, sheetName);

        XLSX.writeFile(newWorkbook, 'output.xlsx');

        console.log('Data has been written to output.xlsx');

    } catch (error) {
        console.error('读取或写入 Excel 文件时发生错误:', error.message);
    }
}

// 执行函数
readExcelFile();
======== diff
const XLSX = require('xlsx');
const {applyDiff} = require('./diff.js');
const fs = require('fs');

// 读取 Excel 文件
function readExcelFile() {
    try {
        // 读取工作簿
        const workbook = XLSX.readFile('AutoApply-Diff-v1.xlsx');

        // 获取第一个工作表
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];

        // 将工作表转换为 JSON 对象
        const data = XLSX.utils.sheet_to_json(worksheet);

        // 处理数据并添加 'applied' 字段
        const dataAppendApplied = data.map(row => {
            const answer = row['DeepSeek-API(V1.0.1)-答案'];
            const change = row['change'];
            const original_code = row['original_code'];
            const applied = applyDiff(original_code, change, {eol: '\n'});
            return {...row, applied};
        });

        // 创建新的工作表
        const newWorksheet = XLSX.utils.json_to_sheet(dataAppendApplied);

        // 创建新的工作簿
        const newWorkbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(newWorkbook, newWorksheet, sheetName);

        // 写入新的 Excel 文件
        XLSX.writeFile(newWorkbook, 'output.xlsx');

        console.log('Data has been written to output.xlsx');

    } catch (error) {
        console.error('读取或写入 Excel 文件时发生错误:', error.message);
    }
}

// 执行函数
readExcelFile();
======== applied
