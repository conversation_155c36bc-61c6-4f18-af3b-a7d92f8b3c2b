======== original
import datetime
from pymongo import MongoClient
from bson import ObjectId

class TodoList:
    def __init__(self):
        # 连接到 MongoDB
        self.client = MongoClient('mongodb://localhost:27017/')
        self.db = self.client['todo_list_db']
        self.tasks_collection = self.db['tasks']

    def add_task(self, task):
        """添加新任务"""
        new_task = {
            "task": task,
            "completed": False,
            "date": datetime.datetime.now()
        }
        result = self.tasks_collection.insert_one(new_task)
        print(f"任务 '{task}' 已添加，ID: {result.inserted_id}")
        print(f"任务 '{task}' 已添加。")

    def view_tasks(self):
        """查看所有任务"""
        tasks = list(self.tasks_collection.find())
        if not tasks:
            print("当前没有任务。")
        else:
            for index, task in enumerate(tasks, 1):
                status = "完成" if task["completed"] else "未完成"
                print(f"{index}. [{status}] {task['task']} (添加于: {task['date'].strftime('%Y-%m-%d %H:%M:%S')}) ID: {task['_id']}")

    def mark_completed(self, task_index):
        """将任务标记为完成"""
        tasks = list(self.tasks_collection.find())
        if 1 <= task_index <= len(tasks):
            task = tasks[task_index-1]
            self.tasks_collection.update_one({"_id": task["_id"]}, {"$set": {"completed": True}})
            print(f"任务 '{task['task']}' 已标记为完成。")
        else:
            print("无效的任务索引。")

    def delete_task(self, task_index):
        """删除任务"""
        tasks = list(self.tasks_collection.find())
        if 1 <= task_index <= len(tasks):
            task = tasks[task_index-1]
            self.tasks_collection.delete_one({"_id": task["_id"]})
            print(f"任务 '{task['task']}' 已删除。")
        else:
            print("无效的任务索引。")

    def close_connection(self):
        """关闭MongoDB连接"""
        self.client.close()

def main():
    todo_list = TodoList()

    while True:
        print("\n==== Todo List ====")
        print("1. 添加任务")
        print("2. 查看任务")
        print("3. 标记任务为完成")
        print("4. 删除任务")
        print("5. 退出")

        choice = input("请选择操作 (1-5): ")

        if choice == '1':
            task = input("请输入新任务: ")
            todo_list.add_task(task)
        elif choice == '2':
            todo_list.view_tasks()
        elif choice == '3':
            todo_list.view_tasks()
            task_index = int(input("请输入要标记为完成的任务编号: "))
            todo_list.mark_completed(task_index)
        elif choice == '4':
            todo_list.view_tasks()
            task_index = int(input("请输入要删除的任务编号: "))
            todo_list.delete_task(task_index)
        elif choice == '5':
            print("感谢使用，再见！")
            todo_list.close_connection()
            break
        else:
            print("无效的选择，请重试。")

if __name__ == "__main__":
    main()
======== diff
--- todo_list.py
+++ todo_list.py
@@ -1,5 +1,6 @@
 import datetime
 from pymongo import MongoClient
+from pymongo.errors import ConnectionFailure
 from bson import ObjectId

 class TodoList:
@@ -7,7 +8,13 @@ class TodoList:
         # 连接到 MongoDB
         self.client = MongoClient('mongodb://localhost:27017/')
         self.db = self.client['todo_list_db']
-        self.tasks_collection = self.db['tasks']
+        try:
+            # 验证连接
+            self.client.admin.command('ismaster')
+            self.tasks_collection = self.db['tasks']
+            print(\"成功连接到 MongoDB\")
+        except ConnectionFailure:
+            print(\"无法连接到 MongoDB，请确保 MongoDB 服务正在运行\")

     def add_task(self, task):
         \"\"\"添加新任务\"\"\"
@@ -17,8 +24,7 @@ class TodoList:
             \"date\": datetime.datetime.now()
         }
         result = self.tasks_collection.insert_one(new_task)
-        print(f\"任务 '{task}' 已添加，ID: {result.inserted_id}\")
-        print(f\"任务 '{task}' 已添加。\")
+        print(f\"任务 '{task}' 已添加，ID: {result.inserted_id}\")

     def view_tasks(self):
         \"\"\"查看所有任务\"\"\"
@@ -32,22 +38,22 @@ class TodoList:

     def mark_completed(self, task_index):
         \"\"\"将任务标记为完成\"\"\"
-        tasks = list(self.tasks_collection.find())
-        if 1 <= task_index <= len(tasks):
-            task = tasks[task_index-1]
-            self.tasks_collection.update_one({\"_id\": task[\"_id\"]}, {\"$set\": {\"completed\": True}})
-            print(f\"任务 '{task['task']}' 已标记为完成。\")
+        task = self.tasks_collection.find_one(sort=[(\"date\", 1)], skip=task_index-1)
+        if task:
+            self.tasks_collection.update_one({\"_id\": task[\"_id\"]}, {\"$set\": {\"completed\": True}})
+            print(f\"任务 '{task['task']}' 已标记为完成。\")
         else:
             print(\"无效的任务索引。\")

     def delete_task(self, task_index):
         \"\"\"删除任务\"\"\"
-        tasks = list(self.tasks_collection.find())
-        if 1 <= task_index <= len(tasks):
-            task = tasks[task_index-1]
-            self.tasks_collection.delete_one({\"_id\": task[\"_id\"]})
-            print(f\"任务 '{task['task']}' 已删除。\")
+        task = self.tasks_collection.find_one(sort=[(\"date\", 1)], skip=task_index-1)
+        if task:
+            self.tasks_collection.delete_one({\"_id\": task[\"_id\"]})
+            print(f\"任务 '{task['task']}' 已删除。\")
         else:
             print(\"无效的任务索引。\")
+
+    def count_tasks(self):
+        \"\"\"统计任务总数\"\"\"
+        return self.tasks_collection.count_documents({})

     def close_connection(self):
         \"\"\"关闭MongoDB连接\"\"\"
@@ -55,31 +61,38 @@ class TodoList:

 def main():
     todo_list = TodoList()
-
-    while True:
-        print(\"\\n==== Todo List ====\")
-        print(\"1. 添加任务\")
-        print(\"2. 查看任务\")
-        print(\"3. 标记任务为完成\")
-        print(\"4. 删除任务\")
-        print(\"5. 退出\")
-
-        choice = input(\"请选择操作 (1-5): \")
-
-        if choice == '1':
-            task = input(\"请输入新任务: \")
-            todo_list.add_task(task)
-        elif choice == '2':
-            todo_list.view_tasks()
-        elif choice == '3':
-            todo_list.view_tasks()
-            task_index = int(input(\"请输入要标记为完成的任务编号: \"))
-            todo_list.mark_completed(task_index)
-        elif choice == '4':
-            todo_list.view_tasks()
-            task_index = int(input(\"请输入要删除的任务编号: \"))
-            todo_list.delete_task(task_index)
-        elif choice == '5':
-            print("感谢使用，再见！")
-            todo_list.close_connection()
-            break
-        else:
-            print("无效的选择，请重试。")
+
+    try:
+        while True:
+            print(\"\\n==== Todo List ====\")
+            print(\"1. 添加任务\")
+            print(\"2. 查看任务\")
+            print(\"3. 标记任务为完成\")
+            print(\"4. 删除任务\")
+            print(\"5. 退出\")
+
+            choice = input(\"请选择操作 (1-5): \")
+
+            if choice == '1':
+                task = input(\"请输入新任务: \")
+                todo_list.add_task(task)
+            elif choice == '2':
+                todo_list.view_tasks()
+            elif choice == '3':
+                if todo_list.count_tasks() > 0:
+                    todo_list.view_tasks()
+                    task_index = int(input(\"请输入要标记为完成的任务编号: \"))
+                    todo_list.mark_completed(task_index)
+                else:
+                    print(\"当前没有任务。\")
+            elif choice == '4':
+                if todo_list.count_tasks() > 0:
+                    todo_list.view_tasks()
+                    task_index = int(input(\"请输入要删除的任务编号: \"))
+                    todo_list.delete_task(task_index)
+                else:
+                    print(\"当前没有任务。\")
+            elif choice == '5':
+                break
+            else:
+                print(\"无效的选择，请重试。\")
+    finally:
+        print(\"正在关闭连接...\")
+        todo_list.close_connection()
+        print(\"感谢使用，再见！\")
======== applied
import datetime
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure
from bson import ObjectId

class TodoList:
    def __init__(self):
        # 连接到 MongoDB
        self.client = MongoClient('mongodb://localhost:27017/')
        self.db = self.client['todo_list_db']
        try:
            # 验证连接
            self.client.admin.command('ismaster')
            self.tasks_collection = self.db['tasks']
            print("成功连接到 MongoDB")
        except ConnectionFailure:
            print("无法连接到 MongoDB，请确保 MongoDB 服务正在运行")

    def add_task(self, task):
        """添加新任务"""
        new_task = {
            "task": task,
            "completed": False,
            "date": datetime.datetime.now()
        }
        result = self.tasks_collection.insert_one(new_task)
        print(f"任务 '{task}' 已添加，ID: {result.inserted_id}")

    def view_tasks(self):
        """查看所有任务"""
        tasks = list(self.tasks_collection.find())
        if not tasks:
            print("当前没有任务。")
        else:
            for index, task in enumerate(tasks, 1):
                status = "完成" if task["completed"] else "未完成"
                print(f"{index}. [{status}] {task['task']} (添加于: {task['date'].strftime('%Y-%m-%d %H:%M:%S')}) ID: {task['_id']}")

    def mark_completed(self, task_index):
        """将任务标记为完成"""
        task = self.tasks_collection.find_one(sort=[("date", 1)], skip=task_index-1)
        if task:
            self.tasks_collection.update_one({"_id": task["_id"]}, {"$set": {"completed": True}})
            print(f"任务 '{task['task']}' 已标记为完成。")
        else:
            print("无效的任务索引。")

    def delete_task(self, task_index):
        """删除任务"""
        task = self.tasks_collection.find_one(sort=[("date", 1)], skip=task_index-1)
        if task:
            self.tasks_collection.delete_one({"_id": task["_id"]})
            print(f"任务 '{task['task']}' 已删除。")
        else:
            print("无效的任务索引。")

    def count_tasks(self):
        """统计任务总数"""
        return self.tasks_collection.count_documents({})

    def close_connection(self):
        """关闭MongoDB连接"""
        self.client.close()

def main():
    todo_list = TodoList()

    try:
        while True:
            print("\n==== Todo List ====")
            print("1. 添加任务")
            print("2. 查看任务")
            print("3. 标记任务为完成")
            print("4. 删除任务")
            print("5. 退出")

            choice = input("请选择操作 (1-5): ")

            if choice == '1':
                task = input("请输入新任务: ")
                todo_list.add_task(task)
            elif choice == '2':
                todo_list.view_tasks()
            elif choice == '3':
                if todo_list.count_tasks() > 0:
                    todo_list.view_tasks()
                    task_index = int(input("请输入要标记为完成的任务编号: "))
                    todo_list.mark_completed(task_index)
                else:
                    print("当前没有任务。")
            elif choice == '4':
                if todo_list.count_tasks() > 0:
                    todo_list.view_tasks()
                    task_index = int(input("请输入要删除的任务编号: "))
                    todo_list.delete_task(task_index)
                else:
                    print("当前没有任务。")
            elif choice == '5':
                break
            else:
                print("无效的选择，请重试。")
    finally:
        print("正在关闭连接...")
        todo_list.close_connection()
        print("感谢使用，再见！")

if __name__ == "__main__":
    main()