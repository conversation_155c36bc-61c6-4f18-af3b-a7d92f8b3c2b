======== original
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
python autodebug demo
"""

import csv

def calculate_average_price(data_dict, stock_code):
    if stock_code in data_dict:
        return sum(data_dict[stock_code]) / len(data_dict[stock_code])
    else:
        return 0.0


def read_and_process_csv(file_path: str) -> dict:
    stock_data = {}

    with open(file_path, 'r') as csvfile:
        csvreader = csv.reader(csvfile)
        _ = next(csvreader)

        for row in csvreader:
            stock_code = row[1]
            close_price = row[2]

            close_price = float(close_price)

            # 更新股票数据字典，如果股票代码不存在则创建新列表
            if stock_code not in stock_data:
                stock_data[stock_code] = []
            stock_data[stock_code].append(close_price)

    average_prices = {}
    for stock_code in stock_data:
        average_prices[stock_code] = calculate_average_price(stock_data, stock_code)

    return average_prices


if __name__ == '__main__':
    average_prices = read_and_process_csv('stock_price.csv')
    print(average_prices)
======== diff
--- value_error_3.py
+++ value_error_3.py
@@ -7,8 +7,15 @@
import csv

-def calculate_average_price(data_dict, stock_code):
+def calculate_average_price(data_dict: dict, stock_code: str) -> float:
+    """
+    计算特定股票代码对应的平均价格。
+
+    参数:
+    data_dict (dict): 包含股票数据的字典，键为股票代码，值为价格列表。
+    stock_code (str): 要计算平均价格的股票代码。
+
+    返回:
+    float: 给定股票代码的平均价格，如果股票代码不存在则返回0.0。
+    """
     if stock_code in data_dict:
         return sum(data_dict[stock_code]) / len(data_dict[stock_code])
     else:
@@ -12,8 +19,16 @@
        return 0.0

def read_and_process_csv(file_path: str) -> dict:
+    """
+    读取CSV文件并处理股票数据，计算每个股票代码的平均价格。
+
+    参数:
+    file_path (str): CSV文件的路径。
+
+    返回:
+    dict: 每个股票代码的平均价格，键为股票代码，值为平均价格。
+    """
     stock_data = {}

     with open(file_path, 'r') as csvfile:
         csvreader = csv.reader(csvfile)
         _ = next(csvreader)

         for row in csvreader:
             stock_code = row[1]
             close_price = row[2]

             close_price = float(close_price)

             # 更新股票数据字典，如果股票代码不存在则创建新列表
@@ -26,6 +41,9 @@

     average_prices = {}
     for stock_code in stock_data:
         average_prices[stock_code] = calculate_average_price(stock_data, stock_code)

     return average_prices

 if __name__ == '__main__':
     average_prices = read_and_process_csv('stock_price.csv')
     print(average_prices)
======== applied
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
python autodebug demo
"""

import csv

def calculate_average_price(data_dict: dict, stock_code: str) -> float:
    """
    计算特定股票代码对应的平均价格。

    参数:
    data_dict (dict): 包含股票数据的字典，键为股票代码，值为价格列表。
    stock_code (str): 要计算平均价格的股票代码。

    返回:
    float: 给定股票代码的平均价格，如果股票代码不存在则返回0.0。
    """
    if stock_code in data_dict:
        return sum(data_dict[stock_code]) / len(data_dict[stock_code])
    else:
        return 0.0


def read_and_process_csv(file_path: str) -> dict:
    """
    读取CSV文件并处理股票数据，计算每个股票代码的平均价格。

    参数:
    file_path (str): CSV文件的路径。

    返回:
    dict: 每个股票代码的平均价格，键为股票代码，值为平均价格。
    """
    stock_data = {}

    with open(file_path, 'r') as csvfile:
        csvreader = csv.reader(csvfile)
        _ = next(csvreader)

        for row in csvreader:
            stock_code = row[1]
            close_price = row[2]

            close_price = float(close_price)

            # 更新股票数据字典，如果股票代码不存在则创建新列表
            if stock_code not in stock_data:
                stock_data[stock_code] = []
            stock_data[stock_code].append(close_price)

    average_prices = {}
    for stock_code in stock_data:
        average_prices[stock_code] = calculate_average_price(stock_data, stock_code)

    return average_prices


if __name__ == '__main__':
    average_prices = read_and_process_csv('stock_price.csv')
    print(average_prices)