======== original
from fastapi import FastAPI, UploadFile, File, HTTPException
from fastapi.responses import FileResponse
from pydantic import BaseModel
from sqlalchemy import create_engine, Column, Integer, String, Text, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
import os

app = FastAPI()

DATABASE_URL = "sqlite:///./database.db"
UPLOAD_FOLDER = 'uploads'
MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB limit

# Ensure the upload folder exists
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

# Database setup
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Models
class User(Base):
    __tablename__ = 'users'
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(80), unique=True, nullable=False)
    password = Column(String(120), nullable=False)
    images = relationship("Image", back_populates="owner")
    comments = relationship("Comment", back_populates="author")

class Image(Base):
    __tablename__ = 'images'
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    filename = Column(String(120), nullable=False)
    description = Column(Text, nullable=True)
    owner = relationship("User", back_populates="images")

class Comment(Base):
    __tablename__ = 'comments'
    id = Column(Integer, primary_key=True, index=True)
    image_id = Column(Integer, ForeignKey('images.id'), nullable=False)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    content = Column(Text, nullable=False)
    author = relationship("User", back_populates="comments")

# Create database tables
Base.metadata.create_all(bind=engine)

@app.get("/")
async def read_root():
    return {"Hello": "World"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
======== diff
--- backend/main.py
+++ backend/main.py
@@ -24,7 +24,15 @@
     os.makedirs(UPLOAD_FOLDER)

 # Database setup
 engine = create_engine(DATABASE_URL)
 SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
 Base = declarative_base()

+def get_db():
+    """
+    Provides a database session and ensures it gets closed after use.
+
+    Yields:
+        Session: A SQLAlchemy database session
+    """
+    db = SessionLocal()
+    try:
+        yield db
+    finally:
+        db.close()

 # Models
-class User(Base):
+class User(Base):
+    """
+    SQLAlchemy User model representing a user in the database.
+
+    Attributes:
+        id (int): Primary key for the user.
+        username (str): Unique username for the user.
+        password (str): Hashed password for the user.
+        images (relationship): Relationship with the Image model.
+        comments (relationship): Relationship with the Comment model.
+    """
     __tablename__ = 'users'
     id = Column(Integer, primary_key=True, index=True)
     username = Column(String(80), unique=True, nullable=False)
     password = Column(String(120), nullable=False)
     images = relationship(\"Image\", back_populates=\"owner\")
     comments = relationship(\"Comment\", back_populates=\"author\")

-class Image(Base):
+class Image(Base):
+    """
+    SQLAlchemy Image model representing an image uploaded by a user.
+
+    Attributes:
+        id (int): Primary key for the image.
+        user_id (int): Foreign key to the user who uploaded the image.
+        filename (str): Name of the uploaded image file.
+        description (str, optional): Description of the image.
+        owner (relationship): Relationship with the User model.
+    """
     __tablename__ = 'images'
     id = Column(Integer, primary_key=True, index=True)
     user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
     filename = Column(String(120), nullable=False)
     description = Column(Text, nullable=True)
     owner = relationship(\"User\", back_populates=\"images\")

-class Comment(Base):
+class Comment(Base):
+    """
+    SQLAlchemy Comment model representing a comment made by a user on an image.
+
+    Attributes:
+        id (int): Primary key for the comment.
+        image_id (int): Foreign key to the image being commented on.
+        user_id (int): Foreign key to the user who made the comment.
+        content (str): The content of the comment.
+        author (relationship): Relationship with the User model.
+    """
     __tablename__ = 'comments'
     id = Column(Integer, primary_key=True, index=True)
     image_id = Column(Integer, ForeignKey('images.id'), nullable=False)
     user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
     content = Column(Text, nullable=False)
     author = relationship(\"User\", back_populates=\"comments\")

-@app.get(\"/\")
+@app.get(\"/\")
async def read_root():
+    """
+    Root endpoint that returns a simple greeting.
+
+    Returns:
+        dict: A simple greeting message.
+    """
     return {\"Hello\": \"World\"}

 if __name__ == \"__main__\":
     import uvicorn
     uvicorn.run(app, host=\"0.0.0.0\", port=8000)
======== applied
from fastapi import FastAPI, UploadFile, File, HTTPException
from fastapi.responses import FileResponse
from pydantic import BaseModel
from sqlalchemy import create_engine, Column, Integer, String, Text, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
import os

app = FastAPI()

DATABASE_URL = "sqlite:///./database.db"
UPLOAD_FOLDER = 'uploads'
MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB limit

# Ensure the upload folder exists
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

# Database setup
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

def get_db():
    """
    Provides a database session and ensures it gets closed after use.

    Yields:
        Session: A SQLAlchemy database session
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Models
class User(Base):
    """
    SQLAlchemy User model representing a user in the database.

    Attributes:
        id (int): Primary key for the user.
        username (str): Unique username for the user.
        password (str): Hashed password for the user.
        images (relationship): Relationship with the Image model.
        comments (relationship): Relationship with the Comment model.
    """
    __tablename__ = 'users'
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(80), unique=True, nullable=False)
    password = Column(String(120), nullable=False)
    images = relationship("Image", back_populates="owner")
    comments = relationship("Comment", back_populates="author")

class Image(Base):
    """
    SQLAlchemy Image model representing an image uploaded by a user.

    Attributes:
        id (int): Primary key for the image.
        user_id (int): Foreign key to the user who uploaded the image.
        filename (str): Name of the uploaded image file.
        description (str, optional): Description of the image.
        owner (relationship): Relationship with the User model.
    """
    __tablename__ = 'images'
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    filename = Column(String(120), nullable=False)
    description = Column(Text, nullable=True)
    owner = relationship("User", back_populates="images")

class Comment(Base):
    """
    SQLAlchemy Comment model representing a comment made by a user on an image.

    Attributes:
        id (int): Primary key for the comment.
        image_id (int): Foreign key to the image being commented on.
        user_id (int): Foreign key to the user who made the comment.
        content (str): The content of the comment.
        author (relationship): Relationship with the User model.
    """
    __tablename__ = 'comments'
    id = Column(Integer, primary_key=True, index=True)
    image_id = Column(Integer, ForeignKey('images.id'), nullable=False)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    content = Column(Text, nullable=False)
    author = relationship("User", back_populates="comments")

# Create database tables
Base.metadata.create_all(bind=engine)

@app.get("/")
async def read_root():
    """
    Root endpoint that returns a simple greeting.

    Returns:
        dict: A simple greeting message.
    """
    return {"Hello": "World"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)