======== original
import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaPlay, FaPause, FaHeart, FaEllipsisH, FaMusic } from 'react-icons/fa';
import { Playlist, Song } from '../App';

interface MainContentProps {
  selectedPlaylist: Playlist | null;
  songs: Song[];
}

const MainContent: React.FC<MainContentProps> = ({ selectedPlaylist, songs }) => {
  const [hoveredSong, setHoveredSong] = useState<number | null>(null);
  const [isPlaying, setIsPlaying] = useState<number | null>(null);
  const [likedSongs, setLikedSongs] = useState<number[]>([]);

  const handlePlay = (songId: number) => {
    setIsPlaying(prevId => prevId === songId ? null : songId);
  };

  const handleLike = (songId: number) => {
    setLikedSongs(prev =>
      prev.includes(songId) ? prev.filter(id => id !== songId) : [...prev, songId]
    );
  };

  return (
    <motion.main
      className="flex-1 overflow-y-auto p-8 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {selectedPlaylist && (
        <motion.h2
          className="text-4xl font-bold mb-8 text-gray-800 dark:text-white"
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          {selectedPlaylist.name} ({selectedPlaylist.songCount} 首歌曲)
        </motion.h2>
      )}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <AnimatePresence>
          {songs.map((song, index) => (
            <motion.div
              key={song.id}
              className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-2xl"
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0, transition: { type: "spring", stiffness: 100 } }}
              exit={{ opacity: 0, y: -50 }}
              transition={{ delay: index * 0.1 }}
              whileHover={{
                scale: 1.05,
                rotateY: 5,
                boxShadow: "0 20px 30px rgba(0,0,0,0.2)"
              }}
              onHoverStart={() => setHoveredSong(song.id)}
              onHoverEnd={() => setHoveredSong(null)}
            >
              <div className="relative pb-[100%]">
                <img
                  src={song.coverUrl}
                  alt={song.album}
                  className="absolute inset-0 w-full h-full object-cover transition-transform duration-300 transform hover:scale-110 rounded-t-2xl"
                />
                <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300">
                  <motion.button
                    whileHover={{ scale: 1.2, rotate: 360 }}
                    whileTap={{ scale: 0.9 }}
                    className="bg-white text-primary p-4 rounded-full shadow-lg"
                    onClick={() => handlePlay(song.id)}
                  >
                    {isPlaying === song.id ? <FaPause /> : <FaPlay />}
                  </motion.button>
                </div>
              </div>
              <motion.div
                className="p-6"
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.2 }}
              >
                <h3 className="font-bold text-xl mb-2 text-gray-800 dark:text-white truncate">{song.name}</h3>
                <p className="text-base text-gray-600 dark:text-gray-300 mb-4 truncate">{song.artist}</p>
                <div className="flex justify-between items-center mt-2">
                  <span className="text-sm text-gray-500 dark:text-gray-400 flex items-center">
                    <FaMusic className="mr-1" /> {song.duration}
                  </span>
                  <div className="flex space-x-2">
                    <motion.button
                      whileHover={{ scale: 1.2 }}
                      whileTap={{ scale: 0.9 }}
                      className={`text-2xl ${likedSongs.includes(song.id) ? 'text-red-500' : 'text-gray-400 hover:text-red-500'}`}
                      onClick={() => handleLike(song.id)}
                    >
                      <FaHeart />
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.2, rotate: 180 }}
                      whileTap={{ scale: 0.9 }}
                      className="text-2xl text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                    >
                      <FaEllipsisH />
                    </motion.button>
                  </div>
                </div>
              </motion.div>
              {isPlaying === song.id && (
                <motion.div
                  className="absolute bottom-0 left-0 right-0 h-1 bg-primary"
                  initial={{ width: 0 }}
                  animate={{ width: '100%' }}
                  transition={{ duration: 30, ease: 'linear' }}
                />
              )}
            </motion.div>
          ))}
        </AnimatePresence>
      </div>
    </motion.main>
  );
};

export default MainContent;
======== diff
@@ -34,7 +34,7 @@
     >
       {selectedPlaylist && (
         <motion.h2
-          className=\"text-4xl font-bold mb-8 text-gray-800 dark:text-white\"
+          className=\"text-3xl font-bold mb-6 text-gray-800 dark:text-white\"
           initial={{ y: -20, opacity: 0 }}
           animate={{ y: 0, opacity: 1 }}
           transition={{ delay: 0.2 }}
@@ -42,12 +42,12 @@
           {selectedPlaylist.name} ({selectedPlaylist.songCount} 首歌曲)
         </motion.h2>
       )}
-      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">
+      <div className=\"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4\">
         <AnimatePresence>
           {songs.map((song, index) => (
             <motion.div
               key={song.id}
-              className=\"bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-2xl\"
+              className=\"bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden transform transition-all duration-300 hover:shadow-md\"
               initial={{ opacity: 0, y: 50 }}
               animate={{ opacity: 1, y: 0, transition: { type: \"spring\", stiffness: 100 } }}
               exit={{ opacity: 0, y: -50 }}
@@ -55,7 +55,7 @@
               whileHover={{
                 scale: 1.05,
                 rotateY: 5,
-                boxShadow: \"0 20px 30px rgba(0,0,0,0.2)\"
+                boxShadow: \"0 10px 20px rgba(0,0,0,0.1)\"
               }}
               onHoverStart={() => setHoveredSong(song.id)}
               onHoverEnd={() => setHoveredSong(null)}
@@ -64,13 +64,13 @@
-                  className=\"absolute inset-0 w-full h-full object-cover transition-transform duration-300 transform hover:scale-110 rounded-t-2xl\"
+                  className=\"absolute inset-0 w-full h-full object-cover transition-transform duration-300 transform hover:scale-110 rounded-t-lg\"
                 />
                 <div className=\"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300\">
                   <motion.button
                     whileHover={{ scale: 1.2, rotate: 360 }}
                     whileTap={{ scale: 0.9 }}
-                    className=\"bg-white text-primary p-4 rounded-full shadow-lg\"
+                    className=\"bg-white text-primary p-2 rounded-full shadow-lg\"
                     onClick={() => handlePlay(song.id)}
                   >
                     {isPlaying === song.id ? <FaPause /> : <FaPlay />}
@@ -78,12 +78,12 @@
                 </div>
               </div>
               <motion.div
-                className=\"p-6\"
+                className=\"p-3\"
                 initial={{ y: 20, opacity: 0 }}
                 animate={{ y: 0, opacity: 1 }}
                 transition={{ delay: 0.2 }}
               >
-                <h3 className=\"font-bold text-xl mb-2 text-gray-800 dark:text-white truncate\">{song.name}</h3>
+                <h3 className=\"font-semibold text-base mb-1 text-gray-800 dark:text-white truncate\">{song.name}</h3>
                 <p className=\"text-base text-gray-600 dark:text-gray-300 mb-4 truncate\">{song.artist}</p>
                 <div className=\"flex justify-between items-center mt-2\">
                   <span className=\"text-sm text-gray-500 dark:text-gray-400 flex items-center\">
@@ -93,7 +93,7 @@
                     <motion.button
                       whileHover={{ scale: 1.2 }}
                       whileTap={{ scale: 0.9 }}
-                      className={`text-2xl ${likedSongs.includes(song.id) ? 'text-red-500' : 'text-gray-400 hover:text-red-500'}`}
+                      className={`text-xl ${likedSongs.includes(song.id) ? 'text-red-500' : 'text-gray-400 hover:text-red-500'}`}
                       onClick={() => handleLike(song.id)}
                     >
                       <FaHeart />
@@ -101,7 +101,7 @@
-                      className=\"text-2xl text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\"
+                      className=\"text-xl text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\"
                     >
                       <FaEllipsisH />
                     </motion.button>
======== applied
import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaPlay, FaPause, FaHeart, FaEllipsisH, FaMusic } from 'react-icons/fa';
import { Playlist, Song } from '../App';

interface MainContentProps {
  selectedPlaylist: Playlist | null;
  songs: Song[];
}

const MainContent: React.FC<MainContentProps> = ({ selectedPlaylist, songs }) => {
  const [hoveredSong, setHoveredSong] = useState<number | null>(null);
  const [isPlaying, setIsPlaying] = useState<number | null>(null);
  const [likedSongs, setLikedSongs] = useState<number[]>([]);

  const handlePlay = (songId: number) => {
    setIsPlaying(prevId => prevId === songId ? null : songId);
  };

  const handleLike = (songId: number) => {
    setLikedSongs(prev =>
      prev.includes(songId) ? prev.filter(id => id !== songId) : [...prev, songId]
    );
  };

  return (
    <motion.main
      className="flex-1 overflow-y-auto p-8 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {selectedPlaylist && (
        <motion.h2
          className="text-3xl font-bold mb-6 text-gray-800 dark:text-white"
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          {selectedPlaylist.name} ({selectedPlaylist.songCount} 首歌曲)
        </motion.h2>
      )}
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
        <AnimatePresence>
          {songs.map((song, index) => (
            <motion.div
              key={song.id}
              className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden transform transition-all duration-300 hover:shadow-md"
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0, transition: { type: "spring", stiffness: 100 } }}
              exit={{ opacity: 0, y: -50 }}
              transition={{ delay: index * 0.1 }}
              whileHover={{
                scale: 1.05,
                rotateY: 5,
                boxShadow: "0 10px 20px rgba(0,0,0,0.1)"
              }}
              onHoverStart={() => setHoveredSong(song.id)}
              onHoverEnd={() => setHoveredSong(null)}
            >
              <div className="relative pb-[100%]">
                <img
                  src={song.coverUrl}
                  alt={song.album}
                  className="absolute inset-0 w-full h-full object-cover transition-transform duration-300 transform hover:scale-110 rounded-t-lg"
                />
                <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300">
                  <motion.button
                    whileHover={{ scale: 1.2, rotate: 360 }}
                    whileTap={{ scale: 0.9 }}
                    className="bg-white text-primary p-2 rounded-full shadow-lg"
                    onClick={() => handlePlay(song.id)}
                  >
                    {isPlaying === song.id ? <FaPause /> : <FaPlay />}
                  </motion.button>
                </div>
              </div>
              <motion.div
                className="p-3"
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.2 }}
              >
                <h3 className="font-semibold text-base mb-1 text-gray-800 dark:text-white truncate">{song.name}</h3>
                <p className="text-base text-gray-600 dark:text-gray-300 mb-4 truncate">{song.artist}</p>
                <div className="flex justify-between items-center mt-2">
                  <span className="text-sm text-gray-500 dark:text-gray-400 flex items-center">
                    <FaMusic className="mr-1" /> {song.duration}
                  </span>
                  <div className="flex space-x-2">
                    <motion.button
                      whileHover={{ scale: 1.2 }}
                      whileTap={{ scale: 0.9 }}
                      className={`text-xl ${likedSongs.includes(song.id) ? 'text-red-500' : 'text-gray-400 hover:text-red-500'}`}
                      onClick={() => handleLike(song.id)}
                    >
                      <FaHeart />
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.2, rotate: 180 }}
                      whileTap={{ scale: 0.9 }}
                      className="text-xl text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                    >
                      <FaEllipsisH />
                    </motion.button>
                  </div>
                </div>
              </motion.div>
              {isPlaying === song.id && (
                <motion.div
                  className="absolute bottom-0 left-0 right-0 h-1 bg-primary"
                  initial={{ width: 0 }}
                  animate={{ width: '100%' }}
                  transition={{ duration: 30, ease: 'linear' }}
                />
              )}
            </motion.div>
          ))}
        </AnimatePresence>
      </div>
    </motion.main>
  );
};

export default MainContent;