======== original
package main

// #cgo CFLAGS: -I./src/helloworld/cpp/output
// #cgo CXXFLAGS: -std=c++11 -yp
// #cgo LDFLAGS: -lstdc++ -L./src/helloworld/cpp/output -lhelloworld
// #include "helloworld.h"
import "C"

// 使用CPP方式打印hello world
func HelloWorldInCPP() {
	C.PrintHelloInCPP()
}

func main() {
	HelloWorldInCPP()
}

// 注意，CXXFLAGS 中 -yp 非法
======== diff
--- gin_cgo_cpp_invalid_options.go
+++ gin_cgo_cpp_invalid_options.go
@@ -1,16 +1,24 @@
 package main

 // #cgo CFLAGS: -I./src/helloworld/cpp/output
-// #cgo CXXFLAGS: -std=c++11 -yp
+// #cgo CXXFLAGS: -std=c++11
 // #cgo LDFLAGS: -lstdc++ -L./src/helloworld/cpp/output -lhelloworld
 // #include \"helloworld.h\"
 import \"C\"
+import (
+    \"log\"
+)

 // 使用CPP方式打印hello world
 func HelloWorldInCPP() {
-\tC.PrintHelloInCPP()
+    log.Println(\"开始调用 C++ 函数 PrintHelloInCPP\")
+    C.PrintHelloInCPP()
+    log.Println(\"C++ 函数 PrintHelloInCPP 调用完成\")
 }

 func main() {
-\tHelloWorldInCPP()
+    log.Println(\"程序开始执行\")
+    HelloWorldInCPP()
+    log.Println(\"程序执行结束\")
 }
-
-// 注意，CXXFLAGS 中 -yp 非法
+
+// CXXFLAGS 中的非法选项 -yp 已被移除
======== applied
package main

// #cgo CFLAGS: -I./src/helloworld/cpp/output
// #cgo CXXFLAGS: -std=c++11
// #cgo LDFLAGS: -lstdc++ -L./src/helloworld/cpp/output -lhelloworld
// #include "helloworld.h"
import "C"
import (
    "log"
)

// 使用CPP方式打印hello world
func HelloWorldInCPP() {
    log.Println("开始调用 C++ 函数 PrintHelloInCPP")
    C.PrintHelloInCPP()
    log.Println("C++ 函数 PrintHelloInCPP 调用完成")
}

func main() {
    log.Println("程序开始执行")
    HelloWorldInCPP()
    log.Println("程序执行结束")
}

// CXXFLAGS 中的非法选项 -yp 已被移除