======== original
def bubble_sort(arr):
    n = len(arr)
    for i in range(n):
        for j in range(0, n-i-1):
            if arr[j] > arr[j+1]:
                arr[j], arr[j+1] = arr[j+1], arr[j]
    return arr

if __name__ == "__main__":
    test_list = [64, 34, 25, 12, 22, 11, 90]
    print("原始列表:", test_list)
    sorted_list = bubble_sort(test_list)
    print("排序后的列表:", sorted_list)
======== diff
--- bubble_sort.py
+++ bubble_sort.py
@@ -1,13 +1,32 @@
+# 定义冒泡排序函数
 def bubble_sort(arr):
+    # 获取数组长度
     n = len(arr)
+
+    # 外层循环：需要进行 n 次遍历
     for i in range(n):
+        # 内层循环：每次遍历都会将当前最大元素\"冒泡\"到末尾
+        # 因为每次遍历后最后 i 个元素已经排好序，所以只需要遍历到 n-i-1
         for j in range(0, n-i-1):
+            # 比较相邻元素，如果前一个元素大于后一个元素，则交换它们
             if arr[j] > arr[j+1]:
                 arr[j], arr[j+1] = arr[j+1], arr[j]
+
+    # 返回排序后的数组
     return arr

+# 如果这个脚本被直接运行（而不是被导入），则执行以下代码
 if __name__ == \"__main__\":
+    # 创建一个测试列表
     test_list = [64, 34, 25, 12, 22, 11, 90]
+
+    # 打印原始列表
     print(\"原始列表:\", test_list)
+
+    # 调用冒泡排序函数并获取排序后的列表
     sorted_list = bubble_sort(test_list)
+
+    # 打印排序后的列表
     print(\"排序后的列表:\", sorted_list)
+
+# 注意：冒泡排序的时间复杂度为 O(n^2)，对于大型数据集可能不是最优选择
======== applied
# 定义冒泡排序函数
def bubble_sort(arr):
    # 获取数组长度
    n = len(arr)

    # 外层循环：需要进行 n 次遍历
    for i in range(n):
        # 内层循环：每次遍历都会将当前最大元素"冒泡"到末尾
        # 因为每次遍历后最后 i 个元素已经排好序，所以只需要遍历到 n-i-1
        for j in range(0, n-i-1):
            # 比较相邻元素，如果前一个元素大于后一个元素，则交换它们
            if arr[j] > arr[j+1]:
                arr[j], arr[j+1] = arr[j+1], arr[j]

    # 返回排序后的数组
    return arr

# 如果这个脚本被直接运行（而不是被导入），则执行以下代码
if __name__ == "__main__":
    # 创建一个测试列表
    test_list = [64, 34, 25, 12, 22, 11, 90]

    # 打印原始列表
    print("原始列表:", test_list)

    # 调用冒泡排序函数并获取排序后的列表
    sorted_list = bubble_sort(test_list)

    # 打印排序后的列表
    print("排序后的列表:", sorted_list)

# 注意：冒泡排序的时间复杂度为 O(n^2)，对于大型数据集可能不是最优选择