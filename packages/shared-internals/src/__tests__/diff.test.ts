// 可以单独执行 ./node_modules/.bin/vitest packages/vscode/src/services/Composer/__tests__/
import path from 'node:path';
import {fileURLToPath} from 'node:url';
import {readFileSync, readdirSync} from 'node:fs';
import {describe, expect, test} from 'vitest';
import {applyDiff} from '../bridge/udiff.js';

const filename = fileURLToPath(import.meta.url);
const dirname = path.dirname(filename);

const extractDiffsFromContent = (testcase: string) => {
    const lines = testcase.split('\n');
    const [original, diff, newContent] = lines.reduce<string[][]>(
        (result, line) => {
            if (line.startsWith('========')) {
                result.push([]);
            }
            else {
                result[result.length - 1].push(line);
            }
            return result;
        },
        []
    );
    return {original: original.join('\n'), diff: diff.join('\n'), newContent: newContent.join('\n')};
};

const submodules = readdirSync(path.join(dirname, 'cases'));
const cases = submodules.filter(name => name.startsWith('case')).map(submodule => {
    const testcase = readFileSync(path.join(dirname, 'cases', submodule), 'utf-8');
    return {name: submodule, ...extractDiffsFromContent(testcase)};
});

describe('Composer', () => {
    for (const testcase of cases) {
        const {name, original, diff, newContent} = testcase;
        test(`applyDiff.${name}`, () => {
            expect(applyDiff(original, diff, {eol: '\n'})).toEqual(newContent);
        });
    }

    test('applied diff without marker should return diff as new content', () => {
        const testcase = readFileSync(path.join(dirname, 'cases', 'diff-without-marker.txt'), 'utf-8');
        const diffs = extractDiffsFromContent(testcase);
        expect(applyDiff(diffs.original, diffs.diff, {eol: '\n'})).toEqual(diffs.diff);
    });
});
